#!/usr/bin/env python3
"""Check all model files for syntax errors and patterns."""

import ast
import os
import re
from pathlib import Path
from typing import Dict, List, <PERSON><PERSON>

def check_file_syntax(file_path: Path) -> Tuple[bool, str]:
    """Check if a Python file has valid syntax."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        ast.parse(content)
        return True, ""
    except SyntaxError as e:
        return False, f"Line {e.lineno}: {e.msg}"
    except Exception as e:
        return False, str(e)

def check_old_error_patterns(file_path: Path) -> List[str]:
    """Check for old error handling patterns."""
    patterns = []
    old_patterns = [
        (r'from\s+.*utils\.circuit_breaker\s+import', 'Old circuit_breaker import'),
        (r'from\s+.*utils\.error_interface\s+import', 'Old error_interface import'),
        (r'from\s+.*interfaces\.error_interface\s+import', 'Old interfaces.error_interface import'),
        (r'@circuit_breaker', 'Old @circuit_breaker decorator'),
        (r'CircuitBreaker\(', 'Old CircuitBreaker usage'),
        (r'ErrorHandler\(', 'Old ErrorHandler usage'),
        (r'with\s+error_handler:', 'Old error_handler context manager'),
    ]
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            line_num = 0
            for line in content.splitlines():
                line_num += 1
                for pattern, desc in old_patterns:
                    if re.search(pattern, line):
                        patterns.append(f"Line {line_num}: {desc}")
    except Exception as e:
        patterns.append(f"Error reading file: {e}")
    
    return patterns

def check_python_310_syntax(file_path: Path) -> List[str]:
    """Check for Python 3.10+ syntax that needs conversion."""
    py310_patterns = []
    patterns = [
        (r'match\s+\w+:', 'Match statement (Python 3.10+)'),
        (r'case\s+\w+:', 'Case statement (Python 3.10+)'),
        (r'\|\s*=', 'Union operator |= (Python 3.9+)'),
        (r':\s*.*\|.*\s*=', 'Union type hint (Python 3.10+)'),
    ]
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            line_num = 0
            for line in content.splitlines():
                line_num += 1
                for pattern, desc in patterns:
                    if re.search(pattern, line):
                        py310_patterns.append(f"Line {line_num}: {desc}")
    except Exception as e:
        py310_patterns.append(f"Error reading file: {e}")
    
    return py310_patterns

def main():
    """Main function to check all model files."""
    models_dir = Path("src/yemen_trade_diagnostic/models")
    
    if not models_dir.exists():
        print("Models directory not found!")
        return
    
    all_files = list(models_dir.rglob("*.py"))
    print(f"Found {len(all_files)} Python files in models directory\n")
    
    issues_summary = {
        'syntax_errors': [],
        'old_error_patterns': [],
        'python_310_syntax': [],
        'clean_files': []
    }
    
    for file_path in sorted(all_files):
        relative_path = file_path.relative_to(models_dir)
        print(f"\nChecking: {relative_path}")
        
        # Check syntax
        is_valid, error_msg = check_file_syntax(file_path)
        if not is_valid:
            issues_summary['syntax_errors'].append((relative_path, error_msg))
            print(f"  ❌ Syntax Error: {error_msg}")
        
        # Check old error patterns
        old_patterns = check_old_error_patterns(file_path)
        if old_patterns:
            issues_summary['old_error_patterns'].append((relative_path, old_patterns))
            print(f"  ⚠️  Old error patterns found:")
            for pattern in old_patterns:
                print(f"     - {pattern}")
        
        # Check Python 3.10+ syntax
        py310_syntax = check_python_310_syntax(file_path)
        if py310_syntax:
            issues_summary['python_310_syntax'].append((relative_path, py310_syntax))
            print(f"  ⚠️  Python 3.10+ syntax found:")
            for syntax in py310_syntax:
                print(f"     - {syntax}")
        
        # If no issues, mark as clean
        if is_valid and not old_patterns and not py310_syntax:
            issues_summary['clean_files'].append(relative_path)
            print(f"  ✅ Clean")
    
    # Print summary
    print("\n" + "="*80)
    print("SUMMARY")
    print("="*80)
    
    print(f"\n🔴 Files with syntax errors: {len(issues_summary['syntax_errors'])}")
    for file_path, error in issues_summary['syntax_errors']:
        print(f"  - {file_path}: {error}")
    
    print(f"\n🟡 Files with old error patterns: {len(issues_summary['old_error_patterns'])}")
    for file_path, patterns in issues_summary['old_error_patterns']:
        print(f"  - {file_path} ({len(patterns)} issues)")
    
    print(f"\n🟠 Files with Python 3.10+ syntax: {len(issues_summary['python_310_syntax'])}")
    for file_path, syntaxes in issues_summary['python_310_syntax']:
        print(f"  - {file_path} ({len(syntaxes)} issues)")
    
    print(f"\n✅ Clean files: {len(issues_summary['clean_files'])}")
    
    print(f"\nTotal files checked: {len(all_files)}")
    print(f"Files needing attention: {len(all_files) - len(issues_summary['clean_files'])}")

if __name__ == "__main__":
    main()