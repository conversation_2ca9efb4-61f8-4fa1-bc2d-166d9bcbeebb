#!/usr/bin/env python3
"""Test script to verify pipeline migrations to new data loader."""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """Test that all pipelines can be imported without errors."""
    print("Testing pipeline imports...")
    
    try:
        from src.yemen_trade_diagnostic.pipelines.rca_exact import RCAExactPipeline
        print("✓ rca_exact.py imports successfully")
    except ImportError as e:
        print(f"✗ Error importing rca_exact.py: {e}")
        return False
    
    try:
        from src.yemen_trade_diagnostic.pipelines.market_v2_updated import MarketPipelineV2
        print("✓ market_v2_updated.py imports successfully")
    except ImportError as e:
        print(f"✗ Error importing market_v2_updated.py: {e}")
        return False
    
    try:
        from src.yemen_trade_diagnostic.pipelines.sophistication_v2_updated import SophisticationPipelineV2
        print("✓ sophistication_v2_updated.py imports successfully")
    except ImportError as e:
        print(f"✗ Error importing sophistication_v2_updated.py: {e}")
        return False
    
    print("\nAll pipeline imports successful!")
    return True

def test_data_loader_usage():
    """Test that the new data loader is being used correctly."""
    print("\nTesting data loader usage...")
    
    # Check for any remaining old imports
    pipeline_dir = project_root / "src" / "yemen_trade_diagnostic" / "pipelines"
    old_import_patterns = [
        "unified_loader_api",
        "MarketDataProvider",
        "old_data_system"
    ]
    
    issues_found = False
    for py_file in pipeline_dir.glob("*.py"):
        content = py_file.read_text()
        for pattern in old_import_patterns:
            if pattern in content and "# Project imports" not in content:  # Exclude comments
                # Check if it's actually being used
                lines = content.split('\n')
                for i, line in enumerate(lines):
                    if pattern in line and not line.strip().startswith('#'):
                        print(f"✗ Found old import '{pattern}' in {py_file.name} at line {i+1}")
                        issues_found = True
    
    if not issues_found:
        print("✓ No old loader imports found")
    
    return not issues_found

def main():
    """Run all tests."""
    print("=== Testing Pipeline Migrations ===\n")
    
    import_success = test_imports()
    loader_success = test_data_loader_usage()
    
    if import_success and loader_success:
        print("\n✅ All tests passed! Pipeline migrations completed successfully.")
        return 0
    else:
        print("\n❌ Some tests failed. Please check the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())