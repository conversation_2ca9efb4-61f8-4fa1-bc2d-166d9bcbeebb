from typing import Any, Dict, Optional, Union
import os
import pandas as pd
import tempfile
import base64
from pathlib import Path

from yemen_trade_diagnostic.errors import protect, error_context, OperationType, VISUALIZATION_CONFIG


@protect("export_chart_to_excel", OperationType.VISUALIZATION)
def export_to_excel(chart: Any, output_path: str) -> str:
    """Export chart to Excel with data.
    
    Args:
        chart: Chart object
        output_path: Path to save Excel file
        
    Returns:
        Path to saved file
        
    Raises:
        ValueError: If chart data is not compatible with Excel
    """
    # Ensure we have data that can be exported
    if not hasattr(chart, "data") or chart.data is None:
        raise ValueError("Chart has no data to export to Excel")
    
    # Make sure directory exists
    os.makedirs(os.path.dirname(os.path.abspath(output_path)), exist_ok=True)
    
    # Get chart data
    data = chart.data
    
    # Create Excel writer
    with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
        # Export data to Excel
        if isinstance(data, pd.DataFrame):
            data.to_excel(writer, sheet_name='Data', index=True)
        elif isinstance(data, dict):
            # Convert dict to dataframe
            df = pd.DataFrame.from_dict(data, orient='index')
            df.to_excel(writer, sheet_name='Data', index=True)
        else:
            # Try to convert to dataframe
            try:
                df = pd.DataFrame(data)
                df.to_excel(writer, sheet_name='Data', index=True)
            except:
                raise ValueError(f"Cannot export data of type {type(data)} to Excel")
        
        # Get workbook and worksheet
        workbook = writer.book
        worksheet = writer.sheets['Data']
        
        # Create a chart sheet
        try:
            # Use openpyxl to create a chart if available
            import openpyxl
            from openpyxl.chart import (
                BarChart, LineChart, PieChart, ScatterChart, Reference
            )
            
            # Determine the chart type
            chart_type = chart.__class__.__name__
            last_row = len(data) + 1  # +1 for header
            last_col = len(data.columns) if hasattr(data, 'columns') else 1
            
            # Create chart based on type
            if 'LineChart' in chart_type:
                excel_chart = LineChart()
                excel_chart.title = chart.title
                excel_chart.x_axis.title = chart.x_label
                excel_chart.y_axis.title = chart.y_label
                
                # Add data
                data_ref = Reference(worksheet, min_col=2, min_row=1, max_row=last_row, max_col=last_col)
                cats_ref = Reference(worksheet, min_col=1, min_row=2, max_row=last_row)
                excel_chart.add_data(data_ref, titles_from_data=True)
                excel_chart.set_categories(cats_ref)
                
            elif 'BarChart' in chart_type:
                excel_chart = BarChart()
                excel_chart.title = chart.title
                excel_chart.x_axis.title = chart.x_label
                excel_chart.y_axis.title = chart.y_label
                
                # Set orientation
                if hasattr(chart, 'bar_config') and hasattr(chart.bar_config, 'orientation'):
                    excel_chart.type = "col" if chart.bar_config.orientation == "vertical" else "bar"
                
                # Add data
                data_ref = Reference(worksheet, min_col=2, min_row=1, max_row=last_row, max_col=last_col)
                cats_ref = Reference(worksheet, min_col=1, min_row=2, max_row=last_row)
                excel_chart.add_data(data_ref, titles_from_data=True)
                excel_chart.set_categories(cats_ref)
                
            elif 'PieChart' in chart_type:
                excel_chart = PieChart()
                excel_chart.title = chart.title
                
                # Add data
                data_ref = Reference(worksheet, min_col=2, min_row=1, max_row=last_row, max_col=2)
                cats_ref = Reference(worksheet, min_col=1, min_row=2, max_row=last_row)
                excel_chart.add_data(data_ref, titles_from_data=True)
                excel_chart.set_categories(cats_ref)
                
            elif 'ScatterChart' in chart_type:
                excel_chart = ScatterChart()
                excel_chart.title = chart.title
                excel_chart.x_axis.title = chart.x_label
                excel_chart.y_axis.title = chart.y_label
                
                # Add data
                x_ref = Reference(worksheet, min_col=1, min_row=2, max_row=last_row)
                y_ref = Reference(worksheet, min_col=2, min_row=1, max_row=last_row)
                series = openpyxl.chart.Series(y_ref, x_ref, title_from_data=True)
                excel_chart.series.append(series)
                
            else:
                # Default to line chart
                excel_chart = LineChart()
                excel_chart.title = chart.title
                
                # Add data
                data_ref = Reference(worksheet, min_col=2, min_row=1, max_row=last_row, max_col=last_col)
                cats_ref = Reference(worksheet, min_col=1, min_row=2, max_row=last_row)
                excel_chart.add_data(data_ref, titles_from_data=True)
                excel_chart.set_categories(cats_ref)
                
            # Add chart to worksheet
            chart_sheet = workbook.create_sheet(title="Chart")
            chart_sheet.add_chart(excel_chart, "A1")
            
        except (ImportError, ModuleNotFoundError, AttributeError) as e:
            # If openpyxl charts are not available, embed an image
            try:
                # Render chart and save as temporary image
                chart.render()
                
                with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp:
                    chart.save(tmp.name, format='png')
                    
                    # Add a new worksheet for the chart
                    chart_sheet = workbook.create_sheet(title="Chart")
                    
                    # Add image to worksheet
                    from openpyxl.drawing.image import Image
                    img = Image(tmp.name)
                    chart_sheet.add_image(img, 'A1')
                    
                    # Delete temporary file
                    os.unlink(tmp.name)
            except Exception as img_error:
                # Failed to embed chart image - silently continue
                pass
    
    return output_path


@protect("embed_chart_in_excel", OperationType.VISUALIZATION)
def embed_chart_in_existing_excel(chart: Any, excel_path: str, sheet_name: str = "Chart",
                                 cell_pos: str = "A1") -> str:
    """Embed chart in an existing Excel file.
    
    Args:
        chart: Chart object
        excel_path: Path to Excel file
        sheet_name: Sheet name to add chart
        cell_pos: Cell position to add chart
        
    Returns:
        Path to modified Excel file
    """
    import openpyxl
    from openpyxl.drawing.image import Image
    
    # Check if file exists
    if not os.path.exists(excel_path):
        raise FileNotFoundError(f"Excel file {excel_path} does not exist")
    
    # Render chart and save as temporary image
    chart.render()
    
    with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp:
        chart.save(tmp.name, format='png')
        
        # Open the Excel file
        workbook = openpyxl.load_workbook(excel_path)
        
        # Create a new sheet if it doesn't exist
        if sheet_name not in workbook.sheetnames:
            workbook.create_sheet(title=sheet_name)
            
        worksheet = workbook[sheet_name]
        
        # Add image to worksheet
        img = Image(tmp.name)
        worksheet.add_image(img, cell_pos)
        
        # Save workbook
        workbook.save(excel_path)
        
        # Delete temporary file
        os.unlink(tmp.name)
    
    return excel_path