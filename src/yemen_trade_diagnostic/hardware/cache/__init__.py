"""
Unified hardware-accelerated cache system.

This module provides a consolidated cache implementation that combines
all the advanced features from various cache utilities:
- Hardware acceleration (Metal, CUDA, etc.)
- Semantic key generation
- Multi-tier storage (memory, disk, progressive)
- Smart invalidation strategies
- Predictive cache warming
- Dependency tracking
- Adaptive memory management

Example usage:
    ```python
    from yemen_trade_diagnostic.hardware.cache import UnifiedCache
    
    # Simple usage with auto-configuration
    cache = UnifiedCache()
    
    # Get or compute a value
    result = cache.get("my_key", compute_func, arg1, arg2)
    
    # Advanced usage with full configuration
    cache = Cache(
        strategy="semantic",      # Semantic key generation
        warming="predictive",     # Predictive cache warming
        invalidation="smart",     # Smart invalidation
        storage_tiers=["memory", "disk", "progressive"],
        enable_compression=True,
        enable_hardware_acceleration=True
    )
    
    # Using decorators
    @cache.memoize(ttl=3600)
    def expensive_computation(data):
        return process(data)
    ```
"""

from yemen_trade_diagnostic.hardware.cache.core import (
    CacheEntry,
    CacheStrategy,
    UnifiedCache,
    create_cache,
)
from yemen_trade_diagnostic.hardware.cache.keys import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>ache<PERSON>eyOptimizer,
    Cache<PERSON>eyType,
    get_cache_key_optimizer,
    optimize_cache_key,
)
from yemen_trade_diagnostic.hardware.cache.storage import (
    DiskStorage,
    MemoryStorage,
    ProgressiveStorage,
    StorageTier,
)
from yemen_trade_diagnostic.hardware.cache.strategies import (
    DependencyInvalidationStrategy,
    EvictionStrategy,
    InvalidationPatternAnalyzer,
    InvalidationReason,
    InvalidationStrategy,
    IntelligentWarmingStrategy,
    LRUEvictionStrategy,
    PriorityBasedEvictionStrategy,
    SmartInvalidationStrategy,
    WarmingStrategy,
)
from yemen_trade_diagnostic.hardware.cache.strategies.compression import (
    AdaptiveCompressionStrategy,
    CompressionAlgorithm,
    CompressionBenchmark,
    CompressionStrategy,
)

# Default cache instance (singleton)
_default_cache = None


def get_cache(**kwargs) -> UnifiedCache:
    """
    Get or create the default cache instance.
    
    Args:
        **kwargs: Configuration options for cache creation
        
    Returns:
        UnifiedCache instance
    """
    global _default_cache
    if _default_cache is None:
        _default_cache = create_cache(**kwargs)
    return _default_cache


# Convenience class alias
Cache = UnifiedCache

# Export main components
__all__ = [
    # Core
    'Cache',
    'UnifiedCache',
    'CacheEntry',
    'CacheStrategy',
    'create_cache',
    'get_cache',
    
    # Keys
    'CacheKeyType',
    'CacheKeyComponents',
    'CacheKeyOptimizer',
    'get_cache_key_optimizer',
    'optimize_cache_key',
    
    # Storage
    'StorageTier',
    'MemoryStorage',
    'DiskStorage', 
    'ProgressiveStorage',
    
    # Strategies
    'EvictionStrategy',
    'LRUEvictionStrategy',
    'PriorityBasedEvictionStrategy',
    'WarmingStrategy',
    'IntelligentWarmingStrategy',
    'InvalidationStrategy',
    'DependencyInvalidationStrategy',
    'SmartInvalidationStrategy',
    'InvalidationPatternAnalyzer',
    'InvalidationReason',
    'CompressionStrategy',
    'AdaptiveCompressionStrategy',
    'CompressionAlgorithm',
    'CompressionBenchmark',
]