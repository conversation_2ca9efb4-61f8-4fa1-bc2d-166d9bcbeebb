"""
Disk storage tier implementation.

This module provides a disk-based storage backend for the cache system,
with optimized I/O patterns and file management.
"""

import hashlib
import json
import os
import pickle
import shutil
import threading
import time
from pathlib import Path
from typing import Any, Dict, Optional

from yemen_trade_diagnostic.errors import protect, OperationType, HARDWARE_CONFIG
from yemen_trade_diagnostic.interfaces.logging_interface import get_logger

logger = get_logger(__name__)


class DiskStorage:
    """
    Disk-based storage tier for larger or less frequently accessed data.
    
    This storage tier persists data to disk, providing larger capacity
    at the cost of slower access times compared to memory storage.
    """
    
    def __init__(self, cache_dir: Path, max_size_mb: int = 10000):
        """
        Initialize disk storage.
        
        Args:
            cache_dir: Directory for cache files
            max_size_mb: Maximum storage size in MB
        """
        self.cache_dir = Path(cache_dir)
        self.max_size_mb = max_size_mb
        self.max_size_bytes = max_size_mb * 1024 * 1024
        
        # Create cache directory
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # Metadata tracking
        self.metadata_file = self.cache_dir / "_metadata.json"
        self._metadata: Dict[str, Dict[str, Any]] = {}
        self._lock = threading.RLock()
        
        # Load existing metadata
        self._load_metadata()
        
        # Size tracking
        self._current_size = self._calculate_current_size()
        
        logger.info(f"Initialized DiskStorage at {cache_dir} with {max_size_mb}MB limit")
    
    def store(self, key: str, value: Any, metadata: Optional[Dict[str, Any]] = None) -> bool:
        """
        Store a value on disk.
        
        Args:
            key: Storage key
            value: Value to store
            metadata: Optional metadata
            
        Returns:
            True if stored successfully
        """
        with self._lock:
            # Generate file path
            file_path = self._get_file_path(key)
            
            # Serialize and write
            try:
                # Ensure parent directory exists
                file_path.parent.mkdir(parents=True, exist_ok=True)
                
                # Write data
                with open(file_path, 'wb') as f:
                    pickle.dump(value, f, protocol=pickle.HIGHEST_PROTOCOL)
                
                # Get file size
                file_size = file_path.stat().st_size
                
                # Check capacity
                if self._current_size + file_size > self.max_size_bytes:
                    # Remove the file we just wrote
                    file_path.unlink()
                    logger.warning(f"Disk storage full, cannot store key: {key}")
                    return False
                
                # Update metadata
                self._metadata[key] = {
                    "file_path": str(file_path),
                    "size": file_size,
                    "created_at": time.time(),
                    "metadata": metadata or {}
                }
                self._save_metadata()
                
                self._current_size += file_size
                
                return True
                
            except Exception as e:
                logger.error(f"Failed to store key {key} to disk: {e}")
                # Clean up partial file
                if file_path.exists():
                    file_path.unlink()
                return False
    
    def retrieve(self, key: str) -> Optional[Any]:
        """
        Retrieve a value from disk.
        
        Args:
            key: Storage key
            
        Returns:
            Stored value or None
        """
        with self._lock:
            if key not in self._metadata:
                return None
            
            file_path = Path(self._metadata[key]["file_path"])
            
            try:
                with open(file_path, 'rb') as f:
                    return pickle.load(f)
            except Exception as e:
                logger.error(f"Failed to retrieve key {key} from disk: {e}")
                # Remove corrupted entry
                self.remove(key)
                return None
    
    def remove(self, key: str) -> bool:
        """
        Remove a value from disk.
        
        Args:
            key: Storage key
            
        Returns:
            True if removed successfully
        """
        with self._lock:
            if key not in self._metadata:
                return False
            
            entry = self._metadata[key]
            file_path = Path(entry["file_path"])
            
            # Remove file
            try:
                if file_path.exists():
                    file_path.unlink()
                    self._current_size -= entry["size"]
            except Exception as e:
                logger.error(f"Failed to remove file {file_path}: {e}")
            
            # Remove metadata
            del self._metadata[key]
            self._save_metadata()
            
            return True
    
    def exists(self, key: str) -> bool:
        """Check if a key exists in storage."""
        with self._lock:
            if key not in self._metadata:
                return False
            
            # Verify file actually exists
            file_path = Path(self._metadata[key]["file_path"])
            if not file_path.exists():
                # Clean up orphaned metadata
                del self._metadata[key]
                self._save_metadata()
                return False
            
            return True
    
    def get_metadata(self, key: str) -> Optional[Dict[str, Any]]:
        """Get metadata for a stored key."""
        with self._lock:
            if key in self._metadata:
                return self._metadata[key].get("metadata", {})
            return None
    
    def update_metadata(self, key: str, metadata: Dict[str, Any]) -> bool:
        """Update metadata for a stored key."""
        with self._lock:
            if key in self._metadata:
                self._metadata[key]["metadata"].update(metadata)
                self._save_metadata()
                return True
            return False
    
    def clear(self) -> None:
        """Clear all stored data."""
        with self._lock:
            # Remove all cache files
            try:
                shutil.rmtree(self.cache_dir)
                self.cache_dir.mkdir(parents=True, exist_ok=True)
            except Exception as e:
                logger.error(f"Failed to clear disk storage: {e}")
            
            # Clear metadata
            self._metadata.clear()
            self._save_metadata()
            self._current_size = 0
    
    def get_stats(self) -> Dict[str, Any]:
        """Get storage statistics."""
        with self._lock:
            return {
                "type": "disk",
                "cache_dir": str(self.cache_dir),
                "max_size_mb": self.max_size_mb,
                "current_size_mb": self._current_size / (1024 * 1024),
                "usage_percent": (self._current_size / self.max_size_bytes) * 100,
                "entry_count": len(self._metadata),
                "average_entry_size_kb": (
                    (self._current_size / len(self._metadata) / 1024)
                    if self._metadata else 0
                ),
            }
    
    def cleanup_orphaned_files(self) -> int:
        """
        Clean up files that exist on disk but not in metadata.
        
        Returns:
            Number of files cleaned up
        """
        cleaned = 0
        
        with self._lock:
            # Get all file paths from metadata
            metadata_paths = {
                Path(entry["file_path"])
                for entry in self._metadata.values()
            }
            
            # Find all cache files on disk
            for file_path in self.cache_dir.rglob("*.pkl"):
                if file_path != self.metadata_file and file_path not in metadata_paths:
                    try:
                        file_path.unlink()
                        cleaned += 1
                        logger.debug(f"Cleaned up orphaned file: {file_path}")
                    except Exception as e:
                        logger.error(f"Failed to clean up {file_path}: {e}")
        
        return cleaned
    
    def _get_file_path(self, key: str) -> Path:
        """Generate a file path for a cache key."""
        # Use hash to create a safe filename
        key_hash = hashlib.sha256(key.encode()).hexdigest()
        
        # Create subdirectories to avoid too many files in one directory
        subdir = key_hash[:2]
        filename = f"{key_hash}.pkl"
        
        return self.cache_dir / subdir / filename
    
    def _load_metadata(self) -> None:
        """Load metadata from disk."""
        if self.metadata_file.exists():
            try:
                with open(self.metadata_file, 'r') as f:
                    self._metadata = json.load(f)
            except Exception as e:
                logger.error(f"Failed to load metadata: {e}")
                self._metadata = {}
    
    def _save_metadata(self) -> None:
        """Save metadata to disk."""
        try:
            with open(self.metadata_file, 'w') as f:
                json.dump(self._metadata, f, indent=2)
        except Exception as e:
            logger.error(f"Failed to save metadata: {e}")
    
    def _calculate_current_size(self) -> int:
        """Calculate the current total size of cached files."""
        total_size = 0
        
        # Check each file in metadata
        for key, entry in list(self._metadata.items()):
            file_path = Path(entry["file_path"])
            if file_path.exists():
                total_size += entry["size"]
            else:
                # Clean up missing files
                del self._metadata[key]
        
        # Save cleaned metadata if needed
        self._save_metadata()
        
        return total_size