"""
Storage tier implementations for the unified cache.

This module provides different storage backends for the cache system,
including memory, disk, and progressive storage tiers.
"""

from enum import Enum

from yemen_trade_diagnostic.hardware.cache.storage.disk import DiskStorage
from yemen_trade_diagnostic.hardware.cache.storage.memory import MemoryStorage
from yemen_trade_diagnostic.hardware.cache.storage.progressive import ProgressiveStorage


class StorageTier(Enum):
    """Storage tier types."""
    MEMORY = "memory"
    DISK = "disk"
    PROGRESSIVE = "progressive"


__all__ = [
    'StorageTier',
    'MemoryStorage',
    'DiskStorage',
    'ProgressiveStorage',
]