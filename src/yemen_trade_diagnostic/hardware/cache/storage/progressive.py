"""
Progressive storage tier implementation.

This module provides a progressive caching mechanism for long-running operations,
allowing intermediate results to be cached and reused.
"""

import json
import pickle
import threading
import time
from dataclasses import dataclass
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

from yemen_trade_diagnostic.errors import protect, OperationType, HARDWARE_CONFIG
from yemen_trade_diagnostic.interfaces.logging_interface import get_logger

logger = get_logger(__name__)


@dataclass
class ProgressiveEntry:
    """Represents a progressive cache entry."""
    key: str
    total_steps: int
    completed_steps: int
    checkpoints: Dict[int, str]  # step -> file path
    metadata: Dict[str, Any]
    created_at: float
    last_updated: float
    
    def is_complete(self) -> bool:
        """Check if all steps are complete."""
        return self.completed_steps >= self.total_steps
    
    def get_latest_checkpoint(self) -> Optional[int]:
        """Get the latest checkpoint step number."""
        if not self.checkpoints:
            return None
        return max(self.checkpoints.keys())


class ProgressiveStorage:
    """
    Progressive storage tier for long-running operations.
    
    This storage tier allows caching of intermediate results during
    long computations, enabling resumption from checkpoints.
    """
    
    def __init__(self, cache_dir: Path):
        """
        Initialize progressive storage.
        
        Args:
            cache_dir: Directory for progressive cache files
        """
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # Manifest tracking
        self.manifest_file = self.cache_dir / "_manifest.json"
        self._entries: Dict[str, ProgressiveEntry] = {}
        self._lock = threading.RLock()
        
        # Load existing manifest
        self._load_manifest()
        
        logger.info(f"Initialized ProgressiveStorage at {cache_dir}")
    
    def store(self, key: str, value: Any, metadata: Optional[Dict[str, Any]] = None) -> bool:
        """
        Store a complete value (non-progressive).
        
        Args:
            key: Storage key
            value: Value to store
            metadata: Optional metadata
            
        Returns:
            True if stored successfully
        """
        # For non-progressive storage, create a single-step entry
        return self.store_checkpoint(key, 0, value, total_steps=1, metadata=metadata)
    
    def retrieve(self, key: str) -> Optional[Any]:
        """
        Retrieve a value from progressive storage.
        
        Args:
            key: Storage key
            
        Returns:
            Stored value or None
        """
        with self._lock:
            if key not in self._entries:
                return None
            
            entry = self._entries[key]
            
            # If complete, return final result
            if entry.is_complete():
                latest_step = entry.get_latest_checkpoint()
                if latest_step is not None:
                    return self.retrieve_checkpoint(key, latest_step)
            
            return None
    
    def store_checkpoint(
        self,
        key: str,
        step: int,
        value: Any,
        total_steps: Optional[int] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Store a checkpoint for a progressive operation.
        
        Args:
            key: Storage key
            step: Step number (0-based)
            value: Checkpoint value
            total_steps: Total number of steps (required for first checkpoint)
            metadata: Optional metadata
            
        Returns:
            True if stored successfully
        """
        with self._lock:
            # Create or update entry
            if key not in self._entries:
                if total_steps is None:
                    logger.error(f"Total steps required for new progressive entry: {key}")
                    return False
                
                entry = ProgressiveEntry(
                    key=key,
                    total_steps=total_steps,
                    completed_steps=0,
                    checkpoints={},
                    metadata=metadata or {},
                    created_at=time.time(),
                    last_updated=time.time()
                )
                self._entries[key] = entry
            else:
                entry = self._entries[key]
                entry.last_updated = time.time()
                if metadata:
                    entry.metadata.update(metadata)
            
            # Generate file path for checkpoint
            file_path = self._get_checkpoint_path(key, step)
            
            try:
                # Write checkpoint data
                file_path.parent.mkdir(parents=True, exist_ok=True)
                with open(file_path, 'wb') as f:
                    pickle.dump(value, f, protocol=pickle.HIGHEST_PROTOCOL)
                
                # Update entry
                entry.checkpoints[step] = str(file_path)
                entry.completed_steps = max(entry.completed_steps, step + 1)
                
                # Save manifest
                self._save_manifest()
                
                logger.debug(f"Stored checkpoint {step}/{entry.total_steps} for key: {key}")
                return True
                
            except Exception as e:
                logger.error(f"Failed to store checkpoint for {key}: {e}")
                if file_path.exists():
                    file_path.unlink()
                return False
    
    def retrieve_checkpoint(self, key: str, step: int) -> Optional[Any]:
        """
        Retrieve a specific checkpoint.
        
        Args:
            key: Storage key
            step: Step number
            
        Returns:
            Checkpoint value or None
        """
        with self._lock:
            if key not in self._entries:
                return None
            
            entry = self._entries[key]
            if step not in entry.checkpoints:
                return None
            
            file_path = Path(entry.checkpoints[step])
            
            try:
                with open(file_path, 'rb') as f:
                    return pickle.load(f)
            except Exception as e:
                logger.error(f"Failed to retrieve checkpoint {step} for {key}: {e}")
                return None
    
    def get_latest_checkpoint(self, key: str) -> Optional[Tuple[int, Any]]:
        """
        Get the latest checkpoint for a key.
        
        Args:
            key: Storage key
            
        Returns:
            Tuple of (step, value) or None
        """
        with self._lock:
            if key not in self._entries:
                return None
            
            entry = self._entries[key]
            latest_step = entry.get_latest_checkpoint()
            
            if latest_step is None:
                return None
            
            value = self.retrieve_checkpoint(key, latest_step)
            if value is not None:
                return (latest_step, value)
            
            return None
    
    def get_progress(self, key: str) -> Optional[Dict[str, Any]]:
        """
        Get progress information for a key.
        
        Args:
            key: Storage key
            
        Returns:
            Progress information or None
        """
        with self._lock:
            if key not in self._entries:
                return None
            
            entry = self._entries[key]
            return {
                "total_steps": entry.total_steps,
                "completed_steps": entry.completed_steps,
                "progress_percent": (entry.completed_steps / entry.total_steps) * 100,
                "is_complete": entry.is_complete(),
                "checkpoints": list(entry.checkpoints.keys()),
                "created_at": entry.created_at,
                "last_updated": entry.last_updated,
                "metadata": entry.metadata
            }
    
    def remove(self, key: str) -> bool:
        """
        Remove a progressive entry and all its checkpoints.
        
        Args:
            key: Storage key
            
        Returns:
            True if removed successfully
        """
        with self._lock:
            if key not in self._entries:
                return False
            
            entry = self._entries[key]
            
            # Remove all checkpoint files
            for step, file_path in entry.checkpoints.items():
                try:
                    Path(file_path).unlink()
                except Exception as e:
                    logger.error(f"Failed to remove checkpoint file {file_path}: {e}")
            
            # Remove entry
            del self._entries[key]
            self._save_manifest()
            
            return True
    
    def exists(self, key: str) -> bool:
        """Check if a key exists in storage."""
        with self._lock:
            return key in self._entries
    
    def get_metadata(self, key: str) -> Optional[Dict[str, Any]]:
        """Get metadata for a stored key."""
        with self._lock:
            if key in self._entries:
                return self._entries[key].metadata
            return None
    
    def update_metadata(self, key: str, metadata: Dict[str, Any]) -> bool:
        """Update metadata for a stored key."""
        with self._lock:
            if key in self._entries:
                self._entries[key].metadata.update(metadata)
                self._entries[key].last_updated = time.time()
                self._save_manifest()
                return True
            return False
    
    def clear(self) -> None:
        """Clear all stored data."""
        with self._lock:
            # Remove all checkpoint files
            for entry in self._entries.values():
                for file_path in entry.checkpoints.values():
                    try:
                        Path(file_path).unlink()
                    except Exception as e:
                        logger.error(f"Failed to remove file {file_path}: {e}")
            
            # Clear entries
            self._entries.clear()
            self._save_manifest()
    
    def get_stats(self) -> Dict[str, Any]:
        """Get storage statistics."""
        with self._lock:
            total_checkpoints = sum(len(e.checkpoints) for e in self._entries.values())
            complete_entries = sum(1 for e in self._entries.values() if e.is_complete())
            
            return {
                "type": "progressive",
                "cache_dir": str(self.cache_dir),
                "entry_count": len(self._entries),
                "complete_entries": complete_entries,
                "in_progress_entries": len(self._entries) - complete_entries,
                "total_checkpoints": total_checkpoints,
                "average_checkpoints_per_entry": (
                    total_checkpoints / len(self._entries)
                    if self._entries else 0
                ),
            }
    
    def cleanup_expired(self, max_age_hours: float = 24) -> int:
        """
        Clean up expired incomplete entries.
        
        Args:
            max_age_hours: Maximum age for incomplete entries
            
        Returns:
            Number of entries cleaned up
        """
        cleaned = 0
        current_time = time.time()
        max_age_seconds = max_age_hours * 3600
        
        with self._lock:
            keys_to_remove = []
            
            for key, entry in self._entries.items():
                # Only clean up incomplete entries
                if not entry.is_complete():
                    age = current_time - entry.last_updated
                    if age > max_age_seconds:
                        keys_to_remove.append(key)
            
            for key in keys_to_remove:
                if self.remove(key):
                    cleaned += 1
                    logger.debug(f"Cleaned up expired progressive entry: {key}")
        
        return cleaned
    
    def _get_checkpoint_path(self, key: str, step: int) -> Path:
        """Generate a file path for a checkpoint."""
        import hashlib
        key_hash = hashlib.sha256(key.encode()).hexdigest()[:16]
        return self.cache_dir / key_hash / f"checkpoint_{step:04d}.pkl"
    
    def _load_manifest(self) -> None:
        """Load manifest from disk."""
        if self.manifest_file.exists():
            try:
                with open(self.manifest_file, 'r') as f:
                    data = json.load(f)
                    
                # Reconstruct entries
                for key, entry_data in data.items():
                    self._entries[key] = ProgressiveEntry(
                        key=key,
                        total_steps=entry_data["total_steps"],
                        completed_steps=entry_data["completed_steps"],
                        checkpoints=entry_data["checkpoints"],
                        metadata=entry_data["metadata"],
                        created_at=entry_data["created_at"],
                        last_updated=entry_data["last_updated"]
                    )
            except Exception as e:
                logger.error(f"Failed to load manifest: {e}")
                self._entries = {}
    
    def _save_manifest(self) -> None:
        """Save manifest to disk."""
        try:
            # Convert entries to serializable format
            data = {}
            for key, entry in self._entries.items():
                data[key] = {
                    "total_steps": entry.total_steps,
                    "completed_steps": entry.completed_steps,
                    "checkpoints": entry.checkpoints,
                    "metadata": entry.metadata,
                    "created_at": entry.created_at,
                    "last_updated": entry.last_updated
                }
            
            with open(self.manifest_file, 'w') as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            logger.error(f"Failed to save manifest: {e}")