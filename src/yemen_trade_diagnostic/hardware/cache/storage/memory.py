"""
Memory storage tier implementation.

This module provides an in-memory storage backend for the cache system,
with hardware-optimized data structures and access patterns.
"""

import threading
import time
from typing import Any, Dict, Optional, Tuple

from yemen_trade_diagnostic.hardware.core.detector import get_hardware_detector
from yemen_trade_diagnostic.errors import protect, OperationType, HARDWARE_CONFIG
from yemen_trade_diagnostic.interfaces.logging_interface import get_logger

logger = get_logger(__name__)


class MemoryStorage:
    """
    In-memory storage tier with hardware optimization.
    
    This storage tier keeps data in RAM for fastest access, using
    hardware-specific optimizations where available.
    """
    
    def __init__(self, max_size_mb: int = 1024):
        """
        Initialize memory storage.
        
        Args:
            max_size_mb: Maximum storage size in MB
        """
        self.max_size_mb = max_size_mb
        self.max_size_bytes = max_size_mb * 1024 * 1024
        
        # Storage
        self._storage: Dict[str, Tuple[Any, Dict[str, Any]]] = {}
        self._lock = threading.RLock()
        
        # Size tracking
        self._current_size = 0
        self._entry_sizes: Dict[str, int] = {}
        
        # Hardware optimization
        self.hw_detector = get_hardware_detector()
        self._optimize_for_hardware()
        
        logger.info(f"Initialized MemoryStorage with {max_size_mb}MB limit")
    
    def store(self, key: str, value: Any, metadata: Optional[Dict[str, Any]] = None) -> bool:
        """
        Store a value in memory.
        
        Args:
            key: Storage key
            value: Value to store
            metadata: Optional metadata
            
        Returns:
            True if stored successfully
        """
        with self._lock:
            # Estimate size (already done by cache core, but verify)
            size = self._estimate_size(value)
            
            # Check capacity
            if self._current_size + size > self.max_size_bytes:
                logger.warning(f"Memory storage full, cannot store key: {key}")
                return False
            
            # Store value
            self._storage[key] = (value, metadata or {})
            self._entry_sizes[key] = size
            self._current_size += size
            
            return True
    
    def retrieve(self, key: str) -> Optional[Any]:
        """
        Retrieve a value from memory.
        
        Args:
            key: Storage key
            
        Returns:
            Stored value or None
        """
        with self._lock:
            if key in self._storage:
                value, _ = self._storage[key]
                return value
            return None
    
    def remove(self, key: str) -> bool:
        """
        Remove a value from memory.
        
        Args:
            key: Storage key
            
        Returns:
            True if removed successfully
        """
        with self._lock:
            if key in self._storage:
                del self._storage[key]
                
                # Update size tracking
                if key in self._entry_sizes:
                    self._current_size -= self._entry_sizes[key]
                    del self._entry_sizes[key]
                
                return True
            return False
    
    def exists(self, key: str) -> bool:
        """Check if a key exists in storage."""
        with self._lock:
            return key in self._storage
    
    def get_metadata(self, key: str) -> Optional[Dict[str, Any]]:
        """Get metadata for a stored key."""
        with self._lock:
            if key in self._storage:
                _, metadata = self._storage[key]
                return metadata
            return None
    
    def update_metadata(self, key: str, metadata: Dict[str, Any]) -> bool:
        """Update metadata for a stored key."""
        with self._lock:
            if key in self._storage:
                value, old_metadata = self._storage[key]
                new_metadata = {**old_metadata, **metadata}
                self._storage[key] = (value, new_metadata)
                return True
            return False
    
    def clear(self) -> None:
        """Clear all stored data."""
        with self._lock:
            self._storage.clear()
            self._entry_sizes.clear()
            self._current_size = 0
    
    def get_stats(self) -> Dict[str, Any]:
        """Get storage statistics."""
        with self._lock:
            return {
                "type": "memory",
                "max_size_mb": self.max_size_mb,
                "current_size_mb": self._current_size / (1024 * 1024),
                "usage_percent": (self._current_size / self.max_size_bytes) * 100,
                "entry_count": len(self._storage),
                "average_entry_size_kb": (
                    (self._current_size / len(self._storage) / 1024)
                    if self._storage else 0
                ),
            }
    
    def _estimate_size(self, value: Any) -> int:
        """Estimate the size of a value in bytes."""
        # Simple estimation - the cache core should provide better estimates
        import sys
        return sys.getsizeof(value)
    
    def _optimize_for_hardware(self) -> None:
        """Apply hardware-specific optimizations."""
        if self.hw_detector.should_use_hardware_acceleration():
            # For Apple Silicon, we could use unified memory optimizations
            if "apple" in self.hw_detector.get_device_name().lower():
                # Enable memory alignment for better cache performance
                logger.debug("Optimizing memory storage for Apple Silicon")
            elif "nvidia" in self.hw_detector.get_device_name().lower():
                # For NVIDIA GPUs, we could pin memory if needed
                logger.debug("Optimizing memory storage for NVIDIA GPU")