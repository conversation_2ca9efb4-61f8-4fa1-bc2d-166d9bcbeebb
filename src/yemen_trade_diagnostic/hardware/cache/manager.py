"""
Cache Management System

This module provides a unified cache management system for efficient data reuse
across operations, reducing disk I/O and memory allocations. It integrates with
the memory pool and compression systems to optimize memory usage.
"""

# Standard library imports
import hashlib
import logging
import os
import pickle
import sys
import threading
import time
from enum import Enum
from typing import Any, Callable, Dict, Generic, List, Optional, Tuple, TypeVar, Union

# Third-party imports
import numpy as np
import pandas as pd

# Project imports
# Local imports
from yemen_trade_diagnostic.errors import protect, OperationType, HARDWARE_CONFIG
from yemen_trade_diagnostic.hardware.core.detector import get_hardware_detector
from yemen_trade_diagnostic.hardware.memory.compression import (
    CompressionAlgorithm,
    get_memory_compressor,
)
from yemen_trade_diagnostic.hardware.memory.pool import get_memory_pool

logger = logging.getLogger(__name__)

T = TypeVar('T')


class CachePriority(Enum):
    """Priority levels for cached items."""
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    CRITICAL = 4


class CacheStorageType(Enum):
    """Storage types for cached items."""
    MEMORY = "memory"      # Stored in memory
    DISK = "disk"          # Stored on disk
    HYBRID = "hybrid"      # Stored in both memory and disk
    COMPRESSED = "compressed"  # Stored in memory but compressed


class CacheItem(Generic[T]):
    """
    Container for cached items with metadata.

    This class stores a cached item along with metadata such as creation time,
    last access time, access count, and priority.
    """

    def __init__(self, 
                key: str, 
                value: T, 
                storage_type: CacheStorageType = CacheStorageType.MEMORY,
                priority: CachePriority = CachePriority.MEDIUM,
                ttl: Optional[float] = None,
                max_access_count: Optional[int] = None):
        """
        Initialize a cache item.

        Args:
            key: Cache key
            value: Cached value
            storage_type: Storage type for the item
            priority: Priority level for the item
            ttl: Time-to-live in seconds (None for no expiration)
            max_access_count: Maximum number of accesses before expiration (None for no limit)
        """
        self.key = key
        self.value = value
        self.storage_type = storage_type
        self.priority = priority
        self.ttl = ttl
        self.max_access_count = max_access_count

        # Metadata
        self.creation_time = time.time()
        self.last_access_time = self.creation_time
        self.access_count = 0
        self.size_bytes = self._calculate_size()

        # For compressed items
        self.compressed_value = None
        self.compression_algorithm = None
        self.compression_level = None
        self.original_size_bytes = None

        # For disk items
        self.disk_path = None

    def _calculate_size(self) -> int:
        """Calculate the approximate size of the cached value in bytes."""
        if isinstance(self.value, np.ndarray):
            return self.value.nbytes
        elif isinstance(self.value, pd.DataFrame):
            return self.value.memory_usage(deep=True).sum()
        elif isinstance(self.value, pd.Series):
            return self.value.memory_usage(deep=True)
        else:
            try:
                return sys.getsizeof(self.value)
            except:
                return 0

    def access(self) -> None:
        """Update metadata when the item is accessed."""
        self.last_access_time = time.time()
        self.access_count += 1

    def is_expired(self) -> bool:
        """Check if the item is expired based on TTL or access count."""
        current_time = time.time()

        # Check TTL
        if self.ttl is not None and current_time - self.creation_time > self.ttl:
            return True

        # Check access count
        if self.max_access_count is not None and self.access_count >= self.max_access_count:
            return True

        return False

    @protect("cache_item_compress", OperationType.HARDWARE_ACCELERATION)
    def compress(self, algorithm: CompressionAlgorithm = CompressionAlgorithm.ZSTD, level: int = 3) -> bool:
        """
        Compress the cached value to reduce memory usage.

        Args:
            algorithm: Compression algorithm to use
            level: Compression level

        Returns:
            bool: True if compression was successful, False otherwise
        """
        if self.storage_type == CacheStorageType.COMPRESSED:
            return True  # Already compressed

        try:
            compressor = get_memory_compressor()
            self.original_size_bytes = self.size_bytes
            self.compressed_value = compressor.compress(self.value, algorithm.value, level)
            self.compression_algorithm = algorithm
            self.compression_level = level
            self.storage_type = CacheStorageType.COMPRESSED
            self.value = None  # Release the original value
            self.size_bytes = sys.getsizeof(self.compressed_value)
            return True
        except Exception as e:
            logger.warning(f"Failed to compress cache item {self.key}: {e}")
            return False

    @protect("cache_item_decompress", OperationType.HARDWARE_ACCELERATION)
    def decompress(self) -> bool:
        """
        Decompress the cached value.

        Returns:
            bool: True if decompression was successful, False otherwise
        """
        if self.storage_type != CacheStorageType.COMPRESSED:
            return True  # Not compressed

        try:
            compressor = get_memory_compressor()
            self.value = compressor.decompress(self.compressed_value, self.compression_algorithm.value)
            self.storage_type = CacheStorageType.MEMORY
            self.compressed_value = None  # Release the compressed value
            self.size_bytes = self.original_size_bytes or self._calculate_size()
            return True
        except Exception as e:
            logger.warning(f"Failed to decompress cache item {self.key}: {e}")
            return False

    @protect("cache_save_to_disk", OperationType.HARDWARE_ACCELERATION)
    def save_to_disk(self, cache_dir: str) -> bool:
        """
        Save the cached value to disk.

        Args:
            cache_dir: Directory to save the cached value

        Returns:
            bool: True if saving was successful, False otherwise
        """
        if self.storage_type == CacheStorageType.DISK:
            return True  # Already on disk

        try:
            os.makedirs(cache_dir, exist_ok=True)
            self.disk_path = os.path.join(cache_dir, f"{self.key}.cache")

            # Save to disk
            with open(self.disk_path, 'wb') as f:
                pickle.dump(self.value, f)

            # Update metadata
            if self.storage_type != CacheStorageType.HYBRID:
                self.value = None  # Release the value if not hybrid

            self.storage_type = CacheStorageType.DISK if self.value is None else CacheStorageType.HYBRID
            return True
        except Exception as e:
            logger.warning(f"Failed to save cache item {self.key} to disk: {e}")
            return False

    @protect("cache_load_from_disk", OperationType.HARDWARE_ACCELERATION)
    def load_from_disk(self) -> bool:
        """
        Load the cached value from disk.

        Returns:
            bool: True if loading was successful, False otherwise
        """
        if self.storage_type not in (CacheStorageType.DISK, CacheStorageType.HYBRID) or not self.disk_path:
            return False  # Not on disk

        try:
            # Load from disk
            with open(self.disk_path, 'rb') as f:
                self.value = pickle.load(f)

            # Update metadata
            self.storage_type = CacheStorageType.MEMORY
            self.size_bytes = self._calculate_size()
            return True
        except Exception as e:
            logger.warning(f"Failed to load cache item {self.key} from disk: {e}")
            return False


class CacheManager:
    """
    Cache manager for efficient data reuse.

    This class provides a unified cache management system for efficient data reuse
    across operations, reducing disk I/O and memory allocations. It integrates with
    the memory pool and compression systems to optimize memory usage.
    """

    def __init__(self, 
                max_memory_size: Optional[int] = None,
                max_disk_size: Optional[int] = None,
                cache_dir: Optional[str] = None,
                auto_compress: bool = True,
                compression_threshold: int = 1024 * 1024,  # 1 MB
                compression_algorithm: CompressionAlgorithm = CompressionAlgorithm.ZSTD,
                compression_level: int = 3,
                enable_disk_cache: bool = True,
                disk_cache_threshold: int = 10 * 1024 * 1024,  # 10 MB
                ttl: Optional[float] = None,
                cleanup_interval: float = 60.0):
        """
        Initialize the cache manager.

        Args:
            max_memory_size: Maximum memory size in bytes (None for no limit)
            max_disk_size: Maximum disk size in bytes (None for no limit)
            cache_dir: Directory for disk cache (None for default)
            auto_compress: Whether to automatically compress large items
            compression_threshold: Size threshold for auto-compression in bytes
            compression_algorithm: Default compression algorithm
            compression_level: Default compression level
            enable_disk_cache: Whether to enable disk caching
            disk_cache_threshold: Size threshold for disk caching in bytes
            ttl: Default time-to-live for cache items in seconds
            cleanup_interval: Interval for automatic cleanup in seconds
        """
        # Cache storage
        self.cache: Dict[str, CacheItem] = {}

        # Configuration
        self.max_memory_size = max_memory_size
        self.max_disk_size = max_disk_size
        self.cache_dir = cache_dir or os.path.join(os.path.expanduser("~"), ".hardware_cache")
        self.auto_compress = auto_compress
        self.compression_threshold = compression_threshold
        self.compression_algorithm = compression_algorithm
        self.compression_level = compression_level
        self.enable_disk_cache = enable_disk_cache
        self.disk_cache_threshold = disk_cache_threshold
        self.default_ttl = ttl
        self.cleanup_interval = cleanup_interval

        # Metadata
        self.current_memory_size = 0
        self.current_disk_size = 0
        self.hit_count = 0
        self.miss_count = 0

        # Thread safety
        self.lock = threading.RLock()

        # Hardware detection
        self.detector = get_hardware_detector()

        # Auto-configure based on hardware
        self._auto_configure()

        # Create cache directory if needed
        if self.enable_disk_cache:
            os.makedirs(self.cache_dir, exist_ok=True)

            # Calculate current disk usage
            self._calculate_disk_usage()

        # Start cleanup thread
        self._start_cleanup_thread()

        # Ensure compression is properly initialized
        self.compression_enabled = auto_compress
        
        # Get memory compressor
        if auto_compress:
            # Project imports
            from yemen_trade_diagnostic.hardware.memory.compression import (
                CompressionConfig,
                get_memory_compressor,
            )
            config = CompressionConfig(
                algorithm=compression_algorithm,
                level=compression_level,
                m3_optimized=True,
                trade_data_mode=True
            )
            self.compressor = get_memory_compressor(config)
        else:
            self.compressor = None

    def _calculate_disk_usage(self) -> None:
        """Calculate the current disk usage of the cache."""
        if not os.path.exists(self.cache_dir):
            self.current_disk_size = 0
            return

        try:
            total_size = 0
            for filename in os.listdir(self.cache_dir):
                file_path = os.path.join(self.cache_dir, filename)
                if os.path.isfile(file_path) and filename.endswith('.cache'):
                    total_size += os.path.getsize(file_path)

            self.current_disk_size = total_size
        except Exception as e:
            logger.warning(f"Failed to calculate disk usage: {e}")
            self.current_disk_size = 0

    @protect("cache_auto_configure", OperationType.HARDWARE_ACCELERATION)
    def _auto_configure(self) -> None:
        """Auto-configure cache settings based on hardware capabilities."""
        # Adjust max memory size based on available memory
        if self.max_memory_size is None and hasattr(self.detector, 'total_memory'):
            # Use 10% of total memory by default
            self.max_memory_size = int(self.detector.total_memory * 0.1)

        # Adjust compression settings for Apple Silicon
        if self.detector.is_apple_silicon:
            # Apple Silicon has efficient compression hardware
            self.compression_threshold = 512 * 1024  # 512 KB
            self.compression_level = 5  # Higher compression level

    def _start_cleanup_thread(self) -> None:
        """Start the background cleanup thread."""
        if self.cleanup_interval <= 0:
            return

        def cleanup_task():
            while True:
                time.sleep(self.cleanup_interval)
                try:
                    self.cleanup()
                except Exception as e:
                    logger.warning(f"Error during cache cleanup: {e}")

        cleanup_thread = threading.Thread(target=cleanup_task, daemon=True)
        cleanup_thread.start()

    def _generate_key(self, key: Any) -> str:
        """
        Generate a string key from any input.

        Args:
            key: Input key (can be any hashable object)

        Returns:
            str: String key
        """
        if isinstance(key, str):
            return key

        try:
            # Try to create a deterministic hash
            key_str = str(key)
            return hashlib.md5(key_str.encode()).hexdigest()
        except:
            # Fallback to object id
            return f"obj_{id(key)}"

    @protect("cache_manage_memory", OperationType.HARDWARE_ACCELERATION)
    def _manage_memory(self, required_size: int = 0) -> None:
        """
        Manage memory usage by compressing or evicting items.

        Args:
            required_size: Required additional memory in bytes
        """
        if self.max_memory_size is None:
            return

        # Check if we need to free up memory
        if self.current_memory_size + required_size <= self.max_memory_size:
            return

        with self.lock:
            # First, remove expired items
            self._remove_expired_items()

            # Check if we've freed up enough memory
            if self.current_memory_size + required_size <= self.max_memory_size:
                return

            # Next, try to compress items
            if self.auto_compress:
                self._compress_items()

            # If still not enough, evict items
            if self.current_memory_size + required_size > self.max_memory_size:
                self._evict_items(required_size)

            # Final check - if we still don't have enough memory, force eviction
            if self.current_memory_size + required_size > self.max_memory_size:
                self._force_eviction(required_size)

    @protect("cache_compress_items", OperationType.HARDWARE_ACCELERATION)
    def _compress_items(self) -> None:
        """Compress items to reduce memory usage."""
        # Get memory items larger than the threshold
        memory_items = [
            item for item in self.cache.values()
            if item.storage_type == CacheStorageType.MEMORY
            and item.size_bytes >= self.compression_threshold
        ]

        # Sort by size (largest first)
        memory_items.sort(key=lambda x: x.size_bytes, reverse=True)

        # Compress items
        for item in memory_items:
            if item.compress(self.compression_algorithm, self.compression_level):
                # Update memory usage
                self.current_memory_size -= (item.original_size_bytes - item.size_bytes)

                # Check if we've freed up enough memory
                if self.max_memory_size is None or self.current_memory_size <= self.max_memory_size * 0.8:
                    break

    def _remove_expired_items(self) -> int:
        """
        Remove expired items from the cache.

        Returns:
            int: Number of items removed
        """
        removed_count = 0
        expired_keys = []

        # Find expired items
        for key, item in list(self.cache.items()):
            if item.is_expired():
                expired_keys.append(key)

        # Remove expired items
        for key in expired_keys:
            item = self.cache[key]
            self.current_memory_size -= item.size_bytes
            if item.storage_type in (CacheStorageType.DISK, CacheStorageType.HYBRID) and item.disk_path:
                try:
                    # Remove disk file if it exists
                    if os.path.exists(item.disk_path):
                        os.remove(item.disk_path)
                        self.current_disk_size -= item.size_bytes
                except Exception as e:
                    logger.warning(f"Failed to remove disk cache file {item.disk_path}: {e}")

            del self.cache[key]
            removed_count += 1

        return removed_count

    def _evict_items(self, required_size: int) -> None:
        """
        Evict items to free up memory.

        Args:
            required_size: Required additional memory in bytes
        """
        # Get all items that are in memory
        memory_items = [
            item for item in self.cache.values()
            if item.storage_type in (CacheStorageType.MEMORY, CacheStorageType.COMPRESSED)
        ]

        if not memory_items:
            return  # No items to evict

        # Group items by priority
        priority_groups = {}
        for item in memory_items:
            if item.priority not in priority_groups:
                priority_groups[item.priority] = []
            priority_groups[item.priority].append(item)

        # Sort priorities from lowest to highest
        sorted_priorities = sorted(priority_groups.keys(), key=lambda p: p.value)

        # Evict items starting from lowest priority
        freed_memory = 0
        for priority in sorted_priorities:
            # Sort items within this priority by last access time (oldest first)
            items = sorted(priority_groups[priority], key=lambda x: x.last_access_time)

            for item in items:
                if item.storage_type == CacheStorageType.MEMORY:
                    # Move to disk if disk cache is enabled
                    if self.enable_disk_cache and self._check_disk_space(item.size_bytes):
                        if item.save_to_disk(self.cache_dir):
                            freed_memory += item.size_bytes
                            self.current_memory_size -= item.size_bytes
                            self.current_disk_size += item.size_bytes
                    else:
                        # Remove from cache
                        del self.cache[item.key]
                        freed_memory += item.size_bytes
                        self.current_memory_size -= item.size_bytes
                elif item.storage_type == CacheStorageType.COMPRESSED:
                    # Move to disk if disk cache is enabled
                    if self.enable_disk_cache and self._check_disk_space(item.original_size_bytes or item.size_bytes):
                        if item.decompress() and item.save_to_disk(self.cache_dir):
                            freed_memory += item.size_bytes
                            self.current_memory_size -= item.size_bytes
                            self.current_disk_size += item.size_bytes
                    else:
                        # Remove from cache
                        del self.cache[item.key]
                        freed_memory += item.size_bytes
                        self.current_memory_size -= item.size_bytes

                # Check if we've freed up enough memory
                if freed_memory >= required_size:
                    return

    def _force_eviction(self, required_size: int) -> None:
        """
        Force eviction of items regardless of priority when we need more space.

        Args:
            required_size: Required additional memory in bytes
        """
        # Get all items that are in memory
        memory_items = [
            item for item in self.cache.values()
            if item.storage_type in (CacheStorageType.MEMORY, CacheStorageType.COMPRESSED)
        ]

        if not memory_items:
            logger.warning("Cannot free up enough memory - no items to evict")
            return

        # Sort by size (largest first) to free up memory quickly
        memory_items.sort(key=lambda x: x.size_bytes, reverse=True)

        # Evict items
        freed_memory = 0
        for item in memory_items:
            # Just remove the item completely
            del self.cache[item.key]
            freed_memory += item.size_bytes
            self.current_memory_size -= item.size_bytes

            # Check if we've freed up enough memory
            if freed_memory >= required_size:
                return

        logger.warning(f"Could not free up enough memory ({required_size} bytes required)")

    def _check_disk_space(self, required_size: int) -> bool:
        """
        Check if there's enough disk space available.

        Args:
            required_size: Required additional disk space in bytes

        Returns:
            bool: True if there's enough space, False otherwise
        """
        if self.max_disk_size is None:
            return True

        # Check if we need to free up disk space
        if self.current_disk_size + required_size <= self.max_disk_size:
            return True

        # Try to free up disk space
        self._manage_disk_space(required_size)

        # Check again
        return self.current_disk_size + required_size <= self.max_disk_size

    def _manage_disk_space(self, required_size: int) -> None:
        """
        Manage disk space by evicting items.

        Args:
            required_size: Required additional disk space in bytes
        """
        if self.max_disk_size is None:
            return

        # Get all items that are on disk
        disk_items = [
            item for item in self.cache.values()
            if item.storage_type in (CacheStorageType.DISK, CacheStorageType.HYBRID)
            and item.disk_path and os.path.exists(item.disk_path)
        ]

        if not disk_items:
            return

        # Group items by priority
        priority_groups = {}
        for item in disk_items:
            if item.priority not in priority_groups:
                priority_groups[item.priority] = []
            priority_groups[item.priority].append(item)

        # Sort priorities from lowest to highest
        sorted_priorities = sorted(priority_groups.keys(), key=lambda p: p.value)

        # Evict items starting from lowest priority
        freed_space = 0
        for priority in sorted_priorities:
            # Sort items within this priority by last access time (oldest first)
            items = sorted(priority_groups[priority], key=lambda x: x.last_access_time)

            for item in items:
                # Get file size
                try:
                    file_size = os.path.getsize(item.disk_path)
                except:
                    file_size = 0

                # Remove the file
                try:
                    os.remove(item.disk_path)
                    freed_space += file_size
                    self.current_disk_size -= file_size
                except Exception as e:
                    logger.warning(f"Failed to remove disk cache file {item.disk_path}: {e}")

                # Update item
                if item.storage_type == CacheStorageType.DISK:
                    # Item only exists on disk, so remove it from cache
                    del self.cache[item.key]
                else:  # HYBRID
                    # Item exists in both memory and disk, so just update storage type
                    item.storage_type = CacheStorageType.MEMORY
                    item.disk_path = None

                # Check if we've freed up enough space
                if freed_space >= required_size:
                    return

    @protect("cache_set", OperationType.HARDWARE_ACCELERATION)
    def set(self, 
           key: Any, 
           value: T, 
           ttl: Optional[float] = None,
           priority: CachePriority = CachePriority.MEDIUM,
           storage_type: Optional[CacheStorageType] = None) -> bool:
        """
        Set a value in the cache.

        Args:
            key: Cache key
            value: Value to cache
            ttl: Time-to-live in seconds (None for default)
            priority: Priority level for the item
            storage_type: Storage type for the item (None for auto)

        Returns:
            bool: True if the value was successfully cached, False otherwise
        """
        # Generate string key
        str_key = self._generate_key(key)

        # Determine storage type
        if storage_type is None:
            # Auto-determine storage type based on size
            try:
                size = sys.getsizeof(value)
                if self.enable_disk_cache and size >= self.disk_cache_threshold:
                    # Check if we have enough disk space
                    if self.max_disk_size is not None and self.current_disk_size + size > self.max_disk_size:
                        # Not enough disk space, use compression instead
                        if self.auto_compress:
                            storage_type = CacheStorageType.COMPRESSED
                        else:
                            storage_type = CacheStorageType.MEMORY
                    else:
                        storage_type = CacheStorageType.DISK
                elif self.auto_compress and size >= self.compression_threshold:
                    storage_type = CacheStorageType.COMPRESSED
                else:
                    storage_type = CacheStorageType.MEMORY
            except:
                storage_type = CacheStorageType.MEMORY

        # Create cache item
        item = CacheItem(
            key=str_key,
            value=value,
            storage_type=storage_type,
            priority=priority,
            ttl=ttl if ttl is not None else self.default_ttl
        )

        # Manage memory before adding the new item
        self._manage_memory(item.size_bytes)

        with self.lock:
            # Remove existing item if present
            if str_key in self.cache:
                old_item = self.cache[str_key]
                self.current_memory_size -= old_item.size_bytes

                # If old item was on disk, remove the disk file
                if old_item.storage_type in (CacheStorageType.DISK, CacheStorageType.HYBRID) and old_item.disk_path:
                    try:
                        if os.path.exists(old_item.disk_path):
                            file_size = os.path.getsize(old_item.disk_path)
                            os.remove(old_item.disk_path)
                            self.current_disk_size -= file_size
                    except Exception as e:
                        logger.warning(f"Failed to remove old disk cache file {old_item.disk_path}: {e}")

            # Handle storage type
            if storage_type == CacheStorageType.COMPRESSED:
                item.compress(self.compression_algorithm, self.compression_level)
            elif storage_type == CacheStorageType.DISK:
                # Check disk space again
                if self.max_disk_size is not None and self.current_disk_size + item.size_bytes > self.max_disk_size:
                    # Manage disk space
                    self._manage_disk_space(item.size_bytes)

                # Save to disk
                if item.save_to_disk(self.cache_dir):
                    # Update disk usage
                    if os.path.exists(item.disk_path):
                        self.current_disk_size += os.path.getsize(item.disk_path)
                else:
                    # Failed to save to disk, use memory instead
                    item.storage_type = CacheStorageType.MEMORY

            # Add to cache
            self.cache[str_key] = item
            self.current_memory_size += item.size_bytes

            return True

    @protect("cache_get", OperationType.HARDWARE_ACCELERATION)
    def get(self, key: Any, default: Any = None) -> Any:
        """
        Get a value from the cache.

        Args:
            key: Cache key
            default: Default value to return if key is not found

        Returns:
            The cached value, or default if not found
        """
        # Generate string key
        str_key = self._generate_key(key)

        with self.lock:
            # Check if key exists
            if str_key not in self.cache:
                self.miss_count += 1
                return default

            # Get cache item
            item = self.cache[str_key]

            # Check if expired
            if item.is_expired():
                del self.cache[str_key]
                self.current_memory_size -= item.size_bytes
                self.miss_count += 1
                return default

            # Update access metadata
            item.access()

            # Handle storage type
            if item.storage_type == CacheStorageType.COMPRESSED:
                item.decompress()
            elif item.storage_type == CacheStorageType.DISK:
                item.load_from_disk()

            self.hit_count += 1
            return item.value

    @protect("cache_delete", OperationType.HARDWARE_ACCELERATION)
    def delete(self, key: Any) -> bool:
        """
        Delete a value from the cache.

        Args:
            key: Cache key

        Returns:
            bool: True if the key was found and deleted, False otherwise
        """
        # Generate string key
        str_key = self._generate_key(key)

        with self.lock:
            # Check if key exists
            if str_key not in self.cache:
                return False

            # Get cache item
            item = self.cache[str_key]

            # Update memory usage
            self.current_memory_size -= item.size_bytes

            # Delete from cache
            del self.cache[str_key]

            return True

    @protect("cache_clear", OperationType.HARDWARE_ACCELERATION)
    def clear(self) -> None:
        """Clear the entire cache."""
        with self.lock:
            self.cache.clear()
            self.current_memory_size = 0
            self.current_disk_size = 0

    @protect("cache_cleanup", OperationType.HARDWARE_ACCELERATION)
    def cleanup(self) -> int:
        """
        Clean up expired items.

        Returns:
            int: Number of items removed
        """
        with self.lock:
            return self._remove_expired_items()

    def get_stats(self) -> Dict[str, Any]:
        """
        Get cache statistics.

        Returns:
            Dict[str, Any]: Cache statistics
        """
        with self.lock:
            stats = {
                "item_count": len(self.cache),
                "memory_size": self.current_memory_size,
                "disk_size": self.current_disk_size,
                "hit_count": self.hit_count,
                "miss_count": self.miss_count,
                "hit_rate": self.hit_count / (self.hit_count + self.miss_count) if (self.hit_count + self.miss_count) > 0 else 0,
                "memory_items": sum(1 for item in self.cache.values() if item.storage_type == CacheStorageType.MEMORY),
                "compressed_items": sum(1 for item in self.cache.values() if item.storage_type == CacheStorageType.COMPRESSED),
                "disk_items": sum(1 for item in self.cache.values() if item.storage_type in (CacheStorageType.DISK, CacheStorageType.HYBRID)),
                "has_compression": self.compression_enabled
            }
            
            # Add compression stats if available
            if self.compression_enabled and self.compressor:
                compression_stats = self.compressor.get_stats()
                stats["compression"] = compression_stats
            
            return stats

    def memoize(self, 
               ttl: Optional[float] = None,
               priority: CachePriority = CachePriority.MEDIUM,
               key_fn: Optional[Callable[..., Any]] = None) -> Callable:
        """
        Decorator for memoizing function results.

        Args:
            ttl: Time-to-live in seconds (None for default)
            priority: Priority level for cached results
            key_fn: Function to generate cache key from function arguments

        Returns:
            Callable: Decorated function
        """
        def decorator(func):
            def wrapper(*args, **kwargs):
                # Generate cache key
                if key_fn is not None:
                    cache_key = key_fn(*args, **kwargs)
                else:
                    # Default key is function name + args + kwargs
                    cache_key = (func.__name__, args, frozenset(kwargs.items()))

                # Check cache
                result = self.get(cache_key)
                if result is not None:
                    return result

                # Call function
                result = func(*args, **kwargs)

                # Cache result
                self.set(cache_key, result, ttl=ttl, priority=priority)

                return result
            return wrapper
        return decorator


# Singleton instance
_cache_manager_instance = None


def get_cache() -> 'CacheManager':
    """
    Get the singleton instance of the cache manager.

    Returns:
        CacheManager: The singleton cache manager instance
    """
    global _cache_manager_instance
    if _cache_manager_instance is None:
        _cache_manager_instance = Cache()
    return _cache_manager_instance


def result_cache(ttl: Optional[float] = None, priority: CachePriority = CachePriority.MEDIUM) -> Callable:
    """
    Decorator for caching function results.

    Args:
        ttl: Time-to-live in seconds (None for default)
        priority: Priority level for cached results

    Returns:
        Callable: Decorated function
    """
    cache = get_cache()
    return cache.memoize(ttl=ttl, priority=priority)


def configure_cache(max_size: int = 1000, ttl: Optional[float] = None, max_memory_mb: Optional[int] = None) -> None:
    """
    Configure the cache settings.

    Args:
        max_size: Maximum number of items in the cache
        ttl: Default time-to-live for cache items in seconds
        max_memory_mb: Maximum memory usage in MB
    """
    # Get cache manager
    cache = get_cache()

    # Configure settings
    if max_memory_mb is not None:
        cache.max_memory_size = max_memory_mb * 1024 * 1024

    cache.default_ttl = ttl


def clear_cache() -> None:
    """Clear the entire cache."""
    cache = get_cache()
    cache.clear()


def get_cache_stats() -> Dict[str, Any]:
    """
    Get cache statistics.

    Returns:
        Dict[str, Any]: Cache statistics
    """
    cache = get_cache()
    return cache.get_stats()
