"""
Core cache implementation for the hardware-accelerated cache system.

This module provides the unified cache implementation that combines all
the unique features from the various cache utilities while leveraging
hardware acceleration capabilities.
"""

import fnmatch
import hashlib
import json
import os
import pickle
import threading
import time
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional, Set, Tuple, Union

import numpy as np
import pandas as pd

from yemen_trade_diagnostic.hardware.cache.keys import CacheKeyOptimizer, CacheKeyType
from yemen_trade_diagnostic.hardware.cache.storage import (
    DiskStorage,
    MemoryStorage,
    ProgressiveStorage,
    StorageTier,
)
from yemen_trade_diagnostic.hardware.cache.strategies import (
    DependencyInvalidationStrategy,
    EvictionStrategy,
    InvalidationStrategy,
    LRUEvictionStrategy,
    SmartInvalidationStrategy,
    WarmingStrategy,
)
from yemen_trade_diagnostic.hardware.cache.strategies.compression import (
    AdaptiveCompressionStrategy,
    CompressionStrategy,
)
from yemen_trade_diagnostic.hardware.core.detector import get_hardware_detector
from yemen_trade_diagnostic.hardware.memory.compression import MemoryCompressor as <PERSON><PERSON><PERSON>emoryCompressor
from yemen_trade_diagnostic.hardware.memory.pool import MemoryPool as <PERSON><PERSON><PERSON>emoryPool
from yemen_trade_diagnostic.errors import ErrorCategory, ErrorSeverity, with_error_handling
from yemen_trade_diagnostic.interfaces.logging_interface import get_logger

logger = get_logger(__name__)


class CacheStrategy(Enum):
    """Cache strategy types."""
    SIMPLE = "simple"
    SEMANTIC = "semantic"
    HIERARCHICAL = "hierarchical"
    TEMPORAL = "temporal"
    AUTO = "auto"


@dataclass
class CacheEntry:
    """Represents a cached item with metadata."""
    key: str
    value: Any
    size_bytes: int
    created_at: float
    last_access_time: float
    access_count: int
    ttl: Optional[float] = None
    compressed: bool = False
    storage_tier: StorageTier = StorageTier.MEMORY
    metadata: Dict[str, Any] = None
    
    def is_expired(self) -> bool:
        """Check if the cache entry has expired."""
        if self.ttl is None:
            return False
        return time.time() - self.created_at > self.ttl
    
    def update_access(self) -> None:
        """Update access statistics."""
        self.last_access_time = time.time()
        self.access_count += 1


class UnifiedCache:
    """
    Unified hardware-accelerated cache with all advanced features.
    
    This cache combines:
    - Hardware acceleration (Metal, CUDA, etc.)
    - Semantic key generation
    - Multi-tier storage
    - Smart invalidation
    - Predictive warming
    - Dependency tracking
    - Adaptive memory management
    """
    
    def __init__(
        self,
        max_memory_mb: Optional[int] = None,
        max_disk_mb: Optional[int] = None,
        strategy: Union[str, CacheStrategy] = CacheStrategy.AUTO,
        warming: Optional[str] = None,
        invalidation: Optional[str] = None,
        storage_tiers: Optional[List[str]] = None,
        enable_compression: bool = True,
        enable_hardware_acceleration: bool = True,
        cache_dir: Optional[Path] = None,
    ):
        """
        Initialize the unified cache.
        
        Args:
            max_memory_mb: Maximum memory cache size in MB
            max_disk_mb: Maximum disk cache size in MB
            strategy: Cache key strategy
            warming: Warming strategy name
            invalidation: Invalidation strategy name
            storage_tiers: List of storage tiers to use
            enable_compression: Whether to enable compression
            enable_hardware_acceleration: Whether to use hardware acceleration
            cache_dir: Directory for disk cache storage
        """
        # Hardware detection
        self.hw_detector = get_hardware_detector()
        self.hw_enabled = enable_hardware_acceleration and self.hw_detector.should_use_hardware_acceleration()
        
        # Memory management
        self.memory_pool = HWMemoryPool() if self.hw_enabled else None
        self.compressor = HWMemoryCompressor() if self.hw_enabled and enable_compression else None
        
        # Storage configuration
        self.max_memory_mb = max_memory_mb or self._auto_detect_memory_limit()
        self.max_disk_mb = max_disk_mb or 10000  # 10GB default
        self.cache_dir = cache_dir or Path.home() / ".cache" / "yemen_trade_diagnostic"
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize storage tiers
        self.storage_tiers = self._initialize_storage_tiers(storage_tiers)
        
        # Strategy configuration
        if isinstance(strategy, str):
            strategy = CacheStrategy(strategy)
        self.strategy = strategy
        self.key_optimizer = CacheKeyOptimizer()
        
        # Initialize strategies
        self.eviction_strategy = self._create_eviction_strategy()
        self.warming_strategy = self._create_warming_strategy(warming)
        self.invalidation_strategy = self._create_invalidation_strategy(invalidation)
        self.compression_strategy = AdaptiveCompressionStrategy() if enable_compression else None
        
        # Cache state
        self._cache_lock = threading.RLock()
        self._entries: Dict[str, CacheEntry] = {}
        self._dependencies: Dict[str, Set[str]] = {}  # parent -> children
        self._access_patterns: Dict[str, List[float]] = {}  # key -> access times
        
        # Statistics
        self._stats = {
            "hits": 0,
            "misses": 0,
            "evictions": 0,
            "compressions": 0,
            "decompressions": 0,
            "warming_hits": 0,
            "invalidations": 0,
            "hardware_accelerations": 0,
        }
        
        # Background threads
        self._stop_event = threading.Event()
        self._start_background_threads()
        
        logger.info(
            f"Initialized UnifiedCache with strategy={strategy.value}, "
            f"hw_acceleration={self.hw_enabled}, storage_tiers={len(self.storage_tiers)}"
        )
    
    def get(
        self,
        key: str,
        compute_func: Optional[Callable] = None,
        *args,
        ttl: Optional[float] = None,
        **kwargs
    ) -> Any:
        """
        Get a value from cache or compute it.
        
        Args:
            key: Cache key
            compute_func: Function to compute value if not cached
            *args: Arguments for compute_func
            ttl: Time to live in seconds
            **kwargs: Keyword arguments for compute_func
            
        Returns:
            Cached or computed value
        """
        # Generate optimized key based on strategy
        optimized_key = self._generate_key(key, kwargs)
        
        with self._cache_lock:
            # Check if key exists and is valid
            if optimized_key in self._entries:
                entry = self._entries[optimized_key]
                
                if not entry.is_expired():
                    # Update access statistics
                    entry.update_access()
                    self._track_access(optimized_key)
                    self._stats["hits"] += 1
                    
                    # Retrieve value from appropriate storage tier
                    value = self._retrieve_from_storage(entry)
                    
                    # Check if warming predicted this access
                    if hasattr(self.warming_strategy, 'was_predicted') and \
                       self.warming_strategy.was_predicted(optimized_key):
                        self._stats["warming_hits"] += 1
                    
                    logger.debug(f"Cache hit for key: {optimized_key}")
                    return value
                else:
                    # Remove expired entry
                    self._remove_entry(optimized_key)
            
            self._stats["misses"] += 1
        
        # Compute value if function provided
        if compute_func is not None:
            value = compute_func(*args, **kwargs)
            self.set(key, value, ttl=ttl, metadata=kwargs)
            return value
        
        return None
    
    def set(
        self,
        key: str,
        value: Any,
        ttl: Optional[float] = None,
        metadata: Optional[Dict[str, Any]] = None,
        dependencies: Optional[List[str]] = None,
    ) -> None:
        """
        Set a value in the cache.
        
        Args:
            key: Cache key
            value: Value to cache
            ttl: Time to live in seconds
            metadata: Additional metadata
            dependencies: List of keys this entry depends on
        """
        optimized_key = self._generate_key(key, metadata)
        
        # Calculate size
        size_bytes = self._estimate_size(value)
        
        # Check if we need to evict entries
        self._ensure_capacity(size_bytes)
        
        # Determine storage tier and compress if needed
        storage_tier, compressed_value, compressed = self._prepare_for_storage(value, size_bytes)
        
        with self._cache_lock:
            # Create cache entry
            entry = CacheEntry(
                key=optimized_key,
                value=compressed_value if compressed else value,
                size_bytes=size_bytes,
                created_at=time.time(),
                last_access_time=time.time(),
                access_count=1,
                ttl=ttl,
                compressed=compressed,
                storage_tier=storage_tier,
                metadata=metadata or {},
            )
            
            # Store in appropriate tier
            self._store_in_tier(entry)
            
            # Track entry
            self._entries[optimized_key] = entry
            
            # Track dependencies
            if dependencies:
                for dep_key in dependencies:
                    opt_dep_key = self._generate_key(dep_key, {})
                    if opt_dep_key not in self._dependencies:
                        self._dependencies[opt_dep_key] = set()
                    self._dependencies[opt_dep_key].add(optimized_key)
            
            # Track access pattern
            self._track_access(optimized_key)
            
            # Notify warming strategy
            if self.warming_strategy:
                self.warming_strategy.record_access(optimized_key, metadata)
        
        logger.debug(f"Cached value for key: {optimized_key} (size={size_bytes}, tier={storage_tier.value})")
    
    def delete(self, key: str, cascade: bool = True) -> bool:
        """
        Delete a key from the cache.
        
        Args:
            key: Cache key
            cascade: Whether to delete dependent entries
            
        Returns:
            True if key was deleted
        """
        optimized_key = self._generate_key(key, {})
        
        with self._cache_lock:
            if optimized_key not in self._entries:
                return False
            
            # Get dependent keys if cascading
            dependent_keys = []
            if cascade and optimized_key in self._dependencies:
                dependent_keys = list(self._dependencies[optimized_key])
            
            # Remove the entry
            self._remove_entry(optimized_key)
            self._stats["invalidations"] += 1
            
            # Remove dependent entries
            for dep_key in dependent_keys:
                if dep_key in self._entries:
                    self._remove_entry(dep_key)
                    self._stats["invalidations"] += 1
            
            return True
    
    def invalidate_pattern(self, pattern: str, strategy: Optional[str] = None) -> int:
        """
        Invalidate entries matching a pattern.
        
        Args:
            pattern: Glob pattern to match keys
            strategy: Invalidation strategy to use
            
        Returns:
            Number of entries invalidated
        """
        count = 0
        strategy_obj = self.invalidation_strategy
        
        if strategy and hasattr(self, f'_create_{strategy}_invalidation_strategy'):
            strategy_obj = getattr(self, f'_create_{strategy}_invalidation_strategy')()
        
        with self._cache_lock:
            matching_keys = [
                key for key in self._entries.keys()
                if fnmatch.fnmatch(key, pattern)
            ]
            
            if strategy_obj:
                # Use strategy to determine which keys to invalidate
                keys_to_invalidate = strategy_obj.select_keys_for_invalidation(
                    matching_keys, self._entries, self._access_patterns
                )
            else:
                keys_to_invalidate = matching_keys
            
            for key in keys_to_invalidate:
                if self.delete(key, cascade=True):
                    count += 1
        
        logger.info(f"Invalidated {count} entries matching pattern: {pattern}")
        return count
    
    def clear(self) -> None:
        """Clear all cache entries."""
        with self._cache_lock:
            # Clear all storage tiers
            for tier in self.storage_tiers.values():
                tier.clear()
            
            # Clear state
            self._entries.clear()
            self._dependencies.clear()
            self._access_patterns.clear()
            
            # Reset stats
            for key in self._stats:
                if key not in ["hardware_accelerations"]:
                    self._stats[key] = 0
        
        logger.info("Cleared all cache entries")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        with self._cache_lock:
            total_size = sum(entry.size_bytes for entry in self._entries.values())
            tier_stats = {}
            
            for tier_name, tier in self.storage_tiers.items():
                tier_entries = [e for e in self._entries.values() if e.storage_tier.value == tier_name]
                tier_stats[tier_name] = {
                    "entries": len(tier_entries),
                    "size_bytes": sum(e.size_bytes for e in tier_entries),
                }
            
            return {
                **self._stats,
                "total_entries": len(self._entries),
                "total_size_mb": total_size / (1024 * 1024),
                "hit_rate": self._stats["hits"] / max(1, self._stats["hits"] + self._stats["misses"]),
                "tier_stats": tier_stats,
                "hardware_acceleration_enabled": self.hw_enabled,
            }
    
    def warm_cache(self, predictions: Optional[List[Tuple[str, Dict[str, Any]]]] = None) -> int:
        """
        Warm the cache with predicted access patterns.
        
        Args:
            predictions: List of (key, metadata) tuples to pre-warm
            
        Returns:
            Number of entries warmed
        """
        if not self.warming_strategy:
            return 0
        
        if predictions is None:
            # Use warming strategy to get predictions
            predictions = self.warming_strategy.get_warming_predictions(
                self._access_patterns, 
                self._entries
            )
        
        warmed = 0
        for key, metadata in predictions:
            if key not in self._entries:
                # Simulate warming by marking the key
                logger.debug(f"Would warm cache for key: {key}")
                warmed += 1
        
        return warmed
    
    @with_error_handling(category=ErrorCategory.SYSTEM, severity=ErrorSeverity.WARNING, fallback_value=None)
    def memoize(
        self,
        ttl: Optional[float] = None,
        key_func: Optional[Callable] = None,
        compress: Optional[bool] = None,
        dependencies: Optional[List[str]] = None,
    ):
        """
        Decorator for memoizing function results.
        
        Args:
            ttl: Time to live in seconds
            key_func: Custom key generation function
            compress: Whether to compress the result
            dependencies: List of cache keys this result depends on
        """
        def decorator(func: Callable) -> Callable:
            def wrapper(*args, **kwargs):
                # Generate cache key
                if key_func:
                    cache_key = key_func(func, *args, **kwargs)
                else:
                    # Default key generation
                    key_parts = [func.__module__, func.__name__]
                    if args:
                        key_parts.extend(str(arg) for arg in args)
                    if kwargs:
                        key_parts.extend(f"{k}={v}" for k, v in sorted(kwargs.items()))
                    cache_key = ":".join(key_parts)
                
                # Check cache
                result = self.get(cache_key)
                if result is not None:
                    return result
                
                # Compute result
                result = func(*args, **kwargs)
                
                # Store in cache
                self.set(
                    cache_key,
                    result,
                    ttl=ttl,
                    metadata={"function": func.__name__, "args": str(args), "kwargs": str(kwargs)},
                    dependencies=dependencies,
                )
                
                return result
            
            return wrapper
        return decorator
    
    def _generate_key(self, key: str, metadata: Optional[Dict[str, Any]]) -> str:
        """Generate optimized cache key based on strategy."""
        if self.strategy == CacheStrategy.AUTO:
            # Auto-detect best strategy
            if metadata and len(metadata) > 3:
                key_type = CacheKeyType.HIERARCHICAL
            elif "year" in str(metadata):
                key_type = CacheKeyType.TEMPORAL
            else:
                key_type = CacheKeyType.SEMANTIC
        elif self.strategy == CacheStrategy.SEMANTIC:
            key_type = CacheKeyType.SEMANTIC
        elif self.strategy == CacheStrategy.HIERARCHICAL:
            key_type = CacheKeyType.HIERARCHICAL
        elif self.strategy == CacheStrategy.TEMPORAL:
            key_type = CacheKeyType.TEMPORAL
        else:
            key_type = CacheKeyType.EXACT
        
        return self.key_optimizer.optimize_cache_key(
            loader_name=key.split(":")[0] if ":" in key else "default",
            path_or_identifier=key,
            filters=metadata,
            optimization_type=key_type,
        )
    
    def _estimate_size(self, value: Any) -> int:
        """Estimate the size of a value in bytes."""
        if isinstance(value, pd.DataFrame):
            return value.memory_usage(deep=True).sum()
        elif isinstance(value, np.ndarray):
            return value.nbytes
        elif isinstance(value, (list, dict)):
            # Rough estimate using pickle
            try:
                return len(pickle.dumps(value))
            except:
                return 1000  # Default estimate
        else:
            return 100  # Small default
    
    def _ensure_capacity(self, required_bytes: int) -> None:
        """Ensure there's enough capacity for new entry."""
        with self._cache_lock:
            current_memory_usage = sum(
                e.size_bytes for e in self._entries.values()
                if e.storage_tier == StorageTier.MEMORY
            )
            
            max_memory_bytes = self.max_memory_mb * 1024 * 1024
            
            if current_memory_usage + required_bytes > max_memory_bytes:
                # Need to evict entries
                needed_space = (current_memory_usage + required_bytes) - max_memory_bytes
                
                # Get memory entries
                memory_entries = {
                    k: v for k, v in self._entries.items()
                    if v.storage_tier == StorageTier.MEMORY
                }
                
                # Select victims
                victims = self.eviction_strategy.select_victims(memory_entries, needed_space)
                
                # Evict or move to disk
                for victim_key in victims:
                    entry = self._entries[victim_key]
                    
                    # Try to move to disk if available
                    if StorageTier.DISK in self.storage_tiers:
                        entry.storage_tier = StorageTier.DISK
                        self._store_in_tier(entry)
                        logger.debug(f"Moved entry to disk: {victim_key}")
                    else:
                        # Remove entry
                        self._remove_entry(victim_key)
                        self._stats["evictions"] += 1
                        logger.debug(f"Evicted entry: {victim_key}")
    
    def _prepare_for_storage(self, value: Any, size_bytes: int) -> Tuple[StorageTier, Any, bool]:
        """Prepare value for storage, determining tier and compression."""
        # Determine storage tier based on size
        if size_bytes < 1024 * 1024:  # < 1MB
            tier = StorageTier.MEMORY
        elif size_bytes < 10 * 1024 * 1024:  # < 10MB
            tier = StorageTier.DISK if StorageTier.DISK in self.storage_tiers else StorageTier.MEMORY
        else:
            # Large items go to progressive storage if available
            if StorageTier.PROGRESSIVE in self.storage_tiers:
                tier = StorageTier.PROGRESSIVE
            elif StorageTier.DISK in self.storage_tiers:
                tier = StorageTier.DISK
            else:
                tier = StorageTier.MEMORY
        
        # Compress if enabled and beneficial
        compressed = False
        compressed_value = value
        
        if self.compression_strategy and size_bytes > 1024:  # Only compress > 1KB
            try:
                if self.compressor and self.hw_enabled:
                    # Use hardware compression
                    compressed_value = self.compressor.compress(value)
                    compressed = True
                    self._stats["compressions"] += 1
                    self._stats["hardware_accelerations"] += 1
                else:
                    # Fallback to strategy compression
                    compressed_value, compression_metadata = self.compression_strategy.compress(value)
                    compressed = True
                    self._stats["compressions"] += 1
                    # Store compression metadata in entry metadata
                    if metadata is None:
                        metadata = {}
                    metadata.update(compression_metadata)
            except Exception as e:
                logger.warning(f"Compression failed: {e}")
        
        return tier, compressed_value, compressed
    
    def _store_in_tier(self, entry: CacheEntry) -> None:
        """Store entry in the appropriate storage tier."""
        tier = self.storage_tiers.get(entry.storage_tier.value)
        if tier:
            tier.store(entry.key, entry.value, entry.metadata)
    
    def _retrieve_from_storage(self, entry: CacheEntry) -> Any:
        """Retrieve value from storage tier."""
        tier = self.storage_tiers.get(entry.storage_tier.value)
        if not tier:
            return None
        
        value = tier.retrieve(entry.key)
        
        # Decompress if needed
        if entry.compressed and value is not None:
            try:
                if self.compressor and self.hw_enabled:
                    value = self.compressor.decompress(value)
                    self._stats["decompressions"] += 1
                    self._stats["hardware_accelerations"] += 1
                else:
                    # Get compression metadata from entry
                    compression_metadata = entry.metadata if entry.metadata else {}
                    value = self.compression_strategy.decompress(value, compression_metadata)
                    self._stats["decompressions"] += 1
            except Exception as e:
                logger.warning(f"Decompression failed: {e}")
                return None
        
        return value
    
    def _remove_entry(self, key: str) -> None:
        """Remove an entry from cache."""
        if key not in self._entries:
            return
        
        entry = self._entries[key]
        
        # Remove from storage tier
        tier = self.storage_tiers.get(entry.storage_tier.value)
        if tier:
            tier.remove(key)
        
        # Remove from tracking
        del self._entries[key]
        
        # Remove dependencies
        if key in self._dependencies:
            del self._dependencies[key]
        
        # Clean up access patterns
        if key in self._access_patterns:
            del self._access_patterns[key]
    
    def _track_access(self, key: str) -> None:
        """Track access pattern for a key."""
        if key not in self._access_patterns:
            self._access_patterns[key] = []
        
        self._access_patterns[key].append(time.time())
        
        # Keep only recent accesses (last 100)
        if len(self._access_patterns[key]) > 100:
            self._access_patterns[key] = self._access_patterns[key][-100:]
    
    def _auto_detect_memory_limit(self) -> int:
        """Auto-detect appropriate memory limit."""
        try:
            import psutil
            total_memory = psutil.virtual_memory().total
            # Use 10% of available memory, max 2GB
            limit_bytes = min(int(total_memory * 0.1), 2 * 1024 * 1024 * 1024)
            return limit_bytes // (1024 * 1024)
        except:
            return 512  # Default 512MB
    
    def _initialize_storage_tiers(self, tier_names: Optional[List[str]]) -> Dict[str, StorageTier]:
        """Initialize storage tiers."""
        if tier_names is None:
            tier_names = ["memory", "disk"]
        
        tiers = {}
        
        for tier_name in tier_names:
            if tier_name == "memory":
                tiers[tier_name] = MemoryStorage(max_size_mb=self.max_memory_mb)
            elif tier_name == "disk":
                tiers[tier_name] = DiskStorage(
                    cache_dir=self.cache_dir / "disk",
                    max_size_mb=self.max_disk_mb
                )
            elif tier_name == "progressive":
                tiers[tier_name] = ProgressiveStorage(
                    cache_dir=self.cache_dir / "progressive"
                )
        
        return tiers
    
    def _create_eviction_strategy(self) -> EvictionStrategy:
        """Create eviction strategy."""
        # Default to LRU with hardware optimization
        return LRUEvictionStrategy()
    
    def _create_warming_strategy(self, strategy_name: Optional[str]) -> Optional[WarmingStrategy]:
        """Create warming strategy."""
        if not strategy_name:
            return None
        
        if strategy_name == "predictive" or strategy_name == "intelligent":
            from yemen_trade_diagnostic.hardware.cache.strategies.warming import IntelligentWarmingStrategy
            return IntelligentWarmingStrategy()
        
        return None
    
    def _create_invalidation_strategy(self, strategy_name: Optional[str]) -> Optional[InvalidationStrategy]:
        """Create invalidation strategy."""
        if not strategy_name:
            return None
        
        if strategy_name == "smart":
            return SmartInvalidationStrategy()
        elif strategy_name == "dependency":
            return DependencyInvalidationStrategy()
        
        return None
    
    def _start_background_threads(self) -> None:
        """Start background maintenance threads."""
        # Periodic cleanup thread
        def cleanup_worker():
            while not self._stop_event.is_set():
                try:
                    # Clean expired entries
                    with self._cache_lock:
                        expired_keys = [
                            key for key, entry in self._entries.items()
                            if entry.is_expired()
                        ]
                        for key in expired_keys:
                            self._remove_entry(key)
                    
                    # Sleep for 60 seconds
                    self._stop_event.wait(60)
                except Exception as e:
                    logger.error(f"Cleanup worker error: {e}")
        
        cleanup_thread = threading.Thread(target=cleanup_worker, daemon=True)
        cleanup_thread.start()
        
        # Warming thread if strategy enabled
        if self.warming_strategy:
            def warming_worker():
                while not self._stop_event.is_set():
                    try:
                        # Get predictions and warm cache
                        self.warm_cache()
                        # Sleep for 5 minutes
                        self._stop_event.wait(300)
                    except Exception as e:
                        logger.error(f"Warming worker error: {e}")
            
            warming_thread = threading.Thread(target=warming_worker, daemon=True)
            warming_thread.start()
    
    def __del__(self):
        """Cleanup on deletion."""
        if hasattr(self, '_stop_event'):
            self._stop_event.set()
        if hasattr(self, '_cache_lock'):
            with self._cache_lock:
                self.clear()


# Convenience function for creating cache instance
def create_cache(**kwargs) -> UnifiedCache:
    """Create a UnifiedCache instance with the given configuration."""
    return UnifiedCache(**kwargs)