"""
Invalidation strategies for the hardware-accelerated cache system.

This module provides various cache invalidation strategies including
dependency tracking and smart invalidation optimized for hardware.
"""

import fnmatch
import threading
import time
from abc import ABC, abstractmethod
from collections import defaultdict
from dataclasses import dataclass
from enum import Enum
from typing import Any, Dict, List, Optional, Pattern, Set, Tuple

import numpy as np

from yemen_trade_diagnostic.hardware.core.detector import get_hardware_detector
from yemen_trade_diagnostic.hardware.parallel.processor import ParallelProcessor
from yemen_trade_diagnostic.errors import protect, OperationType, HARDWARE_CONFIG
from yemen_trade_diagnostic.interfaces.logging_interface import get_logger

logger = get_logger(__name__)


class InvalidationReason(Enum):
    """Reasons for cache invalidation."""
    EXPIRED = "expired"
    STALE = "stale"
    DEPENDENCY = "dependency"
    PATTERN = "pattern"
    MANUAL = "manual"
    MEMORY_PRESSURE = "memory_pressure"


@dataclass
class InvalidationRule:
    """Rule for cache invalidation."""
    rule_id: str
    pattern: str
    conditions: Dict[str, Any]
    priority: int = 100
    enabled: bool = True
    
    def matches_key(self, cache_key: str) -> bool:
        """Check if this rule matches a cache key."""
        return fnmatch.fnmatch(cache_key, self.pattern)


class InvalidationStrategy(ABC):
    """Base class for cache invalidation strategies."""
    
    @abstractmethod
    def select_keys_for_invalidation(
        self,
        candidate_keys: List[str],
        entries: Dict[str, Any],
        access_patterns: Dict[str, List[float]]
    ) -> List[str]:
        """Select which keys to invalidate from candidates."""
        pass


class DependencyInvalidationStrategy(InvalidationStrategy):
    """
    Invalidation strategy based on data dependencies.
    
    This strategy tracks dependencies between cache entries and
    invalidates dependent entries when their dependencies change.
    """
    
    def __init__(self):
        """Initialize dependency invalidation strategy."""
        self._dependencies: Dict[str, Set[str]] = defaultdict(set)  # parent -> children
        self._reverse_deps: Dict[str, Set[str]] = defaultdict(set)  # child -> parents
        self._lock = threading.RLock()
        
        # Hardware optimization
        self.hw_detector = get_hardware_detector()
        self.parallel_processor = ParallelProcessor() if self.hw_detector.should_use_hardware_acceleration() else None
        
        logger.info("Initialized DependencyInvalidationStrategy")
    
    def add_dependency(self, parent_key: str, child_key: str) -> None:
        """Add a dependency relationship."""
        with self._lock:
            self._dependencies[parent_key].add(child_key)
            self._reverse_deps[child_key].add(parent_key)
    
    def remove_dependency(self, parent_key: str, child_key: str) -> None:
        """Remove a dependency relationship."""
        with self._lock:
            if parent_key in self._dependencies:
                self._dependencies[parent_key].discard(child_key)
                if not self._dependencies[parent_key]:
                    del self._dependencies[parent_key]
            
            if child_key in self._reverse_deps:
                self._reverse_deps[child_key].discard(parent_key)
                if not self._reverse_deps[child_key]:
                    del self._reverse_deps[child_key]
    
    def get_dependent_keys(self, parent_key: str, recursive: bool = True) -> Set[str]:
        """Get all keys that depend on the given parent key."""
        with self._lock:
            if not recursive:
                return self._dependencies.get(parent_key, set()).copy()
            
            # Recursive dependency resolution
            all_deps = set()
            to_process = [parent_key]
            processed = set()
            
            while to_process:
                current = to_process.pop(0)
                if current in processed:
                    continue
                
                processed.add(current)
                direct_deps = self._dependencies.get(current, set())
                all_deps.update(direct_deps)
                to_process.extend(direct_deps - processed)
            
            return all_deps
    
    def select_keys_for_invalidation(
        self,
        candidate_keys: List[str],
        entries: Dict[str, Any],
        access_patterns: Dict[str, List[float]]
    ) -> List[str]:
        """Select keys for invalidation based on dependencies."""
        invalidate_keys = set(candidate_keys)
        
        # Add all dependent keys
        with self._lock:
            for key in candidate_keys:
                deps = self.get_dependent_keys(key, recursive=True)
                invalidate_keys.update(deps)
        
        # Filter to only existing keys
        return [key for key in invalidate_keys if key in entries]
    
    def analyze_dependency_graph(self) -> Dict[str, Any]:
        """Analyze the dependency graph for optimization opportunities."""
        with self._lock:
            total_deps = sum(len(children) for children in self._dependencies.values())
            
            # Find circular dependencies
            circular_deps = self._find_circular_dependencies()
            
            # Find orphaned dependencies
            all_children = set()
            for children in self._dependencies.values():
                all_children.update(children)
            
            all_parents = set(self._dependencies.keys())
            orphaned = all_children - all_parents
            
            return {
                "total_dependencies": total_deps,
                "parent_count": len(self._dependencies),
                "child_count": len(self._reverse_deps),
                "circular_dependencies": len(circular_deps),
                "orphaned_dependencies": len(orphaned),
                "max_dependency_depth": self._calculate_max_depth(),
            }
    
    def _find_circular_dependencies(self) -> List[List[str]]:
        """Find circular dependencies in the graph."""
        circular = []
        visited = set()
        rec_stack = set()
        
        def visit(node: str, path: List[str]):
            if node in rec_stack:
                # Found circular dependency
                cycle_start = path.index(node)
                circular.append(path[cycle_start:] + [node])
                return
            
            if node in visited:
                return
            
            visited.add(node)
            rec_stack.add(node)
            
            for child in self._dependencies.get(node, set()):
                visit(child, path + [node])
            
            rec_stack.remove(node)
        
        for node in self._dependencies:
            if node not in visited:
                visit(node, [])
        
        return circular
    
    def _calculate_max_depth(self) -> int:
        """Calculate the maximum dependency depth."""
        if not self._dependencies:
            return 0
        
        depths = {}
        
        def calculate_depth(node: str) -> int:
            if node in depths:
                return depths[node]
            
            children = self._dependencies.get(node, set())
            if not children:
                depths[node] = 0
                return 0
            
            max_child_depth = max(calculate_depth(child) for child in children)
            depths[node] = max_child_depth + 1
            return depths[node]
        
        return max(calculate_depth(node) for node in self._dependencies)


class SmartInvalidationStrategy(InvalidationStrategy):
    """
    Smart invalidation strategy that considers multiple factors.
    
    This strategy combines time-based, usage-based, and pattern-based
    invalidation to minimize false invalidations.
    """
    
    def __init__(self):
        """Initialize smart invalidation strategy."""
        self._invalidation_rules: Dict[str, InvalidationRule] = {}
        self._stats = {
            "total_invalidations": 0,
            "false_invalidations": 0,
            "preserved_entries": 0,
        }
        self._lock = threading.RLock()
        
        # Setup default rules
        self._setup_default_rules()
        
        logger.info("Initialized SmartInvalidationStrategy")
    
    def select_keys_for_invalidation(
        self,
        candidate_keys: List[str],
        entries: Dict[str, Any],
        access_patterns: Dict[str, List[float]]
    ) -> List[str]:
        """Select keys for invalidation using smart heuristics."""
        invalidate_keys = []
        current_time = time.time()
        
        for key in candidate_keys:
            if key not in entries:
                continue
            
            entry = entries[key]
            access_times = access_patterns.get(key, [])
            
            # Decision factors
            should_invalidate = False
            
            # Factor 1: Age-based invalidation
            age = current_time - entry.created_at
            if age > 86400:  # 24 hours
                should_invalidate = True
            
            # Factor 2: Access frequency
            if access_times and len(access_times) > 5:
                # Calculate access frequency
                time_span = access_times[-1] - access_times[0]
                if time_span > 0:
                    frequency = len(access_times) / (time_span / 3600)
                    
                    # Preserve frequently accessed items
                    if frequency > 2.0:  # More than 2 accesses per hour
                        should_invalidate = False
                        self._stats["preserved_entries"] += 1
            
            # Factor 3: Last access time
            if hasattr(entry, 'last_access_time'):
                time_since_access = current_time - entry.last_access_time
                if time_since_access < 300:  # Accessed within 5 minutes
                    should_invalidate = False
                    self._stats["preserved_entries"] += 1
            
            # Factor 4: Size consideration
            if hasattr(entry, 'size_bytes') and entry.size_bytes < 1024:  # Small entries
                # Keep small entries longer
                if age < 172800:  # 48 hours
                    should_invalidate = False
            
            # Apply custom rules
            for rule in self._invalidation_rules.values():
                if rule.enabled and rule.matches_key(key):
                    if self._evaluate_rule(rule, entry, access_times):
                        should_invalidate = True
                        break
            
            if should_invalidate:
                invalidate_keys.append(key)
                self._stats["total_invalidations"] += 1
        
        return invalidate_keys
    
    def add_invalidation_rule(
        self,
        rule_id: str,
        pattern: str,
        conditions: Dict[str, Any],
        priority: int = 100
    ) -> None:
        """Add a custom invalidation rule."""
        with self._lock:
            rule = InvalidationRule(
                rule_id=rule_id,
                pattern=pattern,
                conditions=conditions,
                priority=priority
            )
            self._invalidation_rules[rule_id] = rule
    
    def _evaluate_rule(
        self,
        rule: InvalidationRule,
        entry: Any,
        access_times: List[float]
    ) -> bool:
        """Evaluate if a rule matches an entry."""
        conditions = rule.conditions
        
        # Check age condition
        if "max_age_hours" in conditions:
            age_hours = (time.time() - entry.created_at) / 3600
            if age_hours > conditions["max_age_hours"]:
                return True
        
        # Check access frequency condition
        if "min_access_frequency" in conditions and access_times:
            if len(access_times) > 1:
                time_span = access_times[-1] - access_times[0]
                if time_span > 0:
                    frequency = len(access_times) / (time_span / 3600)
                    if frequency < conditions["min_access_frequency"]:
                        return True
        
        # Check size condition
        if "max_size_mb" in conditions and hasattr(entry, 'size_bytes'):
            if entry.size_bytes > conditions["max_size_mb"] * 1024 * 1024:
                return True
        
        return False
    
    def _setup_default_rules(self) -> None:
        """Setup default invalidation rules."""
        # Rule 1: Invalidate old temporary data
        self.add_invalidation_rule(
            "temp_data_cleanup",
            "*temp*",
            {"max_age_hours": 1},
            priority=90
        )
        
        # Rule 2: Invalidate large old data
        self.add_invalidation_rule(
            "large_data_cleanup",
            "*",
            {"max_age_hours": 12, "max_size_mb": 100},
            priority=80
        )
        
        # Rule 3: Invalidate rarely accessed data
        self.add_invalidation_rule(
            "rare_access_cleanup",
            "*",
            {"max_age_hours": 6, "min_access_frequency": 0.1},
            priority=70
        )
    
    def get_stats(self) -> Dict[str, Any]:
        """Get invalidation statistics."""
        with self._lock:
            stats = self._stats.copy()
            stats["active_rules"] = len([r for r in self._invalidation_rules.values() if r.enabled])
            
            # Calculate effectiveness
            if stats["total_invalidations"] > 0:
                stats["preservation_rate"] = stats["preserved_entries"] / stats["total_invalidations"]
            else:
                stats["preservation_rate"] = 0.0
            
            return stats


class InvalidationPatternAnalyzer:
    """
    Analyzes invalidation patterns to optimize future invalidations.
    
    Uses hardware acceleration to identify patterns in invalidation
    behavior and suggest optimizations.
    """
    
    def __init__(self):
        """Initialize pattern analyzer."""
        self.hw_detector = get_hardware_detector()
        self._invalidation_history = []
        self._pattern_cache = {}
        self._lock = threading.RLock()
        
        logger.info("Initialized InvalidationPatternAnalyzer")
    
    def record_invalidation(
        self,
        key: str,
        reason: InvalidationReason,
        metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """Record an invalidation event."""
        with self._lock:
            self._invalidation_history.append({
                "key": key,
                "reason": reason,
                "timestamp": time.time(),
                "metadata": metadata or {}
            })
            
            # Keep only recent history
            if len(self._invalidation_history) > 10000:
                self._invalidation_history = self._invalidation_history[-5000:]
    
    def analyze_patterns(self) -> Dict[str, Any]:
        """Analyze invalidation patterns."""
        with self._lock:
            if not self._invalidation_history:
                return {"patterns": [], "recommendations": []}
            
            # Group by reason
            by_reason = defaultdict(list)
            for event in self._invalidation_history:
                by_reason[event["reason"]].append(event)
            
            patterns = []
            
            # Analyze each reason
            for reason, events in by_reason.items():
                pattern = self._analyze_reason_pattern(reason, events)
                if pattern:
                    patterns.append(pattern)
            
            # Generate recommendations
            recommendations = self._generate_recommendations(patterns)
            
            return {
                "patterns": patterns,
                "recommendations": recommendations,
                "total_invalidations": len(self._invalidation_history),
                "reasons": dict(by_reason.keys()),
            }
    
    def _analyze_reason_pattern(
        self,
        reason: InvalidationReason,
        events: List[Dict[str, Any]]
    ) -> Optional[Dict[str, Any]]:
        """Analyze pattern for a specific invalidation reason."""
        if len(events) < 10:
            return None
        
        # Extract time series
        timestamps = [e["timestamp"] for e in events]
        
        # Calculate intervals
        intervals = np.diff(timestamps)
        
        if len(intervals) == 0:
            return None
        
        # Statistical analysis
        mean_interval = np.mean(intervals)
        std_interval = np.std(intervals)
        
        # Key patterns
        keys = [e["key"] for e in events]
        unique_keys = len(set(keys))
        
        pattern = {
            "reason": reason.value,
            "event_count": len(events),
            "mean_interval_seconds": mean_interval,
            "std_interval_seconds": std_interval,
            "unique_keys": unique_keys,
            "key_reuse_rate": 1 - (unique_keys / len(keys)),
        }
        
        # Detect periodicity
        if std_interval < mean_interval * 0.2:
            pattern["periodic"] = True
            pattern["period_seconds"] = mean_interval
        
        return pattern
    
    def _generate_recommendations(self, patterns: List[Dict[str, Any]]) -> List[str]:
        """Generate recommendations based on patterns."""
        recommendations = []
        
        for pattern in patterns:
            reason = pattern["reason"]
            
            # High frequency invalidations
            if pattern["mean_interval_seconds"] < 60:
                recommendations.append(
                    f"High frequency {reason} invalidations detected. "
                    f"Consider adjusting TTL or caching strategy."
                )
            
            # High key reuse
            if pattern["key_reuse_rate"] > 0.5:
                recommendations.append(
                    f"Same keys being invalidated repeatedly ({reason}). "
                    f"Consider shorter TTL for these keys."
                )
            
            # Periodic invalidations
            if pattern.get("periodic"):
                period = pattern["period_seconds"]
                recommendations.append(
                    f"Periodic {reason} invalidations detected (every {period:.0f}s). "
                    f"Consider aligning cache TTL with this period."
                )
        
        return recommendations