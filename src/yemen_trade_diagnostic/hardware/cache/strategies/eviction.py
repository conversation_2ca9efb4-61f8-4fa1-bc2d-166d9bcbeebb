"""
Eviction strategies for the hardware-accelerated cache system.

This module provides various eviction strategies optimized for hardware acceleration.
"""

import time
from abc import ABC, abstractmethod
from typing import Any, Dict, List

import numpy as np

from yemen_trade_diagnostic.errors import protect, OperationType, HARDWARE_CONFIG
from yemen_trade_diagnostic.hardware.core.detector import get_hardware_detector
from yemen_trade_diagnostic.interfaces.logging_interface import get_logger

logger = get_logger(__name__)


class EvictionStrategy(ABC):
    """Base class for cache eviction strategies."""
    
    @abstractmethod
    def select_victims(self, cache_items: Dict[str, Any], required_space: int) -> List[str]:
        """Select cache keys to evict."""
        pass


class LRUEvictionStrategy(EvictionStrategy):
    """Least Recently Used eviction strategy."""
    
    def select_victims(self, cache_items: Dict[str, Any], required_space: int) -> List[str]:
        """Select least recently used items for eviction."""
        # Sort by last access time
        sorted_items = sorted(
            cache_items.items(),
            key=lambda x: x[1].last_access_time
        )
        
        victims = []
        freed_space = 0
        
        for key, item in sorted_items:
            victims.append(key)
            freed_space += item.size_bytes
            if freed_space >= required_space:
                break
        
        return victims


class PriorityBasedEvictionStrategy(EvictionStrategy):
    """Priority-based eviction strategy optimized for hardware acceleration."""
    
    def __init__(self):
        """Initialize priority-based eviction."""
        self.detector = get_hardware_detector()
        self._use_simd = self.detector.is_apple_silicon
    
    def select_victims(self, cache_items: Dict[str, Any], required_space: int) -> List[str]:
        """Select items based on priority and hardware-optimized scoring."""
        # Calculate scores for each item
        scored_items = []
        
        for key, item in cache_items.items():
            # Score combines priority, age, and access frequency
            age_hours = (time.time() - item.creation_time) / 3600
            access_rate = item.access_count / max(age_hours, 1)
            
            # Hardware-optimized scoring
            if self._use_simd:
                # Use vectorized operations for Apple Silicon
                score = self._calculate_score_simd(
                    item.priority.value,
                    age_hours,
                    access_rate,
                    item.size_bytes
                )
            else:
                # Standard scoring
                score = (
                    (5 - item.priority.value) * 100 +  # Lower priority = higher score
                    age_hours * 10 -                    # Older = higher score
                    access_rate * 50                    # Less accessed = higher score
                )
            
            scored_items.append((score, key, item.size_bytes))
        
        # Sort by score (highest first for eviction)
        scored_items.sort(key=lambda x: x[0], reverse=True)
        
        victims = []
        freed_space = 0
        
        for score, key, size in scored_items:
            victims.append(key)
            freed_space += size
            if freed_space >= required_space:
                break
        
        return victims
    
    def _calculate_score_simd(self, priority: int, age: float, 
                            access_rate: float, size: int) -> float:
        """Calculate eviction score using SIMD operations."""
        # This would use actual SIMD instructions on Apple Silicon
        # For now, simulate with optimized calculation
        factors = np.array([
            5 - priority,    # Priority factor
            age / 24,        # Age in days
            1 / (access_rate + 1),  # Inverse access rate
            size / (1024 * 1024)    # Size in MB
        ])
        
        weights = np.array([100, 240, 50, 0.1])
        
        return np.dot(factors, weights)