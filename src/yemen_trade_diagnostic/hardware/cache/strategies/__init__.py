"""
Cache Strategies Module

This module provides various strategies for cache operations including
eviction, warming, invalidation, and compression, all optimized for hardware acceleration.
"""

from yemen_trade_diagnostic.hardware.cache.strategies.compression import (
    AdaptiveCompressionStrategy,
    CompressionStrategy,
)
from yemen_trade_diagnostic.hardware.cache.strategies.eviction import (
    EvictionStrategy,
    LRUEvictionStrategy,
    PriorityBasedEvictionStrategy,
)
from yemen_trade_diagnostic.hardware.cache.strategies.invalidation import (
    DependencyInvalidationStrategy,
    InvalidationPatternAnalyzer,
    InvalidationReason,
    InvalidationStrategy,
    SmartInvalidationStrategy,
)
from yemen_trade_diagnostic.hardware.cache.strategies.warming import (
    IntelligentWarmingStrategy,
    WarmingStrategy,
)

__all__ = [
    # Eviction strategies
    'EvictionStrategy',
    'LRUEvictionStrategy',
    'PriorityBasedEvictionStrategy',
    # Warming strategies
    'WarmingStrategy',
    'IntelligentWarmingStrategy',
    # Invalidation strategies
    'InvalidationStrategy',
    'DependencyInvalidationStrategy',
    'SmartInvalidationStrategy',
    'InvalidationPatternAnalyzer',
    'InvalidationReason',
    # Compression strategies
    'CompressionStrategy',
    'AdaptiveCompressionStrategy',
]