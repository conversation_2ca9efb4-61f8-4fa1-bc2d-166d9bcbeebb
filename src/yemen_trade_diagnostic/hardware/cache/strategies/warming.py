"""
Cache warming strategies for the hardware-accelerated cache system.

This module provides strategies for pre-loading cache entries based on
predicted access patterns, optimized for hardware acceleration.
"""

import threading
import time
from abc import ABC, abstractmethod
from collections import defaultdict, deque
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Set, Tuple

import numpy as np

from yemen_trade_diagnostic.hardware.acceleration.workload_analyzer import <PERSON>load<PERSON><PERSON>yzer
from yemen_trade_diagnostic.hardware.core.detector import get_hardware_detector
from yemen_trade_diagnostic.errors import protect, OperationType, HARDWARE_CONFIG
from yemen_trade_diagnostic.interfaces.logging_interface import get_logger

logger = get_logger(__name__)


class WarmingStrategy(ABC):
    """Base class for cache warming strategies."""
    
    @abstractmethod
    def record_access(self, key: str, metadata: Optional[Dict[str, Any]] = None) -> None:
        """Record a cache access for pattern learning."""
        pass
    
    @abstractmethod
    def get_warming_predictions(
        self,
        access_patterns: Dict[str, List[float]],
        current_entries: Dict[str, Any]
    ) -> List[Tuple[str, Dict[str, Any]]]:
        """Get predictions for cache warming."""
        pass
    
    @abstractmethod
    def was_predicted(self, key: str) -> bool:
        """Check if this access was predicted."""
        pass


class IntelligentWarmingStrategy(WarmingStrategy):
    """
    Intelligent cache warming using access pattern analysis and ML predictions.
    
    This strategy uses hardware acceleration (Neural Engine on Apple Silicon)
    to predict future cache accesses based on historical patterns.
    """
    
    def __init__(self, history_size: int = 1000, prediction_window: int = 300):
        """
        Initialize intelligent warming strategy.
        
        Args:
            history_size: Number of access records to keep
            prediction_window: Time window (seconds) for predictions
        """
        self.history_size = history_size
        self.prediction_window = prediction_window
        
        # Access tracking
        self._access_history = deque(maxlen=history_size)
        self._access_patterns = defaultdict(lambda: {
            "times": deque(maxlen=100),
            "metadata": deque(maxlen=10),
            "frequency": 0.0,
            "periodicity": None,
        })
        self._predicted_keys: Set[str] = set()
        self._lock = threading.RLock()
        
        # Hardware optimization
        self.hw_detector = get_hardware_detector()
        self.workload_analyzer = WorkloadAnalyzer() if self.hw_detector.should_use_hardware_acceleration() else None
        
        # Pattern detection parameters
        self.min_pattern_occurrences = 3
        self.pattern_threshold = 0.8
        
        # Start background pattern analysis
        self._stop_event = threading.Event()
        self._start_analysis_thread()
        
        logger.info("Initialized IntelligentWarmingStrategy with ML-based prediction")
    
    def record_access(self, key: str, metadata: Optional[Dict[str, Any]] = None) -> None:
        """Record a cache access for pattern learning."""
        current_time = time.time()
        
        with self._lock:
            # Add to history
            self._access_history.append({
                "key": key,
                "time": current_time,
                "metadata": metadata or {},
            })
            
            # Update pattern tracking
            pattern = self._access_patterns[key]
            pattern["times"].append(current_time)
            if metadata:
                pattern["metadata"].append(metadata)
            
            # Update frequency
            if len(pattern["times"]) > 1:
                time_span = current_time - pattern["times"][0]
                if time_span > 0:
                    pattern["frequency"] = len(pattern["times"]) / (time_span / 3600)
    
    def get_warming_predictions(
        self,
        access_patterns: Dict[str, List[float]],
        current_entries: Dict[str, Any]
    ) -> List[Tuple[str, Dict[str, Any]]]:
        """Get predictions for cache warming."""
        predictions = []
        current_time = time.time()
        
        with self._lock:
            # Analyze each pattern
            for key, pattern in self._access_patterns.items():
                # Skip if already cached
                if key in current_entries:
                    continue
                
                # Check if we expect this key to be accessed soon
                if self._predict_access(key, pattern, current_time):
                    # Generate metadata for warming
                    metadata = self._generate_warming_metadata(pattern)
                    predictions.append((key, metadata))
                    self._predicted_keys.add(key)
            
            # Use workload analyzer for advanced predictions
            if self.workload_analyzer:
                ml_predictions = self._get_ml_predictions(current_time)
                for key, metadata in ml_predictions:
                    if key not in current_entries:
                        predictions.append((key, metadata))
                        self._predicted_keys.add(key)
        
        # Sort by predicted access likelihood
        predictions.sort(key=lambda x: self._calculate_warming_priority(x[0]), reverse=True)
        
        return predictions[:20]  # Limit to top 20 predictions
    
    def was_predicted(self, key: str) -> bool:
        """Check if this access was predicted."""
        with self._lock:
            if key in self._predicted_keys:
                self._predicted_keys.remove(key)
                return True
            return False
    
    def _predict_access(self, key: str, pattern: Dict[str, Any], current_time: float) -> bool:
        """Predict if a key will be accessed soon."""
        times = pattern["times"]
        
        if len(times) < self.min_pattern_occurrences:
            return False
        
        # Check for periodic access
        if pattern["periodicity"] is not None:
            last_access = times[-1]
            expected_next = last_access + pattern["periodicity"]
            
            # Predict if within window
            if current_time <= expected_next <= current_time + self.prediction_window:
                return True
        
        # Check for high frequency access
        if pattern["frequency"] > 1.0:  # More than once per hour
            last_access = times[-1]
            time_since_last = current_time - last_access
            
            # Predict based on frequency
            expected_interval = 3600 / pattern["frequency"]
            if time_since_last >= expected_interval * 0.8:
                return True
        
        # Check for time-based patterns (e.g., daily, hourly)
        time_pattern = self._detect_time_pattern(times)
        if time_pattern:
            return self._match_time_pattern(current_time, time_pattern)
        
        return False
    
    def _detect_time_pattern(self, times: deque) -> Optional[Dict[str, Any]]:
        """Detect time-based access patterns."""
        if len(times) < 3:
            return None
        
        # Convert to intervals
        intervals = []
        for i in range(1, len(times)):
            intervals.append(times[i] - times[i-1])
        
        if not intervals:
            return None
        
        # Check for regular intervals
        mean_interval = np.mean(intervals)
        std_interval = np.std(intervals)
        
        # Low variance suggests periodic pattern
        if std_interval < mean_interval * 0.2:
            return {
                "type": "periodic",
                "interval": mean_interval,
                "confidence": 1 - (std_interval / mean_interval)
            }
        
        # Check for daily patterns
        hours = [datetime.fromtimestamp(t).hour for t in times]
        if len(set(hours)) <= 2:  # Access at similar hours
            return {
                "type": "daily",
                "hours": list(set(hours)),
                "confidence": 0.8
            }
        
        return None
    
    def _match_time_pattern(self, current_time: float, pattern: Dict[str, Any]) -> bool:
        """Check if current time matches a detected pattern."""
        if pattern["type"] == "periodic":
            # Will be handled by periodicity check
            return False
        elif pattern["type"] == "daily":
            current_hour = datetime.fromtimestamp(current_time).hour
            # Check if we're approaching a pattern hour
            for hour in pattern["hours"]:
                time_to_hour = (hour - current_hour) % 24
                if 0 <= time_to_hour <= 1:  # Within next hour
                    return True
        
        return False
    
    def _generate_warming_metadata(self, pattern: Dict[str, Any]) -> Dict[str, Any]:
        """Generate metadata for cache warming."""
        metadata = {
            "warming_reason": "pattern_prediction",
            "frequency": pattern["frequency"],
            "confidence": self._calculate_pattern_confidence(pattern),
        }
        
        # Include common metadata from past accesses
        if pattern["metadata"]:
            common_metadata = self._extract_common_metadata(pattern["metadata"])
            metadata.update(common_metadata)
        
        return metadata
    
    def _calculate_pattern_confidence(self, pattern: Dict[str, Any]) -> float:
        """Calculate confidence score for a pattern."""
        confidence = 0.0
        
        # Factor 1: Number of observations
        obs_count = len(pattern["times"])
        confidence += min(obs_count / 10, 1.0) * 0.3
        
        # Factor 2: Frequency consistency
        if pattern["frequency"] > 0:
            confidence += min(pattern["frequency"] / 5, 1.0) * 0.3
        
        # Factor 3: Periodicity detection
        if pattern["periodicity"] is not None:
            confidence += 0.4
        
        return confidence
    
    def _extract_common_metadata(self, metadata_history: deque) -> Dict[str, Any]:
        """Extract common metadata patterns."""
        if not metadata_history:
            return {}
        
        # Find common keys
        all_keys = set()
        for meta in metadata_history:
            all_keys.update(meta.keys())
        
        common_meta = {}
        for key in all_keys:
            values = [meta.get(key) for meta in metadata_history if key in meta]
            if values:
                # Use most common value
                most_common = max(set(values), key=values.count)
                common_meta[key] = most_common
        
        return common_meta
    
    def _calculate_warming_priority(self, key: str) -> float:
        """Calculate priority for warming a key."""
        with self._lock:
            pattern = self._access_patterns.get(key, {})
            
            priority = 0.0
            
            # Frequency component
            frequency = pattern.get("frequency", 0)
            priority += frequency * 10
            
            # Recency component
            if pattern.get("times"):
                last_access = pattern["times"][-1]
                hours_since = (time.time() - last_access) / 3600
                priority += max(0, 10 - hours_since)
            
            # Pattern confidence
            confidence = self._calculate_pattern_confidence(pattern)
            priority += confidence * 20
            
            return priority
    
    def _get_ml_predictions(self, current_time: float) -> List[Tuple[str, Dict[str, Any]]]:
        """Get ML-based predictions using hardware acceleration."""
        if not self.workload_analyzer:
            return []
        
        predictions = []
        
        try:
            # Prepare features for ML model
            features = self._prepare_ml_features(current_time)
            
            # Get predictions from workload analyzer
            if features:
                # This would use actual ML model on Neural Engine
                predicted_patterns = self.workload_analyzer.analyze_workload(features)
                
                for pattern in predicted_patterns:
                    if pattern.get("predicted_key"):
                        metadata = {
                            "warming_reason": "ml_prediction",
                            "ml_confidence": pattern.get("confidence", 0.5),
                        }
                        predictions.append((pattern["predicted_key"], metadata))
        
        except Exception as e:
            logger.warning(f"ML prediction failed: {e}")
        
        return predictions
    
    def _prepare_ml_features(self, current_time: float) -> Optional[Dict[str, Any]]:
        """Prepare features for ML model."""
        with self._lock:
            if len(self._access_history) < 10:
                return None
            
            # Extract features from access history
            recent_accesses = list(self._access_history)[-100:]
            
            # Time-based features
            access_times = [a["time"] for a in recent_accesses]
            intervals = np.diff(access_times)
            
            features = {
                "current_time": current_time,
                "access_count": len(recent_accesses),
                "mean_interval": np.mean(intervals) if len(intervals) > 0 else 0,
                "std_interval": np.std(intervals) if len(intervals) > 0 else 0,
                "unique_keys": len(set(a["key"] for a in recent_accesses)),
                "hour_of_day": datetime.fromtimestamp(current_time).hour,
                "day_of_week": datetime.fromtimestamp(current_time).weekday(),
            }
            
            return features
    
    def _start_analysis_thread(self) -> None:
        """Start background thread for pattern analysis."""
        def analysis_worker():
            while not self._stop_event.is_set():
                try:
                    self._analyze_patterns()
                    self._stop_event.wait(60)  # Analyze every minute
                except Exception as e:
                    logger.error(f"Pattern analysis error: {e}")
        
        thread = threading.Thread(target=analysis_worker, daemon=True)
        thread.start()
    
    def _analyze_patterns(self) -> None:
        """Analyze access patterns for periodicity."""
        with self._lock:
            for key, pattern in self._access_patterns.items():
                times = pattern["times"]
                
                if len(times) >= 3:
                    # Detect periodicity
                    intervals = []
                    for i in range(1, len(times)):
                        intervals.append(times[i] - times[i-1])
                    
                    if intervals:
                        # Check for consistent intervals
                        mean_interval = np.mean(intervals)
                        std_interval = np.std(intervals)
                        
                        if std_interval < mean_interval * 0.15:  # 15% variance threshold
                            pattern["periodicity"] = mean_interval
                        else:
                            pattern["periodicity"] = None
    
    def __del__(self):
        """Cleanup on deletion."""
        self._stop_event.set()