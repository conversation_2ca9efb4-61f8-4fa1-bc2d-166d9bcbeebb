"""
Cache compression strategies for the hardware-accelerated cache system.

This module provides compression strategies for cache data,
optimized for hardware acceleration capabilities.
"""

import pickle
import zlib
from abc import ABC, abstractmethod
from enum import Enum
from typing import Any, Dict, Optional, Tuple

import numpy as np
import pandas as pd

try:
    import lz4.frame
    HAS_LZ4 = True
except ImportError:
    HAS_LZ4 = False

try:
    import zstandard as zstd
    HAS_ZSTD = True
except ImportError:
    HAS_ZSTD = False

from yemen_trade_diagnostic.hardware.core.detector import get_hardware_detector
from yemen_trade_diagnostic.errors import protect, OperationType, HARDWARE_CONFIG
from yemen_trade_diagnostic.interfaces.logging_interface import get_logger

logger = get_logger(__name__)


class CompressionAlgorithm(Enum):
    """Available compression algorithms."""
    NONE = "none"
    ZLIB = "zlib"
    LZ4 = "lz4"
    ZSTD = "zstd"
    NUMPY = "numpy"  # For numpy arrays
    PARQUET = "parquet"  # For DataFrames


class CompressionStrategy(ABC):
    """Base class for cache compression strategies."""
    
    @abstractmethod
    def compress(self, data: Any) -> Tuple[bytes, Dict[str, Any]]:
        """
        Compress data for cache storage.
        
        Returns:
            Tuple of (compressed_data, metadata)
        """
        pass
    
    @abstractmethod
    def decompress(self, compressed_data: bytes, metadata: Dict[str, Any]) -> Any:
        """Decompress data from cache storage."""
        pass
    
    @abstractmethod
    def estimate_compression_ratio(self, data: Any) -> float:
        """Estimate compression ratio for given data."""
        pass
    
    @abstractmethod
    def should_compress(self, data: Any, size_bytes: int) -> bool:
        """Determine if data should be compressed."""
        pass


class AdaptiveCompressionStrategy(CompressionStrategy):
    """
    Adaptive compression strategy that selects the best compression method
    based on data characteristics and hardware capabilities.
    """
    
    def __init__(self, min_size: int = 1024, compression_threshold: float = 0.9):
        """
        Initialize adaptive compression.
        
        Args:
            min_size: Minimum size in bytes to consider compression
            compression_threshold: Minimum compression ratio to keep compressed
        """
        self.min_size = min_size
        self.compression_threshold = compression_threshold
        
        # Hardware detection
        self.detector = get_hardware_detector()
        self._use_hardware_compression = (
            self.detector.should_use_hardware_acceleration()
        )
        
        # Algorithm preferences based on hardware
        self._setup_algorithm_preferences()
        
        logger.info(
            f"Initialized AdaptiveCompressionStrategy with "
            f"hardware_compression={self._use_hardware_compression}"
        )
    
    def compress(self, data: Any) -> Tuple[bytes, Dict[str, Any]]:
        """Compress data using the most suitable method."""
        # Select algorithm based on data type
        algorithm = self._select_algorithm(data)
        
        metadata = {
            "algorithm": algorithm.value,
            "original_type": type(data).__name__,
        }
        
        if algorithm == CompressionAlgorithm.NONE:
            # No compression
            return pickle.dumps(data), metadata
        
        elif algorithm == CompressionAlgorithm.NUMPY:
            # NumPy-specific compression
            compressed = self._compress_numpy(data)
            metadata["shape"] = data.shape
            metadata["dtype"] = str(data.dtype)
            return compressed, metadata
        
        elif algorithm == CompressionAlgorithm.PARQUET:
            # DataFrame-specific compression
            compressed = self._compress_dataframe(data)
            return compressed, metadata
        
        elif algorithm == CompressionAlgorithm.LZ4 and HAS_LZ4:
            # LZ4 compression
            pickled = pickle.dumps(data)
            compressed = lz4.frame.compress(pickled, compression_level=3)
            return compressed, metadata
        
        elif algorithm == CompressionAlgorithm.ZSTD and HAS_ZSTD:
            # Zstandard compression
            pickled = pickle.dumps(data)
            cctx = zstd.ZstdCompressor(level=3)
            compressed = cctx.compress(pickled)
            return compressed, metadata
        
        else:
            # Default to zlib
            pickled = pickle.dumps(data)
            level = 6 if self._use_hardware_compression else 1
            compressed = zlib.compress(pickled, level=level)
            metadata["algorithm"] = CompressionAlgorithm.ZLIB.value
            return compressed, metadata
    
    def decompress(self, compressed_data: bytes, metadata: Dict[str, Any]) -> Any:
        """Decompress data."""
        algorithm = CompressionAlgorithm(metadata.get("algorithm", "zlib"))
        
        if algorithm == CompressionAlgorithm.NONE:
            return pickle.loads(compressed_data)
        
        elif algorithm == CompressionAlgorithm.NUMPY:
            return self._decompress_numpy(compressed_data, metadata)
        
        elif algorithm == CompressionAlgorithm.PARQUET:
            return self._decompress_dataframe(compressed_data)
        
        elif algorithm == CompressionAlgorithm.LZ4 and HAS_LZ4:
            decompressed = lz4.frame.decompress(compressed_data)
            return pickle.loads(decompressed)
        
        elif algorithm == CompressionAlgorithm.ZSTD and HAS_ZSTD:
            dctx = zstd.ZstdDecompressor()
            decompressed = dctx.decompress(compressed_data)
            return pickle.loads(decompressed)
        
        else:
            # Default to zlib
            decompressed = zlib.decompress(compressed_data)
            return pickle.loads(decompressed)
    
    def estimate_compression_ratio(self, data: Any) -> float:
        """Estimate compression ratio for given data."""
        # Quick estimation based on data type
        if isinstance(data, pd.DataFrame):
            # DataFrames often compress well
            return 0.3
        elif isinstance(data, np.ndarray):
            # Depends on data type and entropy
            if data.dtype in [np.float64, np.float32]:
                return 0.5
            else:
                return 0.7
        elif isinstance(data, (list, dict)):
            # Structured data
            return 0.6
        else:
            # Unknown type
            return 0.8
    
    def should_compress(self, data: Any, size_bytes: int) -> bool:
        """Determine if data should be compressed."""
        # Skip small data
        if size_bytes < self.min_size:
            return False
        
        # Always compress large data
        if size_bytes > 100 * 1024:  # 100KB
            return True
        
        # Estimate if compression would be beneficial
        estimated_ratio = self.estimate_compression_ratio(data)
        return estimated_ratio < self.compression_threshold
    
    def _select_algorithm(self, data: Any) -> CompressionAlgorithm:
        """Select the best compression algorithm for the data."""
        if isinstance(data, np.ndarray):
            return CompressionAlgorithm.NUMPY
        elif isinstance(data, pd.DataFrame):
            return CompressionAlgorithm.PARQUET
        elif HAS_LZ4 and self._prefer_speed:
            return CompressionAlgorithm.LZ4
        elif HAS_ZSTD and self._prefer_ratio:
            return CompressionAlgorithm.ZSTD
        else:
            return CompressionAlgorithm.ZLIB
    
    def _setup_algorithm_preferences(self) -> None:
        """Setup algorithm preferences based on hardware."""
        # Prefer speed on fast hardware
        self._prefer_speed = (
            self.detector.is_apple_silicon or
            self.detector.hardware_info.get('cpu_count', 1) >= 8
        )
        
        # Prefer compression ratio on slower hardware
        self._prefer_ratio = not self._prefer_speed
    
    def _compress_numpy(self, arr: np.ndarray) -> bytes:
        """Compress numpy array efficiently."""
        # Use numpy's built-in compression
        import io
        buffer = io.BytesIO()
        
        if self._use_hardware_compression and arr.dtype in [np.float32, np.float64]:
            # For floating point, quantize if appropriate
            # This is a simplified example - real implementation would be more sophisticated
            np.savez_compressed(buffer, data=arr)
        else:
            np.savez_compressed(buffer, data=arr)
        
        return buffer.getvalue()
    
    def _decompress_numpy(self, compressed_data: bytes, metadata: Dict[str, Any]) -> np.ndarray:
        """Decompress numpy array."""
        import io
        buffer = io.BytesIO(compressed_data)
        loaded = np.load(buffer)
        return loaded['data']
    
    def _compress_dataframe(self, df: pd.DataFrame) -> bytes:
        """Compress DataFrame efficiently."""
        import io
        buffer = io.BytesIO()
        
        # Use parquet for DataFrames - it's highly efficient
        try:
            df.to_parquet(buffer, compression='snappy', engine='pyarrow')
            return buffer.getvalue()
        except:
            # Fallback to pickle
            return pickle.dumps(df)
    
    def _decompress_dataframe(self, compressed_data: bytes) -> pd.DataFrame:
        """Decompress DataFrame."""
        import io
        buffer = io.BytesIO(compressed_data)
        
        try:
            return pd.read_parquet(buffer)
        except:
            # Fallback to pickle
            buffer.seek(0)
            return pickle.loads(buffer.read())


class CompressionBenchmark:
    """Benchmark different compression strategies."""
    
    def __init__(self):
        """Initialize compression benchmark."""
        self.results = {}
    
    def benchmark(self, data: Any, strategies: Optional[Dict[str, CompressionStrategy]] = None) -> Dict[str, Dict[str, float]]:
        """
        Benchmark compression strategies on given data.
        
        Returns:
            Dict with strategy names as keys and performance metrics as values
        """
        import time
        
        if strategies is None:
            strategies = {
                "adaptive": AdaptiveCompressionStrategy(),
            }
        
        original_size = len(pickle.dumps(data))
        results = {}
        
        for name, strategy in strategies.items():
            # Compression benchmark
            start_time = time.time()
            compressed, metadata = strategy.compress(data)
            compress_time = time.time() - start_time
            
            compressed_size = len(compressed)
            
            # Decompression benchmark
            start_time = time.time()
            decompressed = strategy.decompress(compressed, metadata)
            decompress_time = time.time() - start_time
            
            # Verify correctness
            if isinstance(data, pd.DataFrame):
                correct = data.equals(decompressed)
            elif isinstance(data, np.ndarray):
                correct = np.array_equal(data, decompressed)
            else:
                correct = data == decompressed
            
            results[name] = {
                "compression_ratio": original_size / compressed_size,
                "compressed_size": compressed_size,
                "compress_time": compress_time,
                "decompress_time": decompress_time,
                "throughput_mbps": (original_size / (1024 * 1024)) / compress_time,
                "correct": correct,
            }
        
        return results


# Singleton instance
_compressor_instance = None


def get_cache_compressor(
    min_size: int = 1024,
    compression_threshold: float = 0.9
) -> AdaptiveCompressionStrategy:
    """
    Get the singleton compression strategy instance.
    
    Args:
        min_size: Minimum size in bytes to consider compression
        compression_threshold: Minimum compression ratio to keep compressed
        
    Returns:
        AdaptiveCompressionStrategy: The compression strategy instance
    """
    global _compressor_instance
    if _compressor_instance is None:
        _compressor_instance = AdaptiveCompressionStrategy(
            min_size=min_size,
            compression_threshold=compression_threshold
        )
    return _compressor_instance