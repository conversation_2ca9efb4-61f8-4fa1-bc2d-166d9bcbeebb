"""
Enhanced Workload Analyzer Module

This module provides advanced workload analysis capabilities for the Yemen Trade Diagnostic
project, with a focus on macroeconomic operations. It analyzes workload characteristics
to determine optimal execution strategies and hardware acceleration approaches.

Key features:
- Granular workload classification beyond basic types
- Analysis of operation characteristics (compute-bound, memory-bound, I/O-bound)
- Profiling capabilities to measure actual performance characteristics
- Heuristics for predicting optimal execution strategies
- Learning from past executions to improve future recommendations
"""

# Standard library imports
import functools
import logging
import os
import platform
import threading
import time
import tracemalloc
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass, field
from enum import Enum, auto
from typing import Any, Callable, Dict, List, Optional, Set, Tuple, TypeVar, Union

# Third-party imports
import numpy as np
import pandas as pd

# Try to import psutil for resource monitoring
try:
    # Third-party imports
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False

# Project imports
from yemen_trade_diagnostic.hardware.acceleration.cpu.hybrid_core import (
    WorkloadType as HybridWorkloadType,
)

# Import existing workload classifier for integration
from yemen_trade_diagnostic.hardware.acceleration.neural.workload import (
    WorkloadClassifier as NeuralWorkloadClassifier,
)
from yemen_trade_diagnostic.hardware.acceleration.neural.workload import (
    WorkloadType as NeuralWorkloadType,
)
from yemen_trade_diagnostic.hardware.acceleration.neural.workload import (
    get_workload_classifier as get_neural_workload_classifier,
)

# Import hardware detection components
from yemen_trade_diagnostic.hardware.core.detector import get_hardware_detector
from yemen_trade_diagnostic.errors import protect, OperationType, HARDWARE_CONFIG
from yemen_trade_diagnostic.hardware.core.monitor import get_performance_monitor

# Set up logging
logger = logging.getLogger(__name__)

# Type variables for generic functions
T = TypeVar('T')
R = TypeVar('R')


class WorkloadCharacteristic(Enum):
    """Detailed workload characteristics for fine-grained classification."""
    # Computation characteristics
    COMPUTE_INTENSIVE = auto()
    COMPUTE_MODERATE = auto()
    COMPUTE_LIGHT = auto()

    # Memory characteristics
    MEMORY_INTENSIVE = auto()
    MEMORY_MODERATE = auto()
    MEMORY_LIGHT = auto()

    # I/O characteristics
    IO_INTENSIVE = auto()
    IO_MODERATE = auto()
    IO_LIGHT = auto()

    # Parallelism characteristics
    HIGHLY_PARALLEL = auto()
    MODERATELY_PARALLEL = auto()
    SEQUENTIAL = auto()

    # Data size characteristics
    LARGE_DATA = auto()
    MEDIUM_DATA = auto()
    SMALL_DATA = auto()

    # Operation complexity
    COMPLEX_OPERATION = auto()
    MODERATE_COMPLEXITY = auto()
    SIMPLE_OPERATION = auto()

    # Macroeconomic-specific characteristics
    TIME_SERIES_ANALYSIS = auto()
    RATIO_CALCULATION = auto()
    TREND_ANALYSIS = auto()
    AGGREGATION = auto()
    NORMALIZATION = auto()
    
    # Growth-specific characteristics
    INDEX_CALCULATION = auto()
    GROWTH_CALCULATION = auto()
    AGGREGATE_CALCULATION = auto()
    VOLATILITY_CALCULATION = auto()
    
    # Concentration-specific characteristics
    CONCENTRATION_CALCULATION = auto()
    POINT_IN_TIME_ANALYSIS = auto()
    
    # RCA and Sophistication-specific characteristics
    MATRIX_OPERATION = auto()
    MATRIX_OPERATIONS = auto()
    GRAPH_OPERATIONS = auto()
    
    # Data-specific characteristics
    DATA_TRANSFORMATION = auto()
    DATA_VALIDATION = auto()
    STATISTICAL_ANALYSIS = auto()


class AccelerationType(Enum):
    """Types of hardware acceleration to consider."""
    NONE = auto()
    CPU_MULTICORE = auto()
    SIMD = auto()
    GPU = auto()
    NEURAL_ENGINE = auto()
    HYBRID = auto()


@dataclass
class WorkloadProfile:
    """Detailed profile of a workload's characteristics."""
    # Basic information
    name: str
    operation_type: str

    # Characteristics scores (0.0 to 1.0)
    compute_intensity: float = 0.0
    memory_intensity: float = 0.0
    io_intensity: float = 0.0
    parallelism: float = 0.0

    # Data characteristics
    data_size: int = 0
    data_dimensions: Tuple[int, ...] = field(default_factory=tuple)
    data_type: str = ""

    # Execution characteristics
    estimated_execution_time: float = 0.0
    measured_execution_time: Optional[float] = None

    # Characteristics set
    characteristics: Set[WorkloadCharacteristic] = field(default_factory=set)

    # Recommended acceleration
    recommended_acceleration: AccelerationType = AccelerationType.NONE

    # Performance history
    execution_history: List[Dict[str, Any]] = field(default_factory=list)

    def add_characteristic(self, characteristic: WorkloadCharacteristic) -> None:
        """Add a characteristic to the profile."""
        self.characteristics.add(characteristic)

    def remove_characteristic(self, characteristic: WorkloadCharacteristic) -> None:
        """Remove a characteristic from the profile."""
        if characteristic in self.characteristics:
            self.characteristics.remove(characteristic)

    def has_characteristic(self, characteristic: WorkloadCharacteristic) -> bool:
        """Check if the profile has a specific characteristic."""
        return characteristic in self.characteristics

    def update_from_execution(self, execution_metrics: Dict[str, Any]) -> None:
        """Update profile based on execution metrics."""
        if 'execution_time' in execution_metrics:
            self.measured_execution_time = execution_metrics['execution_time']

        # Add to history, keeping last 10 executions
        self.execution_history.append(execution_metrics)
        if len(self.execution_history) > 10:
            self.execution_history.pop(0)

        # Update intensity scores based on metrics
        if 'cpu_usage_percent' in execution_metrics:
            self.compute_intensity = max(self.compute_intensity,
                                        execution_metrics['cpu_usage_percent'] / 100.0)

        if 'memory_usage_mb' in execution_metrics:
            self.memory_intensity = max(self.memory_intensity,
                                       min(1.0, execution_metrics['memory_usage_mb'] / 1000.0))

        if 'io_operations' in execution_metrics:
            self.io_intensity = max(self.io_intensity,
                                   min(1.0, execution_metrics['io_operations'] / 1000.0))


class WorkloadAnalyzer:
    """
    Enhanced workload analyzer for optimal execution strategy selection.

    This class provides advanced workload analysis capabilities, building on the
    existing WorkloadClassifier but with more granular classification and
    optimization recommendations specifically tailored for macroeconomic operations.
    """

    @protect("workload_analyzer_init", OperationType.HARDWARE_ACCELERATION)
    def __init__(self, use_history: bool = True, history_size: int = 100):
        """
        Initialize the workload analyzer.

        Args:
            use_history: Whether to use historical data for analysis
            history_size: Maximum number of historical entries to keep
        """
        self.use_history = use_history
        self.history: Dict[str, List[Dict[str, Any]]] = {}
        self.history_size = history_size
        self.profiles: Dict[str, WorkloadProfile] = {}

        # Initialize hardware detector
        self.hardware_detector = get_hardware_detector()
        self.hardware_capabilities = self.hardware_detector.get_acceleration_capabilities()

        # Initialize performance monitor
        self.performance_monitor = get_performance_monitor()

        # Initialize neural workload classifier for integration
        self.neural_classifier = get_neural_workload_classifier(use_history=use_history)

        # Thread-local storage for profiling
        self._thread_local = threading.local()

        # Initialize operation type patterns
        self._initialize_operation_patterns()

    def _initialize_operation_patterns(self) -> None:
        """Initialize patterns for recognizing operation types."""
        # Macroeconomic operation patterns
        self.macroeconomic_patterns = {
            "time_series": ["time_series", "timeseries", "time-series", "temporal", "trend"],
            "ratio_calculation": ["ratio", "percentage", "proportion", "divide", "quotient"],
            "trend_analysis": ["trend", "growth", "decline", "change", "delta", "difference"],
            "aggregation": ["aggregate", "sum", "mean", "average", "total", "count"],
            "normalization": ["normalize", "standardize", "scale", "adjust", "index"]
        }

        # Trade-specific operation patterns
        self.trade_patterns = {
            "trade_balance": ["trade_balance", "trade balance", "export-import", "net_export"],
            "trade_openness": ["trade_openness", "openness", "trade_gdp_ratio", "trade_to_gdp"],
            "terms_of_trade": ["terms_of_trade", "tot", "price_ratio", "export_price", "import_price"]
        }

        # Data size patterns
        self.data_size_patterns = {
            "small": ["small", "tiny", "minimal"],
            "medium": ["medium", "moderate", "average"],
            "large": ["large", "big", "huge", "massive"]
        }

    @protect("analyze_workload_method", OperationType.HARDWARE_ACCELERATION)
    def analyze_workload(self, func: Callable, *args, **kwargs) -> WorkloadProfile:
        """
        Analyze a function's workload characteristics.

        Args:
            func: The function to analyze
            *args: Arguments to pass to the function
            **kwargs: Keyword arguments to pass to the function

        Returns:
            WorkloadProfile: Detailed profile of the workload
        """
        # Get function name and create profile key
        func_name = func.__qualname__ if hasattr(func, '__qualname__') else str(func)
        profile_key = f"{func.__module__}.{func_name}" if hasattr(func, '__module__') else func_name

        # Check if we already have a profile for this function
        if profile_key in self.profiles:
            profile = self.profiles[profile_key]
        else:
            # Create new profile
            profile = WorkloadProfile(
                name=func_name,
                operation_type=self._determine_operation_type(func)
            )

            # Analyze function characteristics
            self._analyze_function_characteristics(func, profile)

            # Store profile
            self.profiles[profile_key] = profile

        # Analyze arguments if provided
        if args or kwargs:
            self._analyze_arguments(profile, *args, **kwargs)

        # Determine recommended acceleration
        profile.recommended_acceleration = self._determine_acceleration(profile)

        return profile

    @protect("determine_operation_type", OperationType.HARDWARE_ACCELERATION)
    def _determine_operation_type(self, func: Callable) -> str:
        """
        Determine the type of operation based on function name and module.

        Args:
            func: The function to analyze

        Returns:
            str: Operation type
        """
        func_name = func.__qualname__.lower() if hasattr(func, '__qualname__') else str(func).lower()
        module_name = func.__module__.lower() if hasattr(func, '__module__') else ""

        # Check for macroeconomic operations
        for op_type, patterns in self.macroeconomic_patterns.items():
            if any(pattern in func_name or pattern in module_name for pattern in patterns):
                return op_type

        # Check for trade-specific operations
        for op_type, patterns in self.trade_patterns.items():
            if any(pattern in func_name or pattern in module_name for pattern in patterns):
                return op_type

        # Default to generic operation type
        if "calculate" in func_name or "compute" in func_name:
            return "computation"
        elif "load" in func_name or "read" in func_name or "import" in func_name:
            return "data_loading"
        elif "transform" in func_name or "process" in func_name:
            return "data_processing"
        elif "visualize" in func_name or "plot" in func_name or "chart" in func_name:
            return "visualization"
        else:
            return "unknown"

    @protect("analyze_function_characteristics", OperationType.HARDWARE_ACCELERATION)
    def _analyze_function_characteristics(self, func: Callable, profile: WorkloadProfile) -> None:
        """
        Analyze function characteristics based on name, module, and docstring.

        Args:
            func: The function to analyze
            profile: The workload profile to update
        """
        func_name = func.__qualname__.lower() if hasattr(func, '__qualname__') else str(func).lower()
        module_name = func.__module__.lower() if hasattr(func, '__module__') else ""
        docstring = func.__doc__.lower() if hasattr(func, '__doc__') and func.__doc__ else ""

        # Check for computation intensity
        if any(x in func_name or x in docstring for x in ["calculate", "compute", "process", "analyze"]):
            profile.add_characteristic(WorkloadCharacteristic.COMPUTE_INTENSIVE)
            profile.compute_intensity = 0.8

        # Check for memory intensity
        if any(x in func_name or x in docstring for x in ["dataframe", "array", "matrix", "large", "memory"]):
            profile.add_characteristic(WorkloadCharacteristic.MEMORY_INTENSIVE)
            profile.memory_intensity = 0.8

        # Check for I/O intensity
        if any(x in func_name or x in docstring for x in ["load", "save", "read", "write", "file", "io"]):
            profile.add_characteristic(WorkloadCharacteristic.IO_INTENSIVE)
            profile.io_intensity = 0.8

        # Check for parallelism
        if any(x in func_name or x in docstring for x in ["parallel", "concurrent", "multi", "thread"]):
            profile.add_characteristic(WorkloadCharacteristic.HIGHLY_PARALLEL)
            profile.parallelism = 0.9

        # Check for macroeconomic-specific characteristics
        if profile.operation_type == "time_series":
            profile.add_characteristic(WorkloadCharacteristic.TIME_SERIES_ANALYSIS)
        elif profile.operation_type == "ratio_calculation":
            profile.add_characteristic(WorkloadCharacteristic.RATIO_CALCULATION)
        elif profile.operation_type == "trend_analysis":
            profile.add_characteristic(WorkloadCharacteristic.TREND_ANALYSIS)
        elif profile.operation_type == "aggregation":
            profile.add_characteristic(WorkloadCharacteristic.AGGREGATION)
        elif profile.operation_type == "normalization":
            profile.add_characteristic(WorkloadCharacteristic.NORMALIZATION)

        # Check for trade-specific characteristics
        if "trade_balance" in func_name or "trade_balance" in module_name:
            profile.add_characteristic(WorkloadCharacteristic.RATIO_CALCULATION)
            profile.add_characteristic(WorkloadCharacteristic.TIME_SERIES_ANALYSIS)
        elif "trade_openness" in func_name or "trade_openness" in module_name:
            profile.add_characteristic(WorkloadCharacteristic.RATIO_CALCULATION)
            profile.add_characteristic(WorkloadCharacteristic.NORMALIZATION)
        elif "terms_of_trade" in func_name or "terms_of_trade" in module_name:
            profile.add_characteristic(WorkloadCharacteristic.RATIO_CALCULATION)
            profile.add_characteristic(WorkloadCharacteristic.TIME_SERIES_ANALYSIS)

    @protect("analyze_arguments", OperationType.HARDWARE_ACCELERATION)
    def _analyze_arguments(self, profile: WorkloadProfile, *args, **kwargs) -> None:
        """
        Analyze function arguments to determine data characteristics.

        Args:
            profile: The workload profile to update
            *args: Function arguments
            **kwargs: Function keyword arguments
        """
        # Analyze numpy arrays
        numpy_arrays = [arg for arg in args if isinstance(arg, np.ndarray)]
        for array in numpy_arrays:
            self._analyze_numpy_array(profile, array)

        # Analyze pandas DataFrames
        dataframes = [arg for arg in args if isinstance(arg, pd.DataFrame)]
        for df in dataframes:
            self._analyze_dataframe(profile, df)

        # Analyze keyword arguments
        for key, value in kwargs.items():
            if isinstance(value, np.ndarray):
                self._analyze_numpy_array(profile, value)
            elif isinstance(value, pd.DataFrame):
                self._analyze_dataframe(profile, value)

    @protect("analyze_numpy_array", OperationType.HARDWARE_ACCELERATION)
    def _analyze_numpy_array(self, profile: WorkloadProfile, array: np.ndarray) -> None:
        """
        Analyze a numpy array to determine data characteristics.

        Args:
            profile: The workload profile to update
            array: The numpy array to analyze
        """
        # Update data size
        profile.data_size = max(profile.data_size, array.size)
        profile.data_dimensions = array.shape
        profile.data_type = str(array.dtype)

        # Determine data size characteristic
        if array.size < 10000:  # Small: < 10K elements
            profile.add_characteristic(WorkloadCharacteristic.SMALL_DATA)
        elif array.size < 1000000:  # Medium: 10K - 1M elements
            profile.add_characteristic(WorkloadCharacteristic.MEDIUM_DATA)
        else:  # Large: > 1M elements
            profile.add_characteristic(WorkloadCharacteristic.LARGE_DATA)

        # Determine memory intensity based on size
        profile.memory_intensity = max(profile.memory_intensity, min(1.0, array.nbytes / (100 * 1024 * 1024)))

        # Determine parallelism potential based on shape
        if len(array.shape) >= 2 and min(array.shape) > 100:
            profile.add_characteristic(WorkloadCharacteristic.HIGHLY_PARALLEL)
            profile.parallelism = 0.9
        elif array.size > 10000:
            profile.add_characteristic(WorkloadCharacteristic.MODERATELY_PARALLEL)
            profile.parallelism = 0.6

    @protect("analyze_dataframe", OperationType.HARDWARE_ACCELERATION)
    def _analyze_dataframe(self, profile: WorkloadProfile, df: pd.DataFrame) -> None:
        """
        Analyze a pandas DataFrame to determine data characteristics.

        Args:
            profile: The workload profile to update
            df: The DataFrame to analyze
        """
        # Update data size
        profile.data_size = max(profile.data_size, df.size)
        profile.data_dimensions = (df.shape[0], df.shape[1])

        # Determine data size characteristic
        if df.size < 10000:  # Small: < 10K elements
            profile.add_characteristic(WorkloadCharacteristic.SMALL_DATA)
        elif df.size < 1000000:  # Medium: 10K - 1M elements
            profile.add_characteristic(WorkloadCharacteristic.MEDIUM_DATA)
        else:  # Large: > 1M elements
            profile.add_characteristic(WorkloadCharacteristic.LARGE_DATA)

        # Determine memory intensity based on estimated memory usage
        estimated_memory_mb = df.memory_usage(deep=True).sum() / (1024 * 1024)
        profile.memory_intensity = max(profile.memory_intensity, min(1.0, estimated_memory_mb / 100))

        # Determine parallelism potential based on shape
        if df.shape[0] > 10000:
            profile.add_characteristic(WorkloadCharacteristic.HIGHLY_PARALLEL)
            profile.parallelism = 0.9
        elif df.shape[0] > 1000:
            profile.add_characteristic(WorkloadCharacteristic.MODERATELY_PARALLEL)
            profile.parallelism = 0.6

    @protect("determine_acceleration", OperationType.HARDWARE_ACCELERATION)
    def _determine_acceleration(self, profile: WorkloadProfile) -> AccelerationType:
        """
        Determine the optimal acceleration type for a workload profile.

        Args:
            profile: The workload profile to analyze

        Returns:
            AccelerationType: Recommended acceleration type
        """
        # Check hardware capabilities
        has_neural_engine = self.hardware_capabilities.get('neural_engine', False)
        has_gpu = self.hardware_capabilities.get('metal', False)
        has_simd = self.hardware_capabilities.get('simd', True)  # Assume SIMD is available on most CPUs

        # Neural Engine is best for large matrix operations and specific patterns
        if has_neural_engine and (
            profile.has_characteristic(WorkloadCharacteristic.COMPUTE_INTENSIVE) and
            profile.has_characteristic(WorkloadCharacteristic.LARGE_DATA) and
            profile.operation_type in ["time_series", "trend_analysis", "normalization"]
        ):
            return AccelerationType.NEURAL_ENGINE

        # GPU is good for highly parallel operations with large data
        if has_gpu and (
            profile.has_characteristic(WorkloadCharacteristic.HIGHLY_PARALLEL) and
            (profile.has_characteristic(WorkloadCharacteristic.LARGE_DATA) or
             profile.has_characteristic(WorkloadCharacteristic.MEDIUM_DATA))
        ):
            return AccelerationType.GPU

        # SIMD is good for vectorized operations
        if has_simd and (
            profile.has_characteristic(WorkloadCharacteristic.COMPUTE_INTENSIVE) or
            profile.has_characteristic(WorkloadCharacteristic.MODERATELY_PARALLEL)
        ):
            return AccelerationType.SIMD

        # Multi-core CPU is good for parallel operations
        if profile.has_characteristic(WorkloadCharacteristic.HIGHLY_PARALLEL) or \
           profile.has_characteristic(WorkloadCharacteristic.MODERATELY_PARALLEL):
            return AccelerationType.CPU_MULTICORE

        # Hybrid approach for mixed workloads
        if (profile.compute_intensity > 0.3 and profile.memory_intensity > 0.3) or \
           (profile.has_characteristic(WorkloadCharacteristic.COMPUTE_INTENSIVE) and
            profile.has_characteristic(WorkloadCharacteristic.MEMORY_INTENSIVE)):
            return AccelerationType.HYBRID

        # Default to no special acceleration
        return AccelerationType.NONE

    @protect("profile_execution_method", OperationType.HARDWARE_ACCELERATION)
    def profile_execution(self, func: Callable, *args, **kwargs) -> Tuple[Any, WorkloadProfile]:
        """
        Profile the execution of a function and update its workload profile.

        Args:
            func: The function to profile
            *args: Arguments to pass to the function
            **kwargs: Keyword arguments to pass to the function

        Returns:
            Tuple of function result and updated workload profile
        """
        # Get or create profile
        func_name = func.__qualname__ if hasattr(func, '__qualname__') else str(func)
        profile_key = f"{func.__module__}.{func_name}" if hasattr(func, '__module__') else func_name

        if profile_key in self.profiles:
            profile = self.profiles[profile_key]
        else:
            profile = self.analyze_workload(func, *args, **kwargs)

        # Start performance monitoring
        with self.performance_monitor.track(profile_key):
            # Start resource monitoring if psutil is available
            if PSUTIL_AVAILABLE:
                process = psutil.Process()
                start_cpu = process.cpu_percent()
                start_memory = process.memory_info().rss / (1024 * 1024)  # MB
                start_io = process.io_counters() if hasattr(process, 'io_counters') else None
                start_time = time.time()

            # Execute function
            result = func(*args, **kwargs)

            # Collect resource usage if psutil is available
            if PSUTIL_AVAILABLE:
                end_time = time.time()
                end_cpu = process.cpu_percent()
                end_memory = process.memory_info().rss / (1024 * 1024)  # MB
                end_io = process.io_counters() if hasattr(process, 'io_counters') else None

                # Calculate metrics
                execution_time = end_time - start_time
                cpu_usage = end_cpu
                memory_usage = end_memory - start_memory

                # Calculate I/O metrics if available
                io_operations = 0
                if start_io and end_io:
                    io_operations = (
                        (end_io.read_count - start_io.read_count) +
                        (end_io.write_count - start_io.write_count)
                    )

                # Update profile with execution metrics
                profile.update_from_execution({
                    'execution_time': execution_time,
                    'cpu_usage_percent': cpu_usage,
                    'memory_usage_mb': memory_usage,
                    'io_operations': io_operations
                })

        # Get performance metrics from monitor
        metrics = self.performance_monitor.get_metrics(profile_key)
        if metrics:
            profile.measured_execution_time = metrics.execution_time

        # Update profile in storage
        self.profiles[profile_key] = profile

        return result, profile

    @protect("get_optimal_execution_strategy", OperationType.HARDWARE_ACCELERATION)
    def get_optimal_execution_strategy(self, profile: WorkloadProfile) -> Dict[str, Any]:
        """
        Get the optimal execution strategy for a workload profile.

        Args:
            profile: The workload profile to analyze

        Returns:
            Dict[str, Any]: Execution strategy parameters
        """
        # Determine acceleration type if not already set
        if profile.recommended_acceleration == AccelerationType.NONE:
            profile.recommended_acceleration = self._determine_acceleration(profile)

        # Base strategy
        strategy = {
            'acceleration_type': profile.recommended_acceleration,
            'parallelism': profile.parallelism > 0.5,
            'batch_size': self._determine_optimal_batch_size(profile),
            'memory_optimization': profile.memory_intensity > 0.5,
            'io_optimization': profile.io_intensity > 0.5
        }

        # Add specific parameters based on acceleration type
        if profile.recommended_acceleration == AccelerationType.NEURAL_ENGINE:
            strategy.update({
                'use_neural_engine': True,
                'precision': 'mixed',  # Use mixed precision for Neural Engine
                'batch_processing': True
            })
        elif profile.recommended_acceleration == AccelerationType.GPU:
            strategy.update({
                'use_gpu': True,
                'precision': 'float32',
                'use_shared_memory': True
            })
        elif profile.recommended_acceleration == AccelerationType.SIMD:
            strategy.update({
                'use_simd': True,
                'vector_width': 'auto',
                'unroll_factor': 4
            })
        elif profile.recommended_acceleration == AccelerationType.CPU_MULTICORE:
            strategy.update({
                'use_multicore': True,
                'thread_count': 'auto',
                'work_stealing': True
            })
        elif profile.recommended_acceleration == AccelerationType.HYBRID:
            strategy.update({
                'use_hybrid': True,
                'cpu_gpu_ratio': 0.7,  # 70% CPU, 30% GPU
                'adaptive_scheduling': True
            })

        # Add macroeconomic-specific optimizations
        if profile.has_characteristic(WorkloadCharacteristic.TIME_SERIES_ANALYSIS):
            strategy.update({
                'time_series_optimization': True,
                'window_optimization': True
            })

        if profile.has_characteristic(WorkloadCharacteristic.RATIO_CALCULATION):
            strategy.update({
                'ratio_optimization': True,
                'division_optimization': True
            })

        return strategy

    @protect("determine_optimal_batch_size", OperationType.HARDWARE_ACCELERATION)
    def _determine_optimal_batch_size(self, profile: WorkloadProfile) -> int:
        """
        Determine the optimal batch size for processing based on workload profile.

        Args:
            profile: The workload profile to analyze

        Returns:
            int: Optimal batch size
        """
        # Default batch size
        batch_size = 1000

        # Adjust based on data size
        if profile.has_characteristic(WorkloadCharacteristic.SMALL_DATA):
            batch_size = 100
        elif profile.has_characteristic(WorkloadCharacteristic.LARGE_DATA):
            batch_size = 10000

        # Adjust based on memory intensity
        if profile.memory_intensity > 0.8:
            batch_size = max(100, batch_size // 2)  # Reduce batch size for memory-intensive operations

        # Adjust based on compute intensity
        if profile.compute_intensity > 0.8:
            batch_size = max(100, batch_size // 2)  # Reduce batch size for compute-intensive operations

        # Adjust based on parallelism
        if profile.parallelism > 0.8:
            batch_size = min(100000, batch_size * 2)  # Increase batch size for highly parallel operations

        return batch_size


# Singleton instance
_workload_analyzer = None

@protect("get_workload_analyzer_instance", OperationType.HARDWARE_ACCELERATION)
def get_workload_analyzer(use_history: bool = True) -> WorkloadAnalyzer:
    """
    Get or create the singleton WorkloadAnalyzer instance.

    Args:
        use_history: Whether to use historical data for analysis

    Returns:
        WorkloadAnalyzer instance
    """
    global _workload_analyzer
    if _workload_analyzer is None:
        _workload_analyzer = WorkloadAnalyzer(use_history=use_history)
    return _workload_analyzer


def analyze_workload(func: Callable) -> Callable:
    """
    Decorator to analyze a function's workload characteristics.

    Args:
        func: The function to analyze

    Returns:
        Decorated function
    """
    @functools.wraps(func)
    @protect("analyze_workload_decorator", OperationType.HARDWARE_ACCELERATION)
    def wrapper(*args, **kwargs):
        # Get workload analyzer
        analyzer = get_workload_analyzer()

        # Analyze workload
        profile = analyzer.analyze_workload(func, *args, **kwargs)

        # Store profile on function for future reference
        func._workload_profile = profile

        # Execute function normally
        return func(*args, **kwargs)

    return wrapper


def profile_execution(func: Callable) -> Callable:
    """
    Decorator to profile a function's execution and update its workload profile.

    Args:
        func: The function to profile

    Returns:
        Decorated function
    """
    @functools.wraps(func)
    @protect("profile_execution_decorator", OperationType.HARDWARE_ACCELERATION)
    def wrapper(*args, **kwargs):
        # Get workload analyzer
        analyzer = get_workload_analyzer()

        # Profile execution
        result, profile = analyzer.profile_execution(func, *args, **kwargs)

        # Store profile on function for future reference
        func._workload_profile = profile

        return result

    return wrapper


def optimize_execution(func: Callable) -> Callable:
    """
    Decorator to optimize a function's execution based on its workload profile.

    Args:
        func: The function to optimize

    Returns:
        Decorated function
    """
    @functools.wraps(func)
    @protect("optimize_execution_decorator", OperationType.HARDWARE_ACCELERATION)
    def wrapper(*args, **kwargs):
        # Get workload analyzer
        analyzer = get_workload_analyzer()

        # Analyze workload if not already analyzed
        if not hasattr(func, '_workload_profile'):
            profile = analyzer.analyze_workload(func, *args, **kwargs)
            func._workload_profile = profile
        else:
            profile = func._workload_profile

        # Get optimal execution strategy
        strategy = analyzer.get_optimal_execution_strategy(profile)

        # Apply strategy to execution
        if strategy['acceleration_type'] == AccelerationType.NEURAL_ENGINE:
            # Try to use Neural Engine
            try:
                # Project imports
                from yemen_trade_diagnostic.hardware.acceleration.neural.workload import (
                    optimize_for_neural_engine,
                )
                optimized_func = optimize_for_neural_engine()(func)
                return optimized_func(*args, **kwargs)
            except (ImportError, Exception) as e:
                logger.warning(f"Failed to use Neural Engine acceleration: {e}")

        elif strategy['acceleration_type'] == AccelerationType.GPU:
            # Try to use GPU
            try:
                # Project imports
                from yemen_trade_diagnostic.hardware.acceleration.metal.integrated import (
                    get_integrated_metal_accelerator,
                )
                accelerator = get_integrated_metal_accelerator()

                # Check if we have a DataFrame as first argument
                if args and isinstance(args[0], pd.DataFrame):
                    df = args[0]
                    other_args = args[1:]

                    # Use GPU acceleration for DataFrame operation
                    return accelerator.optimize_dataframe_operation(
                        df, lambda df: func(df, *other_args, **kwargs)
                    )
                else:
                    # Standard execution
                    return func(*args, **kwargs)
            except (ImportError, Exception) as e:
                logger.warning(f"Failed to use GPU acceleration: {e}")

        elif strategy['acceleration_type'] == AccelerationType.SIMD:
            # Try to use SIMD
            try:
                # Project imports
                from yemen_trade_diagnostic.hardware.acceleration.cpu.simd import (
                    get_simd_accelerator,
                )
                accelerator = get_simd_accelerator()

                # Check if we have a numpy array as first argument
                if args and isinstance(args[0], np.ndarray):
                    array = args[0]
                    other_args = args[1:]

                    # Use SIMD acceleration for array operation
                    return accelerator.accelerate_vector_operation(
                        lambda x: func(x, *other_args, **kwargs), array
                    )
                else:
                    # Standard execution
                    return func(*args, **kwargs)
            except (ImportError, Exception) as e:
                logger.warning(f"Failed to use SIMD acceleration: {e}")

        elif strategy['acceleration_type'] == AccelerationType.CPU_MULTICORE:
            # Try to use multicore
            try:
                # Project imports
                from yemen_trade_diagnostic.hardware.acceleration.cpu.hybrid_core import (
                    get_hybrid_core_scheduler,
                )
                scheduler = get_hybrid_core_scheduler()

                # Submit task to scheduler
                task_id = scheduler.submit(
                    func, args=args, kwargs=kwargs,
                    workload_type=_convert_to_hybrid_workload_type(profile)
                )

                # Wait for result
                return scheduler.get_result(task_id)
            except (ImportError, Exception) as e:
                logger.warning(f"Failed to use multicore acceleration: {e}")

        # Fall back to standard execution
        return func(*args, **kwargs)

    return wrapper

@protect("convert_to_hybrid_workload_type", OperationType.HARDWARE_ACCELERATION)
def _convert_to_hybrid_workload_type(profile: WorkloadProfile) -> HybridWorkloadType:
    """
    Convert a workload profile to a HybridCoreScheduler WorkloadType.

    Args:
        profile: The workload profile to convert

    Returns:
        HybridWorkloadType: HybridCoreScheduler workload type
    """
    # Project imports
    from yemen_trade_diagnostic.hardware.acceleration.cpu.hybrid_core import (
        WorkloadType as HybridWorkloadType,
    )

    if profile.compute_intensity > 0.7:
        return HybridWorkloadType.COMPUTE_INTENSIVE
    elif profile.memory_intensity > 0.7:
        return HybridWorkloadType.MEMORY_INTENSIVE
    elif profile.io_intensity > 0.7:
        return HybridWorkloadType.IO_INTENSIVE
    else:
        return HybridWorkloadType.MIXED
