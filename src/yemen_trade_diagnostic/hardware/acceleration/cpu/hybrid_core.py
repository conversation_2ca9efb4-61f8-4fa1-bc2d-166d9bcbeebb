"""
Hybrid Core Scheduler Module

This module provides intelligent task scheduling for hybrid architectures
like Apple's M-series chips, which combine high-performance cores
with energy-efficient cores for optimal workload distribution.
"""

# Standard library imports
import concurrent.futures
import logging
import os
import platform
import queue
import subprocess
import threading
import time
from dataclasses import dataclass, field
from enum import Enum, auto
from typing import Any, Callable, Dict, List, Optional, Set, Tuple, TypeVar

# Third-party imports
import numpy as np
import pandas as pd

# Project imports
from yemen_trade_diagnostic.errors import protect, OperationType

logger = logging.getLogger(__name__)

T = TypeVar('T')
R = TypeVar('R')


class CoreType(Enum):
    """Core types for hybrid architectures."""
    PERFORMANCE = 1
    EFFICIENCY = 2
    UNKNOWN = 3


class WorkloadType(Enum):
    """Types of computational workloads."""
    COMPUTE_INTENSIVE = 1  # CPU-heavy computation, best on performance cores
    MEMORY_INTENSIVE = 2   # Memory-heavy operations, can use either core type
    IO_INTENSIVE = 3       # I/O bound operations, best on efficiency cores
    MIXED = 4              # Mixed workload, can be balanced across core types
    UNKNOWN = 5            # Unknown type, will be analyzed


class EnhancedWorkloadType(Enum):
    """Enhanced workload types with more granular classification."""
    COMPUTE_INTENSIVE_HIGH = auto()  # Extremely compute-intensive (e.g., matrix operations)
    COMPUTE_INTENSIVE = auto()       # Standard compute-intensive operations
    COMPUTE_INTENSIVE_LOW = auto()   # Moderately compute-intensive
    MEMORY_INTENSIVE_HIGH = auto()   # Very memory-intensive (e.g., large dataframes)
    MEMORY_INTENSIVE = auto()        # Standard memory-intensive operations
    MEMORY_INTENSIVE_LOW = auto()    # Moderately memory-intensive
    IO_INTENSIVE_HIGH = auto()       # Very I/O-intensive (e.g., large file operations)
    IO_INTENSIVE = auto()            # Standard I/O-intensive operations
    IO_INTENSIVE_LOW = auto()        # Moderately I/O-intensive
    MIXED = auto()                   # Mixed workload
    UNKNOWN = auto()                 # Unknown type


class M3ProVariant(Enum):
    """M3 Pro chip variants."""
    M3_PRO_12_CORE = auto()  # 12-core M3 Pro (6P+6E, 14-core GPU)
    M3_PRO_14_CORE = auto()  # 14-core M3 Pro (6P+4E, 16-core GPU)
    M3_PRO_16_CORE = auto()  # 16-core M3 Pro (6P+6E, 18-core GPU)
    M3_PRO_UNKNOWN = auto()  # Unknown M3 Pro variant


class TaskStatus(Enum):
    """Status of a scheduled task."""
    PENDING = 1
    RUNNING = 2
    COMPLETED = 3
    FAILED = 4
    CANCELLED = 5


@dataclass
class Task:
    """Task for hybrid core scheduling."""
    id: str
    function: Callable
    args: tuple = field(default_factory=tuple)
    kwargs: dict = field(default_factory=dict)
    workload_type: WorkloadType = WorkloadType.UNKNOWN
    priority: int = 0  # Higher number = higher priority
    preferred_core_type: Optional[CoreType] = None
    status: TaskStatus = TaskStatus.PENDING
    result: Any = None
    error: Optional[Exception] = None
    execution_time: float = 0.0
    start_time: Optional[float] = None
    end_time: Optional[float] = None


class SchedulerError(Exception):
    """Exception raised for scheduler errors."""
    pass


class CoreAllocationError(Exception):
    """Exception raised for core allocation errors."""
    pass


class HybridCoreScheduler:
    """
    Hybrid Core Scheduler for optimal workload distribution on Apple Silicon.

    This class intelligently distributes computational tasks between performance
    and efficiency cores to maximize throughput and optimize energy efficiency.
    It's particularly optimized for Apple M-series chips, with special 
    enhancements for M3 Pro.
    """

    @protect("__init__", OperationType.HARDWARE_ACCELERATION)
    def __init__(self, max_workers: Optional[int] = None):
        """
        Initialize the hybrid core scheduler.

        Args:
            max_workers: Maximum number of worker threads/processes (optional)
        """
        # Task management
        self.tasks = {}
        self._task_queue_performance = queue.PriorityQueue()
        self._task_queue_efficiency = queue.PriorityQueue()
        self._futures = {}
        self._task_counter = 0
        self._lock = threading.RLock()

        # Hardware detection
        self._detect_hardware()
        
        # Enhanced M3 Pro detection and configuration
        self.m3_pro_variant = None
        self.optimal_thread_count = self._calculate_optimal_thread_count(max_workers)
        
        # Calculate worker pool sizes
        if max_workers is None:
            max_workers = self.optimal_thread_count
        self._max_workers = max_workers

        # Initialize thread pools
        self._initialize_thread_pools()
        
        # Now that thread pools are initialized, detect M3 Pro variant if applicable
        if self.is_m3_pro:
            self.m3_pro_variant = self._detect_m3_pro_variant()
        
        # Initialize performance metrics
        self._initialize_metrics()
        
        # Enhanced workload classification
        self._workload_classifiers = self._initialize_workload_classifiers()
        
        # Trade analysis specific optimizations for M3 Pro
        if self.is_m3_pro:
            self._trade_analysis_patterns = self._initialize_trade_analysis_patterns()
            
        logger.info(f"Hybrid Core Scheduler initialized with {self._max_workers} workers")
        logger.info(f"Performance cores: {self.performance_cores}, Efficiency cores: {self.efficiency_cores}")
        logger.info(f"Is Apple Silicon: {self.is_apple_silicon}, Is M3 Pro: {self.is_m3_pro}")
        if self.is_m3_pro and self.m3_pro_variant:
            logger.info(f"M3 Pro variant: {self.m3_pro_variant.name}")

    @protect("_detect_hardware", OperationType.HARDWARE_ACCELERATION)
    def _detect_hardware(self) -> None:
        """Detect hardware capabilities and core configuration."""
        # Check for Apple Silicon
        self.is_apple_silicon = platform.processor() == 'arm' or 'apple' in platform.processor().lower()
        self.is_arm = platform.machine().startswith('arm') or self.is_apple_silicon
        self.is_x86 = platform.machine() == 'x86_64' or platform.machine() == 'i386'
        
        # Determine if we're on a hybrid architecture
        self.is_hybrid_architecture = self.is_apple_silicon  # Currently only Apple Silicon is detected as hybrid
        
        # Core counts, will be refined in more detailed detection
        self.total_cores = os.cpu_count() or 4
        self.performance_cores = 0
        self.efficiency_cores = 0
        
        # M3 Pro detection flags
        self.is_m3_pro = False
        self.m3_pro_variant_str = None
        
        # If Apple Silicon, get more detailed core information
        if self.is_apple_silicon:
            try:
                # Try to get the model name
                model_result = subprocess.run(
                    ["sysctl", "hw.model"],
                    capture_output=True, text=True, check=False
                )
                
                if model_result.returncode == 0:
                    model_info = model_result.stdout.lower()
                    
                    # Check if it's an M3 Pro chip
                    if "mac" in model_info and "pro" in model_info and "m3" in model_info:
                        self.is_m3_pro = True
                        self.m3_pro_variant_str = model_info
                
                # Get information about cores
                if self.is_hybrid_architecture:
                    if self.is_m3_pro:
                        # Based on known M3 Pro configurations
                        if self.total_cores == 12:
                            # Likely 6P+6E
                            self.performance_cores = 6
                            self.efficiency_cores = 6
                        elif self.total_cores == 11:
                            # Likely 5P+6E
                            self.performance_cores = 5
                            self.efficiency_cores = 6
                        elif self.total_cores == 14:
                            # Likely 10P+4E
                            self.performance_cores = 10
                            self.efficiency_cores = 4
                        else:
                            # Default assumption for unknown configuration
                            self.performance_cores = self.total_cores // 2
                            self.efficiency_cores = self.total_cores - self.performance_cores
                    else:
                        # Generic Apple Silicon
                        # Typically a 50/50 split on base models, heavier on performance for Pro/Max/Ultra
                        if "max" in model_info or "ultra" in model_info:
                            # Higher-end chips tend to have more performance cores
                            self.performance_cores = (self.total_cores * 2) // 3
                            self.efficiency_cores = self.total_cores - self.performance_cores
                        else:
                            # Base models tend to have 50/50 or slight preference to efficiency cores
                            self.performance_cores = self.total_cores // 2
                            self.efficiency_cores = self.total_cores - self.performance_cores
                else:
                    # For non-hybrid architectures, all cores are performance cores
                    self.performance_cores = self.total_cores
                    self.efficiency_cores = 0
            
            except Exception as e:
                logger.warning(f"Error during hardware detection: {e}")
                # Fallback to simple allocation
                if self.is_hybrid_architecture:
                    self.performance_cores = self.total_cores // 2
                    self.efficiency_cores = self.total_cores - self.performance_cores
                else:
                    self.performance_cores = self.total_cores
                    self.efficiency_cores = 0
        else:
            # For non-Apple CPUs, treat all cores as performance cores
            self.performance_cores = self.total_cores
            self.efficiency_cores = 0

    @protect("_calculate_optimal_thread_count", OperationType.HARDWARE_ACCELERATION)
    def _calculate_optimal_thread_count(self, max_workers: Optional[int] = None) -> int:
        """
        Calculate the optimal thread count based on hardware capabilities.

        This method optimizes thread count for M3 Pro variants, taking into account
        the specific core configuration and memory bandwidth characteristics.

        Args:
            max_workers: Maximum number of worker threads/processes (optional)

        Returns:
            int: Optimal thread count
        """
        # If max_workers is specified, use it as an upper bound
        if max_workers is not None:
            logger.info(f"Using user-specified max_workers: {max_workers}")
            return max_workers

        # For M3 Pro, optimize based on detected core configuration
        if self.is_m3_pro:
            # Use P cores and half of E cores for optimal performance
            optimal_count = self.performance_cores + (self.efficiency_cores // 2)
            logger.info(f"Optimal thread count for M3 Pro: {optimal_count}")
            return optimal_count

        # For other hardware, use a generic formula
        # Use 75% of total cores to avoid oversubscription
        optimal_count = max(1, int(self.total_cores * 0.75))
        logger.info(f"Optimal thread count for generic hardware: {optimal_count}")
        return optimal_count

    @protect("_detect_m3_pro_variant", OperationType.HARDWARE_ACCELERATION)
    def _detect_m3_pro_variant(self) -> M3ProVariant:
        """
        Detect specific M3 Pro variant with enhanced detection logic.

        Returns:
            M3ProVariant: Detected M3 Pro variant
        """
        if not self.is_m3_pro:
            return M3ProVariant.M3_PRO_UNKNOWN

        # Enhanced detection based on core counts
        logger.info(f"Detecting M3 Pro variant based on core counts: P={self.performance_cores}, E={self.efficiency_cores}")

        if self.performance_cores == 12 and self.efficiency_cores == 4:
            # 16-core M3 Pro (12P+4E)
            logger.info("Detected 16-core M3 Pro (12P+4E)")
            return M3ProVariant.M3_PRO_16_CORE
        elif self.performance_cores == 10 and self.efficiency_cores == 4:
            # 14-core M3 Pro (10P+4E)
            logger.info("Detected 14-core M3 Pro (10P+4E)")
            return M3ProVariant.M3_PRO_14_CORE
        elif self.performance_cores == 6 and self.efficiency_cores == 6:
            # 12-core M3 Pro (6P+6E)
            logger.info("Detected 12-core M3 Pro (6P+6E)")
            return M3ProVariant.M3_PRO_12_CORE
        elif self.performance_cores == 8 and self.efficiency_cores == 4:
            # 12-core M3 Pro (8P+4E)
            logger.info("Detected 12-core M3 Pro (8P+4E)")
            return M3ProVariant.M3_PRO_12_CORE
        elif self.performance_cores == 5 and self.efficiency_cores == 6:
            # 11-core M3 Pro (5P+6E)
            logger.info("Detected 11-core M3 Pro (5P+6E), mapping to 12-core variant")
            return M3ProVariant.M3_PRO_12_CORE

        # Try to detect using GPU core count
        try:
            gpu_cores = 0

            # Try to get GPU information using system_profiler
            result = subprocess.run(
                ["system_profiler", "SPDisplaysDataType"],
                capture_output=True, text=True, check=False
            )

            if result.returncode == 0:
                output = result.stdout.lower()

                # Look for GPU core count
                # Standard library imports
                import re
                gpu_match = re.search(r'total number of cores:\s*(\d+)', output)
                if gpu_match:
                    gpu_cores = int(gpu_match.group(1))
                    logger.info(f"Detected GPU cores from system_profiler: {gpu_cores}")
                else:
                    # Alternative pattern
                    gpu_match = re.search(r'(\d+)[- ]core gpu', output)
                    if gpu_match:
                        gpu_cores = int(gpu_match.group(1))
                        logger.info(f"Detected GPU cores from system_profiler (alt pattern): {gpu_cores}")

            # Determine variant based on GPU cores
            if gpu_cores >= 18:
                logger.info(f"Detected 16-core M3 Pro based on {gpu_cores}-core GPU")
                return M3ProVariant.M3_PRO_16_CORE
            elif gpu_cores >= 16:
                logger.info(f"Detected 14-core M3 Pro based on {gpu_cores}-core GPU")
                return M3ProVariant.M3_PRO_14_CORE
            elif gpu_cores >= 14:
                logger.info(f"Detected 12-core M3 Pro based on {gpu_cores}-core GPU")
                return M3ProVariant.M3_PRO_12_CORE

        except Exception as e:
            logger.warning(f"Error during enhanced M3 Pro variant detection using GPU cores: {e}")

        # Default to 12-core if we couldn't determine the variant
        logger.warning("Could not determine specific M3 Pro variant, defaulting to 12-CORE.")
        return M3ProVariant.M3_PRO_12_CORE

    @protect("_initialize_thread_pools", OperationType.HARDWARE_ACCELERATION)
    def _initialize_thread_pools(self) -> None:
        """Initialize thread pools for performance and efficiency cores."""
        # Calculate thread pool sizes
        if self.is_hybrid_architecture and self.performance_cores > 0 and self.efficiency_cores > 0:
            # Distribute threads according to core counts
            perf_workers = min(self.performance_cores, self._max_workers)
            eff_workers = min(self.efficiency_cores, self._max_workers - perf_workers)
            
            # Need at least 1 worker for each core type
            perf_workers = max(1, perf_workers)
            eff_workers = max(1, eff_workers)
        else:
            # For non-hybrid architectures, use all workers for the performance pool
            perf_workers = self._max_workers
            eff_workers = 0
        
        # Create thread pools
        self._thread_pool_performance = concurrent.futures.ThreadPoolExecutor(
            max_workers=perf_workers,
            thread_name_prefix="PerfCore"
        )
        
        self._thread_pool_efficiency = concurrent.futures.ThreadPoolExecutor(
            max_workers=eff_workers,
            thread_name_prefix="EffCore"
        ) if eff_workers > 0 else None
        
        # Start scheduler threads
        self._running = True
        self._scheduler_thread_performance = threading.Thread(
            target=self._scheduler_loop,
            args=(CoreType.PERFORMANCE,),
            name="SchedulerPerf",
            daemon=True
        )
        self._scheduler_thread_performance.start()
        
        if self._thread_pool_efficiency is not None:
            self._scheduler_thread_efficiency = threading.Thread(
                target=self._scheduler_loop,
                args=(CoreType.EFFICIENCY,),
                name="SchedulerEff",
                daemon=True
            )
            self._scheduler_thread_efficiency.start()
        else:
            self._scheduler_thread_efficiency = None

    @protect("_initialize_metrics", OperationType.HARDWARE_ACCELERATION)
    def _initialize_metrics(self) -> None:
        """Initialize performance metrics for the scheduler."""
        self.performance_metrics = {
            "tasks_completed": 0,
            "tasks_failed": 0,
            "total_execution_time": 0.0,
            "performance_core_time": 0.0,
            "efficiency_core_time": 0.0,
            "avg_task_execution_time": 0.0,
            "task_type_distribution": {
                WorkloadType.COMPUTE_INTENSIVE.name: 0,
                WorkloadType.MEMORY_INTENSIVE.name: 0,
                WorkloadType.IO_INTENSIVE.name: 0,
                WorkloadType.MIXED.name: 0,
                WorkloadType.UNKNOWN.name: 0
            },
            "core_type_distribution": {
                CoreType.PERFORMANCE.name: 0,
                CoreType.EFFICIENCY.name: 0
            }
        }
        
        # Enhanced metrics for M3 Pro
        if hasattr(self, 'is_m3_pro') and self.is_m3_pro:
            self.enhanced_metrics = {
                "core_utilization": {
                    "performance": [],
                    "efficiency": []
                },
                "workload_distribution": {
                    "performance_cores": {},
                    "efficiency_cores": {}
                },
                "task_execution_times": {
                    "by_workload_type": {},
                    "by_core_type": {}
                },
                "m3_pro_specific": {
                    "variant": self.m3_pro_variant.name if hasattr(self, 'm3_pro_variant') and self.m3_pro_variant else "UNKNOWN",
                    "performance_cores": self.performance_cores,
                    "efficiency_cores": self.efficiency_cores,
                    "optimal_thread_count": self.optimal_thread_count
                }
            }
            
            # Initialize workload type tracking
            if hasattr(self, 'EnhancedWorkloadType'):
                for workload_type in EnhancedWorkloadType:
                    self.enhanced_metrics["task_execution_times"]["by_workload_type"][workload_type.name] = []
            
            # Initialize core type tracking
            for core_type in CoreType:
                self.enhanced_metrics["task_execution_times"]["by_core_type"][core_type.name] = []

    @protect("_initialize_workload_classifiers", OperationType.HARDWARE_ACCELERATION)
    def _initialize_workload_classifiers(self) -> Dict[str, Callable]:
        """
        Initialize workload classification functions.

        Returns:
            Dict[str, Callable]: Dictionary of classifier functions
        """
        classifiers = {
            "compute_intensive": self._is_compute_intensive,
            "memory_intensive": self._is_memory_intensive,
            "io_intensive": self._is_io_intensive
        }
        
        # Add trade analysis classifiers for M3 Pro
        if hasattr(self, 'is_m3_pro') and self.is_m3_pro:
            classifiers.update({
                "trade_analysis": self._is_trade_analysis_task,
                "matrix_operation": self._is_matrix_operation,
                "data_loading": self._is_data_loading_task,
                "data_preparation": self._is_data_preparation_task,
                "rca_calculation": self._is_rca_calculation,
                "hhi_calculation": self._is_hhi_calculation
            })
        
        return classifiers

    @protect("_initialize_trade_analysis_patterns", OperationType.HARDWARE_ACCELERATION)
    def _initialize_trade_analysis_patterns(self) -> Dict[str, List[str]]:
        """
        Initialize patterns for detecting trade analysis specific tasks.
        This is primarily used for M3 Pro optimization of the Yemen Trade Paper.

        Returns:
            Dict[str, List[str]]: Dictionary of pattern lists by category
        """
        # This is a simplified version, focusing on key patterns
        patterns = {
            "function_names": [
                # RCA related functions
                "calculate_rca", "compute_rca", "rca_matrix", "revealed_comparative_advantage",
                # HHI related functions
                "calculate_hhi", "compute_hhi", "hhi_index", "herfindahl_index",
                # Concentration related functions
                "product_concentration", "export_concentration", "import_concentration", 
                # Trade flow related functions
                "trade_flow", "export_flow", "import_flow", "bilateral_trade",
                # BACI related functions
                "process_baci", "load_baci", "read_baci", "parse_baci", "baci_loader"
            ],
            "module_names": [
                # Analysis categories
                "comparative_advantage", "concentration", "sophistication", "growth",
                "market", "composition", "diversification", "specialization",
                # Trade related modules
                "trade", "export", "import", "bilateral"
            ],
            "data_columns": [
                # Value columns
                "trade_value", "export_value", "import_value",
                # Entity identifier columns
                "exporter_code", "importer_code", "country_code", "product_code"
            ]
        }
        
        return patterns

    @protect("_scheduler_loop", OperationType.HARDWARE_ACCELERATION)
    def _scheduler_loop(self, core_type: CoreType) -> None:
        """
        Main scheduler loop for processing tasks on a specific core type.

        Args:
            core_type: Type of core to process tasks on
        """
        task_queue = self._task_queue_performance if core_type == CoreType.PERFORMANCE else self._task_queue_efficiency
        thread_pool = self._thread_pool_performance if core_type == CoreType.PERFORMANCE else self._thread_pool_efficiency
        
        logger.info(f"Starting scheduler loop for {core_type.name} cores")
        
        while self._running:
            try:
                # Get the next task from the queue (with priority)
                priority, count, task_id = task_queue.get(timeout=0.1)
                
                # Skip if task is no longer in tasks dict (might have been canceled)
                if task_id not in self.tasks:
                    task_queue.task_done()
                    continue
                
                # Get task from tasks dict
                with self._lock:
                    task = self.tasks[task_id]
                    if task.status != TaskStatus.PENDING:
                        # Task might have been canceled or already started
                        task_queue.task_done()
                        continue
                    
                    # Mark task as running
                    task.status = TaskStatus.RUNNING
                    task.start_time = time.time()
                
                # Submit task to thread pool
                thread_pool.submit(self._execute_task, task_id, core_type)
                
                # Mark queue task as done
                task_queue.task_done()
            
            except queue.Empty:
                # No tasks in queue, continue loop
                pass
            except Exception as e:
                logger.error(f"Error in scheduler loop for {core_type.name} cores: {e}", exc_info=True)

    @protect("_execute_task", OperationType.HARDWARE_ACCELERATION)
    def _execute_task(self, task_id: str, core_type: CoreType) -> None:
        """
        Execute a task and record performance metrics.

        Args:
            task_id: ID of the task to execute
            core_type: Type of core executing the task
        """
        task = None
        start_time = time.time()
        enhanced_workload_type = None
        
        try:
            with self._lock:
                task = self.tasks.get(task_id)
                if task is None or task.status != TaskStatus.RUNNING:
                    # Task might have been cancelled or already completed/failed
                    return

                # Extract enhanced workload type if available
                if hasattr(self, 'is_m3_pro') and self.is_m3_pro:
                    if isinstance(task.kwargs, dict) and '_enhanced_workload_type' in task.kwargs:
                        enhanced_workload_type = task.kwargs.pop('_enhanced_workload_type')

            # Execute the task function
            current_kwargs = task.kwargs if isinstance(task.kwargs, dict) else {}
            result = task.function(*task.args, **current_kwargs)

            # Record execution time
            end_time = time.time()
            execution_time = end_time - start_time

            # Update task with result and timing
            with self._lock:
                if task_id in self.tasks:  # Re-check if task still exists
                    task = self.tasks[task_id]
                    task.status = TaskStatus.COMPLETED
                    task.result = result
                    task.end_time = end_time
                    task.execution_time = execution_time
                    
                    # Update performance metrics
                    self.performance_metrics["tasks_completed"] += 1
                    self.performance_metrics["total_execution_time"] += execution_time
                    self.performance_metrics["task_type_distribution"][task.workload_type.name] += 1
                    self.performance_metrics["core_type_distribution"][core_type.name] += 1
                    
                    if core_type == CoreType.PERFORMANCE:
                        self.performance_metrics["performance_core_time"] += execution_time
                    else:
                        self.performance_metrics["efficiency_core_time"] += execution_time
                    
                    # Update enhanced metrics for M3 Pro
                    if hasattr(self, 'is_m3_pro') and self.is_m3_pro and hasattr(self, 'enhanced_metrics'):
                        if enhanced_workload_type is not None:
                            workload_type_name = enhanced_workload_type.name
                            if workload_type_name not in self.enhanced_metrics["task_execution_times"]["by_workload_type"]:
                                self.enhanced_metrics["task_execution_times"]["by_workload_type"][workload_type_name] = []
                            self.enhanced_metrics["task_execution_times"]["by_workload_type"][workload_type_name].append(execution_time)

                        core_type_name = core_type.name
                        if core_type_name not in self.enhanced_metrics["task_execution_times"]["by_core_type"]:
                             self.enhanced_metrics["task_execution_times"]["by_core_type"][core_type_name] = []
                        self.enhanced_metrics["task_execution_times"]["by_core_type"][core_type_name].append(execution_time)

                        # Update workload distribution metrics
                        dist_cores = "performance_cores" if core_type == CoreType.PERFORMANCE else "efficiency_cores"
                        if enhanced_workload_type is not None:
                            workload_type_name = enhanced_workload_type.name
                            if workload_type_name not in self.enhanced_metrics["workload_distribution"][dist_cores]:
                                self.enhanced_metrics["workload_distribution"][dist_cores][workload_type_name] = 0
                            self.enhanced_metrics["workload_distribution"][dist_cores][workload_type_name] += 1

            # If using futures, set result there
            if task_id in self._futures:
                self._futures[task_id].set_result(result)
        
        except Exception as e:
            # Record the error
            end_time = time.time()
            execution_time = end_time - start_time  # Can be calculated even on error

            with self._lock:
                if task_id in self.tasks:
                    task = self.tasks[task_id]
                    task.status = TaskStatus.FAILED
                    task.error = e
                    task.end_time = end_time
                    task.execution_time = execution_time  # Record time even for failed tasks
                    
                    # Update performance metrics
                    self.performance_metrics["tasks_failed"] += 1

            logger.error(f"Task {task_id} failed on {core_type.name} core: {e}", exc_info=True)
            # If using futures, set exception there
            if task_id in self._futures:
                self._futures[task_id].set_exception(e)

    @protect("submit", OperationType.HARDWARE_ACCELERATION)
    def submit(self, function: Callable, *args, 
               task_id: Optional[str] = None,
               workload_type: WorkloadType = WorkloadType.UNKNOWN,
               priority: int = 0,
               preferred_core_type: Optional[CoreType] = None,
               **kwargs) -> str:
        """
        Submit a task for execution on the appropriate core type.

        Args:
            function: Function to execute
            *args: Arguments to pass to the function
            task_id: Optional task identifier (generated if not provided)
            workload_type: Type of workload for core assignment (can be inferred)
            priority: Task priority (higher number = higher priority)
            preferred_core_type: Preferred core type for execution
            **kwargs: Keyword arguments to pass to the function

        Returns:
            str: Task ID
        """
        # Generate task ID if not provided
        if task_id is None:
            task_id = f"task_{self._task_counter}"
            self._task_counter += 1
        
        # Create task object
        task = Task(
            id=task_id,
            function=function,
            args=args,
            kwargs=kwargs,
            workload_type=workload_type,
            priority=priority,
            preferred_core_type=preferred_core_type,
            status=TaskStatus.PENDING
        )
        
        # If workload type is unknown, try to infer it
        if task.workload_type == WorkloadType.UNKNOWN:
            task.workload_type = self._classify_workload(task)
        
        # Determine the appropriate core type if not specified
        if task.preferred_core_type is None:
            task.preferred_core_type = self._determine_core_type(task)
        
        # Enhanced M3 Pro processing
        enhanced_workload_type = None
        if hasattr(self, 'is_m3_pro') and self.is_m3_pro:
            # Perform enhanced workload classification for M3 Pro
            enhanced_workload_type = self._classify_enhanced_workload(task)
            
            # Store enhanced workload type in task kwargs for later reference
            if not isinstance(task.kwargs, dict):
                task.kwargs = {}
            task.kwargs['_enhanced_workload_type'] = enhanced_workload_type
            
            # Adjust priority based on workload type for M3 Pro optimization
            if enhanced_workload_type == EnhancedWorkloadType.COMPUTE_INTENSIVE_HIGH:
                if task.preferred_core_type == CoreType.PERFORMANCE or task.preferred_core_type is None:
                    task.priority += 2
            elif enhanced_workload_type == EnhancedWorkloadType.IO_INTENSIVE_HIGH:
                if task.preferred_core_type == CoreType.EFFICIENCY or task.preferred_core_type is None:
                    task.priority += 2
            
            # Check for trade analysis tasks, which get priority boost
            if hasattr(self, '_is_trade_analysis_task') and self._is_trade_analysis_task(task):
                if hasattr(self, '_is_rca_calculation') and self._is_rca_calculation(task):
                    task.priority += 3  # RCA calculations are critical
                elif hasattr(self, '_is_hhi_calculation') and self._is_hhi_calculation(task):
                    task.priority += 2  # HHI calculations are important
                else:
                    task.priority += 1  # Other trade tasks get small boost
        
        # Get the appropriate task queue based on core type
        task_queue = self._task_queue_performance if task.preferred_core_type == CoreType.PERFORMANCE else self._task_queue_efficiency
        
        # Store task in tasks dictionary
        with self._lock:
            self.tasks[task_id] = task
        
        # Add task to queue with priority
        # Use negative priority for the queue (higher number = higher priority)
        # Add counter as tiebreaker for equal priorities
        task_queue.put((-task.priority, self._task_counter, task_id))
        
        return task_id

    @protect("get_result", OperationType.HARDWARE_ACCELERATION)
    def get_result(self, task_id: str, timeout: Optional[float] = None) -> Any:
        """
        Get the result of a task, waiting for completion if necessary.

        Args:
            task_id: ID of the task to get the result from
            timeout: Maximum time to wait for the result (optional)

        Returns:
            Any: Result of the task

        Raises:
            SchedulerError: If the task does not exist
            TimeoutError: If the timeout is exceeded
            Exception: If the task failed, the original exception is raised
        """
        # Check if task exists
        if task_id not in self.tasks:
            raise SchedulerError(f"Task {task_id} does not exist")
        
        # Get task
        task = self.tasks[task_id]
        
        # If task is already completed, return result
        if task.status == TaskStatus.COMPLETED:
            return task.result
        
        # If task failed, raise the original exception
        if task.status == TaskStatus.FAILED:
            raise task.error if task.error else SchedulerError(f"Task {task_id} failed")
        
        # If task was cancelled, raise error
        if task.status == TaskStatus.CANCELLED:
            raise SchedulerError(f"Task {task_id} was cancelled")
        
        # Create a Future for this task if it doesn't exist yet
        if task_id not in self._futures:
            self._futures[task_id] = concurrent.futures.Future()
        
        # Wait for task completion
        try:
            return self._futures[task_id].result(timeout=timeout)
        except concurrent.futures.TimeoutError:
            raise TimeoutError(f"Timeout waiting for task {task_id}")

    @protect("get_task_status", OperationType.HARDWARE_ACCELERATION)
    def get_task_status(self, task_id: str) -> Optional[Task]:
        """
        Get the current status of a task.

        Args:
            task_id: ID of the task to get the status of

        Returns:
            Optional[Task]: Task object or None if task does not exist
        """
        return self.tasks.get(task_id)

    @protect("cancel_task", OperationType.HARDWARE_ACCELERATION)
    def cancel_task(self, task_id: str) -> bool:
        """
        Cancel a pending task.

        Args:
            task_id: ID of the task to cancel

        Returns:
            bool: True if the task was cancelled, False if it was already running/completed
        """
        with self._lock:
            # Check if task exists
            if task_id not in self.tasks:
                return False
            
            # Get task
            task = self.tasks[task_id]
            
            # Can only cancel pending tasks
            if task.status != TaskStatus.PENDING:
                return False
            
            # Mark task as cancelled
            task.status = TaskStatus.CANCELLED
            
            # If using futures, cancel the future
            if task_id in self._futures:
                self._futures[task_id].cancel()
            
            return True

    @protect("get_metrics", OperationType.HARDWARE_ACCELERATION)
    def get_metrics(self) -> Dict[str, Any]:
        """
        Get performance metrics for the scheduler.

        Returns:
            Dict[str, Any]: Dictionary with performance metrics
        """
        with self._lock:
            # Calculate average task execution time
            if self.performance_metrics["tasks_completed"] > 0:
                self.performance_metrics["avg_task_execution_time"] = (
                    self.performance_metrics["total_execution_time"] / 
                    self.performance_metrics["tasks_completed"]
                )
            
            # Return a copy of the metrics to avoid race conditions
            return dict(self.performance_metrics)

    @protect("get_enhanced_metrics", OperationType.HARDWARE_ACCELERATION)
    def get_enhanced_metrics(self) -> Dict[str, Any]:
        """
        Get enhanced performance metrics, particularly useful for M3 Pro.

        Returns:
            Dict[str, Any]: Enhanced performance metrics
        """
        if not (hasattr(self, 'is_m3_pro') and self.is_m3_pro and hasattr(self, 'enhanced_metrics')):
            # Return standard metrics if enhanced metrics aren't available
            return self.get_metrics()
        
        with self._lock:
            # Create a copy to avoid modifying the stored raw times
            metrics_summary = {
                "core_utilization": self.enhanced_metrics["core_utilization"],
                "workload_distribution": self.enhanced_metrics["workload_distribution"],
                "task_execution_times": {
                    "by_workload_type": {},
                    "by_core_type": {}
                },
                "m3_pro_specific": self.enhanced_metrics["m3_pro_specific"]
            }

            # Calculate averages for workload types
            for workload_type_name, times in self.enhanced_metrics["task_execution_times"]["by_workload_type"].items():
                if times:
                    avg_time = sum(times) / len(times)
                    metrics_summary["task_execution_times"]["by_workload_type"][workload_type_name] = {
                        "average": avg_time,
                        "count": len(times),
                        "total": sum(times),
                        "min": min(times),
                        "max": max(times)
                    }
                else:
                    metrics_summary["task_execution_times"]["by_workload_type"][workload_type_name] = {
                        "average": 0, "count": 0, "total": 0, "min": 0, "max": 0
                    }

            # Calculate averages for core types
            for core_type_name, times in self.enhanced_metrics["task_execution_times"]["by_core_type"].items():
                if times:
                    avg_time = sum(times) / len(times)
                    metrics_summary["task_execution_times"]["by_core_type"][core_type_name] = {
                        "average": avg_time,
                        "count": len(times),
                        "total": sum(times),
                        "min": min(times),
                        "max": max(times)
                    }
                else:
                     metrics_summary["task_execution_times"]["by_core_type"][core_type_name] = {
                        "average": 0, "count": 0, "total": 0, "min": 0, "max": 0
                    }

            # Calculate core utilization balance if data is available
            perf_tasks_total = sum(metrics_summary["workload_distribution"]["performance_cores"].values())
            eff_tasks_total = sum(metrics_summary["workload_distribution"]["efficiency_cores"].values())

            if self.performance_cores > 0 and self.efficiency_cores > 0:
                # Simple balance ratio (tasks per P-core vs tasks per E-core)
                # Avoid division by zero if no tasks on a core type
                load_per_p_core = perf_tasks_total / self.performance_cores if perf_tasks_total > 0 else 0
                load_per_e_core = eff_tasks_total / self.efficiency_cores if eff_tasks_total > 0 else 0

                if load_per_e_core > 0:  # Avoid division by zero
                    balance_ratio = load_per_p_core / load_per_e_core
                    metrics_summary["m3_pro_specific"]["p_e_balance_ratio"] = balance_ratio
                else:
                    metrics_summary["m3_pro_specific"]["p_e_balance_ratio"] = "N/A (no E-core tasks)"

            return metrics_summary

    @protect("_classify_workload", OperationType.HARDWARE_ACCELERATION)
    def _classify_workload(self, task: Task) -> WorkloadType:
        """
        Classify a task's workload type.

        Args:
            task: Task to classify

        Returns:
            WorkloadType: Classified workload type
        """
        # Check function name for hints about workload type
        function_name = task.function.__name__ if hasattr(task.function, '__name__') else str(task.function)
        
        # Check for compute-intensive patterns
        compute_keywords = ["calculate", "compute", "process", "transform", "analyze", "optimize", "decompose"]
        for keyword in compute_keywords:
            if keyword in function_name.lower():
                return WorkloadType.COMPUTE_INTENSIVE
        
        # Check for I/O-intensive patterns
        io_keywords = ["read", "write", "load", "save", "download", "upload", "fetch", "file", "http"]
        for keyword in io_keywords:
            if keyword in function_name.lower():
                return WorkloadType.IO_INTENSIVE
        
        # Check for memory-intensive patterns
        memory_keywords = ["dataframe", "merge", "join", "array", "matrix", "reshape", "filter"]
        for keyword in memory_keywords:
            if keyword in function_name.lower():
                return WorkloadType.MEMORY_INTENSIVE
        
        # Check args for pandas DataFrames or large numpy arrays (memory-intensive)
        for arg in task.args:
            if hasattr(arg, "__class__") and hasattr(arg.__class__, "__name__"):
                if "DataFrame" in arg.__class__.__name__ or "Series" in arg.__class__.__name__:
                    return WorkloadType.MEMORY_INTENSIVE
                if "ndarray" in arg.__class__.__name__ and hasattr(arg, "size") and arg.size > 10000:
                    return WorkloadType.MEMORY_INTENSIVE
        
        # Check kwargs for the same
        for key, value in task.kwargs.items() if isinstance(task.kwargs, dict) else []:
            if hasattr(value, "__class__") and hasattr(value.__class__, "__name__"):
                if "DataFrame" in value.__class__.__name__ or "Series" in value.__class__.__name__:
                    return WorkloadType.MEMORY_INTENSIVE
                if "ndarray" in value.__class__.__name__ and hasattr(value, "size") and value.size > 10000:
                    return WorkloadType.MEMORY_INTENSIVE
        
        # Default to mixed if can't determine more specific type
        return WorkloadType.MIXED

    @protect("_determine_core_type", OperationType.HARDWARE_ACCELERATION)
    def _determine_core_type(self, task: Task) -> CoreType:
        """
        Determine the optimal core type for a task.

        Args:
            task: Task to analyze

        Returns:
            CoreType: Recommended core type for the task
        """
        # If not a hybrid architecture, use performance cores
        if not self.is_hybrid_architecture or self.efficiency_cores == 0:
            return CoreType.PERFORMANCE
        
        # Use workload type to determine core type
        if task.workload_type == WorkloadType.COMPUTE_INTENSIVE:
            return CoreType.PERFORMANCE
        elif task.workload_type == WorkloadType.IO_INTENSIVE:
            return CoreType.EFFICIENCY
        
        # For memory-intensive and mixed workloads, balance based on current load
        if self._task_queue_performance and self._task_queue_efficiency:
            perf_queue_size = self._task_queue_performance.qsize()
            eff_queue_size = self._task_queue_efficiency.qsize()
            
            # Normalize by core count
            perf_load = perf_queue_size / self.performance_cores if self.performance_cores > 0 else float('inf')
            eff_load = eff_queue_size / self.efficiency_cores if self.efficiency_cores > 0 else float('inf')
            
            # Use the less loaded core type
            if perf_load <= eff_load:
                return CoreType.PERFORMANCE
            else:
                return CoreType.EFFICIENCY
        
        # Default to performance cores
        return CoreType.PERFORMANCE

    @protect("_classify_enhanced_workload", OperationType.HARDWARE_ACCELERATION)
    def _classify_enhanced_workload(self, task: Task) -> EnhancedWorkloadType:
        """
        Classify a task's workload using enhanced classification for M3 Pro.

        Args:
            task: Task to classify

        Returns:
            EnhancedWorkloadType: Classified enhanced workload type
        """
        # Check function name and other task properties
        function_name = task.function.__name__ if hasattr(task.function, '__name__') else str(task.function)

        # Check for compute-intensive patterns
        if any(keyword in function_name.lower() for keyword in [
            "matrix", "multiply", "invert", "decompose", "svd", "eigen", "factorize",
            "optimize", "gradient", "backprop", "neural", "train", "predict"
        ]):
            return EnhancedWorkloadType.COMPUTE_INTENSIVE_HIGH

        # Check for memory-intensive patterns
        if any(keyword in function_name.lower() for keyword in [
            "dataframe", "merge", "join", "concat", "groupby", "pivot", "reshape",
            "load_data", "read_csv", "read_parquet", "read_excel"
        ]):
            return EnhancedWorkloadType.MEMORY_INTENSIVE_HIGH

        # Check for I/O-intensive patterns
        if any(keyword in function_name.lower() for keyword in [
            "file", "read", "write", "save", "load", "import", "export", "download",
            "upload", "stream", "buffer", "io", "disk"
        ]):
            return EnhancedWorkloadType.IO_INTENSIVE_HIGH

        # Check for trade analysis specific patterns
        if hasattr(self, '_trade_analysis_patterns') and any(pattern in function_name.lower() for pattern in self._trade_analysis_patterns["function_names"]):
            # Further classify based on specific trade analysis function
            if any(keyword in function_name.lower() for keyword in [
                "rca", "comparative_advantage", "revealed", "balassa"
            ]):
                return EnhancedWorkloadType.COMPUTE_INTENSIVE
            elif any(keyword in function_name.lower() for keyword in [
                "hhi", "concentration", "hirschman", "herfindahl"
            ]):
                return EnhancedWorkloadType.COMPUTE_INTENSIVE_LOW
            elif any(keyword in function_name.lower() for keyword in [
                "load", "read", "parse", "import", "baci"
            ]):
                return EnhancedWorkloadType.MEMORY_INTENSIVE

            # Default trade analysis classification
            return EnhancedWorkloadType.MIXED

        # Default to UNKNOWN if we can't classify
        return EnhancedWorkloadType.UNKNOWN

    @protect("_is_compute_intensive", OperationType.HARDWARE_ACCELERATION)
    def _is_compute_intensive(self, task: Task) -> bool:
        """
        Detect if a task is compute-intensive.

        Args:
            task: Task to analyze

        Returns:
            bool: True if the task is compute-intensive
        """
        # Check function name for compute-intensive keywords
        function_name = task.function.__name__ if hasattr(task.function, '__name__') else str(task.function)
        compute_keywords = [
            "calculate", "compute", "matrix", "multiply", "invert", "decompose",
            "eigen", "svd", "factorize", "optimize", "regression", "cluster",
            "transform", "normalize", "standardize", "vectorize", "parallel"
        ]

        for keyword in compute_keywords:
            if keyword in function_name.lower():
                return True

        # Check if it's a matrix operation
        if hasattr(self, '_is_matrix_operation') and self._is_matrix_operation(task):
            return True

        # Check if it's an RCA or HHI calculation
        if (hasattr(self, '_is_rca_calculation') and self._is_rca_calculation(task)) or \
           (hasattr(self, '_is_hhi_calculation') and self._is_hhi_calculation(task)):
            return True

        return False

    @protect("_is_memory_intensive", OperationType.HARDWARE_ACCELERATION)
    def _is_memory_intensive(self, task: Task) -> bool:
        """
        Detect if a task is memory-intensive.

        Args:
            task: Task to analyze

        Returns:
            bool: True if the task is memory-intensive
        """
        # Check function name for memory-intensive keywords
        function_name = task.function.__name__ if hasattr(task.function, '__name__') else str(task.function)
        memory_keywords = [
            "dataframe", "large", "chunk", "partition", "merge", "join",
            "concat", "groupby", "pivot", "reshape", "memory", "allocate",
            "buffer", "array", "matrix", "tensor", "batch", "dataset"
        ]

        for keyword in memory_keywords:
            if keyword in function_name.lower():
                return True

        # Check arguments for large data structures
        for arg in task.args:
            # Check for pandas DataFrames
            if hasattr(arg, 'shape') and hasattr(arg, 'dtypes'):
                # Check DataFrame dimensions
                if hasattr(arg, 'shape'):
                    try:
                        rows, cols = arg.shape
                        # Large DataFrames are memory-intensive
                        if rows * cols > 1000000:  # 1M cells
                            return True
                    except:
                        pass

            # Check for numpy arrays
            elif hasattr(arg, 'shape') and hasattr(arg, 'dtype'):
                try:
                    # Calculate array size
                    size_bytes = arg.size * arg.itemsize
                    # If more than 50MB, consider memory-intensive
                    if size_bytes > 50 * 1024 * 1024:
                        return True
                except:
                    pass

            # Check for large lists or dictionaries
            elif isinstance(arg, (list, dict, set)):
                try:
                    # If more than 100K elements, consider memory-intensive
                    if len(arg) > 100000:
                        return True
                except:
                    pass

        return False

    @protect("_is_io_intensive", OperationType.HARDWARE_ACCELERATION)
    def _is_io_intensive(self, task: Task) -> bool:
        """
        Detect if a task is I/O-intensive.

        Args:
            task: Task to analyze

        Returns:
            bool: True if the task is I/O-intensive
        """
        # Check function name for I/O-intensive keywords
        function_name = task.function.__name__ if hasattr(task.function, '__name__') else str(task.function)
        io_keywords = [
            "read", "write", "load", "save", "open", "close", "file", "csv",
            "excel", "json", "parquet", "hdf", "database", "sql", "query",
            "fetch", "download", "upload", "stream", "http", "request", "api"
        ]

        for keyword in io_keywords:
            if keyword in function_name.lower():
                return True

        # Check if it's a data loading task
        if hasattr(self, '_is_data_loading_task') and self._is_data_loading_task(task):
            return True

        return False

    @protect("_is_matrix_operation", OperationType.HARDWARE_ACCELERATION)
    def _is_matrix_operation(self, task: Task) -> bool:
        """
        Detect if a task involves matrix operations.

        Args:
            task: Task to analyze

        Returns:
            bool: True if the task involves matrix operations
        """
        # Check function name for matrix operation keywords
        function_name = task.function.__name__ if hasattr(task.function, '__name__') else str(task.function)
        matrix_keywords = [
            "matrix", "matmul", "dot", "inner", "outer", "cross", "transpose",
            "inverse", "inv", "det", "determinant", "eigen", "eigenvalue",
            "eigenvector", "svd", "singular", "decomposition", "factorization",
            "cholesky", "qr", "lu", "solve", "lstsq", "linear", "system"
        ]

        for keyword in matrix_keywords:
            if keyword in function_name.lower():
                return True

        # Check arguments for matrix-like objects
        for arg in task.args:
            # Check for 2D numpy arrays
            if hasattr(arg, 'ndim') and hasattr(arg, 'shape'):
                try:
                    if arg.ndim == 2 and min(arg.shape) > 1:
                        return True
                except:
                    pass

        return False

    @protect("_is_rca_calculation", OperationType.HARDWARE_ACCELERATION)
    def _is_rca_calculation(self, task: Task) -> bool:
        """
        Detect if a task is an RCA (Revealed Comparative Advantage) calculation.

        Args:
            task: Task to analyze

        Returns:
            bool: True if the task is an RCA calculation
        """
        # Check function name for RCA keywords
        function_name = task.function.__name__ if hasattr(task.function, '__name__') else str(task.function)
        rca_keywords = [
            "rca", "revealed_comparative_advantage", "comparative_advantage",
            "calculate_rca", "compute_rca", "rca_matrix", "rca_calculation",
            "trade_specialization", "export_specialization", "balassa"
        ]

        for keyword in rca_keywords:
            if keyword.lower() in function_name.lower():
                return True

        # Check module path for RCA contexts
        if hasattr(task.function, '__module__') and task.function.__module__:
            module_name = task.function.__module__

            rca_modules = [
                "comparative_advantage", "rca", "trade_specialization",
                "export_patterns", "export_analysis", "trade_analysis.comparative"
            ]

            for module in rca_modules:
                if module in module_name.lower():
                    return True

        return False

    @protect("_is_hhi_calculation", OperationType.HARDWARE_ACCELERATION)
    def _is_hhi_calculation(self, task: Task) -> bool:
        """
        Detect if a task is an HHI (Herfindahl-Hirschman Index) calculation.

        Args:
            task: Task to analyze

        Returns:
            bool: True if the task is an HHI calculation
        """
        # Check function name for HHI keywords
        function_name = task.function.__name__ if hasattr(task.function, '__name__') else str(task.function)
        hhi_keywords = [
            "hhi", "herfindahl", "hirschman", "concentration", "market_concentration",
            "calculate_hhi", "compute_hhi", "hhi_index", "concentration_index",
            "market_share", "product_concentration", "export_concentration"
        ]

        for keyword in hhi_keywords:
            if keyword.lower() in function_name.lower():
                return True

        # Check module path for HHI contexts
        if hasattr(task.function, '__module__') and task.function.__module__:
            module_name = task.function.__module__

            hhi_modules = [
                "concentration", "hhi", "herfindahl", "hirschman",
                "market_power", "market_structure", "market_analysis",
                "diversification", "trade_patterns", "export_diversity"
            ]

            for module in hhi_modules:
                if module in module_name.lower():
                    return True

        return False

    @protect("_is_data_loading_task", OperationType.HARDWARE_ACCELERATION)
    def _is_data_loading_task(self, task: Task) -> bool:
        """
        Detect if a task is a data loading operation.

        Args:
            task: Task to analyze

        Returns:
            bool: True if the task is a data loading operation
        """
        # Check function name for data loading keywords
        function_name = task.function.__name__ if hasattr(task.function, '__name__') else str(task.function)
        loading_keywords = [
            "load", "read", "import", "parse", "open", "fetch", "get_data",
            "load_data", "read_file", "read_csv", "read_excel", "load_from",
            "fetch_data", "retrieve", "download", "acquire", "extract_from",
            "read_from_", "load_baci", "load_comtrade"
        ]

        for keyword in loading_keywords:
            if keyword.lower() in function_name.lower():
                return True

        # Check module path for data loading contexts
        if hasattr(task.function, '__module__') and task.function.__module__:
            module_name = task.function.__module__

            loader_modules = [
                "loaders", "loading", "readers", "data.io",
                "importers", "baci_loader", "data_loader"
            ]

            for module in loader_modules:
                if module in module_name.lower():
                    return True

        return False

    @protect("_is_trade_analysis_task", OperationType.HARDWARE_ACCELERATION)
    def _is_trade_analysis_task(self, task: Task) -> bool:
        """
        Detect if a task is related to trade analysis.

        Args:
            task: Task to analyze

        Returns:
            bool: True if the task is related to trade analysis
        """
        if not hasattr(self, '_trade_analysis_patterns'):
            return False
            
        # Check function name for trade analysis keywords
        function_name = task.function.__name__ if hasattr(task.function, '__name__') else str(task.function)

        # Check against our predefined patterns
        for pattern in self._trade_analysis_patterns["function_names"]:
            if pattern.lower() in function_name.lower():
                logger.debug(f"Trade analysis task detected by function name: {pattern} in {function_name}")
                return True

        # Check module path for trade analysis contexts
        if hasattr(task.function, '__module__') and task.function.__module__:
            module_name = task.function.__module__

            for pattern in self._trade_analysis_patterns["module_names"]:
                if pattern in module_name.lower():
                    logger.debug(f"Trade analysis task detected by module name: {pattern} in {module_name}")
                    return True

        # Check if it's an RCA or HHI calculation
        if (hasattr(self, '_is_rca_calculation') and self._is_rca_calculation(task)) or \
           (hasattr(self, '_is_hhi_calculation') and self._is_hhi_calculation(task)):
            logger.debug(f"Trade analysis task detected as RCA or HHI calculation")
            return True

        return False

    @protect("shutdown", OperationType.HARDWARE_ACCELERATION)
    def shutdown(self, wait: bool = True) -> None:
        """
        Shutdown the scheduler and its thread pools.

        Args:
            wait: Whether to wait for all pending tasks to complete
        """
        logger.info("Shutting down Hybrid Core Scheduler...")
        
        # Set running flag to False to stop scheduler loops
        self._running = False
        
        # Shutdown thread pools
        if self._thread_pool_performance:
            self._thread_pool_performance.shutdown(wait=wait)
        
        if self._thread_pool_efficiency:
            self._thread_pool_efficiency.shutdown(wait=wait)
        
        logger.info("Hybrid Core Scheduler shut down.")


# Singleton instance
_hybrid_core_scheduler = None

@protect("get_hybrid_core_scheduler", OperationType.HARDWARE_ACCELERATION)
def get_hybrid_core_scheduler(max_workers: Optional[int] = None) -> 'HybridCoreScheduler':
    """
    Get the singleton instance of the hybrid core scheduler.

    Args:
        max_workers: Maximum number of worker threads/processes (optional)

    Returns:
        HybridCoreScheduler: Singleton instance
    """
    global _hybrid_core_scheduler
    if _hybrid_core_scheduler is None:
        _hybrid_core_scheduler = HybridCoreScheduler(max_workers=max_workers)
    return _hybrid_core_scheduler