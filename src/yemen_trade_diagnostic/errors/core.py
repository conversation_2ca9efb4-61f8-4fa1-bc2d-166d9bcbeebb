"""
Perfect Error Handling System for Yemen Trade Diagnostic

This module provides the unified, comprehensive error handling system that integrates:
- Circuit breakers with adaptive thresholds
- Graceful degradation strategies  
- Recovery orchestration
- Error analytics and pattern detection
- Resource-aware error handling
- Hardware integration
- Performance monitoring

Design Principles:
- Single point of entry for all error handling
- Modern Python patterns (context managers, decorators, type hints)
- Hardware-aware and performance-optimized
- Comprehensive monitoring and analytics
- Clean, intuitive API
"""

import asyncio
import functools
import logging
import threading
import time
from contextlib import contextmanager
from dataclasses import dataclass, field
from enum import Enum, auto
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional, TypeVar, Union, Protocol
from collections import defaultdict, deque

import numpy as np
import pandas as pd

# Import existing components
from .error_types import ErrorCategory, ErrorSeverity, CircuitBreakerState
from .enhanced_circuit_breaker import EnhancedCircuitBreaker, CircuitBreakerConfig
from .graceful_degradation import GracefulDegradationManager, DegradationLevel
from .recovery_manager import RecoveryManager
from .resource_manager import ResourceManager
from .error_analytics import ErrorAnalytics
from .health_checks import CompositeHealthCheck

T = TypeVar('T')
F = TypeVar('F', bound=Callable[..., Any])

class ErrorHandlingMode(Enum):
    """Error handling modes for different scenarios"""
    STRICT = auto()      # Fail fast, no degradation
    RESILIENT = auto()   # Use circuit breakers and fallbacks
    GRACEFUL = auto()    # Full graceful degradation
    PERFORMANCE = auto() # Optimize for performance over safety

class OperationType(Enum):
    """Types of operations for error handling configuration"""
    DATA_LOADING = auto()
    COMPUTATION = auto()
    VISUALIZATION = auto()
    HARDWARE_ACCELERATION = auto()
    PIPELINE_EXECUTION = auto()
    MODEL_CALCULATION = auto()

@dataclass
class ErrorConfig:
    """Configuration for error handling behavior"""
    mode: ErrorHandlingMode = ErrorHandlingMode.RESILIENT
    operation_type: OperationType = OperationType.COMPUTATION
    circuit_breaker_enabled: bool = True
    graceful_degradation_enabled: bool = True
    retry_enabled: bool = True
    monitoring_enabled: bool = True
    hardware_aware: bool = True
    max_retries: int = 3
    retry_delay: float = 1.0
    retry_backoff: float = 2.0
    timeout_seconds: Optional[float] = None
    circuit_breaker_threshold: int = 5
    circuit_breaker_timeout: int = 60

class ErrorManager:
    """
    Unified Error Manager - The single point of entry for all error handling.
    
    This class coordinates all error handling subsystems and provides a clean,
    modern API for the entire application.
    """
    
    _instance: Optional['ErrorManager'] = None
    _lock = threading.Lock()
    
    def __init__(self):
        """Initialize the error manager with all subsystems"""
        self.logger = logging.getLogger(__name__)
        
        # Initialize subsystems (lazy loaded to avoid circular imports)
        self.circuit_breakers: Dict[str, EnhancedCircuitBreaker] = {}
        self._degradation_manager = None
        self._recovery_manager = None
        self._resource_manager = None
        self._error_analytics = None
        self._health_check = None
        
        # Register this instance
        register_manager('error_manager', self)
        
        # Configuration per operation type
        self.configs: Dict[OperationType, ErrorConfig] = {
            OperationType.DATA_LOADING: ErrorConfig(
                mode=ErrorHandlingMode.RESILIENT,
                circuit_breaker_threshold=3,
                max_retries=5,
                timeout_seconds=300
            ),
            OperationType.COMPUTATION: ErrorConfig(
                mode=ErrorHandlingMode.GRACEFUL,
                circuit_breaker_threshold=5,
                max_retries=3,
                timeout_seconds=60
            ),
            OperationType.VISUALIZATION: ErrorConfig(
                mode=ErrorHandlingMode.RESILIENT,
                circuit_breaker_threshold=3,
                max_retries=2,
                timeout_seconds=30
            ),
            OperationType.HARDWARE_ACCELERATION: ErrorConfig(
                mode=ErrorHandlingMode.PERFORMANCE,
                circuit_breaker_threshold=2,
                max_retries=1,
                timeout_seconds=10,
                hardware_aware=True
            ),
            OperationType.PIPELINE_EXECUTION: ErrorConfig(
                mode=ErrorHandlingMode.GRACEFUL,
                circuit_breaker_threshold=3,
                max_retries=2,
                timeout_seconds=600
            ),
            OperationType.MODEL_CALCULATION: ErrorConfig(
                mode=ErrorHandlingMode.RESILIENT,
                circuit_breaker_threshold=5,
                max_retries=3,
                timeout_seconds=120
            )
        }
        
        # Performance tracking
        self.operation_stats: Dict[str, Dict[str, Any]] = defaultdict(
            lambda: {
                'total_calls': 0,
                'total_errors': 0,
                'total_recoveries': 0,
                'avg_duration': 0.0,
                'last_error': None
            }
        )
        
    @classmethod
    def get_instance(cls) -> 'ErrorManager':
        """Get singleton instance of ErrorManager"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = cls()
        return cls._instance
    
    @property
    def degradation_manager(self):
        """Lazy load degradation manager."""
        if self._degradation_manager is None:
            from .graceful_degradation import GracefulDegradationManager
            self._degradation_manager = GracefulDegradationManager()
        return self._degradation_manager
    
    @property
    def recovery_manager(self):
        """Lazy load recovery manager."""
        if self._recovery_manager is None:
            from .recovery_manager import RecoveryManager
            self._recovery_manager = RecoveryManager()
        return self._recovery_manager
    
    @property
    def resource_manager(self):
        """Lazy load resource manager."""
        if self._resource_manager is None:
            from .resource_manager import ResourceManager
            self._resource_manager = ResourceManager()
        return self._resource_manager
    
    @property
    def error_analytics(self):
        """Lazy load error analytics."""
        if self._error_analytics is None:
            from .error_analytics import ErrorAnalytics
            self._error_analytics = ErrorAnalytics()
        return self._error_analytics
    
    @property
    def health_check(self):
        """Lazy load health check."""
        if self._health_check is None:
            from .health_checks import CompositeHealthCheck
            self._health_check = CompositeHealthCheck(
                name="error_manager_health",
                checks=[]
            )
        return self._health_check
    
    def get_circuit_breaker(self, name: str, config: Optional[ErrorConfig] = None) -> EnhancedCircuitBreaker:
        """Get or create a circuit breaker for a specific operation"""
        if name not in self.circuit_breakers:
            cb_config = CircuitBreakerConfig()
            if config:
                cb_config.failure_threshold = config.circuit_breaker_threshold
                cb_config.recovery_timeout = config.circuit_breaker_timeout
            
            self.circuit_breakers[name] = EnhancedCircuitBreaker(name, cb_config)
        
        return self.circuit_breakers[name]
    
    def execute_with_protection(
        self,
        func: Callable[..., T],
        operation_name: str,
        operation_type: OperationType = OperationType.COMPUTATION,
        config: Optional[ErrorConfig] = None,
        *args,
        **kwargs
    ) -> T:
        """
        Execute a function with full error protection and monitoring.
        
        This is the main method that provides comprehensive error handling.
        """
        start_time = time.time()
        effective_config = config or self.configs[operation_type]
        
        # Update operation stats
        self.operation_stats[operation_name]['total_calls'] += 1
        
        try:
            # Check resource constraints if hardware-aware
            if effective_config.hardware_aware:
                if not self.resource_manager.can_execute_operation(operation_name):
                    raise ResourceError(f"Insufficient resources for {operation_name}")
            
            # Execute with circuit breaker protection
            if effective_config.circuit_breaker_enabled:
                circuit_breaker = self.get_circuit_breaker(operation_name, effective_config)
                result = circuit_breaker.call(func, *args, **kwargs)
            else:
                result = func(*args, **kwargs)
            
            # Update success metrics
            duration = time.time() - start_time
            stats = self.operation_stats[operation_name]
            stats['avg_duration'] = (stats['avg_duration'] * (stats['total_calls'] - 1) + duration) / stats['total_calls']
            
            return result
            
        except Exception as e:
            # Update error metrics
            self.operation_stats[operation_name]['total_errors'] += 1
            self.operation_stats[operation_name]['last_error'] = str(e)
            
            # Record error for analytics
            self.error_analytics.record_error(
                component='error_manager',
                operation=operation_name,
                error=e,
                context={
                    'operation_type': operation_type.name,
                    'duration': time.time() - start_time
                }
            )
            
            # Attempt graceful degradation
            if effective_config.graceful_degradation_enabled:
                try:
                    result = self.degradation_manager.execute_with_degradation(
                        func, fallback_value=None, *args, **kwargs
                    )
                    self.operation_stats[operation_name]['total_recoveries'] += 1
                    return result
                except Exception:
                    pass  # Fall through to re-raise original exception
            
            # Log the error
            self.logger.error(f"Error in {operation_name} ({operation_type.name}): {e}")
            raise
    
    def protect(
        self,
        operation_name: Optional[str] = None,
        operation_type: OperationType = OperationType.COMPUTATION,
        config: Optional[ErrorConfig] = None
    ):
        """
        Decorator to protect a function with comprehensive error handling.
        
        Usage:
            @protect("data_loading", OperationType.DATA_LOADING)
            def load_data():
                # Your code here
                pass
        """
        def decorator(func: F) -> F:
            name = operation_name or f"{func.__module__}.{func.__name__}"
            
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                return self.execute_with_protection(
                    func, name, operation_type, config, *args, **kwargs
                )
            
            return wrapper
        return decorator
    
    @contextmanager
    def error_context(
        self,
        operation_name: str,
        operation_type: OperationType = OperationType.COMPUTATION,
        config: Optional[ErrorConfig] = None
    ):
        """
        Context manager for error handling.
        
        Usage:
            with error_context("data_processing"):
                # Your code here
                pass
        """
        start_time = time.time()
        effective_config = config or self.configs[operation_type]
        
        try:
            yield
        except Exception as e:
            # Handle the error using the same logic as execute_with_protection
            self.operation_stats[operation_name]['total_errors'] += 1
            self.error_analytics.record_error(
                component='error_manager',
                operation=operation_name,
                error=e,
                context={
                    'operation_type': operation_type.name,
                    'duration': time.time() - start_time
                }
            )
            
            if effective_config.graceful_degradation_enabled:
                self.logger.warning(f"Error in {operation_name}, attempting degraded operation: {e}")
            
            raise
    
    def get_health_status(self) -> Dict[str, Any]:
        """Get comprehensive health status of all error handling subsystems"""
        return {
            'circuit_breakers': {
                name: {
                    'state': cb.state.name,
                    'failure_count': cb.failure_count,
                    'success_count': cb.success_count
                }
                for name, cb in self.circuit_breakers.items()
            },
            'operation_stats': dict(self.operation_stats),
            'resource_status': self.resource_manager.get_status(),
            'degradation_level': self.degradation_manager.current_degradation_level.name,
            'health_checks': self.health_check.check().to_dict()
        }
    
    def get_performance_metrics(self) -> pd.DataFrame:
        """Get performance metrics as a DataFrame for analysis"""
        data = []
        for operation_name, stats in self.operation_stats.items():
            data.append({
                'operation': operation_name,
                'total_calls': stats['total_calls'],
                'total_errors': stats['total_errors'],
                'total_recoveries': stats['total_recoveries'],
                'error_rate': stats['total_errors'] / max(stats['total_calls'], 1),
                'recovery_rate': stats['total_recoveries'] / max(stats['total_errors'], 1),
                'avg_duration': stats['avg_duration']
            })
        
        return pd.DataFrame(data)
    
    def reset_stats(self, operation_name: Optional[str] = None):
        """Reset statistics for all operations or a specific operation"""
        if operation_name:
            if operation_name in self.operation_stats:
                del self.operation_stats[operation_name]
        else:
            self.operation_stats.clear()
    
    def configure_operation(self, operation_type: OperationType, config: ErrorConfig):
        """Configure error handling for a specific operation type"""
        self.configs[operation_type] = config

# Import registry to register the manager
from .manager_registry import register_manager

# Convenience functions and decorators for easy access
def protect(
    operation_name: Optional[str] = None,
    operation_type: OperationType = OperationType.COMPUTATION,
    config: Optional[ErrorConfig] = None
):
    """Global decorator for error protection"""
    from .manager_registry import get_error_manager
    return get_error_manager().protect(operation_name, operation_type, config)

def error_context(
    operation_name: str,
    operation_type: OperationType = OperationType.COMPUTATION,
    config: Optional[ErrorConfig] = None
):
    """Global context manager for error handling"""
    from .manager_registry import get_error_manager
    return get_error_manager().error_context(operation_name, operation_type, config)

def execute_protected(
    func: Callable[..., T],
    operation_name: str,
    operation_type: OperationType = OperationType.COMPUTATION,
    config: Optional[ErrorConfig] = None,
    *args,
    **kwargs
) -> T:
    """Global function for protected execution"""
    from .manager_registry import get_error_manager
    return get_error_manager().execute_with_protection(
        func, operation_name, operation_type, config, *args, **kwargs
    )

def get_health_status() -> Dict[str, Any]:
    """Get global health status"""
    from .manager_registry import get_error_manager
    return get_error_manager().get_health_status()

def get_performance_metrics() -> pd.DataFrame:
    """Get global performance metrics"""
    from .manager_registry import get_error_manager
    return get_error_manager().get_performance_metrics()

# Exception classes
class ResourceError(Exception):
    """Raised when insufficient resources are available"""
    pass

class OperationTimeoutError(Exception):
    """Raised when an operation times out"""
    pass

class CircuitBreakerOpenError(Exception):
    """Raised when a circuit breaker is open"""
    pass