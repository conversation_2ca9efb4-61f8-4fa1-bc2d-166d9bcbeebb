"""
Error handling decorators.

This module provides decorators for error handling that can be used
throughout the codebase without creating circular imports.
"""

import functools
import logging
import time
from typing import Any, Callable, Optional, TypeVar, cast

from .error_types import ErrorCategory, ErrorSeverity
from .base_classes import ErrorMetricsCollector

T = TypeVar('T')
F = TypeVar('F', bound=Callable)

_logger = logging.getLogger(__name__)


def with_error_handling(
    category: ErrorCategory,
    severity: ErrorSeverity,
    fallback_value: Any = None,
    retry_count: int = 0,
    retry_delay: float = 1.0,
    log_errors: bool = True
) -> Callable[[F], F]:
    """
    Basic error handling decorator without circular dependencies.
    
    Args:
        category: Error category for classification
        severity: Error severity level
        fallback_value: Value to return on error
        retry_count: Number of retry attempts
        retry_delay: Delay between retries in seconds
        log_errors: Whether to log errors
        
    Returns:
        Decorated function with error handling
    """
    def decorator(func: F) -> F:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(retry_count + 1):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    
                    if attempt < retry_count:
                        if log_errors:
                            _logger.info(
                                f"Retry {attempt+1}/{retry_count} for {func.__name__} "
                                f"after error: {str(e)}"
                            )
                        if retry_delay > 0:
                            time.sleep(retry_delay)
                    else:
                        # Log the final error
                        if log_errors:
                            log_level = {
                                ErrorSeverity.DEBUG: logging.DEBUG,
                                ErrorSeverity.INFO: logging.INFO,
                                ErrorSeverity.WARNING: logging.WARNING,
                                ErrorSeverity.ERROR: logging.ERROR,
                                ErrorSeverity.CRITICAL: logging.CRITICAL
                            }.get(severity, logging.ERROR)
                            
                            _logger.log(
                                log_level,
                                f"{category.value} error in {func.__name__}: {str(e)}",
                                exc_info=(severity.value >= ErrorSeverity.ERROR.value)
                            )
                        
                        # Record metrics if collector is available
                        try:
                            metrics = ErrorMetricsCollector()
                            metrics.get_metric("error_count").inc()
                            metrics.get_metric("error_count_by_category").inc(
                                labels={"category": category.value}
                            )
                            metrics.get_metric("error_count_by_severity").inc(
                                labels={"severity": severity.value}
                            )
                        except Exception:
                            pass  # Ignore metrics errors
                        
                        if fallback_value is not None:
                            return fallback_value
                        raise
            
            if last_exception:
                raise last_exception
                
        return cast(F, wrapper)
    
    return decorator


def with_fallback(
    fallback_value: Any = None,
    fallback_function: Optional[Callable] = None,
    log_errors: bool = True
) -> Callable[[F], F]:
    """
    Simple fallback decorator.
    
    Args:
        fallback_value: Value to return on error
        fallback_function: Function to call for fallback
        log_errors: Whether to log errors
        
    Returns:
        Decorated function with fallback
    """
    def decorator(func: F) -> F:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                if log_errors:
                    _logger.info(f"Using fallback for {func.__name__}: {e}")
                if fallback_function:
                    return fallback_function(*args, **kwargs)
                return fallback_value
                
        return cast(F, wrapper)
    
    return decorator


def with_retry(
    retry_count: int = 3,
    retry_delay: float = 1.0,
    exponential_backoff: bool = True,
    log_retries: bool = True
) -> Callable[[F], F]:
    """
    Simple retry decorator.
    
    Args:
        retry_count: Number of retry attempts
        retry_delay: Base delay between retries
        exponential_backoff: Whether to use exponential backoff
        log_retries: Whether to log retry attempts
        
    Returns:
        Decorated function with retry
    """
    def decorator(func: F) -> F:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            for attempt in range(retry_count + 1):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    if attempt < retry_count:
                        delay = retry_delay * (2 ** attempt if exponential_backoff else 1)
                        if log_retries:
                            _logger.info(
                                f"Retry {attempt+1}/{retry_count} for {func.__name__} "
                                f"after {delay}s: {str(e)}"
                            )
                        time.sleep(delay)
                    else:
                        raise
                        
        return cast(F, wrapper)
    
    return decorator