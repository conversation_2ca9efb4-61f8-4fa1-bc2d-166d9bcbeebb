"""
Graceful Degradation Strategies for Yemen Trade Diagnostic

This module provides degradation strategies for different components,
allowing the system to continue operating with reduced functionality
when failures occur.
"""

import logging
from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum
from typing import Any, Callable, Dict, List, Optional, Type, TypeVar

import pandas as pd
import numpy as np

from .error_types import (
    ErrorCategory, ErrorSeverity, ErrorContext
)
# from yemen_trade_diagnostic.hardware.cache import get_cache  # Removed to avoid circular import

T = TypeVar('T')


class DegradationLevel(Enum):
    """Levels of degradation for system operations"""
    FULL = "full"  # Full functionality
    PARTIAL = "partial"  # Some features disabled
    MINIMAL = "minimal"  # Only essential features
    CACHED = "cached"  # Using cached data only
    UNAVAILABLE = "unavailable"  # Service unavailable


@dataclass
class DegradationContext:
    """Context for degradation decisions"""
    component: str
    operation: str
    error: Exception
    error_category: ErrorCategory
    error_severity: ErrorSeverity
    available_resources: Dict[str, bool]
    cache_available: bool
    fallback_data_available: bool


class DegradationStrategy(ABC):
    """Base class for degradation strategies"""
    
    def __init__(self, name: str):
        self.name = name
        self.logger = logging.getLogger(__name__)
        self.cache = None  # Disabled to avoid circular import
    
    @abstractmethod
    def can_degrade(self, context: DegradationContext) -> bool:
        """Check if this strategy can handle the degradation"""
        pass
    
    @abstractmethod
    def degrade(self, original_func: Callable, 
                context: DegradationContext,
                *args, **kwargs) -> Any:
        """Execute degraded operation"""
        pass
    
    @abstractmethod
    def get_degradation_level(self) -> DegradationLevel:
        """Get the level of degradation this strategy provides"""
        pass


class CachedDataStrategy(DegradationStrategy):
    """Strategy to use cached data when fresh data is unavailable"""
    
    def __init__(self, cache_prefix: str, max_age_hours: int = 24):
        super().__init__("cached_data")
        self.cache_prefix = cache_prefix
        self.max_age_hours = max_age_hours
    
    def can_degrade(self, context: DegradationContext) -> bool:
        """Check if cached data is available"""
        if not context.cache_available:
            return False
        
        # Check if we have cached data for this operation
        cache_key = f"{self.cache_prefix}:{context.component}:{context.operation}"
        return False  # Cache disabled to avoid circular import
    
    def degrade(self, original_func: Callable,
                context: DegradationContext,
                *args, **kwargs) -> Any:
        """Return cached data instead of fresh computation"""
        cache_key = f"{self.cache_prefix}:{context.component}:{context.operation}"
        
        # Cache disabled to avoid circular import
        cached_data = None
        if False:  # cached_data is not None:
            self.logger.warning(
                f"Using cached data for {context.component}.{context.operation} "
                f"due to {context.error_category.value} error"
            )
            return cached_data
        
        # If no cache found, raise the original error
        raise context.error
    
    def get_degradation_level(self) -> DegradationLevel:
        return DegradationLevel.CACHED


class ReducedPrecisionStrategy(DegradationStrategy):
    """Strategy to compute with reduced precision/sampling"""
    
    def __init__(self, sample_fraction: float = 0.1):
        super().__init__("reduced_precision")
        self.sample_fraction = sample_fraction
    
    def can_degrade(self, context: DegradationContext) -> bool:
        """Check if reduced precision is applicable"""
        # Only for calculation and memory errors
        return context.error_category in (
            ErrorCategory.CALCULATION,
            ErrorCategory.MEMORY
        )
    
    def degrade(self, original_func: Callable,
                context: DegradationContext,
                *args, **kwargs) -> Any:
        """Execute function with sampled data"""
        # Look for DataFrame arguments
        modified_args = list(args)
        modified_kwargs = dict(kwargs)
        
        for i, arg in enumerate(args):
            if isinstance(arg, pd.DataFrame):
                # Sample the DataFrame
                sample_size = int(len(arg) * self.sample_fraction)
                modified_args[i] = arg.sample(n=min(sample_size, len(arg)))
                self.logger.warning(
                    f"Reduced DataFrame from {len(arg)} to {len(modified_args[i])} rows"
                )
        
        for key, value in kwargs.items():
            if isinstance(value, pd.DataFrame):
                sample_size = int(len(value) * self.sample_fraction)
                modified_kwargs[key] = value.sample(n=min(sample_size, len(value)))
                self.logger.warning(
                    f"Reduced DataFrame '{key}' from {len(value)} to "
                    f"{len(modified_kwargs[key])} rows"
                )
        
        # Try with reduced data
        try:
            return original_func(*modified_args, **modified_kwargs)
        except Exception as e:
            self.logger.error(f"Reduced precision strategy failed: {str(e)}")
            raise context.error
    
    def get_degradation_level(self) -> DegradationLevel:
        return DegradationLevel.PARTIAL


class SimplifiedCalculationStrategy(DegradationStrategy):
    """Strategy to use simplified calculations"""
    
    def __init__(self, simplification_map: Dict[str, Callable]):
        super().__init__("simplified_calculation")
        self.simplification_map = simplification_map
    
    def can_degrade(self, context: DegradationContext) -> bool:
        """Check if we have a simplified version"""
        operation_key = f"{context.component}.{context.operation}"
        return operation_key in self.simplification_map
    
    def degrade(self, original_func: Callable,
                context: DegradationContext,
                *args, **kwargs) -> Any:
        """Use simplified calculation"""
        operation_key = f"{context.component}.{context.operation}"
        simplified_func = self.simplification_map.get(operation_key)
        
        if simplified_func:
            self.logger.warning(
                f"Using simplified calculation for {operation_key}"
            )
            return simplified_func(*args, **kwargs)
        
        raise context.error
    
    def get_degradation_level(self) -> DegradationLevel:
        return DegradationLevel.PARTIAL


class AggregatedDataStrategy(DegradationStrategy):
    """Strategy to return aggregated/summary data"""
    
    def __init__(self, aggregation_level: str = "month"):
        super().__init__("aggregated_data")
        self.aggregation_level = aggregation_level
    
    def can_degrade(self, context: DegradationContext) -> bool:
        """Check if aggregation is possible"""
        return context.error_category in (
            ErrorCategory.MEMORY,
            ErrorCategory.TIMEOUT,
            ErrorCategory.CALCULATION
        )
    
    def degrade(self, original_func: Callable,
                context: DegradationContext,
                *args, **kwargs) -> Any:
        """Return aggregated data instead of detailed"""
        # This is a placeholder - actual implementation would
        # aggregate data based on the specific operation
        self.logger.warning(
            f"Returning aggregated data for {context.component}.{context.operation}"
        )
        
        # For DataFrames, aggregate by time periods
        for i, arg in enumerate(args):
            if isinstance(arg, pd.DataFrame) and 'date' in arg.columns:
                # Aggregate by the specified level
                return self._aggregate_dataframe(arg)
        
        raise NotImplementedError(
            f"Aggregation not implemented for {context.operation}"
        )
    
    def _aggregate_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """Aggregate DataFrame by time period"""
        if 'date' not in df.columns:
            return df
        
        # Convert to datetime if needed
        df['date'] = pd.to_datetime(df['date'])
        
        # Determine aggregation columns
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        
        # Aggregate
        if self.aggregation_level == "month":
            df['period'] = df['date'].dt.to_period('M')
        elif self.aggregation_level == "quarter":
            df['period'] = df['date'].dt.to_period('Q')
        elif self.aggregation_level == "year":
            df['period'] = df['date'].dt.to_period('Y')
        else:
            df['period'] = df['date'].dt.to_period('M')
        
        # Group and aggregate
        agg_dict = {col: 'sum' for col in numeric_cols if col != 'period'}
        result = df.groupby('period').agg(agg_dict).reset_index()
        
        return result
    
    def get_degradation_level(self) -> DegradationLevel:
        return DegradationLevel.PARTIAL


class GracefulDegradationManager:
    """Manages degradation strategies for different components"""
    
    def __init__(self):
        self.strategies: Dict[str, List[DegradationStrategy]] = {}
        self.default_strategies: List[DegradationStrategy] = []
        self.logger = logging.getLogger(__name__)
        self.current_degradation_level = DegradationLevel.FULL
        
        # Initialize default strategies
        self._init_default_strategies()
    
    def _init_default_strategies(self):
        """Initialize default degradation strategies"""
        self.default_strategies = [
            CachedDataStrategy("default", max_age_hours=24),
            ReducedPrecisionStrategy(sample_fraction=0.1),
            AggregatedDataStrategy(aggregation_level="month")
        ]
    
    def register_strategy(self, component: str, strategy: DegradationStrategy) -> None:
        """Register a degradation strategy for a component"""
        if component not in self.strategies:
            self.strategies[component] = []
        self.strategies[component].append(strategy)
        self.logger.info(
            f"Registered {strategy.name} strategy for component {component}"
        )
    
    def register_default_strategy(self, strategy: DegradationStrategy) -> None:
        """Register a default degradation strategy"""
        self.default_strategies.append(strategy)
    
    def get_fallback(self, component: str, operation: str,
                     error: Exception, original_func: Callable,
                     *args, **kwargs) -> Any:
        """Get appropriate fallback for a failed component"""
        # Create degradation context
        context = DegradationContext(
            component=component,
            operation=operation,
            error=error,
            error_category=ErrorCategory.from_exception(error),
            error_severity=ErrorSeverity.from_exception(error),
            available_resources=self._check_available_resources(),
            cache_available=self._is_cache_available(),
            fallback_data_available=self._is_fallback_data_available()
        )
        
        # Try component-specific strategies first
        strategies_to_try = []
        if component in self.strategies:
            strategies_to_try.extend(self.strategies[component])
        strategies_to_try.extend(self.default_strategies)
        
        # Try each strategy in order
        for strategy in strategies_to_try:
            if strategy.can_degrade(context):
                try:
                    self.logger.info(
                        f"Attempting {strategy.name} degradation for "
                        f"{component}.{operation}"
                    )
                    result = strategy.degrade(original_func, context, *args, **kwargs)
                    self.logger.info(
                        f"Successfully degraded using {strategy.name} "
                        f"(level: {strategy.get_degradation_level().value})"
                    )
                    return result
                except Exception as e:
                    self.logger.warning(
                        f"Degradation strategy {strategy.name} failed: {str(e)}"
                    )
                    continue
        
        # No strategy worked, re-raise original error
        self.logger.error(
            f"No degradation strategy available for {component}.{operation}"
        )
        raise error
    
    def _check_available_resources(self) -> Dict[str, bool]:
        """Check which resources are available"""
        import psutil
        
        memory = psutil.virtual_memory()
        
        return {
            "memory_ok": memory.percent < 80,
            "cpu_ok": psutil.cpu_percent(interval=0.1) < 80,
            "disk_ok": psutil.disk_usage('/').percent < 90
        }
    
    def _is_cache_available(self) -> bool:
        """Check if cache is available"""
        try:
            cache = None  # Disabled to avoid circular import
            # Try a simple operation
            cache.set("_health_check", 1, ttl=1)
            return True
        except:
            return False
    
    def _is_fallback_data_available(self) -> bool:
        """Check if fallback data sources are available"""
        # This would check for backup data sources
        # For now, return True
        return True
    
    def get_degradation_status(self) -> Dict[str, Any]:
        """Get current degradation status"""
        return {
            "registered_components": list(self.strategies.keys()),
            "default_strategies": [s.name for s in self.default_strategies],
            "resources": self._check_available_resources(),
            "cache_available": self._is_cache_available()
        }


# Pipeline-specific degradation strategies

class RCAPipelineDegradation(DegradationStrategy):
    """Degradation strategy for RCA pipeline"""
    
    def __init__(self):
        super().__init__("rca_pipeline_degradation")
        self.min_products = 10  # Minimum products for meaningful RCA
    
    def can_degrade(self, context: DegradationContext) -> bool:
        """RCA can degrade for memory/calculation errors"""
        return context.component == "rca_pipeline" and \
               context.error_category in (ErrorCategory.MEMORY, ErrorCategory.CALCULATION)
    
    def degrade(self, original_func: Callable,
                context: DegradationContext,
                *args, **kwargs) -> Any:
        """Use cached RCA values or simplified calculation"""
        # First try cache
        cache_key = f"rca:{kwargs.get('country', 'default')}:{kwargs.get('year', 'latest')}"
        cached_rca = self.cache.get(cache_key)
        
        if cached_rca is not None:
            self.logger.info("Using cached RCA values")
            return cached_rca
        
        # Try simplified calculation with top products only
        if args and isinstance(args[0], pd.DataFrame):
            df = args[0]
            
            # Get top products by export value
            if 'export_value' in df.columns:
                top_products = df.nlargest(100, 'export_value')['product_code'].unique()
                filtered_df = df[df['product_code'].isin(top_products)]
                
                self.logger.info(
                    f"Calculating RCA for top {len(top_products)} products only"
                )
                
                # Try with filtered data
                modified_args = list(args)
                modified_args[0] = filtered_df
                return original_func(*modified_args, **kwargs)
        
        raise context.error
    
    def get_degradation_level(self) -> DegradationLevel:
        return DegradationLevel.PARTIAL


class MarketPipelineDegradation(DegradationStrategy):
    """Degradation strategy for Market Analysis pipeline"""
    
    def __init__(self):
        super().__init__("market_pipeline_degradation")
    
    def can_degrade(self, context: DegradationContext) -> bool:
        """Market analysis can degrade for various errors"""
        return context.component == "market_pipeline"
    
    def degrade(self, original_func: Callable,
                context: DegradationContext,
                *args, **kwargs) -> Any:
        """Return partial market data or aggregated results"""
        # For market concentration, return cached or top markets only
        if context.operation == "calculate_concentration":
            # Try cache first
            cache_key = f"market_concentration:{kwargs.get('year', 'latest')}"
            cached_data = self.cache.get(cache_key)
            if cached_data:
                return cached_data
            
            # Calculate for top markets only
            if args and isinstance(args[0], pd.DataFrame):
                df = args[0]
                if 'partner' in df.columns and 'export_value' in df.columns:
                    # Get top 20 partners
                    top_partners = df.groupby('partner')['export_value'].sum()\
                                    .nlargest(20).index
                    filtered_df = df[df['partner'].isin(top_partners)]
                    
                    modified_args = list(args)
                    modified_args[0] = filtered_df
                    return original_func(*modified_args, **kwargs)
        
        raise context.error
    
    def get_degradation_level(self) -> DegradationLevel:
        return DegradationLevel.PARTIAL


class VisualizationDegradation(DegradationStrategy):
    """Degradation strategy for visualization generation"""
    
    def __init__(self):
        super().__init__("visualization_degradation")
    
    def can_degrade(self, context: DegradationContext) -> bool:
        """Visualization can always degrade"""
        return "visualization" in context.component or \
               context.operation.startswith("plot_") or \
               context.operation.startswith("generate_chart")
    
    def degrade(self, original_func: Callable,
                context: DegradationContext,
                *args, **kwargs) -> Any:
        """Generate simplified charts or return cached visualizations"""
        # Try cached chart first
        cache_key = f"chart:{context.component}:{context.operation}"
        cached_chart = self.cache.get(cache_key)
        if cached_chart:
            self.logger.info("Returning cached visualization")
            return cached_chart
        
        # Simplify chart parameters
        simplified_kwargs = dict(kwargs)
        
        # Reduce data points
        if 'data' in kwargs and isinstance(kwargs['data'], pd.DataFrame):
            df = kwargs['data']
            if len(df) > 1000:
                # Sample or aggregate
                simplified_kwargs['data'] = df.sample(n=1000)
        
        # Disable animations and complex features
        simplified_kwargs.update({
            'animate': False,
            'show_tooltips': False,
            'interactive': False,
            'high_quality': False
        })
        
        try:
            return original_func(*args, **simplified_kwargs)
        except:
            # Return a placeholder message
            return {
                'status': 'degraded',
                'message': f'Chart temporarily unavailable: {context.operation}',
                'error': str(context.error)
            }
    
    def get_degradation_level(self) -> DegradationLevel:
        return DegradationLevel.MINIMAL


# Global manager instance
_degradation_manager: Optional[GracefulDegradationManager] = None


def get_degradation_manager() -> GracefulDegradationManager:
    """Get the global degradation manager"""
    global _degradation_manager
    if _degradation_manager is None:
        _degradation_manager = GracefulDegradationManager()
        
        # Register pipeline-specific strategies
        _degradation_manager.register_strategy(
            "rca_pipeline", RCAPipelineDegradation()
        )
        _degradation_manager.register_strategy(
            "market_pipeline", MarketPipelineDegradation()
        )
        _degradation_manager.register_strategy(
            "visualization", VisualizationDegradation()
        )
    
    return _degradation_manager