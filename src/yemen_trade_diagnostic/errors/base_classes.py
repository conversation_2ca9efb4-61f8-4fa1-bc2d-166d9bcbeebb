"""
Base classes for error handling that were previously in error_interface.

This module provides the essential error handling classes that are needed
but weren't part of the enhanced error modules.
"""

import logging
import threading
from collections import defaultdict, deque
from typing import Any, Dict, List, Optional
from abc import ABC, abstractmethod

from .error_types import <PERSON>rror<PERSON>ategor<PERSON>, ErrorSeverity, ErrorContext, MetricType


class ErrorCollector:
    """
    Collector for validation and processing errors.
    
    Aggregates errors during validation or processing operations,
    allowing for batch error reporting and analysis.
    """
    
    def __init__(self):
        """Initialize the error collector."""
        self._errors: List[ErrorContext] = []
        self._warnings: List[ErrorContext] = []
        self._lock = threading.RLock()
        self._error_count_by_severity = defaultdict(int)
        self._error_count_by_category = defaultdict(int)
    
    def add_error(self, 
                  error: Exception,
                  context: Optional[Dict[str, Any]] = None,
                  severity: Optional[ErrorSeverity] = None,
                  category: Optional[ErrorCategory] = None) -> None:
        """Add an error to the collection."""
        with self._lock:
            if severity is None:
                severity = ErrorSeverity.from_exception(error)
            if category is None:
                category = ErrorCategory.from_exception(error)
                
            error_context = ErrorContext(
                exception=error,
                category=category,
                severity=severity,
                component=context.get('component', 'unknown') if context else 'unknown',
                operation=context.get('operation', 'unknown') if context else 'unknown',
                extra=context or {}
            )
            
            if severity.value >= ErrorSeverity.ERROR.value:
                self._errors.append(error_context)
            else:
                self._warnings.append(error_context)
                
            self._error_count_by_severity[severity] += 1
            self._error_count_by_category[category] += 1
    
    def add_validation_error(self, 
                           field: str,
                           message: str,
                           value: Any = None,
                           row: Optional[int] = None) -> None:
        """Add a validation error."""
        context = {
            'field': field,
            'value': value,
            'row': row,
            'component': 'validation',
            'operation': 'validate_field'
        }
        error = ValueError(f"{field}: {message}")
        self.add_error(error, context, ErrorSeverity.ERROR, ErrorCategory.VALIDATION)
    
    def clear(self) -> None:
        """Clear all collected errors."""
        with self._lock:
            self._errors.clear()
            self._warnings.clear()
            self._error_count_by_severity.clear()
            self._error_count_by_category.clear()
    
    def has_errors(self) -> bool:
        """Check if any errors have been collected."""
        return len(self._errors) > 0
    
    def has_warnings(self) -> bool:
        """Check if any warnings have been collected."""
        return len(self._warnings) > 0
    
    def get_errors(self) -> List[ErrorContext]:
        """Get all collected errors."""
        with self._lock:
            return self._errors.copy()
    
    def get_warnings(self) -> List[ErrorContext]:
        """Get all collected warnings."""
        with self._lock:
            return self._warnings.copy()
    
    def get_summary(self) -> Dict[str, Any]:
        """Get a summary of collected errors."""
        with self._lock:
            return {
                'total_errors': len(self._errors),
                'total_warnings': len(self._warnings),
                'errors_by_severity': dict(self._error_count_by_severity),
                'errors_by_category': dict(self._error_count_by_category),
                'error_messages': [str(e.exception) for e in self._errors[:10]],
                'warning_messages': [str(e.exception) for e in self._warnings[:10]]
            }
    
    def raise_if_errors(self) -> None:
        """Raise an exception if any errors have been collected."""
        if self.has_errors():
            summary = self.get_summary()
            error_msg = f"Validation failed with {summary['total_errors']} errors"
            if summary['error_messages']:
                error_msg += f": {'; '.join(summary['error_messages'][:3])}"
                if len(summary['error_messages']) > 3:
                    error_msg += f" and {len(summary['error_messages']) - 3} more"
            raise ValueError(error_msg)


class ErrorMetric:
    """Metric for tracking error statistics."""
    
    def __init__(self, name: str, description: str, metric_type: MetricType):
        """Initialize a metric."""
        self.name = name
        self.description = description
        self.metric_type = metric_type
        self._lock = threading.RLock()
        
        if metric_type == MetricType.COUNTER:
            self._counter = 0
            self._counters = defaultdict(int)
        elif metric_type == MetricType.GAUGE:
            self._value = 0.0
            self._values = defaultdict(float)
        elif metric_type == MetricType.HISTOGRAM:
            self._samples = []
            self._samples_by_label = defaultdict(list)
            self._sample_limit = 1000
    
    def inc(self, amount: float = 1.0, labels: Optional[Dict[str, str]] = None) -> None:
        """Increment counter metric."""
        if self.metric_type != MetricType.COUNTER:
            raise ValueError(f"Cannot increment metric of type {self.metric_type}")
        
        with self._lock:
            if labels:
                label_key = self._labels_to_key(labels)
                self._counters[label_key] += amount
            else:
                self._counter += amount
    
    def set(self, value: float, labels: Optional[Dict[str, str]] = None) -> None:
        """Set gauge metric value."""
        if self.metric_type != MetricType.GAUGE:
            raise ValueError(f"Cannot set value for metric of type {self.metric_type}")
        
        with self._lock:
            if labels:
                label_key = self._labels_to_key(labels)
                self._values[label_key] = value
            else:
                self._value = value
    
    def observe(self, value: float, labels: Optional[Dict[str, str]] = None) -> None:
        """Observe a value for histogram metric."""
        if self.metric_type != MetricType.HISTOGRAM:
            raise ValueError(f"Cannot observe value for metric of type {self.metric_type}")
        
        with self._lock:
            if labels:
                label_key = self._labels_to_key(labels)
                self._samples_by_label[label_key].append(value)
                if len(self._samples_by_label[label_key]) > self._sample_limit:
                    self._samples_by_label[label_key] = (
                        self._samples_by_label[label_key][-self._sample_limit:]
                    )
            else:
                self._samples.append(value)
                if len(self._samples) > self._sample_limit:
                    self._samples = self._samples[-self._sample_limit:]
    
    def _labels_to_key(self, labels: Dict[str, str]) -> str:
        """Convert labels dictionary to string key."""
        return "|".join(f"{k}={v}" for k, v in sorted(labels.items()))
    
    def get_snapshot(self) -> Dict[str, Any]:
        """Get a snapshot of the metric."""
        with self._lock:
            result = {
                "name": self.name,
                "description": self.description,
                "type": self.metric_type.value
            }
            
            if self.metric_type == MetricType.COUNTER:
                result["value"] = self._counter
                result["values_by_label"] = dict(self._counters)
            elif self.metric_type == MetricType.GAUGE:
                result["value"] = self._value
                result["values_by_label"] = dict(self._values)
            elif self.metric_type == MetricType.HISTOGRAM:
                samples = self._samples
                result["count"] = len(samples)
                result["sum"] = sum(samples) if samples else 0
                result["min"] = min(samples) if samples else 0
                result["max"] = max(samples) if samples else 0
                result["avg"] = sum(samples) / len(samples) if samples else 0
            
            return result


class ErrorMetricsCollector:
    """Collector for error-related metrics."""
    
    _instance = None
    
    def __new__(cls):
        """Implement the Singleton pattern."""
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        """Initialize the metrics collector."""
        if getattr(self, '_initialized', False):
            return
        
        self._lock = threading.RLock()
        self._metrics = {}
        self._create_standard_metrics()
        self._initialized = True
    
    def _create_standard_metrics(self):
        """Create standard error metrics."""
        self.register_metric("error_count", "Total number of errors", MetricType.COUNTER)
        self.register_metric("error_count_by_category", "Number of errors by category", MetricType.COUNTER)
        self.register_metric("error_count_by_severity", "Number of errors by severity", MetricType.COUNTER)
        self.register_metric("recovery_attempts", "Number of recovery attempts", MetricType.COUNTER)
        self.register_metric("successful_recoveries", "Number of successful recoveries", MetricType.COUNTER)
        self.register_metric("circuit_breaker_trips", "Number of circuit breaker trips", MetricType.COUNTER)
        self.register_metric("circuit_breaker_state", "Current circuit breaker state", MetricType.GAUGE)
    
    def register_metric(self, name: str, description: str, metric_type: MetricType) -> ErrorMetric:
        """Register a new metric."""
        with self._lock:
            if name in self._metrics:
                return self._metrics[name]
            metric = ErrorMetric(name, description, metric_type)
            self._metrics[name] = metric
            return metric
    
    def get_metric(self, name: str) -> Optional[ErrorMetric]:
        """Get a metric by name."""
        return self._metrics.get(name)
    
    def record_error(self, error_context: ErrorContext) -> None:
        """Record metrics for an error."""
        self.get_metric("error_count").inc()
        self.get_metric("error_count_by_category").inc(
            labels={"category": error_context.category.value}
        )
        self.get_metric("error_count_by_severity").inc(
            labels={"severity": error_context.severity.value}
        )
    
    def record_circuit_breaker_trip(self, component: str, operation: str) -> None:
        """Record a circuit breaker trip."""
        self.get_metric("circuit_breaker_trips").inc(
            labels={"component": component, "operation": operation}
        )
    
    def update_circuit_breaker_state(self, component: str, operation: str, state: Any) -> None:
        """Update circuit breaker state metric."""
        from .error_types import CircuitBreakerState
        state_value = {
            CircuitBreakerState.CLOSED: 0,
            CircuitBreakerState.HALF_OPEN: 1,
            CircuitBreakerState.OPEN: 2
        }.get(state, -1)
        
        self.get_metric("circuit_breaker_state").set(
            state_value,
            labels={"component": component, "operation": operation}
        )
    
    def get_all_metrics(self) -> Dict[str, Dict[str, Any]]:
        """Get snapshots of all metrics."""
        return {name: metric.get_snapshot() for name, metric in self._metrics.items()}


class ErrorPolicy(ABC):
    """Abstract base class for error handling policies."""
    
    @abstractmethod
    def should_handle(self, error_context: ErrorContext) -> bool:
        """Check if this policy should handle the error."""
        pass
    
    @abstractmethod
    def handle_error(self, error_context: ErrorContext) -> bool:
        """Handle an error according to the policy."""
        pass


class RetryPolicy(ErrorPolicy):
    """Policy for retrying operations on failure."""
    
    def __init__(self, categories: set, max_retries: int = 3):
        self.categories = categories
        self.max_retries = max_retries
    
    def should_handle(self, error_context: ErrorContext) -> bool:
        return error_context.category in self.categories
    
    def handle_error(self, error_context: ErrorContext) -> bool:
        # This is a placeholder - actual retry logic would be in decorators
        error_context.recovery_attempted = True
        error_context.recovery_strategy = "retry"
        return False


class FallbackPolicy(ErrorPolicy):
    """Policy for using fallback values on failure."""
    
    def __init__(self, categories: set, default_value: Any = None):
        self.categories = categories
        self.default_value = default_value
    
    def should_handle(self, error_context: ErrorContext) -> bool:
        return error_context.category in self.categories
    
    def handle_error(self, error_context: ErrorContext) -> bool:
        error_context.recovery_attempted = True
        error_context.recovery_strategy = "fallback"
        error_context.recovery_succeeded = True
        return True


class CircuitBreakerPolicy(ErrorPolicy):
    """Policy for implementing circuit breaker pattern."""
    
    def __init__(self, categories: set):
        self.categories = categories
    
    def should_handle(self, error_context: ErrorContext) -> bool:
        return error_context.category in self.categories
    
    def handle_error(self, error_context: ErrorContext) -> bool:
        error_context.recovery_attempted = True
        error_context.recovery_strategy = "circuit_breaker"
        return False