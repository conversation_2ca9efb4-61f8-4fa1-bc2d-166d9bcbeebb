"""
Resource Manager for Graceful Degradation

This module manages system resources and coordinates degradation decisions
based on resource availability and system state.
"""

import logging
import os
import threading
import time
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Callable, Dict, List, Optional, Set, Tuple

import psutil
import pandas as pd

from .error_types import ErrorCategory


class ResourceType(Enum):
    """Types of system resources"""
    MEMORY = "memory"
    CPU = "cpu"
    DISK = "disk"
    NETWORK = "network"
    CACHE = "cache"
    DATABASE = "database"


class ResourceStatus(Enum):
    """Resource availability status"""
    HEALTHY = "healthy"  # Resource is fully available
    CONSTRAINED = "constrained"  # Resource is limited
    CRITICAL = "critical"  # Resource is critically low
    UNAVAILABLE = "unavailable"  # Resource is not available


@dataclass
class ResourceMetrics:
    """Metrics for a specific resource"""
    resource_type: ResourceType
    status: ResourceStatus
    usage_percent: float
    available: float
    total: float
    timestamp: float
    details: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ResourceConstraints:
    """Constraints for resource usage"""
    max_memory_mb: float = 1000  # Maximum memory per operation
    max_cpu_percent: float = 80  # Maximum CPU usage
    max_execution_time: float = 300  # Maximum execution time in seconds
    max_concurrent_operations: int = 5  # Maximum concurrent operations
    cache_size_mb: float = 500  # Maximum cache size
    
    # Thresholds for status determination
    memory_warning_threshold: float = 70  # % for warning
    memory_critical_threshold: float = 85  # % for critical
    cpu_warning_threshold: float = 70
    cpu_critical_threshold: float = 85
    disk_warning_threshold: float = 80
    disk_critical_threshold: float = 90


class ResourceManager:
    """Manages system resources for graceful degradation"""
    
    def __init__(self, constraints: Optional[ResourceConstraints] = None):
        self.constraints = constraints or ResourceConstraints()
        self.logger = logging.getLogger(__name__)
        self._cache = None  # Lazy initialization to avoid circular import
        self._disable_cache_checks = os.environ.get('DISABLE_CACHE_HEALTH_CHECKS', 'false').lower() == 'true'
        
        # Resource monitoring
        self._resource_metrics: Dict[ResourceType, ResourceMetrics] = {}
        self._operation_registry: Dict[str, Dict[str, Any]] = {}
        self._active_operations: Set[str] = set()
        
        # Thread safety
        self._lock = threading.RLock()
        
        # Monitoring thread
        self._monitor_thread = None
        self._stop_monitoring = threading.Event()
        self._monitoring_interval = 5.0  # seconds
        
        # Start monitoring
        if not os.environ.get('DISABLE_RESOURCE_MONITORING', 'false').lower() == 'true':
            self._start_monitoring()
    
    @property
    def cache(self):
        """Lazy load cache to avoid circular import."""
        if self._cache is None:
            from yemen_trade_diagnostic.hardware.cache import get_cache
            self._cache = get_cache()
        return self._cache
    
    def _start_monitoring(self):
        """Start resource monitoring thread"""
        def monitor_loop():
            while not self._stop_monitoring.wait(self._monitoring_interval):
                self._update_resource_metrics()
        
        self._monitor_thread = threading.Thread(
            target=monitor_loop,
            name="ResourceMonitor",
            daemon=True
        )
        self._monitor_thread.start()
        
        # Initial metrics update
        self._update_resource_metrics()
    
    def _update_resource_metrics(self):
        """Update current resource metrics"""
        try:
            # Memory metrics
            memory = psutil.virtual_memory()
            memory_status = self._get_status(
                memory.percent,
                self.constraints.memory_warning_threshold,
                self.constraints.memory_critical_threshold
            )
            
            self._resource_metrics[ResourceType.MEMORY] = ResourceMetrics(
                resource_type=ResourceType.MEMORY,
                status=memory_status,
                usage_percent=memory.percent,
                available=memory.available / (1024 * 1024),  # MB
                total=memory.total / (1024 * 1024),  # MB
                timestamp=time.time(),
                details={
                    'used_mb': memory.used / (1024 * 1024),
                    'cached_mb': getattr(memory, 'cached', 0) / (1024 * 1024),
                    'buffers_mb': getattr(memory, 'buffers', 0) / (1024 * 1024)
                }
            )
            
            # CPU metrics
            cpu_percent = psutil.cpu_percent(interval=0.1)
            cpu_status = self._get_status(
                cpu_percent,
                self.constraints.cpu_warning_threshold,
                self.constraints.cpu_critical_threshold
            )
            
            self._resource_metrics[ResourceType.CPU] = ResourceMetrics(
                resource_type=ResourceType.CPU,
                status=cpu_status,
                usage_percent=cpu_percent,
                available=100 - cpu_percent,
                total=100,
                timestamp=time.time(),
                details={
                    'cpu_count': psutil.cpu_count(),
                    'load_average': getattr(psutil, 'getloadavg', lambda: (0, 0, 0))()
                }
            )
            
            # Disk metrics
            disk = psutil.disk_usage('/')
            disk_status = self._get_status(
                disk.percent,
                self.constraints.disk_warning_threshold,
                self.constraints.disk_critical_threshold
            )
            
            self._resource_metrics[ResourceType.DISK] = ResourceMetrics(
                resource_type=ResourceType.DISK,
                status=disk_status,
                usage_percent=disk.percent,
                available=disk.free / (1024 * 1024 * 1024),  # GB
                total=disk.total / (1024 * 1024 * 1024),  # GB
                timestamp=time.time(),
                details={
                    'used_gb': disk.used / (1024 * 1024 * 1024)
                }
            )
            
            # Cache metrics (skip if disabled to avoid circular dependencies)
            if not self._disable_cache_checks:
                cache_status = self._check_cache_status()
                self._resource_metrics[ResourceType.CACHE] = cache_status
            
        except Exception as e:
            self.logger.error(f"Failed to update resource metrics: {str(e)}")
    
    def _get_status(self, usage_percent: float,
                    warning_threshold: float,
                    critical_threshold: float) -> ResourceStatus:
        """Determine resource status based on usage"""
        if usage_percent >= critical_threshold:
            return ResourceStatus.CRITICAL
        elif usage_percent >= warning_threshold:
            return ResourceStatus.CONSTRAINED
        else:
            return ResourceStatus.HEALTHY
    
    def _check_cache_status(self) -> ResourceMetrics:
        """Check cache availability and metrics"""
        try:
            # Try cache operation
            test_key = "_resource_manager_health_check"
            # Use error context to properly handle any exceptions
            from yemen_trade_diagnostic.errors import error_context, OperationType
            
            with error_context("cache_health_check", OperationType.DATA_LOADING):
                self.cache.set(test_key, 1, ttl=1)
                self.cache.get(test_key)
            
            # Get cache stats if available
            cache_stats = getattr(self.cache, 'get_stats', lambda: {})()
            
            return ResourceMetrics(
                resource_type=ResourceType.CACHE,
                status=ResourceStatus.HEALTHY,
                usage_percent=cache_stats.get('usage_percent', 0),
                available=cache_stats.get('available_mb', 100),
                total=cache_stats.get('total_mb', 100),
                timestamp=time.time(),
                details=cache_stats
            )
        except Exception as e:
            self.logger.warning(f"Cache health check failed: {str(e)}")
            return ResourceMetrics(
                resource_type=ResourceType.CACHE,
                status=ResourceStatus.UNAVAILABLE,
                usage_percent=100,
                available=0,
                total=0,
                timestamp=time.time(),
                details={'error': str(e)}
            )
    
    def can_execute_operation(self, operation_name: str) -> bool:
        """
        Check if an operation can be executed based on current resources.
        
        Args:
            operation_name: Name of the operation
            
        Returns:
            bool: True if operation can proceed, False otherwise
        """
        # Check if resources are critically low
        for resource_type, metrics in self._resource_metrics.items():
            if metrics.status == ResourceStatus.CRITICAL:
                self.logger.warning(
                    f"Cannot execute {operation_name}: {resource_type.value} is critical"
                )
                return False
        
        # Check concurrent operations limit
        if len(self._active_operations) >= self.constraints.max_concurrent_operations:
            self.logger.warning(
                f"Cannot execute {operation_name}: concurrent operations limit reached"
            )
            return False
        
        return True
    
    def register_operation(self, operation_id: str,
                          component: str,
                          resource_requirements: Dict[str, Any]) -> bool:
        """
        Register an operation and check if resources are available
        
        Args:
            operation_id: Unique operation identifier
            component: Component performing the operation
            resource_requirements: Expected resource requirements
            
        Returns:
            True if operation can proceed, False otherwise
        """
        with self._lock:
            # Check concurrent operations limit
            if len(self._active_operations) >= self.constraints.max_concurrent_operations:
                self.logger.warning(
                    f"Concurrent operations limit reached ({self.constraints.max_concurrent_operations})"
                )
                return False
            
            # Check resource availability
            if not self._check_resource_availability(resource_requirements):
                return False
            
            # Register operation
            self._operation_registry[operation_id] = {
                'component': component,
                'requirements': resource_requirements,
                'start_time': time.time(),
                'status': 'active'
            }
            self._active_operations.add(operation_id)
            
            self.logger.info(f"Registered operation {operation_id} for {component}")
            return True
    
    def _check_resource_availability(self, requirements: Dict[str, Any]) -> bool:
        """Check if required resources are available"""
        # Check memory
        if 'memory_mb' in requirements:
            memory_metrics = self._resource_metrics.get(ResourceType.MEMORY)
            if memory_metrics and memory_metrics.available < requirements['memory_mb']:
                self.logger.warning(
                    f"Insufficient memory: {memory_metrics.available:.1f}MB available, "
                    f"{requirements['memory_mb']}MB required"
                )
                return False
        
        # Check CPU
        cpu_metrics = self._resource_metrics.get(ResourceType.CPU)
        if cpu_metrics and cpu_metrics.status == ResourceStatus.CRITICAL:
            self.logger.warning("CPU usage is critical")
            return False
        
        # Check disk
        if 'disk_gb' in requirements:
            disk_metrics = self._resource_metrics.get(ResourceType.DISK)
            if disk_metrics and disk_metrics.available < requirements['disk_gb']:
                self.logger.warning(
                    f"Insufficient disk space: {disk_metrics.available:.1f}GB available, "
                    f"{requirements['disk_gb']}GB required"
                )
                return False
        
        return True
    
    def unregister_operation(self, operation_id: str) -> None:
        """Unregister a completed operation"""
        with self._lock:
            if operation_id in self._active_operations:
                self._active_operations.remove(operation_id)
                
                if operation_id in self._operation_registry:
                    self._operation_registry[operation_id]['status'] = 'completed'
                    self._operation_registry[operation_id]['end_time'] = time.time()
                
                self.logger.info(f"Unregistered operation {operation_id}")
    
    def get_resource_status(self) -> Dict[ResourceType, ResourceMetrics]:
        """Get current resource status"""
        return dict(self._resource_metrics)
    
    def get_degradation_recommendations(self) -> Dict[str, Any]:
        """Get recommendations for degradation based on resources"""
        recommendations = {
            'should_degrade': False,
            'degradation_level': 'none',
            'reasons': [],
            'suggested_actions': []
        }
        
        # Check each resource
        for resource_type, metrics in self._resource_metrics.items():
            if metrics.status == ResourceStatus.CRITICAL:
                recommendations['should_degrade'] = True
                recommendations['degradation_level'] = 'severe'
                recommendations['reasons'].append(
                    f"{resource_type.value} is critical ({metrics.usage_percent:.1f}%)"
                )
                
                if resource_type == ResourceType.MEMORY:
                    recommendations['suggested_actions'].extend([
                        'Use sampling for large datasets',
                        'Enable caching for computed results',
                        'Reduce batch sizes'
                    ])
                elif resource_type == ResourceType.CPU:
                    recommendations['suggested_actions'].extend([
                        'Disable complex calculations',
                        'Use pre-computed values',
                        'Reduce parallelism'
                    ])
                elif resource_type == ResourceType.DISK:
                    recommendations['suggested_actions'].extend([
                        'Clean temporary files',
                        'Use in-memory processing',
                        'Compress cached data'
                    ])
                    
            elif metrics.status == ResourceStatus.CONSTRAINED:
                if not recommendations['should_degrade']:
                    recommendations['should_degrade'] = True
                    recommendations['degradation_level'] = 'moderate'
                recommendations['reasons'].append(
                    f"{resource_type.value} is constrained ({metrics.usage_percent:.1f}%)"
                )
        
        return recommendations
    
    def estimate_operation_feasibility(self, 
                                     operation: str,
                                     estimated_requirements: Dict[str, Any]) -> Dict[str, Any]:
        """
        Estimate if an operation is feasible given current resources
        
        Args:
            operation: Operation name
            estimated_requirements: Estimated resource requirements
            
        Returns:
            Feasibility assessment
        """
        assessment = {
            'feasible': True,
            'confidence': 1.0,
            'constraints': [],
            'suggestions': []
        }
        
        # Check memory
        if 'memory_mb' in estimated_requirements:
            memory_metrics = self._resource_metrics.get(ResourceType.MEMORY)
            if memory_metrics:
                if memory_metrics.available < estimated_requirements['memory_mb']:
                    assessment['feasible'] = False
                    assessment['constraints'].append('insufficient_memory')
                    assessment['suggestions'].append(
                        f"Need {estimated_requirements['memory_mb'] - memory_metrics.available:.1f}MB more memory"
                    )
                elif memory_metrics.available < estimated_requirements['memory_mb'] * 1.5:
                    assessment['confidence'] *= 0.7
                    assessment['suggestions'].append("Memory usage will be tight")
        
        # Check concurrent operations
        if len(self._active_operations) >= self.constraints.max_concurrent_operations - 1:
            assessment['confidence'] *= 0.5
            assessment['constraints'].append('high_concurrency')
            assessment['suggestions'].append("Consider waiting for other operations to complete")
        
        # Check historical success
        historical_success = self._get_historical_success_rate(operation)
        if historical_success < 0.8:
            assessment['confidence'] *= historical_success
            assessment['suggestions'].append(
                f"Historical success rate is {historical_success:.1%}"
            )
        
        return assessment
    
    def _get_historical_success_rate(self, operation: str) -> float:
        """Get historical success rate for an operation type"""
        # This would query historical data
        # For now, return a default value
        return 0.9
    
    def wait_for_resources(self, 
                          required_resources: Dict[str, Any],
                          timeout: float = 30.0) -> bool:
        """
        Wait for resources to become available
        
        Args:
            required_resources: Required resources
            timeout: Maximum wait time in seconds
            
        Returns:
            True if resources became available, False if timeout
        """
        start_time = time.time()
        check_interval = 1.0
        
        while time.time() - start_time < timeout:
            if self._check_resource_availability(required_resources):
                return True
            
            # Wait before next check
            time.sleep(check_interval)
            
            # Update metrics
            self._update_resource_metrics()
        
        return False
    
    def get_resource_allocation_strategy(self) -> Dict[str, Any]:
        """Get current resource allocation strategy"""
        strategy = {
            'memory_allocation': {},
            'cpu_allocation': {},
            'priority_components': [],
            'throttled_components': []
        }
        
        # Determine allocation based on current state
        memory_metrics = self._resource_metrics.get(ResourceType.MEMORY)
        if memory_metrics:
            if memory_metrics.status == ResourceStatus.CRITICAL:
                strategy['memory_allocation'] = {
                    'mode': 'strict',
                    'max_per_operation_mb': self.constraints.max_memory_mb * 0.5
                }
                strategy['throttled_components'].extend(['visualization', 'analysis'])
            elif memory_metrics.status == ResourceStatus.CONSTRAINED:
                strategy['memory_allocation'] = {
                    'mode': 'conservative',
                    'max_per_operation_mb': self.constraints.max_memory_mb * 0.75
                }
            else:
                strategy['memory_allocation'] = {
                    'mode': 'normal',
                    'max_per_operation_mb': self.constraints.max_memory_mb
                }
        
        # Priority components always get resources
        strategy['priority_components'] = ['rca_pipeline', 'data_loader']
        
        return strategy
    
    def get_status(self) -> Dict[str, Any]:
        """Get current resource manager status."""
        return {
            'resource_metrics': self.get_resource_status(),
            'active_operations': list(self._active_operations),
            'degradation_recommendations': self.get_degradation_recommendations(),
            'allocation_strategy': self.get_resource_allocation_strategy()
        }
    
    def cleanup(self):
        """Clean up resources"""
        self._stop_monitoring.set()
        if self._monitor_thread:
            self._monitor_thread.join(timeout=5)


class ResourceAwareDecorator:
    """Decorator for resource-aware function execution"""
    
    def __init__(self, resource_manager: ResourceManager):
        self.resource_manager = resource_manager
        self.logger = logging.getLogger(__name__)
    
    def with_resources(self,
                      memory_mb: Optional[float] = None,
                      timeout: float = 300,
                      component: str = "unknown"):
        """
        Decorator to manage resources for a function
        
        Args:
            memory_mb: Expected memory usage
            timeout: Maximum execution time
            component: Component name
        """
        def decorator(func: Callable) -> Callable:
            def wrapper(*args, **kwargs):
                operation_id = f"{component}:{func.__name__}:{time.time()}"
                
                # Estimate requirements
                requirements = {}
                if memory_mb:
                    requirements['memory_mb'] = memory_mb
                
                # Check feasibility
                feasibility = self.resource_manager.estimate_operation_feasibility(
                    func.__name__,
                    requirements
                )
                
                if not feasibility['feasible']:
                    raise ResourceError(
                        f"Insufficient resources for {func.__name__}: "
                        f"{', '.join(feasibility['constraints'])}"
                    )
                
                # Register operation
                if not self.resource_manager.register_operation(
                    operation_id, component, requirements
                ):
                    # Get degradation recommendations
                    recommendations = self.resource_manager.get_degradation_recommendations()
                    
                    if recommendations['should_degrade']:
                        self.logger.warning(
                            f"Resources constrained, degradation recommended: "
                            f"{recommendations['degradation_level']}"
                        )
                        # This would trigger degradation logic
                        raise ResourceConstraintError(
                            "Resources constrained",
                            recommendations=recommendations
                        )
                    else:
                        raise ResourceError("Unable to allocate resources")
                
                try:
                    # Execute function
                    result = func(*args, **kwargs)
                    return result
                    
                finally:
                    # Always unregister
                    self.resource_manager.unregister_operation(operation_id)
            
            return wrapper
        return decorator


class ResourceError(Exception):
    """Base exception for resource-related errors"""
    pass


class ResourceConstraintError(ResourceError):
    """Exception raised when resources are constrained"""
    
    def __init__(self, message: str, recommendations: Dict[str, Any]):
        super().__init__(message)
        self.recommendations = recommendations


# Global resource manager instance
_resource_manager: Optional[ResourceManager] = None


def get_resource_manager() -> ResourceManager:
    """Get the global resource manager"""
    global _resource_manager
    if _resource_manager is None:
        _resource_manager = ResourceManager()
    return _resource_manager