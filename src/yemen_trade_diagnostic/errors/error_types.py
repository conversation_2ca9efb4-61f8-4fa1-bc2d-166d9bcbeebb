"""
Base error types and enums for Yemen Trade Diagnostic.

This module contains the fundamental error types that are used
throughout the error handling system.
"""

from enum import Enum, auto
from typing import Any, Dict, Optional


class ErrorCategory(Enum):
    """Categories of errors for classification and policy application."""
    VALIDATION = "validation"        # Data validation errors
    DATA_ACCESS = "data_access"      # Problems accessing data sources
    CALCULATION = "calculation"      # Errors in computation or analysis
    HARDWARE = "hardware"            # Hardware-related failures
    PIPELINE = "pipeline"            # Pipeline execution issues
    VISUALIZATION = "visualization"  # Chart generation errors
    CONFIGURATION = "configuration"  # Configuration problems
    NETWORK = "network"              # Network-related errors
    TIMEOUT = "timeout"              # Time limit exceeded errors
    MEMORY = "memory"                # Memory-related issues
    SYSTEM = "system"                # System-level problems
    UNKNOWN = "unknown"              # Unclassified errors

    @classmethod
    def from_exception(cls, exc: Exception) -> 'ErrorCategory':
        """Determine error category from exception type."""
        exc_type = type(exc).__name__

        # Map common exception types to categories
        if exc_type in ('ValueError', 'ValidationError', 'SchemaError'):
            return cls.VALIDATION
        elif exc_type in ('FileNotFoundError', 'PermissionError', 'IOError', 'OSError'):
            return cls.DATA_ACCESS
        elif exc_type in ('ArithmeticError', 'OverflowError', 'ZeroDivisionError'):
            return cls.CALCULATION
        elif exc_type in ('CudaError', 'CuDNNError', 'MetalError', 'HardwareError'):
            return cls.HARDWARE
        elif exc_type in ('PipelineError', 'DependencyError', 'OrchestratorError'):
            return cls.PIPELINE
        elif exc_type in ('ChartError', 'MatplotlibError', 'PlotlyError'):
            return cls.VISUALIZATION
        elif exc_type in ('ConfigError', 'MissingConfigurationError'):
            return cls.CONFIGURATION
        elif exc_type in ('ConnectionError', 'ConnectionRefusedError', 'HTTPError'):
            return cls.NETWORK
        elif exc_type in ('TimeoutError', 'TimeoutExpired'):
            return cls.TIMEOUT
        elif exc_type in ('MemoryError', 'OutOfMemoryError'):
            return cls.MEMORY
        else:
            return cls.UNKNOWN


class ErrorSeverity(Enum):
    """Severity levels for classifying error impact."""
    DEBUG = 10    # Informational errors for debugging
    INFO = 20     # Minor issues with minimal impact
    WARNING = 30  # Important issues that don't affect critical functionality
    ERROR = 40    # Serious issues affecting functionality but not fatal
    CRITICAL = 50 # Fatal errors that prevent system operation

    @classmethod
    def from_exception(cls, exc: Exception) -> 'ErrorSeverity':
        """Determine error severity from exception type."""
        # Extract any severity hint from the exception's attributes
        if hasattr(exc, 'severity'):
            severity_hint = getattr(exc, 'severity')
            if isinstance(severity_hint, ErrorSeverity):
                return severity_hint
            elif isinstance(severity_hint, str) and hasattr(cls, severity_hint.upper()):
                return getattr(cls, severity_hint.upper())

        # Determine based on exception type
        exc_type = type(exc).__name__

        if exc_type in ('SystemExit', 'KeyboardInterrupt', 'MemoryError'):
            return cls.CRITICAL
        elif exc_type in ('RuntimeError', 'IOError', 'ConnectionError', 'TimeoutError'):
            return cls.ERROR
        elif exc_type in ('ValueError', 'KeyError', 'TypeError', 'AttributeError'):
            return cls.WARNING
        else:
            return cls.INFO


class CircuitBreakerState(Enum):
    """Circuit breaker states for fault isolation."""
    CLOSED = auto()     # Normal operation
    OPEN = auto()       # Circuit breaker tripped
    HALF_OPEN = auto()  # Testing recovery state


class RetryStrategy(Enum):
    """Strategies for retrying operations after failures."""
    IMMEDIATE = "immediate"
    FIXED_DELAY = "fixed_delay"
    EXPONENTIAL_BACKOFF = "exponential_backoff"
    RANDOM_DELAY = "random_delay"


class RecoveryMode(Enum):
    """Recovery modes for handling errors."""
    RETRY = "retry"
    FALLBACK = "fallback"
    PARTIAL_RESULT = "partial_result"
    RECONFIGURE = "reconfigure"
    HARDWARE_FALLBACK = "hardware_fallback"
    FAIL_FAST = "fail_fast"


class MetricType(Enum):
    """Types of metrics for error tracking."""
    COUNTER = "counter"  # Cumulative count
    GAUGE = "gauge"      # Value that can go up or down
    HISTOGRAM = "histogram"  # Distribution of values


class ErrorContext:
    """Context information for an error."""
    
    def __init__(self,
                 exception: Exception,
                 category: ErrorCategory,
                 severity: ErrorSeverity,
                 component: str,
                 operation: str,
                 timestamp: Optional[float] = None,
                 traceback_str: Optional[str] = None,
                 extra: Optional[Dict[str, Any]] = None) -> None:
        """Initialize an error context."""
        import time
        import traceback
        
        self.exception = exception
        self.exception_type = type(exception).__name__
        self.message = str(exception)
        self.category = category
        self.severity = severity
        self.component = component
        self.operation = operation
        self.timestamp = timestamp or time.time()
        self.traceback = traceback_str or ''.join(traceback.format_exception(
            type(exception), exception, exception.__traceback__
        ))
        self.extra = extra or {}
        self.handled = False
        self.recovery_attempted = False
        self.recovery_succeeded = False
        self.recovery_strategy = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "exception_type": self.exception_type,
            "message": self.message,
            "category": self.category.value,
            "severity": self.severity.value,
            "component": self.component,
            "operation": self.operation,
            "timestamp": self.timestamp,
            "traceback": self.traceback,
            "extra": self.extra,
            "handled": self.handled,
            "recovery_attempted": self.recovery_attempted,
            "recovery_succeeded": self.recovery_succeeded,
            "recovery_strategy": self.recovery_strategy
        }

    def __str__(self) -> str:
        """String representation of the error context."""
        return (f"{self.severity.value} {self.category.value} error in "
                f"{self.component}.{self.operation}: {self.message}")