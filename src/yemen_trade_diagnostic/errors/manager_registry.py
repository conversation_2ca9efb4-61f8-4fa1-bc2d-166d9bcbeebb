"""
Manager Registry for Yemen Trade Diagnostic Error System

This module provides a central registry for manager instances to avoid
circular imports. Managers register themselves when they are created.
"""

from typing import Any, Dict, Optional


class ManagerRegistry:
    """Central registry for manager instances."""
    
    _instance = None
    
    def __init__(self):
        self._managers: Dict[str, Any] = {}
    
    @classmethod
    def get_instance(cls) -> 'ManagerRegistry':
        """Get singleton instance of the registry."""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    def register(self, name: str, manager: Any) -> None:
        """Register a manager instance."""
        self._managers[name] = manager
    
    def get(self, name: str) -> Optional[Any]:
        """Get a registered manager instance."""
        return self._managers.get(name)
    
    def unregister(self, name: str) -> None:
        """Unregister a manager instance."""
        if name in self._managers:
            del self._managers[name]


# Global registry instance
_registry = ManagerRegistry.get_instance()


def register_manager(name: str, manager: Any) -> None:
    """Register a manager in the global registry."""
    _registry.register(name, manager)


def get_manager(name: str) -> Optional[Any]:
    """Get a manager from the global registry."""
    return _registry.get(name)


def get_error_manager():
    """Get the error manager instance."""
    manager = get_manager('error_manager')
    if manager is None:
        # Lazy import to avoid circular dependency
        from .core import ErrorManager
        manager = ErrorManager.get_instance()
    return manager


def get_resource_manager():
    """Get the resource manager instance."""
    manager = get_manager('resource_manager')
    if manager is None:
        # Lazy import to avoid circular dependency
        from .resource_manager import ResourceManager
        manager = ResourceManager()
        register_manager('resource_manager', manager)
    return manager


def get_recovery_manager():
    """Get the recovery manager instance."""
    manager = get_manager('recovery_manager')
    if manager is None:
        # Lazy import to avoid circular dependency
        from .recovery_manager import RecoveryManager
        manager = RecoveryManager()
        register_manager('recovery_manager', manager)
    return manager