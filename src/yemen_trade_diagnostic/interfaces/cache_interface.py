
"""
Cache Interface (V2)

This module provides a standardized interface for caching in the Yemen Trade
Diagnostic project, with support for different cache levels, priorities,
and data lifetimes.

"""

# Standard library imports
import functools
import hashlib
import logging
import os
import pickle
import time
from enum import Enum
from typing import Any, Callable, Dict, Generic, List, Optional, Tuple, TypeVar, Union, cast

# Third-party imports
import pandas as pd

# Define type variables for generic functions
T = TypeVar('T')
R = TypeVar('R')
K = TypeVar('K')
V = TypeVar('V')

# Project imports
# Import the logging interface
from yemen_trade_diagnostic.interfaces.logging_interface import get_logger

# Import compression utilities
from yemen_trade_diagnostic.hardware.cache.strategies.compression import get_cache_compressor

# Import unified error handling system
from yemen_trade_diagnostic.errors import protect, error_context, OperationType

# Get logger
logger = get_logger(__name__)

# Memory-efficient cache integration
_memory_efficient_cache_instance = None


class CacheLevel(Enum):
    """Cache levels for the Yemen Trade Diagnostic project."""
    MEMORY = "memory"  # In-memory cache (fastest, lost on restart)
    DISK = "disk"      # Disk cache (persistent, but slower)
    DISK_HEAVY = "disk_heavy"  # Heavy disk cache for large results (added for pipeline compatibility)
    DISTRIBUTED = "distributed"  # Distributed cache (shared between instances)
    ALL = "all"        # All cache levels


class DataLifetime(Enum):
    """Data lifetime options for cached data."""
    TEMPORARY = "temporary"  # Short-lived data (minutes to hours)
    DAILY = "daily"          # Daily data (refreshed daily)
    PERSISTENT = "persistent"  # Long-lived data (weeks to months)
    PERMANENT = "permanent"   # Permanent data (never expires unless invalidated)


class CachePriority(Enum):
    """Priority levels for cached data."""
    LOW = "low"          # Low priority (can be evicted first)
    MEDIUM = "medium"    # Medium priority
    HIGH = "high"        # High priority (try to keep in cache)
    CRITICAL = "critical"  # Critical priority (only evict if necessary)


class CacheEntry(Generic[V]):
    """Cache entry with metadata."""
    
    def __init__(self, 
                value: V,
                created_at: float = None,
                expires_at: Optional[float] = None,
                lifetime: DataLifetime = DataLifetime.TEMPORARY,
                priority: CachePriority = CachePriority.MEDIUM,
                metadata: Dict[str, Any] = None):
        """
        Initialize a cache entry.
        
        Args:
            value: The cached value
            created_at: Time when the entry was created (default: current time)
            expires_at: Time when the entry expires (default: None = never)
            lifetime: Data lifetime category
            priority: Cache priority
            metadata: Additional metadata for the entry
        """
        self.value = value
        self.created_at = created_at or time.time()
        self.expires_at = expires_at
        self.lifetime = lifetime
        self.priority = priority
        self.metadata = metadata or {}
        self.last_accessed = self.created_at
        self.access_count = 0
    
    def is_expired(self) -> bool:
        """
        Check if the entry is expired.
        
        Returns:
            bool: True if the entry is expired, False otherwise
        """
        if self.expires_at is None:
            return False
        return time.time() > self.expires_at
    
    def access(self) -> None:
        """Record an access to this cache entry."""
        self.last_accessed = time.time()
        self.access_count += 1
    
    def get_age(self) -> float:
        """
        Get the age of the entry in seconds.
        
        Returns:
            float: Age in seconds
        """
        return time.time() - self.created_at
    
    def get_last_access_age(self) -> float:
        """
        Get the time since last access in seconds.
        
        Returns:
            float: Time since last access in seconds
        """
        return time.time() - self.last_accessed
    

class CacheConfig:
    """Configuration for cache behavior."""
    
    def __init__(self,
                 max_memory_entries: int = 2000,  # Increased from 1000
                 max_disk_entries: int = 20000,   # Increased from 10000
                 memory_cache_dir: str = ".cache/memory",
                 disk_cache_dir: str = ".cache/disk",
                 default_ttl: float = 3600.0,  # 1 hour
                 enable_stats: bool = True,
                 enable_compression: bool = True,  # New: Enable DataFrame compression
                 compression_threshold: int = 1024*1024,  # New: Compress entries > 1MB
                 pre_warm_enabled: bool = True,  # New: Enable cache pre-warming
                 pre_warm_patterns: Optional[List[str]] = None):
        """
        Initialize cache configuration.
        
        Args:
            max_memory_entries: Maximum number of in-memory cache entries
            max_disk_entries: Maximum number of disk cache entries
            memory_cache_dir: Directory for memory cache persistence
            disk_cache_dir: Directory for disk cache
            default_ttl: Default time-to-live for cache entries in seconds
            enable_stats: Whether to collect cache statistics
        """
        self.max_memory_entries = max_memory_entries
        self.max_disk_entries = max_disk_entries
        self.memory_cache_dir = memory_cache_dir
        self.disk_cache_dir = disk_cache_dir
        self.default_ttl = default_ttl
        self.enable_stats = enable_stats
        self.enable_compression = enable_compression
        self.compression_threshold = compression_threshold
        self.pre_warm_enabled = pre_warm_enabled
        self.pre_warm_patterns = pre_warm_patterns or []
        
        # Create cache directories if they don't exist
        for directory in [memory_cache_dir, disk_cache_dir]:
            if not os.path.exists(directory):
                os.makedirs(directory, exist_ok=True)


class CacheStats:
    """Statistics for cache operations."""
    
    def __init__(self):
        """Initialize cache statistics."""
        self.memory_hits = 0
        self.memory_misses = 0
        self.disk_hits = 0
        self.disk_misses = 0
        self.distributed_hits = 0
        self.distributed_misses = 0
        self.set_operations = 0
        self.get_operations = 0
        self.delete_operations = 0
        self.invalidate_operations = 0
        self.evictions = 0
    
    def get_hit_ratio(self, level: CacheLevel = CacheLevel.ALL) -> float:
        """
        Get the cache hit ratio for a specific cache level.
        
        Args:
            level: Cache level to get hit ratio for
            
        Returns:
            float: Hit ratio (0.0 to 1.0) or NaN if no operations
        """
        if level == CacheLevel.MEMORY:
            total = self.memory_hits + self.memory_misses
            return self.memory_hits / total if total > 0 else float('nan')
        elif level == CacheLevel.DISK:
            total = self.disk_hits + self.disk_misses
            return self.disk_hits / total if total > 0 else float('nan')
        elif level == CacheLevel.DISTRIBUTED:
            total = self.distributed_hits + self.distributed_misses
            return self.distributed_hits / total if total > 0 else float('nan')
        else:
            total = (self.memory_hits + self.memory_misses +
                    self.disk_hits + self.disk_misses +
                    self.distributed_hits + self.distributed_misses)
            hits = self.memory_hits + self.disk_hits + self.distributed_hits
            return hits / total if total > 0 else float('nan')
    
    def get_summary(self) -> Dict[str, Any]:
        """
        Get a summary of cache statistics.
        
        Returns:
            Dict[str, Any]: Statistics summary
        """
        return {
            "memory_hit_ratio": self.get_hit_ratio(CacheLevel.MEMORY),
            "disk_hit_ratio": self.get_hit_ratio(CacheLevel.DISK),
            "distributed_hit_ratio": self.get_hit_ratio(CacheLevel.DISTRIBUTED),
            "overall_hit_ratio": self.get_hit_ratio(CacheLevel.ALL),
            "total_operations": self.get_operations + self.set_operations + self.delete_operations,
            "eviction_ratio": self.evictions / self.set_operations if self.set_operations > 0 else 0.0
        }


class MemoryCache(Generic[K, V]):
    """In-memory cache implementation with compression support."""
    
    def __init__(self, config: CacheConfig, stats: CacheStats):
        """
        Initialize the memory cache.
        
        Args:
            config: Cache configuration
            stats: Cache statistics
        """
        self.cache: Dict[K, CacheEntry[V]] = {}
        self.config = config
        self.stats = stats
        self.compressor = get_cache_compressor() if config.enable_compression else None
    
    def get(self, key: K) -> Optional[V]:
        """
        Get a value from the cache.
        
        Args:
            key: Cache key
            
        Returns:
            Optional[V]: The cached value, or None if not found or expired
        """
        if key not in self.cache:
            self.stats.memory_misses += 1
            return None
        
        entry = self.cache[key]
        
        if entry.is_expired():
            # Remove expired entry
            del self.cache[key]
            self.stats.memory_misses += 1
            return None
        
        # Update access statistics
        entry.access()
        
        self.stats.memory_hits += 1
        return entry.value
    
    def set(self, key: K, value: V, ttl: Optional[float] = None,
           lifetime: DataLifetime = DataLifetime.TEMPORARY,
           priority: CachePriority = CachePriority.MEDIUM,
           metadata: Optional[Dict[str, Any]] = None) -> None:
        """
        Set a value in the cache.
        
        Args:
            key: Cache key
            value: Value to cache
            ttl: Time-to-live in seconds (None for no expiration)
            lifetime: Data lifetime category
            priority: Cache priority
            metadata: Additional metadata for the entry
        """
        # Check if we need to evict an entry
        if len(self.cache) >= self.config.max_memory_entries and key not in self.cache:
            self._evict()
        
        # Calculate expiration time
        expires_at = None
        if ttl is not None:
            expires_at = time.time() + ttl
        
        # Create cache entry
        entry = CacheEntry(
            value=value,
            expires_at=expires_at,
            lifetime=lifetime,
            priority=priority,
            metadata=metadata
        )
        
        # Store in cache
        self.cache[key] = entry
    
    def delete(self, key: K) -> bool:
        """
        Delete a value from the cache.
        
        Args:
            key: Cache key
            
        Returns:
            bool: True if the key was found and deleted, False otherwise
        """
        if key in self.cache:
            del self.cache[key]
            return True
        return False
    
    def clear(self) -> None:
        """Clear the cache."""
        self.cache.clear()
    
    def _evict(self) -> None:
        """Evict an entry from the cache based on priority and access patterns."""
        if not self.cache:
            return
        
        # First try to remove expired entries
        expired_keys = [k for k, v in self.cache.items() if v.is_expired()]
        if expired_keys:
            for key in expired_keys:
                del self.cache[key]
            self.stats.evictions += len(expired_keys)
            return
        
        # Then try to remove low priority entries
        low_priority_keys = [k for k, v in self.cache.items() 
                           if v.priority == CachePriority.LOW]
        if low_priority_keys:
            # Remove the least recently used low priority entry
            lru_key = min(low_priority_keys, key=lambda k: self.cache[k].last_accessed)
            del self.cache[lru_key]
            self.stats.evictions += 1
            return
        
        # Then try to remove medium priority entries
        medium_priority_keys = [k for k, v in self.cache.items() 
                              if v.priority == CachePriority.MEDIUM]
        if medium_priority_keys:
            # Remove the least recently used medium priority entry
            lru_key = min(medium_priority_keys, key=lambda k: self.cache[k].last_accessed)
            del self.cache[lru_key]
            self.stats.evictions += 1
            return
        
        # Finally, remove the least recently used entry
        lru_key = min(self.cache.keys(), key=lambda k: self.cache[k].last_accessed)
        del self.cache[lru_key]
        self.stats.evictions += 1
    
    def save_to_disk(self) -> None:
        """Save the cache to disk for persistence."""
        try:
            cache_file = os.path.join(self.config.memory_cache_dir, "memory_cache.pkl")
            with open(cache_file, 'wb') as f:
                pickle.dump(self.cache, f)
        except Exception as e:
            logger.warning(f"Failed to save memory cache to disk: {e}")
    
    def load_from_disk(self) -> None:
        """Load the cache from disk."""
        try:
            cache_file = os.path.join(self.config.memory_cache_dir, "memory_cache.pkl")
            if os.path.exists(cache_file):
                with open(cache_file, 'rb') as f:
                    self.cache = pickle.load(f)
                
                # Remove expired entries
                now = time.time()
                expired_keys = [k for k, v in self.cache.items() 
                              if v.expires_at and v.expires_at < now]
                for key in expired_keys:
                    del self.cache[key]
        except Exception as e:
            logger.warning(f"Failed to load memory cache from disk: {e}")


class DiskCache(Generic[K, V]):
    """Disk-based cache implementation."""
    
    def __init__(self, config: CacheConfig, stats: CacheStats):
        """
        Initialize the disk cache.
        
        Args:
            config: Cache configuration
            stats: Cache statistics
        """
        self.config = config
        self.stats = stats
        self.cache_dir = config.disk_cache_dir
        
        # Ensure cache directory exists
        os.makedirs(self.cache_dir, exist_ok=True)
        
        # Create index file
        self.index_file = os.path.join(self.cache_dir, "index.pkl")
        self.index: Dict[K, Dict[str, Any]] = {}
        
        # Load index if it exists
        self._load_index()
    
    def _get_cache_path(self, key: K) -> str:
        """
        Get the cache file path for a key.
        
        Args:
            key: Cache key
            
        Returns:
            str: Path to the cache file
        """
        # Create a hash of the key for the filename
        key_str = str(key)
        key_hash = hashlib.md5(key_str.encode()).hexdigest()
        return os.path.join(self.cache_dir, key_hash + ".cache")
    
    def _load_index(self) -> None:
        """Load the cache index from disk."""
        try:
            if os.path.exists(self.index_file):
                with open(self.index_file, 'rb') as f:
                    self.index = pickle.load(f)
                
                # Remove entries for files that don't exist
                keys_to_remove = []
                for key, metadata in self.index.items():
                    cache_path = metadata.get("path")
                    if not cache_path or not os.path.exists(cache_path):
                        keys_to_remove.append(key)
                
                for key in keys_to_remove:
                    del self.index[key]
        except Exception as e:
            logger.warning(f"Failed to load disk cache index: {e}")
            self.index = {}
    
    def _save_index(self) -> None:
        """Save the cache index to disk."""
        try:
            with open(self.index_file, 'wb') as f:
                pickle.dump(self.index, f)
        except Exception as e:
            logger.warning(f"Failed to save disk cache index: {e}")
    
    def get(self, key: K) -> Optional[V]:
        """
        Get a value from the cache.
        
        Args:
            key: Cache key
            
        Returns:
            Optional[V]: The cached value, or None if not found or expired
        """
        if key not in self.index:
            self.stats.disk_misses += 1
            return None
        
        metadata = self.index[key]
        cache_path = metadata.get("path")
        
        # Check if file exists
        if not cache_path or not os.path.exists(cache_path):
            # Remove from index
            del self.index[key]
            self._save_index()
            self.stats.disk_misses += 1
            return None
        
        # Check if expired
        expires_at = metadata.get("expires_at")
        if expires_at and time.time() > expires_at:
            # Remove expired entry
            os.remove(cache_path)
            del self.index[key]
            self._save_index()
            self.stats.disk_misses += 1
            return None
        
        # Load value from disk
        try:
            with open(cache_path, 'rb') as f:
                value = pickle.load(f)
            
            # Update access statistics
            metadata["last_accessed"] = time.time()
            metadata["access_count"] = metadata.get("access_count", 0) + 1
            self._save_index()
            
            self.stats.disk_hits += 1
            return value
        except Exception as e:
            logger.warning(f"Failed to load value from disk cache: {e}")
            self.stats.disk_misses += 1
            return None
    
    def set(self, key: K, value: V, ttl: Optional[float] = None,
           lifetime: DataLifetime = DataLifetime.TEMPORARY,
           priority: CachePriority = CachePriority.MEDIUM,
           metadata: Optional[Dict[str, Any]] = None) -> None:
        """
        Set a value in the cache.
        
        Args:
            key: Cache key
            value: Value to cache
            ttl: Time-to-live in seconds (None for no expiration)
            lifetime: Data lifetime category
            priority: Cache priority
            metadata: Additional metadata for the entry
        """
        # Check if we need to evict an entry
        if len(self.index) >= self.config.max_disk_entries and key not in self.index:
            self._evict()
        
        # Calculate expiration time
        expires_at = None
        if ttl is not None:
            expires_at = time.time() + ttl
        
        # Get cache file path
        cache_path = self._get_cache_path(key)
        
        # Create metadata
        entry_metadata = {
            "created_at": time.time(),
            "expires_at": expires_at,
            "lifetime": lifetime.value,
            "priority": priority.value,
            "path": cache_path,
            "last_accessed": time.time(),
            "access_count": 0
        }
        
        if metadata:
            entry_metadata.update(metadata)
        
        # Save value to disk
        try:
            with open(cache_path, 'wb') as f:
                pickle.dump(value, f)
            
            # Update index
            self.index[key] = entry_metadata
            self._save_index()
        except Exception as e:
            logger.warning(f"Failed to save value to disk cache: {e}")
    
    def delete(self, key: K) -> bool:
        """
        Delete a value from the cache.
        
        Args:
            key: Cache key
            
        Returns:
            bool: True if the key was found and deleted, False otherwise
        """
        if key not in self.index:
            return False
        
        metadata = self.index[key]
        cache_path = metadata.get("path")
        
        # Delete file if it exists
        if cache_path and os.path.exists(cache_path):
            try:
                os.remove(cache_path)
            except Exception as e:
                logger.warning(f"Failed to delete cache file: {e}")
        
        # Remove from index
        del self.index[key]
        self._save_index()
        
        return True
    
    def clear(self) -> None:
        """Clear the cache."""
        # Delete all cache files
        for metadata in self.index.values():
            cache_path = metadata.get("path")
            if cache_path and os.path.exists(cache_path):
                try:
                    os.remove(cache_path)
                except Exception:
                    pass
        
        # Clear index
        self.index = {}
        self._save_index()
    
    def _evict(self) -> None:
        """Evict an entry from the cache based on priority and access patterns."""
        if not self.index:
            return
        
        # First try to remove expired entries
        now = time.time()
        expired_keys = [k for k, v in self.index.items() 
                       if v.get("expires_at") and v.get("expires_at") < now]
        if expired_keys:
            for key in expired_keys:
                self.delete(key)
            self.stats.evictions += len(expired_keys)
            return
        
        # Then try to remove low priority entries
        low_priority_keys = [k for k, v in self.index.items() 
                           if v.get("priority") == CachePriority.LOW.value]
        if low_priority_keys:
            # Remove the least recently used low priority entry
            lru_key = min(low_priority_keys, 
                         key=lambda k: self.index[k].get("last_accessed", 0))
            self.delete(lru_key)
            self.stats.evictions += 1
            return
        
        # Then try to remove medium priority entries
        medium_priority_keys = [k for k, v in self.index.items() 
                              if v.get("priority") == CachePriority.MEDIUM.value]
        if medium_priority_keys:
            # Remove the least recently used medium priority entry
            lru_key = min(medium_priority_keys, 
                         key=lambda k: self.index[k].get("last_accessed", 0))
            self.delete(lru_key)
            self.stats.evictions += 1
            return
        
        # Finally, remove the least recently used entry
        lru_key = min(self.index.keys(), 
                     key=lambda k: self.index[k].get("last_accessed", 0))
        self.delete(lru_key)
        self.stats.evictions += 1


class CacheManager:
    """Manager for cache operations across multiple cache levels."""
    
    _instance = None
    
    @classmethod
    def get_instance(cls) -> 'CacheManager':
        """
        Get the singleton instance of CacheManager.
        
        Returns:
            CacheManager: The singleton instance
        """
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    def __init__(self, config: Optional[CacheConfig] = None):
        """
        Initialize the cache manager with enhanced features.
        
        Args:
            config: Cache configuration (optional)
        """
        self.config = config or CacheConfig()
        self.stats = CacheStats()
        
        # Create caches
        self.memory_cache = MemoryCache(self.config, self.stats)
        self.disk_cache = DiskCache(self.config, self.stats)
        
        # Initialize distributed cache (placeholder)
        # Distributed cache is not implemented yet
        self.distributed_cache = None
        
        # Initialize compression
        try:
            # Project imports
            from yemen_trade_diagnostic.hardware.cache.strategies.compression import get_cache_compressor
            self.compressor = get_cache_compressor()
            self.compression_enabled = True
        except ImportError:
            self.compressor = None
            self.compression_enabled = False
        
        # Initialize cache optimization components (lazy loading to avoid circular dependencies)
        self.key_optimizer = None
        self.intelligent_warmer = None
        self.smart_invalidator = None
        self.optimizations_enabled = False
        self._optimizations_initialized = False
        
        # Initialize memory-efficient cache
        self.memory_efficient_cache = None
        self._memory_efficient_enabled = False
        
        # Initialize pre-warming if enabled
        if self.config.pre_warm_enabled:
            self._schedule_pre_warming()
    
    def _initialize_optimizations(self):
        """Initialize cache optimization components with lazy loading."""
        if self._optimizations_initialized:
            return
        
        try:
            # Project imports
            from yemen_trade_diagnostic.hardware.cache.keys import get_cache_key_optimizer
            from yemen_trade_diagnostic.hardware.cache.strategies.invalidation import (
                get_smart_cache_invalidator,
            )
            
            self.key_optimizer = get_cache_key_optimizer()
            self.smart_invalidator = get_smart_cache_invalidator()
            
            # Note: intelligent_warmer is not initialized here to avoid circular dependency
            # It will be initialized separately when needed
            
            self.optimizations_enabled = True
            self._optimizations_initialized = True
            
            logger.info("Cache optimizations enabled: semantic keys, smart invalidation")
        except ImportError as e:
            logger.warning(f"Cache optimizations not available: {e}")
            self.optimizations_enabled = False
            self._optimizations_initialized = True
        
        # Initialize memory-efficient cache
        try:
            # Project imports
            from yemen_trade_diagnostic.hardware.cache import (
                get_memory_efficient_cache,
            )
            
            self.memory_efficient_cache = get_memory_efficient_cache(
                max_size=self.config.max_memory_entries,
                memory_limit_mb=512,  # 512MB limit
                enable_compression=self.config.enable_compression,
                enable_adaptive_sizing=True
            )
            self._memory_efficient_enabled = True
            
            logger.info("Memory-efficient cache enabled with LRU eviction and adaptive sizing")
        except ImportError as e:
            logger.warning(f"Memory-efficient cache not available: {e}")
            self._memory_efficient_enabled = False
    
    @protect("cache_get", OperationType.DATA_LOADING)
    def get(self, key: Any, level: CacheLevel = CacheLevel.ALL) -> Optional[Any]:
        """
        Get a value from the cache with intelligent optimizations.
        
        Args:
            key: Cache key
            level: Cache level to search
            
        Returns:
            Optional[Any]: The cached value, or None if not found
        """
        self.stats.get_operations += 1
        
        # Initialize optimizations if needed
        self._initialize_optimizations()
        
        # Track access for smart invalidation and warming
        if self.optimizations_enabled and self.smart_invalidator:
            self.smart_invalidator.track_access(str(key))
        
        # Try memory cache first
        if level in [CacheLevel.MEMORY, CacheLevel.ALL]:
            value = self.memory_cache.get(key)
            if value is not None:
                # Note: Related data warming is handled by the intelligent warmer independently
                # to avoid circular dependencies
                return value
        
        # Then try disk cache
        if level in [CacheLevel.DISK, CacheLevel.ALL]:
            value = self.disk_cache.get(key)
            if value is not None:
                # Cache in memory for faster access next time
                if level == CacheLevel.ALL:
                    self.memory_cache.set(key, value)
                return value
        
        # Finally try distributed cache
        if level in [CacheLevel.DISTRIBUTED, CacheLevel.ALL] and self.distributed_cache:
            value = self.distributed_cache.get(key)
            if value is not None:
                # Cache in memory and disk for faster access next time
                if level == CacheLevel.ALL:
                    self.memory_cache.set(key, value)
                    self.disk_cache.set(key, value)
                return value
        
        return None
    
    @protect("cache_set", OperationType.DATA_LOADING)
    def set(self, key: Any, value: Any, ttl: Optional[float] = None,
           level: CacheLevel = CacheLevel.ALL,
           lifetime: DataLifetime = DataLifetime.TEMPORARY,
           priority: CachePriority = CachePriority.MEDIUM,
           metadata: Optional[Dict[str, Any]] = None) -> None:
        """
        Set a value in the cache.
        
        Args:
            key: Cache key
            value: Value to cache
            ttl: Time-to-live in seconds (None for no expiration)
            level: Cache level to store the value at
            lifetime: Data lifetime category
            priority: Cache priority
            metadata: Additional metadata for the entry
        """
        self.stats.set_operations += 1
        
        # Apply compression if enabled and appropriate
        compressed_value = value
        compressed_metadata = metadata or {}
        
        # Calculate size for compression check
        value_size = len(pickle.dumps(value)) if value is not None else 0
        
        if self.compression_enabled and self.compressor and self.compressor.should_compress(value, value_size):
            compressed_value, compression_metadata = self.compressor.compress(value)
            compressed_metadata = compressed_metadata.copy() if compressed_metadata else {}
            compressed_metadata.update({"compression": compression_metadata})
        
        # Set in memory cache
        if level in [CacheLevel.MEMORY, CacheLevel.ALL]:
            self.memory_cache.set(key, compressed_value, ttl, lifetime, priority, compressed_metadata)
        
        # Set in disk cache
        if level in [CacheLevel.DISK, CacheLevel.ALL]:
            self.disk_cache.set(key, compressed_value, ttl, lifetime, priority, compressed_metadata)
        
        # Set in distributed cache
        if level in [CacheLevel.DISTRIBUTED, CacheLevel.ALL] and self.distributed_cache:
            self.distributed_cache.set(key, compressed_value, ttl, lifetime, priority, compressed_metadata)
    
    @protect("cache_delete", OperationType.DATA_LOADING)
    def delete(self, key: Any, level: CacheLevel = CacheLevel.ALL) -> bool:
        """
        Delete a value from the cache.
        
        Args:
            key: Cache key
            level: Cache level to delete from
            
        Returns:
            bool: True if the key was found and deleted, False otherwise
        """
        self.stats.delete_operations += 1
        
        result = False
        
        # Delete from memory cache
        if level in [CacheLevel.MEMORY, CacheLevel.ALL]:
            if self.memory_cache.delete(key):
                result = True
        
        # Delete from disk cache
        if level in [CacheLevel.DISK, CacheLevel.ALL]:
            if self.disk_cache.delete(key):
                result = True
        
        # Delete from distributed cache
        if level in [CacheLevel.DISTRIBUTED, CacheLevel.ALL] and self.distributed_cache:
            if self.distributed_cache.delete(key):
                result = True
        
        return result
    
    @protect("cache_clear", OperationType.DATA_LOADING)
    def clear(self, level: CacheLevel = CacheLevel.ALL) -> None:
        """
        Clear the cache.
        
        Args:
            level: Cache level to clear
        """
        # Clear memory cache
        if level in [CacheLevel.MEMORY, CacheLevel.ALL]:
            self.memory_cache.clear()
        
        # Clear disk cache
        if level in [CacheLevel.DISK, CacheLevel.ALL]:
            self.disk_cache.clear()
        
        # Clear distributed cache
        if level in [CacheLevel.DISTRIBUTED, CacheLevel.ALL] and self.distributed_cache:
            self.distributed_cache.clear()
    
    def get_or_compute(self, key: Any, compute_func: Callable[[], Any],
                      ttl: Optional[float] = None,
                      level: CacheLevel = CacheLevel.ALL,
                      lifetime: DataLifetime = DataLifetime.TEMPORARY,
                      priority: CachePriority = CachePriority.MEDIUM) -> Any:
        """
        Get a value from the cache, or compute and cache it if not found.
        
        Args:
            key: Cache key
            compute_func: Function to compute the value if not in cache
            ttl: Time-to-live for the cached value in seconds
            level: Cache level to use
            lifetime: Data lifetime category
            priority: Cache priority
            
        Returns:
            Any: The cached or computed value
        """
        # Try to get from cache
        value = self.get(key, level)
        if value is not None:
            return value
        
        # Compute the value
        value = compute_func()
        
        # Cache the computed value
        self.set(key, value, ttl, level, lifetime, priority)
        
        return value
    
    def invalidate(self, pattern: str, level: CacheLevel = CacheLevel.ALL) -> int:
        """
        Invalidate keys matching a pattern using smart invalidation.
        
        Args:
            pattern: Pattern to match keys against
            level: Cache level to invalidate
            
        Returns:
            int: Number of invalidated keys
        """
        self.stats.invalidate_operations += 1
        
        # Use smart cache invalidation
        try:
            # Project imports
            from yemen_trade_diagnostic.hardware.cache.strategies.invalidation import (
                get_smart_cache_invalidator,
            )
            invalidator = get_smart_cache_invalidator()
            return invalidator.invalidate_pattern(pattern, level)
        except ImportError:
            logger.warning("Smart cache invalidation not available, using basic invalidation")
            return 0
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Get cache statistics.
        
        Returns:
            Dict[str, Any]: Cache statistics
        """
        base_stats = self.stats.get_summary()
        
        # Add memory-efficient cache stats if available
        if self._memory_efficient_enabled and self.memory_efficient_cache:
            try:
                mem_stats = self.memory_efficient_cache.get_stats()
                base_stats.update({
                    'memory_efficient_cache': mem_stats,
                    'memory_pressure_level': mem_stats.get('memory_pressure_level', 'unknown'),
                    'adaptive_factor': mem_stats.get('adaptive_factor', 1.0),
                    'memory_efficiency': mem_stats.get('memory_efficiency', 0.0)
                })
            except Exception as e:
                logger.debug(f"Could not get memory-efficient cache stats: {e}")
        
        return base_stats
    
    def _schedule_pre_warming(self) -> None:
        """Schedule cache pre-warming."""
        try:
            # Project imports
            from yemen_trade_diagnostic.hardware.cache import get_cache_pre_warmer
            pre_warmer = get_cache_pre_warmer()
            
            # Run initial pre-warming
            results = pre_warmer.pre_warm(self.config.pre_warm_patterns)
            logger.info(f"Pre-warmed {sum(results.values())} cache entries")
            
        except Exception as e:
            logger.warning(f"Failed to pre-warm cache: {e}")
    
    def compress_value(self, value: Any) -> Tuple[Any, Dict[str, Any]]:
        """
        Compress a value if needed.
        
        Args:
            value: Value to potentially compress
            
        Returns:
            Tuple of (processed_value, metadata)
        """
        if not self.config.enable_compression:
            return value, {}
        
        try:
            # Project imports
            from yemen_trade_diagnostic.hardware.cache.strategies.compression import get_cache_compressor
            compressor = get_cache_compressor()
            
            # Calculate size for compression check
            value_size = len(pickle.dumps(value)) if value is not None else 0
            
            if compressor.should_compress(value, value_size):
                compressed_data, metadata = compressor.compress(value)
                return compressed_data, metadata
            else:
                return value, {"compressed": False}
                
        except Exception as e:
            logger.warning(f"Compression failed: {e}")
            return value, {"compressed": False}
    
    def decompress_value(self, value: Any, metadata: Dict[str, Any]) -> Any:
        """
        Decompress a value if needed.
        
        Args:
            value: Potentially compressed value
            metadata: Compression metadata
            
        Returns:
            Decompressed value
        """
        if not metadata.get("compressed", False):
            return value
        
        try:
            # Project imports
            from yemen_trade_diagnostic.hardware.cache.strategies.compression import get_cache_compressor
            compressor = get_cache_compressor()
            return compressor.decompress(value, metadata)
            
        except Exception as e:
            logger.error(f"Decompression failed: {e}")
            return None
    
    def shutdown(self) -> None:
        """Perform cleanup operations before shutdown."""
        # Save memory cache to disk
        self.memory_cache.save_to_disk()


def get_cache() -> CacheManager:
    """
    Get the singleton instance of CacheManager.
    
    Returns:
        CacheManager: The cache manager instance
    """
    return CacheManager.get_instance()


# Convenience functions for direct use

def cache_get(key: Any, level: CacheLevel = CacheLevel.ALL) -> Optional[Any]:
    """
    Get a value from the cache.
    
    Args:
        key: Cache key
        level: Cache level to search
        
    Returns:
        Optional[Any]: The cached value, or None if not found
    """
    return get_cache().get(key, level)


def cache_set(key: Any, value: Any, ttl: Optional[float] = None,
             level: CacheLevel = CacheLevel.ALL,
             lifetime: DataLifetime = DataLifetime.TEMPORARY,
             priority: CachePriority = CachePriority.MEDIUM,
             metadata: Optional[Dict[str, Any]] = None) -> None:
    """
    Set a value in the cache.
    
    Args:
        key: Cache key
        value: Value to cache
        ttl: Time-to-live in seconds (None for no expiration)
        level: Cache level to store the value at
        lifetime: Data lifetime category
        priority: Cache priority
        metadata: Additional metadata for the entry
    """
    return get_cache().set(key, value, ttl, level, lifetime, priority, metadata)


def cache_delete(key: Any, level: CacheLevel = CacheLevel.ALL) -> bool:
    """
    Delete a value from the cache.
    
    Args:
        key: Cache key
        level: Cache level to delete from
        
    Returns:
        bool: True if the key was found and deleted, False otherwise
    """
    return get_cache().delete(key, level)


def cache_clear(level: CacheLevel = CacheLevel.ALL) -> None:
    """
    Clear the cache.
    
    Args:
        level: Cache level to clear
    """
    return get_cache().clear(level)


def cache_invalidate(pattern: str, level: CacheLevel = CacheLevel.ALL) -> int:
    """
    Invalidate keys matching a pattern.
    
    Args:
        pattern: Pattern to match keys against
        level: Cache level to invalidate
        
    Returns:
        int: Number of invalidated keys
    """
    return get_cache().invalidate(pattern, level)


def cache_get_or_compute(key: Any, compute_func: Callable[[], T],
                        ttl: Optional[float] = None,
                        level: CacheLevel = CacheLevel.ALL,
                        lifetime: DataLifetime = DataLifetime.TEMPORARY,
                        priority: CachePriority = CachePriority.MEDIUM) -> T:
    """
    Get a value from the cache, or compute and cache it if not found.
    
    Args:
        key: Cache key
        compute_func: Function to compute the value if not in cache
        ttl: Time-to-live for the cached value in seconds
        level: Cache level to use
        lifetime: Data lifetime category
        priority: Cache priority
        
    Returns:
        T: The cached or computed value
    """
    return get_cache().get_or_compute(key, compute_func, ttl, level, lifetime, priority)


def memoize(ttl: Optional[float] = None,
           level: CacheLevel = CacheLevel.MEMORY,
           lifetime: DataLifetime = DataLifetime.TEMPORARY,
           priority: CachePriority = CachePriority.MEDIUM,
           key_generator: Optional[Callable[..., Any]] = None):
    """
    Decorator to memoize a function's results.
    
    Args:
        ttl: Time-to-live for the cached value in seconds
        level: Cache level to use
        lifetime: Data lifetime category
        priority: Cache priority
        key_generator: Optional function to generate cache keys
        
    Returns:
        Callable: Function decorator
    """
    def decorator(func: Callable[..., R]) -> Callable[..., R]:
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> R:
            # Generate cache key
            if key_generator:
                key = key_generator(*args, **kwargs)
            else:
                # Default key generation
                key_parts = [func.__module__, func.__name__]
                
                # Add args to key
                for arg in args:
                    try:
                        key_parts.append(repr(arg))
                    except:
                        key_parts.append(str(type(arg)))
                
                # Add kwargs to key
                for k, v in sorted(kwargs.items()):
                    key_parts.append(k)
                    try:
                        key_parts.append(repr(v))
                    except:
                        key_parts.append(str(type(v)))
                
                # Join key parts
                key = "|".join(key_parts)
            
            # Get cache manager
            cache_manager = get_cache()
            
            # Try to get from cache
            value = cache_manager.get(key, level)
            if value is not None:
                return value
            
            # Compute the value
            value = func(*args, **kwargs)
            
            # Cache the computed value
            cache_manager.set(key, value, ttl, level, lifetime, priority)
            
            return value
        
        return wrapper
    
    return decorator


# Export all symbols
__all__ = [
    'CacheLevel', 'DataLifetime', 'CachePriority', 'CacheEntry',
    'CacheConfig', 'CacheStats', 'CacheManager', 'get_cache',
    'cache_get', 'cache_set', 'cache_delete', 'cache_clear',
    'cache_invalidate', 'cache_get_or_compute', 'memoize'
]
