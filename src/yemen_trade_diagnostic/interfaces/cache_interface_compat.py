"""
Cache interface compatibility layer.

This module provides a bridge between the old interfaces.cache_interface
and the new hardware.cache module.
"""

import warnings
from typing import Any, Optional

# Import the new cache module
from yemen_trade_diagnostic.hardware.cache import Cache, get_cache, StorageTier

# Import the old interface to extend it
from yemen_trade_diagnostic.interfaces.cache_interface import (
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    CacheManager as OldCacheManager,
    get_cache as old_get_cache,
)


# Create compatibility mapping for CacheLevel
_LEVEL_MAPPING = {
    CacheLevel.MEMORY: StorageTier.MEMORY,
    CacheLevel.DISK: StorageTier.DISK,
    CacheLevel.DISK_HEAVY: StorageTier.DISK,
    CacheLevel.DISTRIBUTED: StorageTier.DISK,  # Map to disk for now
    CacheLevel.ALL: None,  # Use all tiers
}


class HardwareCache:
    """Wrapper to make hardware cache compatible with old interface."""
    
    def __init__(self):
        self._cache = get_cache()
    
    def get(self, key: Any, level: CacheLevel = CacheLevel.ALL) -> Optional[Any]:
        """Get value from cache."""
        result = self._cache.get(str(key))
        return result
    
    def set(self, key: Any, value: Any, ttl: Optional[float] = None,
            level: CacheLevel = CacheLevel.ALL, **kwargs) -> None:
        """Set value in cache."""
        self._cache.set(str(key), value, ttl=ttl, metadata=kwargs)
    
    def delete(self, key: Any, level: CacheLevel = CacheLevel.ALL) -> bool:
        """Delete key from cache."""
        return self._cache.delete(str(key))
    
    def invalidate(self, pattern: str, level: CacheLevel = CacheLevel.ALL) -> int:
        """Invalidate keys matching pattern."""
        return self._cache.invalidate_pattern(pattern)
    
    def clear(self, level: CacheLevel = CacheLevel.ALL) -> None:
        """Clear cache."""
        self._cache.clear()
    
    def get_stats(self) -> dict:
        """Get cache statistics."""
        return self._cache.get_stats()


# Override the get_cache function to return our wrapper
_hardware_cache_instance = None


def get_cache_manager():
    """Get cache manager that uses hardware cache."""
    global _hardware_cache_instance
    if _hardware_cache_instance is None:
        # First try to use the old cache manager
        try:
            old_cache = old_get_cache()
            # If it works, return it
            return old_cache
        except Exception:
            # Fall back to hardware cache wrapper
            warnings.warn(
                "Using hardware cache compatibility layer. "
                "Consider migrating to hardware.cache directly.",
                DeprecationWarning,
                stacklevel=2
            )
            _hardware_cache_instance = HardwareCache()
    
    return _hardware_cache_instance


# Monkey patch the old module
import sys
if 'yemen_trade_diagnostic.interfaces.cache_interface' in sys.modules:
    cache_module = sys.modules['yemen_trade_diagnostic.interfaces.cache_interface']
    # Only override if not already using new cache
    if not hasattr(cache_module.CacheManager, '_hardware_cache_patched'):
        cache_module.get_cache_manager = get_cache_manager
        cache_module.CacheManager._hardware_cache_patched = True