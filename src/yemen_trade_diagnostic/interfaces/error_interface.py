"""
Error Interface - DEPRECATED

This module has been replaced by the unified error handling system.
Please update your imports to use:

    from yemen_trade_diagnostic.errors import protect, error_context, OperationType

The new error handling system provides:
- @protect decorator for function protection
- error_context for context-based error handling  
- Comprehensive monitoring and analytics
- Hardware-aware error handling
- Graceful degradation strategies

Examples:
    @protect("operation_name", OperationType.COMPUTATION)
    def my_function():
        pass
    
    with error_context("data_loading", OperationType.DATA_LOADING):
        # Your code here
        pass
"""

import warnings

warnings.warn(
    "yemen_trade_diagnostic.interfaces.error_interface is DEPRECATED. "
    "Use 'from yemen_trade_diagnostic.errors import protect, error_context, OperationType' instead. "
    "See the module docstring for migration examples.",
    DeprecationWarning,
    stacklevel=2
)

# Re-export for temporary compatibility (will be removed in future version)
from yemen_trade_diagnostic.errors import *