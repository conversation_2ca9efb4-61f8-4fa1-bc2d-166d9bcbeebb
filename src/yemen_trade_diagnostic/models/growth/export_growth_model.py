"""
Export Growth Model for Yemen Trade Diagnostic (V2)

This module calculates export growth metrics for trade data, adapted for V2 interfaces.
"""
# Standard library imports
from pathlib import Path  # Retained for type hinting if any param uses it.
from typing import Any, Callable, Dict, List, Optional, Tuple, Union

# Third-party imports
import numpy as np
import pandas as pd

# Project imports
from yemen_trade_diagnostic.interfaces.cache_interface import (
    StorageTier,
    get_cache_manager,
    memoize
)
from yemen_trade_diagnostic.interfaces.hardware_interface import (
    AccelerationType,
    get_hardware_manager
)
from yemen_trade_diagnostic.interfaces.logging_interface import (
    LogLevel,
    get_logger,
    log_execution_time
)
from yemen_trade_diagnostic.interfaces.validation_interface import (
    ValidationResult,
    SchemaValidationRule,
    ValidationIssueLevel,
    ValidationMode,
    get_validation_manager
)
from yemen_trade_diagnostic.errors import protect, OperationType

# Initialize logger first
logger = get_logger(__name__)

# V2 Growth Model Utilities - Define functions if not available
try:
    from yemen_trade_diagnostic.models.growth.growth_model_utils import (
        validate_export_data_v2,
        calculate_growth_rate_v2,
        get_grouped_yearly_exports_v2
    )
except ImportError:
    logger.warning("growth_model_utils not found, defining fallback functions")
    
    def validate_export_data_v2(df: pd.DataFrame, required_cols: List[str]) -> Tuple[pd.DataFrame, bool]:
        """Fallback validation function"""
        if df is None or df.empty:
            return pd.DataFrame(), False
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            logger.error(f"Missing required columns: {missing_cols}")
            return df, False
        return df.dropna(subset=required_cols), True
    
    def calculate_growth_rate_v2(current_values: np.ndarray, previous_values: np.ndarray) -> Optional[np.ndarray]:
        """Fallback growth rate calculation"""
        if len(current_values) != len(previous_values):
            return None
        with np.errstate(divide='ignore', invalid='ignore'):
            growth_rates = ((current_values - previous_values) / previous_values) * 100
        return growth_rates
    
    def get_grouped_yearly_exports_v2(df: pd.DataFrame, year_col: str, value_cols: List[str]) -> pd.DataFrame:
        """Fallback yearly grouping function"""
        if df.empty:
            return pd.DataFrame()
        return df.groupby(year_col)[value_cols].sum().reset_index()

def get_growth_direction_v2(growth_rate: Optional[float]) -> str:
    """Determine growth direction based on growth rate (V2)."""
    if growth_rate is None or pd.isna(growth_rate):
        return 'neutral' # Or 'unknown' depending on desired output for NaN
    if growth_rate > 0: return 'positive'
    if growth_rate < 0: return 'negative'
    return 'neutral'

@protect("calculate_volume_index_v2", OperationType.MODEL_CALCULATION)
def calculate_volume_index_v2(quantities: np.ndarray, base_quantity: float) -> np.ndarray:
    """Calculate volume index (V2)."""
    if not isinstance(quantities, np.ndarray):
        logger.error("Input 'quantities' must be a NumPy array.")
        return np.array([])
    if not isinstance(base_quantity, (int, float)):
        logger.error("Input 'base_quantity' must be numeric.")
        return np.array([])
    if quantities.size == 0:
        return np.array([])
    if base_quantity <= 0:
        logger.warning(f"Base quantity is {base_quantity}, resulting in zero or undefined index.")
        return np.zeros_like(quantities, dtype=float)
    
    # Explicit hardware acceleration attempt
    hw_manager = get_hardware_manager()
    q_float = quantities.astype(float)
    result_array: Optional[np.ndarray] = None

    if hw_manager.is_hardware_acceleration_available():
        logger.debug("Attempting HW acceleration for volume index calculation.")
        try:
            # Step 1: quantities / base_quantity
            temp_result = hw_manager.accelerate_array(q_float, "divide", base_quantity)
            if temp_result is not None:
                # Step 2: result * 100
                final_result_acc = hw_manager.accelerate_array(temp_result, "multiply", 100.0)
                if final_result_acc is not None:
                    result_array = final_result_acc
                    logger.debug("Volume index HW acceleration successful.")
                else:
                    logger.warning("Volume index HW acceleration (multiply step) failed, falling back.")
                    result_array = (q_float / base_quantity) * 100.0 # Fallback for multiply step
            else:
                logger.warning("Volume index HW acceleration (divide step) failed, falling back.")
                result_array = (q_float / base_quantity) * 100.0 # Fallback for divide step
        except Exception as e:
            logger.warning(f"Volume index HW acceleration attempt failed: {e}. Falling back to NumPy.")
            result_array = (q_float / base_quantity) * 100.0
    else:
        logger.debug("HW acceleration not available for volume index. Using NumPy.")
        result_array = (q_float / base_quantity) * 100.0

    return result_array if result_array is not None else np.zeros_like(q_float) # Ensure return non-None array

@protect("calculate_export_volume_index_v2", OperationType.MODEL_CALCULATION)
@memoize(ttl=3600, level=StorageTier.MEMORY)
@log_execution_time(logger=logger)
def calculate_export_volume_index_v2(
    exports: pd.DataFrame,
    base_year: int = 2002,
    year_col: str = 't',
    quantity_col: str = 'q' # Standard BACI quantity column name
) -> pd.DataFrame:
    """Calculate export volume index with the specified base year = 100 (V2)."""
    logger.info(f"Calculating export volume index, base year: {base_year}.")
    required_cols = [year_col, quantity_col]
    validated_exports, is_valid = validate_export_data_v2(exports, required_cols)
    if not is_valid:
        logger.error("Input data validation failed for export volume index calculation.")
        return pd.DataFrame(columns=[year_col, quantity_col, 'index'])
    
    yearly_exports = get_grouped_yearly_exports_v2(validated_exports, year_col, [quantity_col])
    if yearly_exports.empty:
        logger.warning("No yearly aggregated data to calculate volume index.")
        return pd.DataFrame(columns=[year_col, quantity_col, 'index'])

    base_year_data = yearly_exports[yearly_exports[year_col] == base_year]
    if base_year_data.empty:
        logger.error(f"Base year {base_year} not found in the data.")
        # Return yearly_exports with NaN index if base year not found but other years exist
        yearly_exports['index'] = np.nan
        return yearly_exports[[year_col, quantity_col, 'index']]
        
    base_quantity = base_year_data[quantity_col].iloc[0]
    
    yearly_exports['index'] = calculate_volume_index_v2(yearly_exports[quantity_col].values, base_quantity)
    logger.info("Export volume index calculated successfully.")
    return yearly_exports[[year_col, quantity_col, 'index']]

@protect("calculate_cagr_v2", OperationType.COMPUTATION)
def calculate_cagr_v2(start_value: float, end_value: float, num_years: int) -> float:
    """Calculate Compound Annual Growth Rate (CAGR) (V2)."""
    if not all(isinstance(v, (int, float)) for v in [start_value, end_value, num_years]):
        logger.error("Inputs start_value, end_value, num_years must be numeric.")
        return 0.0 # Or np.nan based on desired error signaling
    if num_years <= 0:
        logger.warning("Number of years for CAGR must be positive.")
        return 0.0 # Or np.nan
    if start_value <= 0:
        logger.warning(f"Start value is {start_value}, CAGR cannot be meaningfully calculated or will be infinite/undefined.")
        # Behavior for start_value <= 0: if end_value is also <=0, could be 0 or NaN. If end_value > 0, infinite growth.
        # Returning NaN for non-positive start_value to indicate issues.
        return np.nan 
    
    cagr = (float(end_value) / float(start_value)) ** (1.0 / float(num_years)) - 1
    return cagr * 100  # As percentage

@protect("calculate_average_growth_rate_v2", OperationType.COMPUTATION)
@memoize(ttl=7200, level=StorageTier.MEMORY) # Cache for 2 hours
def calculate_average_growth_rate_v2(
    exports: pd.DataFrame,
    start_year: int,
    end_year: int,
    year_col: str = 't',
    value_col: str = 'v'
) -> float:
    """Calculate the average annual growth rate (CAGR) over a period (V2)."""
    logger.info(f"Calculating average growth rate (CAGR) from {start_year} to {end_year}.")
    required_cols = [year_col, value_col]
    validated_exports, is_valid = validate_export_data_v2(exports, required_cols)
    if not is_valid:
        logger.error("Input data validation failed for average growth rate.")
        return 0.0

    filtered_exports = validated_exports[(validated_exports[year_col] >= start_year) & (validated_exports[year_col] <= end_year)]
    yearly_exports = get_grouped_yearly_exports_v2(filtered_exports, year_col, [value_col])

    if yearly_exports.empty:
        logger.warning("No data after filtering and grouping for CAGR.")
        return 0.0

    start_year_data = yearly_exports[yearly_exports[year_col] == start_year]
    end_year_data = yearly_exports[yearly_exports[year_col] == end_year]

    if start_year_data.empty or end_year_data.empty:
        logger.warning(f"Data for start year {start_year} or end year {end_year} not found.")
        return 0.0

    start_val = start_year_data[value_col].iloc[0]
    end_val = end_year_data[value_col].iloc[0]
    num_years_period = end_year - start_year

    if num_years_period <= 0:
        logger.warning("Number of years for CAGR must be positive (end_year > start_year).")
        return 0.0 # Or handle as error based on requirements

    cagr = calculate_cagr_v2(start_val, end_val, num_years_period)
    logger.info(f"CAGR from {start_year} to {end_year} calculated: {cagr:.2f}%")
    return cagr

@protect("process_single_product_growth", OperationType.COMPUTATION)
def process_single_product_growth(prod_code, filtered_exports, year_col, value_col, product_col):
    """Process growth calculation for a single product."""
    product_data = filtered_exports[filtered_exports[product_col] == prod_code]
    yearly_product_data = get_grouped_yearly_exports_v2(product_data, year_col, [value_col])
    
    if yearly_product_data.empty or len(yearly_product_data) < 2:
        # Create NaN growth rates for all years this product appears in filtered_exports
        # or for all unique years in the original range if desired.
        # For simplicity, returning what we have with NaNs.
        temp_df = product_data[[product_col, year_col, value_col]].drop_duplicates().copy()
        temp_df['growth_rate'] = np.nan
        return temp_df
        
    values = yearly_product_data[value_col].values
    previous_values = np.roll(values, 1).astype(float)
    if len(previous_values)>0: previous_values[0] = np.nan
    
    growth_rates_np = calculate_growth_rate_v2(values, previous_values)
    yearly_product_data['growth_rate'] = growth_rates_np if growth_rates_np is not None else np.nan
    yearly_product_data[product_col] = prod_code # Add product_col back
    return yearly_product_data[[product_col, year_col, value_col, 'growth_rate']]

@protect("calculate_growth_by_product_v2", OperationType.COMPUTATION)
@memoize(ttl=3600, level=StorageTier.MEMORY)
def calculate_growth_by_product_v2(
    exports: pd.DataFrame,
    product_col: str = 'k',
    year_col: str = 't',
    value_col: str = 'v',
    start_year: Optional[int] = None,
    end_year: Optional[int] = None
) -> pd.DataFrame:
    """Calculate growth rates by product over time (V2)."""
    logger.info(f"Calculating growth by product: {product_col}.")
    required_cols = [product_col, year_col, value_col]
    validated_exports, is_valid = validate_export_data_v2(exports, required_cols)
    if not is_valid:
        logger.error("Input data validation failed for growth by product.")
        return pd.DataFrame(columns=required_cols + ['growth_rate'])

    filtered_exports = validated_exports.copy()
    if start_year is not None: filtered_exports = filtered_exports[filtered_exports[year_col] >= start_year]
    if end_year is not None: filtered_exports = filtered_exports[filtered_exports[year_col] <= end_year]

    if filtered_exports.empty:
        logger.warning("No data after year filtering for growth by product.")
        return pd.DataFrame(columns=required_cols + ['growth_rate'])

    hardware_manager = get_hardware_manager()
    all_product_results = []

    unique_products = filtered_exports[product_col].unique()

    if hardware_manager.is_hardware_acceleration_available() and hasattr(hardware_manager, 'parallelize') and len(unique_products) > 1: # Condition for parallelization
        logger.debug(f"Attempting parallel processing for {len(unique_products)} products.")
        try:
            # Create partial function with fixed parameters
            def process_product(prod_code):
                return process_single_product_growth(prod_code, filtered_exports, year_col, value_col, product_col)
            
            results_list_of_dfs = hardware_manager.parallelize(process_product, list(unique_products))
            # Filter out None results if any task failed and error handler returned None instead of empty DF
            all_product_results = [df for df in results_list_of_dfs if df is not None and not df.empty]
        except Exception as e_par:
            logger.warning(f"Parallel processing for product growth failed: {e_par}. Falling back to sequential.")
            all_product_results = [process_single_product_growth(p, filtered_exports, year_col, value_col, product_col) for p in unique_products]
    else:
        logger.debug(f"Using sequential processing for {len(unique_products)} products.")
        all_product_results = [process_single_product_growth(p, filtered_exports, year_col, value_col, product_col) for p in unique_products]

    # Filter out None results
    all_product_results = [df for df in all_product_results if df is not None and not df.empty]

    if not all_product_results:
        logger.warning("No product growth results calculated.")
        return pd.DataFrame(columns=required_cols + ['growth_rate'])
        
    final_df = pd.concat(all_product_results, ignore_index=True)
    final_df = final_df.sort_values([product_col, year_col]).reset_index(drop=True)
    logger.info("Growth by product calculation completed.")
    return final_df

if __name__ == '__main__':
    # Project imports
    from yemen_trade_diagnostic.interfaces.logging_interface import configure_logging
    configure_logging(log_level=LogLevel.DEBUG, log_to_console=True, log_to_file=False)
    logger.info("Starting Export Growth Model V2 example.")

    # Sample data
    exports_sample = pd.DataFrame({
        't': [2019, 2020, 2021, 2020, 2021, 2019, 2020, 2021],
        'v': [100, 110, 120, 200, 220, 50, 55, 60],
        'q': [10, 11, 12, 20, 22, 5, 5.5, 6],
        'k': ['P1', 'P1', 'P1', 'P2', 'P2', 'P3', 'P3', 'P3']
    })
    logger.debug(f"Sample data for tests:\n{exports_sample}")

    # Test calculate_export_volume_index_v2
    volume_index = calculate_export_volume_index_v2(exports_sample.copy(), base_year=2020, year_col='t', quantity_col='q')
    logger.info(f"Export Volume Index (base 2020):\n{volume_index}")

    # Test calculate_average_growth_rate_v2 (CAGR)
    avg_growth = calculate_average_growth_rate_v2(exports_sample.copy(), start_year=2019, end_year=2021, year_col='t', value_col='v')
    logger.info(f"Average Growth Rate (CAGR, 2019-2021, total value): {avg_growth:.2f}%")

    # Test calculate_growth_by_product_v2
    growth_by_prod = calculate_growth_by_product_v2(exports_sample.copy(), product_col='k', year_col='t', value_col='v', start_year=2019, end_year=2021)
    logger.info(f"Growth by Product (2019-2021):\n{growth_by_prod}")

    logger.info("Export Growth Model V2 example finished.")