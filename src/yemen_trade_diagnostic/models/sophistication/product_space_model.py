"""
Product Space Model for Yemen Trade Diagnostic (V2)

Visualizes product relationships based on export values and RCA using V2 interfaces.
"""
# Standard library imports
import logging  # Keep for type hinting if needed, but use V2 logger for actual logging
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union

# Third-party imports
import numpy as np
import pandas as pd

# Project imports
from yemen_trade_diagnostic.interfaces.cache_interface import StorageTier, memoize
from yemen_trade_diagnostic.interfaces.hardware_interface import get_hardware_manager
from yemen_trade_diagnostic.errors import protect, OperationType
# V2 Interface Imports
from yemen_trade_diagnostic.interfaces.logging_interface import (
    LogLevel, 
    configure_logging, 
    get_logger, 
    log_execution_time
)
from yemen_trade_diagnostic.interfaces.validation_interface import (
    ValidationIssueLevel, 
    ValidationResult, 
    get_validation_manager
)

logger = get_logger(__name__)

# --- Constants (V2) ---
HS2_CATEGORY_MAPPING_V2: Dict[str, str] = {
    '01': 'Animal Products', '02': 'Animal Products', '03': 'Animal Products', '04': 'Animal Products', '05': 'Animal Products',
    '06': 'Vegetable Products', '07': 'Vegetable Products', '08': 'Vegetable Products', '09': 'Vegetable Products', '10': 'Vegetable Products',
    '11': 'Vegetable Products', '12': 'Vegetable Products', '13': 'Vegetable Products', '14': 'Vegetable Products',
    '15': 'Foodstuffs', '16': 'Foodstuffs', '17': 'Foodstuffs', '18': 'Foodstuffs', '19': 'Foodstuffs',
    '20': 'Foodstuffs', '21': 'Foodstuffs', '22': 'Foodstuffs', '23': 'Foodstuffs', '24': 'Foodstuffs',
    '25': 'Mineral Products', '26': 'Mineral Products', '27': 'Mineral Products',
    '28': 'Chemicals', '29': 'Chemicals', '30': 'Chemicals', '31': 'Chemicals', '32': 'Chemicals',
    '33': 'Chemicals', '34': 'Chemicals', '35': 'Chemicals', '36': 'Chemicals', '37': 'Chemicals', '38': 'Chemicals',
    '39': 'Plastics & Rubbers', '40': 'Plastics & Rubbers',
    '41': 'Raw Hides & Leather', '42': 'Raw Hides & Leather', '43': 'Raw Hides & Leather',
    '44': 'Wood Products', '45': 'Wood Products', '46': 'Wood Products',
    '47': 'Paper Goods', '48': 'Paper Goods', '49': 'Paper Goods',
    '50': 'Textiles', '51': 'Textiles', '52': 'Textiles', '53': 'Textiles', '54': 'Textiles', '55': 'Textiles',
    '56': 'Textiles', '57': 'Textiles', '58': 'Textiles', '59': 'Textiles', '60': 'Textiles', '61': 'Textiles',
    '62': 'Textiles', '63': 'Textiles',
    '64': 'Footwear & Headwear', '65': 'Footwear & Headwear', '66': 'Footwear & Headwear', '67': 'Footwear & Headwear',
    '68': 'Stone & Glass', '69': 'Stone & Glass', '70': 'Stone & Glass', '71': 'Stone & Glass',
    '72': 'Metals', '73': 'Metals', '74': 'Metals', '75': 'Metals', '76': 'Metals', '77': 'Metals',
    '78': 'Metals', '79': 'Metals', '80': 'Metals', '81': 'Metals', '82': 'Metals', '83': 'Metals',
    '84': 'Machinery & Electrical', '85': 'Machinery & Electrical',
    '86': 'Transportation', '87': 'Transportation', '88': 'Transportation', '89': 'Transportation',
    '90': 'Instruments', '91': 'Instruments', '92': 'Instruments',
    '93': 'Arms & Ammunition',
    '94': 'Miscellaneous', '95': 'Miscellaneous', '96': 'Miscellaneous', '97': 'Miscellaneous',
    '98': 'Miscellaneous', '99': 'Miscellaneous'
}

# --- Validation Schemas (V2) ---
EXPORTS_SCHEMA_PRODUCT_SPACE_V2 = {
    'required_columns': ['year', 'exporter', 'hs2', 'value'], # Assuming hs2 for product_column
    'column_types': {'year': 'integer', 'exporter': 'integer', 'hs2': 'string', 'value': 'numeric'}
}
RCA_SCHEMA_PRODUCT_SPACE_V2 = {
    'required_columns': ['year', 'rca', 'hs2', 'advantage_type'], # Assuming hs2 for product_column
    'column_types': {'year': 'integer', 'rca': 'numeric', 'hs2': 'string', 'advantage_type': 'string'}
}

# --- Helper for V2 Validation ---
def _validate_df_product_space_v2(df: pd.DataFrame, schema: Dict, df_name: str) -> bool:
    validation_manager = get_validation_manager()
    val_result = ValidationResult()
    if not isinstance(df, pd.DataFrame): 
        val_result.add_issue(f"{df_name} not a DataFrame.", ValidationIssueLevel.ERROR)
    else:
        # Try to handle column mappings for common alternative names
        missing_cols = [col for col in schema.get('required_columns', []) if col not in df.columns]
        if missing_cols:
            alternative_mappings = {
                'exporter': ['exporter_iso', 'reporter_code', 'i'],
                'hs2': ['product_code', 'k', 'product'],
                'value': ['trade_value', 'trade_value_usd', 'v'],
                'rca': ['rca_value', 'rca_index'],
                'advantage_type': ['advantage', 'comparative_advantage']
            }

            # Apply mappings for missing columns
            for missing_col in missing_cols:
                for alt_col in alternative_mappings.get(missing_col, []):
                    if alt_col in df.columns:
                        logger.info(f"Mapping column {alt_col} to {missing_col} for {df_name}")
                        df[missing_col] = df[alt_col]
                        break

            # Update the missing columns after mapping
            missing_cols = [col for col in schema.get('required_columns', []) if col not in df.columns]

            # Add validation issues for any remaining missing columns
            for col in missing_cols:
                val_result.add_issue(f"Required col '{col}' missing in {df_name}.", ValidationIssueLevel.ERROR, location=col)

        if val_result.is_valid:
            for col, type_str in schema.get('column_types', {}).items():
                if col in df.columns:
                    actual_dt = df[col].dtype
                    ok = False
                    if type_str == 'integer':
                        ok = pd.api.types.is_integer_dtype(actual_dt)
                        # Try to convert if possible
                        if not ok and pd.api.types.is_numeric_dtype(actual_dt):
                            try:
                                df[col] = df[col].astype(int)
                                ok = True
                                logger.info(f"Converted column {col} to integer type in {df_name}")
                            except:
                                pass
                    elif type_str == 'string':
                        ok = pd.api.types.is_string_dtype(actual_dt) or pd.api.types.is_object_dtype(actual_dt)
                        # Try to convert if possible
                        if not ok:
                            try:
                                df[col] = df[col].astype(str)
                                ok = True
                                logger.info(f"Converted column {col} to string type in {df_name}")
                            except:
                                pass
                    elif type_str == 'numeric':
                        ok = pd.api.types.is_numeric_dtype(actual_dt)
                        # Try to convert if possible
                        if not ok:
                            try:
                                df[col] = pd.to_numeric(df[col], errors='coerce')
                                ok = True
                                logger.info(f"Converted column {col} to numeric type in {df_name}")
                            except:
                                pass

                    if not ok:
                        val_result.add_issue(f"Col '{col}' in {df_name} wrong type.", ValidationIssueLevel.ERROR, location=col)

    if not val_result.is_valid:
        logger.error(f"Validation: {df_name} {val_result.issues}")
        return False
    return True

@memoize(level=StorageTier.MEMORY)
def map_hs2_to_category_v2(hs2_code: Optional[str]) -> str:
    if not hs2_code or not isinstance(hs2_code, str) or len(hs2_code) < 2 or not hs2_code[:2].isdigit():
        raise ValueError(f"Invalid hs2_code provided for category mapping: {hs2_code}")
    return HS2_CATEGORY_MAPPING_V2.get(hs2_code[:2], 'Other') # Ensure only first 2 chars used

@protect("calculate_product_space_data_v2", OperationType.COMPUTATION)
@memoize(ttl=3600, level=StorageTier.MEMORY)
@log_execution_time(logger=logger)
def calculate_product_space_data_v2(
    exports_df: pd.DataFrame,
    rca_data_df: pd.DataFrame,
    year: int,
    country_code: int = 887, # Yemen
    product_col_name: str = 'hs2',
    value_col_name: str = 'value',
    exporter_col_name: str = 'exporter', # Added for clarity, assuming this is in exports_df
    min_export_val: float = 1000
) -> pd.DataFrame:
    logger.info(f"Calculating product space data for year {year}, country {country_code} (V2)")
    
    # Update schema to include exporter_col_name
    exports_schema_ps = {
        'required_columns': ['year', exporter_col_name, product_col_name, value_col_name],
        'column_types': {'year': 'integer', exporter_col_name: 'integer', product_col_name: 'string', value_col_name: 'numeric'}
    }
    if not _validate_df_product_space_v2(exports_df, exports_schema_ps, "exports_df"): 
        raise ValueError("Validation failed for exports_df in calculate_product_space_data_v2")
    # Assuming RCA_SCHEMA_PRODUCT_SPACE_V2 is fine (it uses 'hs2' which matches default product_col_name)
    if not _validate_df_product_space_v2(rca_data_df, RCA_SCHEMA_PRODUCT_SPACE_V2, "rca_data_df"): 
        raise ValueError("Validation failed for rca_data_df in calculate_product_space_data_v2")

    hw_manager = get_hardware_manager()
    use_hw_accel = hw_manager.is_hardware_acceleration_available()

    # Filter exports for the specific year and country
    year_exports = exports_df[(exports_df['year'] == year) & (exports_df[exporter_col_name] == country_code)].copy()
    if year_exports.empty: 
        logger.warning(f"No export data for year {year}, country {country_code}.")
        raise ValueError(f"No export data found for year {year}, country {country_code}.")

    # Attempt to optimize the DataFrame early
    if use_hw_accel:
        try:
            accelerated_year_exports = hw_manager.accelerate_dataframe(year_exports) # General acceleration
            if accelerated_year_exports is not None:
                year_exports = accelerated_year_exports
                logger.debug("year_exports DataFrame potentially accelerated.")
        except Exception as e: 
            logger.warning(f"Hardware acceleration failed: {e}")
            
    # Aggregate exports by product
    # Placeholder for hw_manager.accelerated_groupby_agg(df, by, agg_dict) if available
    product_exports = year_exports.groupby(product_col_name, observed=True)[value_col_name].sum().reset_index()
    product_exports.rename(columns={value_col_name: 'yemen_exports'}, inplace=True)
    product_exports = product_exports[product_exports['yemen_exports'] >= min_export_val]
    
    if product_exports.empty: 
        logger.warning(f"No products found after filtering by min_export_val ({min_export_val}).")
        raise ValueError(f"No products found meeting minimum export value ({min_export_val}) for year {year}, country {country_code}.")

    year_rca = rca_data_df[rca_data_df['year'] == year].copy()
    product_space_data: pd.DataFrame

    if year_rca.empty: 
        logger.warning(f"No RCA data for year {year}. Product space will lack RCA info.")
        product_space_data = product_exports.copy()
        product_space_data['rca'] = 0.0
        product_space_data['advantage_type'] = 'Unknown'
    else:
        # Placeholder for hw_manager.accelerated_merge(df1, df2, on, how) if available
        product_space_data = pd.merge(product_exports, year_rca[[product_col_name, 'rca', 'advantage_type']], on=product_col_name, how='left')
        product_space_data['rca'] = product_space_data['rca'].fillna(0)
        product_space_data['advantage_type'] = product_space_data['advantage_type'].fillna('Unknown')

    # Map product categories
    # Placeholder for hw_manager.parallel_map(series, function) if available
    product_space_data['category'] = product_space_data[product_col_name].apply(map_hs2_to_category_v2)
    
    # Calculate log_exports (vectorized, typically fast in pandas/numpy)
    # Ensure 'yemen_exports' is numeric and handle non-positive values for log
    safe_export_values = product_space_data['yemen_exports'].astype(float).clip(lower=1) # Clip at 1 to avoid log(0) or log(negative)
    product_space_data['log_exports'] = np.log10(safe_export_values)
    
    # Sort by export value
    # Placeholder for hw_manager.accelerated_sort(df, by, ascending) if available
    product_space_data = product_space_data.sort_values('yemen_exports', ascending=False)
    
    logger.info(f"Product space data calculated: {len(product_space_data)} products.")
    return product_space_data

@protect("calculate_product_proximity_v2", OperationType.COMPUTATION)
@memoize(ttl=3600*24, level=StorageTier.DISK)
@log_execution_time(logger=logger)
def calculate_product_proximity_v2(
    rca_data_all_countries: pd.DataFrame,
    year: int,
    product_col_name: str = 'hs2',
    exporter_col_name: str = 'exporter', # Ensure this column exists in rca_data_all_countries
    rca_col_name: str = 'rca',
    min_countries_for_rca: int = 5,
    rca_threshold_proximity: float = 1.0,
    year_col_name: str = 'year' # Added for filtering year_rca
) -> pd.DataFrame:
    logger.info(f"Calculating product proximity for year {year} (V2)")
    if rca_data_all_countries.empty or not all(c in rca_data_all_countries.columns for c in [year_col_name, exporter_col_name, product_col_name, rca_col_name]):
        raise ValueError("RCA data for proximity is empty or missing required columns.")

    year_rca = rca_data_all_countries[rca_data_all_countries[year_col_name] == year].copy()
    if year_rca.empty: 
        logger.warning(f"No RCA data for year {year}.")
        raise ValueError(f"No RCA data found for year {year} to calculate proximity.")

    country_product_matrix = year_rca.pivot_table(index=exporter_col_name, columns=product_col_name, values=rca_col_name, aggfunc='max').fillna(0)
    binary_matrix = (country_product_matrix > rca_threshold_proximity).astype(int)
    
    product_counts = binary_matrix.sum(axis=0)
    valid_products = product_counts[product_counts >= min_countries_for_rca].index
    if not valid_products.any(): 
        logger.warning(f"No products with >= {min_countries_for_rca} countries having RCA > {rca_threshold_proximity}.")
        raise ValueError(f"No products found meeting RCA ubiquity criteria ({min_countries_for_rca} countries with RCA > {rca_threshold_proximity}).")
    
    filtered_binary_matrix = binary_matrix[valid_products].values # Numpy array for faster ops
    num_valid_products = filtered_binary_matrix.shape[1]
    
    # Co-occurrence matrix C_ij = M_ik * M_jk (sum over k countries)
    # M is (countries x products), so M.T @ M gives (products x products) co-occurrence
    cooccurrence_matrix: Optional[np.ndarray] = None
    hw_manager = get_hardware_manager()
    use_hw_accel_matmul = hw_manager.is_hardware_acceleration_available() # Generic check, could be specific like AccelerationType.GPU

    if use_hw_accel_matmul:
        logger.debug("Attempting hardware-accelerated co-occurrence matrix (filtered_binary_matrix.T @ filtered_binary_matrix).")
        try:
            # Ensure arrays are float32 for many GPU backends, though int8 might also work for some boolean ops.
            # For general matmul, float32 is safer.
            matrix_T = filtered_binary_matrix.T.astype(np.float32)
            matrix_ = filtered_binary_matrix.astype(np.float32)
            # Using accelerate_matrix_operation as a placeholder for A @ B
            cooccurrence_matrix = hw_manager.accelerate_matrix_operation(matrix_T, "multiply", matrix_)
            if cooccurrence_matrix is None:
                logger.warning("Hardware-accelerated co-occurrence calculation returned None. Falling back.")
                use_hw_accel_matmul = False # Fallback
            else:
                logger.info("Hardware-accelerated co-occurrence matrix calculation successful.")
        except Exception as e:
            logger.warning(f"Hardware-accelerated co-occurrence calculation failed: {e}. Falling back.")
            use_hw_accel_matmul = False # Fallback

    if not use_hw_accel_matmul:
        logger.debug("Using NumPy for co-occurrence matrix calculation.")
        cooccurrence_matrix = filtered_binary_matrix.T @ filtered_binary_matrix
    
    if cooccurrence_matrix is None: # Should not happen
        raise RuntimeError("Co-occurrence matrix is None after calculation attempts.")
            
    # k_p is the number of countries exporting product p with RCA > threshold_proximity
    k_p = np.diag(cooccurrence_matrix) # This is sum(M_cp) for each product p, effectively product_counts for valid_products
    
    proximity_values = np.zeros((num_valid_products, num_valid_products))
    for i in range(num_valid_products):
        for j in range(i, num_valid_products): # Symmetric matrix
            if k_p[i] == 0 or k_p[j] == 0:
                proximity = 0.0
            else:
                # Proximity = min ( P(j|i), P(i|j) ) = min ( C_ij/k_i, C_ij/k_j )
                prob_j_given_i = cooccurrence_matrix[i, j] / k_p[i]
                prob_i_given_j = cooccurrence_matrix[i, j] / k_p[j]
                proximity = min(prob_j_given_i, prob_i_given_j)
            proximity_values[i, j] = proximity
            proximity_values[j, i] = proximity
            
    proximity_df = pd.DataFrame(proximity_values, index=valid_products, columns=valid_products)
    proximity_long_df = proximity_df.stack().reset_index()
    proximity_long_df.columns = ['product1', 'product2', 'proximity']
    proximity_long_df = proximity_long_df[proximity_long_df['proximity'] > 0] # Filter for actual links
    logger.info(f"Product proximity calculated for {len(valid_products)} products.")
    return proximity_long_df

@memoize(level=StorageTier.MEMORY)
@log_execution_time(logger=logger)
def get_product_space_chart_data_v2(
    product_space_df: pd.DataFrame,
    proximity_df: Optional[pd.DataFrame] = None,
    product_col_name: str = 'hs2',
    top_n_products: int = 50,
    min_proximity_link: float = 0.1 # Adjusted default based on common proximity values
) -> Dict[str, Any]:
    if product_space_df.empty: 
        logger.warning("Product space data is empty for chart.")
        raise ValueError("Input product_space_df cannot be empty for chart data generation.")
    
    top_products_df = product_space_df.nlargest(top_n_products, 'yemen_exports')
    if top_products_df.empty: 
        logger.warning("No top products found for chart.")
        raise ValueError("Could not determine top products from product_space_df.")

    chart_data = {
        'product': top_products_df[product_col_name].tolist(),
        'rca': top_products_df['rca'].tolist(),
        'yemen_exports': top_products_df['yemen_exports'].tolist(),
        'category': top_products_df['category'].tolist(),
        'advantage_type': top_products_df['advantage_type'].tolist() if 'advantage_type' in top_products_df else ['Unknown'] * len(top_products_df)
    }
    if proximity_df is not None and not proximity_df.empty:
        top_product_list = top_products_df[product_col_name].tolist()
        filtered_proximity = proximity_df[
            proximity_df['product1'].isin(top_product_list) &
            proximity_df['product2'].isin(top_product_list) &
            (proximity_df['product1'] != proximity_df['product2']) & # Avoid self-loops unless meaningful
            (proximity_df['proximity'] >= min_proximity_link)
        ]
        chart_data['links'] = filtered_proximity.to_dict(orient='records')
    else:
        chart_data['links'] = []
    logger.debug("Product space chart data prepared.")
    return chart_data

if __name__ == '__main__':
    configure_logging(log_level=LogLevel.DEBUG, log_to_console=True)
    logger.info("--- Running Product Space Model V2 Example ---")

    # Sample Data
    exports_sample = pd.DataFrame({
        'year': [2020, 2020, 2020, 2020, 2021, 2021],
        'exporter': [887, 887, 887, 123, 123, 887],
        'hs2': ['01', '02', '03', '01', '02', '01'],
        'value': [10000, 50000, 2000, 15000, 6000, 12000]
    })
    rca_sample = pd.DataFrame({
        'year': [2020, 2020, 2020, 2020, 2021, 2021],
        'exporter': [887, 887, 887, 123, 123, 887],
        'hs2': ['01', '02', '03', '01', '02', '01'],
        'rca': [1.5, 0.8, 2.5, 0.5, 1.2, 1.8],
        'advantage_type': ['Slight Advantage', 'Slight Disadvantage', 'Strong Advantage', 'Strong Disadvantage', 'Slight Advantage', 'Slight Advantage']
    })
    rca_all_countries_sample = pd.concat([
        rca_sample, 
        pd.DataFrame({
            'year': [2020, 2020, 2020], 'exporter': [456, 456, 456], 'hs2': ['01', '02', '04'], 'rca': [2.2, 0.1, 3.0], 'advantage_type':['SA','SD','SA']
        })
    ], ignore_index=True)

    logger.info("\n--- Testing calculate_product_space_data_v2 ---")
    ps_data = calculate_product_space_data_v2(exports_sample, rca_sample, year=2020, country_code=887)
    logger.info(f"Product Space Data (Yemen, 2020):\n{ps_data}")

    logger.info("\n--- Testing calculate_product_proximity_v2 ---")
    prox_data = calculate_product_proximity_v2(rca_all_countries_sample, year=2020, exporter_col_name='exporter', rca_col_name='rca')
    logger.info(f"Product Proximity Data (2020):\n{prox_data}")

    logger.info("\n--- Testing get_product_space_chart_data_v2 ---")
    chart_data = get_product_space_chart_data_v2(ps_data, prox_data)
    if chart_data:
        logger.info(f"Chart Data Nodes: {len(chart_data.get('product',[]))}, Links: {len(chart_data.get('links',[]))}")
        # logger.info(f"Full Chart Data: {chart_data}") # Can be verbose
    else:
        logger.warning("Chart data generation failed.")
        
    logger.info("--- Product Space Model V2 Example Finished ---")