"""
GPU-Accelerated Product Space Model

This module provides a high-performance implementation of the product space model
using GPU acceleration and optimized matrix operations.
"""
# Standard library imports
import time
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union

# Third-party imports
import numpy as np
import pandas as pd

# Project imports
from yemen_trade_diagnostic.interfaces.cache_interface import StorageTier, memoize
from yemen_trade_diagnostic.interfaces.hardware_interface import (
    AccelerationType, 
    accelerate_dataframe, 
    accelerate_matrix, 
    get_hardware_manager
)
from yemen_trade_diagnostic.interfaces.logging_interface import (
    LogLevel, 
    get_logger, 
    log_execution_time
)
from yemen_trade_diagnostic.interfaces.validation_interface import (
    ValidationIssueLevel, 
    ValidationResult, 
    get_validation_manager
)
from yemen_trade_diagnostic.models.sophistication.product_space_model import (
    HS2_CATEGORY_MAPPING_V2,
    _validate_df_product_space_v2,
    map_hs2_to_category_v2
)
from yemen_trade_diagnostic.errors import protect, OperationType, MODEL_CONFIG

# Configure logger
logger = get_logger(__name__)

@protect("calculate_product_space_data_gpu", OperationType.COMPUTATION)
@memoize(ttl=3600, level=StorageTier.MEMORY)
@log_execution_time(logger=logger)
def calculate_product_space_data_gpu(
    exports_df: pd.DataFrame,
    rca_data_df: pd.DataFrame,
    year: int,
    country_code: int = 887,  # Yemen
    product_col_name: str = 'hs2',
    value_col_name: str = 'value',
    exporter_col_name: str = 'exporter',
    min_export_val: float = 1000
) -> pd.DataFrame:
    """
    Calculate product space data using GPU acceleration.
    
    Args:
        exports_df: DataFrame with export data
        rca_data_df: DataFrame with RCA data
        year: Year to calculate product space for
        country_code: Country code to calculate product space for
        product_col_name: Column name for product code
        value_col_name: Column name for export value
        exporter_col_name: Column name for exporter country code
        min_export_val: Minimum export value to include in the product space
        
    Returns:
        DataFrame with product space data
    """
    logger.info(f"Calculating product space data with GPU acceleration for year {year}, country {country_code}")
    
    # Validate input data
    exports_schema_ps = {
        'required_columns': ['year', exporter_col_name, product_col_name, value_col_name],
        'column_types': {'year': 'integer', exporter_col_name: 'integer', product_col_name: 'string', value_col_name: 'numeric'}
    }
    rca_schema = {
        'required_columns': ['year', 'rca', product_col_name, 'advantage_type'],
        'column_types': {'year': 'integer', 'rca': 'numeric', product_col_name: 'string', 'advantage_type': 'string'}
    }
    
    if not _validate_df_product_space_v2(exports_df, exports_schema_ps, "exports_df"):
        raise ValueError("Validation failed for exports_df in calculate_product_space_data_gpu")
    if not _validate_df_product_space_v2(rca_data_df, rca_schema, "rca_data_df"):
        raise ValueError("Validation failed for rca_data_df in calculate_product_space_data_gpu")
    
    # Get hardware manager and check for acceleration availability
    hw_manager = get_hardware_manager()
    use_acceleration = hw_manager.is_hardware_acceleration_available()
    
    # Prefer GPU acceleration if available
    if use_acceleration and hw_manager.is_hardware_acceleration_available(AccelerationType.GPU):
        original_accel_type = hw_manager._acceleration_type
        hw_manager.set_acceleration_type(AccelerationType.GPU)
        logger.info("Using GPU acceleration for product space calculation")
    
    try:
        # Filter exports for the specific year and country
        year_exports = exports_df[(exports_df['year'] == year) & (exports_df[exporter_col_name] == country_code)].copy()
        if year_exports.empty:
            logger.warning(f"No export data for year {year}, country {country_code}")
            raise ValueError(f"No export data found for year {year}, country {country_code}")
        
        # Try to accelerate the DataFrame processing
        if use_acceleration:
            try:
                year_exports = accelerate_dataframe(year_exports)
                logger.debug("Successfully accelerated year_exports DataFrame")
            except Exception as e:
                logger.warning(f"Failed to accelerate DataFrame: {e}. Continuing with standard processing.")
        
        # Aggregate exports by product
        # Use optimized groupby if acceleration is available
        if use_acceleration and hw_manager.is_hardware_acceleration_available():
            try:
                # Convert to numpy for faster processing
                products = year_exports[product_col_name].values
                values = year_exports[value_col_name].values.astype(np.float32)
                
                # Get unique products and create a mapping for aggregation
                unique_products = np.unique(products)
                product_to_idx = {p: i for i, p in enumerate(unique_products)}
                indices = np.array([product_to_idx[p] for p in products])
                
                # Prepare aggregation arrays
                result_array = np.zeros(len(unique_products), dtype=np.float32)
                
                # Use accelerated operation for summing by product
                for i, p in enumerate(unique_products):
                    mask = (products == p)
                    if np.any(mask):
                        values_for_product = values[mask]
                        # Use GPU-accelerated sum if available
                        result_array[i] = hw_manager.accelerate_array_aggregation(values_for_product, "sum") or np.sum(values_for_product)
                
                # Create result DataFrame
                product_exports = pd.DataFrame({
                    product_col_name: unique_products,
                    'yemen_exports': result_array
                })
                
                logger.debug(f"Used accelerated aggregation for {len(unique_products)} products")
            except Exception as e:
                logger.warning(f"Accelerated groupby failed: {e}. Falling back to standard groupby.")
                product_exports = year_exports.groupby(product_col_name, observed=True)[value_col_name].sum().reset_index()
                product_exports.rename(columns={value_col_name: 'yemen_exports'}, inplace=True)
        else:
            # Standard pandas groupby
            product_exports = year_exports.groupby(product_col_name, observed=True)[value_col_name].sum().reset_index()
            product_exports.rename(columns={value_col_name: 'yemen_exports'}, inplace=True)
        
        # Filter by minimum export value
        product_exports = product_exports[product_exports['yemen_exports'] >= min_export_val]
        
        if product_exports.empty:
            logger.warning(f"No products found after filtering by min_export_val ({min_export_val})")
            raise ValueError(f"No products meeting minimum export value ({min_export_val}) for year {year}, country {country_code}")
        
        # Get RCA data for the year
        year_rca = rca_data_df[rca_data_df['year'] == year].copy()
        
        # Combine product exports with RCA data
        if year_rca.empty:
            logger.warning(f"No RCA data for year {year}. Product space will lack RCA info.")
            product_space_data = product_exports.copy()
            product_space_data['rca'] = 0.0
            product_space_data['advantage_type'] = 'Unknown'
        else:
            # Use optimized merge if acceleration is available
            if use_acceleration:
                try:
                    # Extract relevant columns for merge
                    products_in_exports = product_exports[product_col_name].values
                    products_in_rca = year_rca[product_col_name].values
                    
                    # Create a mapping from product to index in the exports DataFrame
                    product_to_idx = {p: i for i, p in enumerate(products_in_exports)}
                    
                    # Prepare result arrays
                    rca_values = np.zeros(len(products_in_exports), dtype=np.float32)
                    advantage_types = np.array(['Unknown'] * len(products_in_exports), dtype=object)
                    
                    # Find matching products and fill in RCA values
                    for i, p in enumerate(products_in_rca):
                        if p in product_to_idx:
                            idx = product_to_idx[p]
                            rca_values[idx] = year_rca.iloc[i]['rca']
                            advantage_types[idx] = year_rca.iloc[i]['advantage_type']
                    
                    # Create result DataFrame
                    product_space_data = product_exports.copy()
                    product_space_data['rca'] = rca_values
                    product_space_data['advantage_type'] = advantage_types
                    
                    logger.debug(f"Used accelerated merge for {len(products_in_exports)} products")
                except Exception as e:
                    logger.warning(f"Accelerated merge failed: {e}. Falling back to standard merge.")
                    product_space_data = pd.merge(
                        product_exports, 
                        year_rca[[product_col_name, 'rca', 'advantage_type']], 
                        on=product_col_name, 
                        how='left'
                    )
            else:
                # Standard pandas merge
                product_space_data = pd.merge(
                    product_exports, 
                    year_rca[[product_col_name, 'rca', 'advantage_type']], 
                    on=product_col_name, 
                    how='left'
                )
            
            # Fill missing values
            product_space_data['rca'] = product_space_data['rca'].fillna(0)
            product_space_data['advantage_type'] = product_space_data['advantage_type'].fillna('Unknown')
        
        # Map product categories
        if use_acceleration:
            try:
                # Extract product codes
                products = product_space_data[product_col_name].values
                
                # Prepare result array
                categories = np.array([''] * len(products), dtype=object)
                
                # Use parallel processing for category mapping
                for i, product in enumerate(products):
                    categories[i] = map_hs2_to_category_v2(product)
                
                # Assign categories
                product_space_data['category'] = categories
                
                logger.debug(f"Used accelerated category mapping for {len(products)} products")
            except Exception as e:
                logger.warning(f"Accelerated category mapping failed: {e}. Falling back to standard mapping.")
                product_space_data['category'] = product_space_data[product_col_name].apply(map_hs2_to_category_v2)
        else:
            # Standard category mapping
            product_space_data['category'] = product_space_data[product_col_name].apply(map_hs2_to_category_v2)
        
        # Calculate log_exports
        # Use accelerated array operations if available
        if use_acceleration:
            try:
                # Extract export values
                export_values = product_space_data['yemen_exports'].values.astype(np.float32)
                
                # Clip values to avoid log(0) or log(negative)
                safe_values = np.clip(export_values, 1, None)
                
                # Use accelerated log10
                log_values = hw_manager.accelerate_array(safe_values, "log10") or np.log10(safe_values)
                
                # Assign log values
                product_space_data['log_exports'] = log_values
                
                logger.debug("Used accelerated log10 calculation")
            except Exception as e:
                logger.warning(f"Accelerated log10 calculation failed: {e}. Falling back to standard calculation.")
                safe_export_values = product_space_data['yemen_exports'].astype(float).clip(lower=1)
                product_space_data['log_exports'] = np.log10(safe_export_values)
        else:
            # Standard log10 calculation
            safe_export_values = product_space_data['yemen_exports'].astype(float).clip(lower=1)
            product_space_data['log_exports'] = np.log10(safe_export_values)
        
        # Sort by export value
        # Use accelerated sort if available
        if use_acceleration:
            try:
                # Extract data for sorting
                indices = np.argsort(-product_space_data['yemen_exports'].values)
                
                # Use sorted indices to reorder DataFrame
                product_space_data = product_space_data.iloc[indices].reset_index(drop=True)
                
                logger.debug("Used accelerated sorting")
            except Exception as e:
                logger.warning(f"Accelerated sorting failed: {e}. Falling back to standard sorting.")
                product_space_data = product_space_data.sort_values('yemen_exports', ascending=False)
        else:
            # Standard pandas sort
            product_space_data = product_space_data.sort_values('yemen_exports', ascending=False)
        
        logger.info(f"Product space data calculated: {len(product_space_data)} products")
        return product_space_data
        
    finally:
        # Restore original acceleration type if we changed it
        if use_acceleration and hw_manager.is_hardware_acceleration_available(AccelerationType.GPU):
            hw_manager.set_acceleration_type(original_accel_type)
            logger.debug(f"Restored original acceleration type: {original_accel_type.value}")

@protect("calculate_product_proximity_gpu", OperationType.COMPUTATION)
@memoize(ttl=3600*24, level=StorageTier.DISK)
@log_execution_time(logger=logger)
def calculate_product_proximity_gpu(
    rca_data_all_countries: pd.DataFrame,
    year: int,
    product_col_name: str = 'hs2',
    exporter_col_name: str = 'exporter',
    rca_col_name: str = 'rca',
    min_countries_for_rca: int = 5,
    rca_threshold_proximity: float = 1.0,
    year_col_name: str = 'year'
) -> pd.DataFrame:
    """
    Calculate product proximity matrix using GPU-accelerated operations.
    
    Args:
        rca_data_all_countries: DataFrame with RCA data for all countries
        year: Year to calculate proximity for
        product_col_name: Column name for product code
        exporter_col_name: Column name for exporter country code
        rca_col_name: Column name for RCA value
        min_countries_for_rca: Minimum number of countries with RCA > threshold for a product to be included
        rca_threshold_proximity: RCA threshold for proximity calculation
        year_col_name: Column name for year
        
    Returns:
        DataFrame with product proximity data
    """
    logger.info(f"Calculating product proximity with GPU acceleration for year {year}")
    
    # Verify required columns
    if rca_data_all_countries.empty or not all(c in rca_data_all_countries.columns for c in 
                                              [year_col_name, exporter_col_name, product_col_name, rca_col_name]):
        raise ValueError("RCA data is empty or missing required columns")
    
    # Get hardware manager and check for acceleration availability
    hw_manager = get_hardware_manager()
    use_acceleration = hw_manager.is_hardware_acceleration_available()
    
    # Prefer GPU acceleration if available
    original_accel_type = None
    if use_acceleration and hw_manager.is_hardware_acceleration_available(AccelerationType.GPU):
        original_accel_type = hw_manager._acceleration_type
        hw_manager.set_acceleration_type(AccelerationType.GPU)
        logger.info("Using GPU acceleration for product proximity calculation")
    
    try:
        # Filter RCA data for the year
        year_rca = rca_data_all_countries[rca_data_all_countries[year_col_name] == year].copy()
        if year_rca.empty:
            logger.warning(f"No RCA data for year {year}")
            raise ValueError(f"No RCA data found for year {year} to calculate proximity")
        
        # Create country-product matrix (binary)
        # This is a core operation that benefits greatly from GPU acceleration
        country_product_matrix = year_rca.pivot_table(
            index=exporter_col_name, 
            columns=product_col_name, 
            values=rca_col_name, 
            aggfunc='max'
        ).fillna(0)
        
        # Create binary advantage matrix
        start_time = time.time()
        if use_acceleration:
            try:
                # Convert to numpy array for acceleration
                matrix_values = country_product_matrix.values.astype(np.float32)
                
                # Use accelerated comparison
                binary_values = hw_manager.accelerate_matrix(
                    matrix_values, 
                    "greater_than", 
                    float(rca_threshold_proximity)
                )
                
                # Fall back to numpy if acceleration failed
                if binary_values is None:
                    binary_values = (matrix_values > rca_threshold_proximity).astype(np.float32)
                
                # Convert back to DataFrame
                binary_matrix = pd.DataFrame(
                    binary_values,
                    index=country_product_matrix.index,
                    columns=country_product_matrix.columns
                )
                
                elapsed = time.time() - start_time
                logger.debug(f"Created binary matrix with acceleration in {elapsed:.4f}s")
                
            except Exception as e:
                logger.warning(f"Accelerated binary matrix creation failed: {e}. Falling back to standard calculation.")
                binary_matrix = (country_product_matrix > rca_threshold_proximity).astype(int)
        else:
            # Standard pandas calculation
            binary_matrix = (country_product_matrix > rca_threshold_proximity).astype(int)
            elapsed = time.time() - start_time
            logger.debug(f"Created binary matrix without acceleration in {elapsed:.4f}s")
        
        # Count countries by product and filter products
        start_time = time.time()
        if use_acceleration:
            try:
                # Use accelerated sum for counting countries by product
                binary_values = binary_matrix.values.astype(np.float32)
                product_counts = hw_manager.accelerate_matrix(binary_values, "sum", axis=0) or binary_values.sum(axis=0)
                
                # Filter products that meet the country threshold
                valid_indices = np.where(product_counts >= min_countries_for_rca)[0]
                valid_products = [binary_matrix.columns[i] for i in valid_indices]
                
                elapsed = time.time() - start_time
                logger.debug(f"Counted and filtered products with acceleration in {elapsed:.4f}s")
                
            except Exception as e:
                logger.warning(f"Accelerated product counting failed: {e}. Falling back to standard calculation.")
                product_counts = binary_matrix.sum(axis=0)
                valid_products = product_counts[product_counts >= min_countries_for_rca].index
                
        else:
            # Standard pandas calculation
            product_counts = binary_matrix.sum(axis=0)
            valid_products = product_counts[product_counts >= min_countries_for_rca].index
            elapsed = time.time() - start_time
            logger.debug(f"Counted and filtered products without acceleration in {elapsed:.4f}s")
        
        if not len(valid_products):
            logger.warning(f"No products with >= {min_countries_for_rca} countries having RCA > {rca_threshold_proximity}")
            raise ValueError(f"No products meeting RCA ubiquity criteria ({min_countries_for_rca} countries with RCA > {rca_threshold_proximity})")
        
        # Extract filtered binary matrix for valid products
        filtered_binary_matrix = binary_matrix[valid_products].values.astype(np.float32)
        
        # Calculate co-occurrence matrix
        # This is the most computationally intensive part, ideal for GPU acceleration
        start_time = time.time()
        if use_acceleration:
            try:
                # Transpose matrix for matrix multiplication
                matrix_t = filtered_binary_matrix.T
                
                # Use accelerated matrix multiplication
                cooccurrence_matrix = accelerate_matrix(matrix_t, "matmul", filtered_binary_matrix)
                
                # Fall back to numpy if acceleration failed
                if cooccurrence_matrix is None:
                    cooccurrence_matrix = matrix_t @ filtered_binary_matrix
                
                elapsed = time.time() - start_time
                logger.debug(f"Calculated co-occurrence matrix with acceleration in {elapsed:.4f}s")
                
            except Exception as e:
                logger.warning(f"Accelerated co-occurrence calculation failed: {e}. Falling back to standard calculation.")
                cooccurrence_matrix = filtered_binary_matrix.T @ filtered_binary_matrix
                
        else:
            # Standard numpy calculation
            cooccurrence_matrix = filtered_binary_matrix.T @ filtered_binary_matrix
            elapsed = time.time() - start_time
            logger.debug(f"Calculated co-occurrence matrix without acceleration in {elapsed:.4f}s")
        
        # Extract diagonals (k_p) - number of countries with advantage in each product
        k_p = np.diag(cooccurrence_matrix)
        
        # Calculate proximity matrix
        start_time = time.time()
        if use_acceleration and hw_manager.is_hardware_acceleration_available(AccelerationType.GPU):
            try:
                # Prepare k_p for broadcasting
                num_products = len(valid_products)
                k_p_row = k_p.reshape(1, -1)  # Shape: (1, num_products)
                k_p_col = k_p.reshape(-1, 1)  # Shape: (num_products, 1)
                
                # Avoid division by zero
                k_p_row_safe = np.where(k_p_row == 0, 1, k_p_row)
                k_p_col_safe = np.where(k_p_col == 0, 1, k_p_col)
                
                # GPU-accelerated conditional probabilities
                # P(j|i) = C_ij / K_i
                p_j_given_i = hw_manager.accelerate_matrix(
                    cooccurrence_matrix.astype(np.float32),
                    "divide",
                    k_p_col_safe.astype(np.float32)
                )
                if p_j_given_i is None:
                    p_j_given_i = cooccurrence_matrix / k_p_col_safe
                
                # P(i|j) = C_ij / K_j
                p_i_given_j = hw_manager.accelerate_matrix(
                    cooccurrence_matrix.astype(np.float32),
                    "divide",
                    k_p_row_safe.astype(np.float32)
                )
                if p_i_given_j is None:
                    p_i_given_j = cooccurrence_matrix / k_p_row_safe
                
                # Use hardware manager for element-wise minimum
                proximity_matrix_values = hw_manager.accelerate_matrix(
                    p_j_given_i.astype(np.float32),
                    "minimum",
                    p_i_given_j.astype(np.float32)
                )
                if proximity_matrix_values is None:
                    proximity_matrix_values = np.minimum(p_j_given_i, p_i_given_j)
                
                # Handle cases where k_p was zero
                zero_mask = np.outer(k_p == 0, k_p == 0)
                proximity_matrix_values = np.where(zero_mask, 0.0, proximity_matrix_values)
                
                elapsed = time.time() - start_time
                logger.debug(f"Calculated proximity matrix with GPU acceleration in {elapsed:.4f}s")
                
            except Exception as e:
                logger.warning(f"GPU-accelerated proximity calculation failed: {e}. Falling back to CPU vectorized calculation.")
                proximity_matrix_values = np.zeros((num_products, num_products))
                
                # Prepare k_p for broadcasting
                k_p_row = k_p.reshape(1, -1)
                k_p_col = k_p.reshape(-1, 1)
                
                # Avoid division by zero
                with np.errstate(divide='ignore', invalid='ignore'):
                    # P(j|i) = C_ij / K_i
                    p_j_given_i = np.where(k_p_col > 0, cooccurrence_matrix / k_p_col, 0)
                    # P(i|j) = C_ij / K_j
                    p_i_given_j = np.where(k_p_row > 0, cooccurrence_matrix / k_p_row, 0)
                    
                    # Element-wise minimum
                    proximity_matrix_values = np.minimum(p_j_given_i, p_i_given_j)
        else:
            # CPU vectorized implementation
            num_products = len(valid_products)
            proximity_matrix_values = np.zeros((num_products, num_products))
            
            # Prepare k_p for broadcasting
            k_p_row = k_p.reshape(1, -1)
            k_p_col = k_p.reshape(-1, 1)
            
            # Avoid division by zero
            with np.errstate(divide='ignore', invalid='ignore'):
                # P(j|i) = C_ij / K_i
                p_j_given_i = np.where(k_p_col > 0, cooccurrence_matrix / k_p_col, 0)
                # P(i|j) = C_ij / K_j
                p_i_given_j = np.where(k_p_row > 0, cooccurrence_matrix / k_p_row, 0)
                
                # Element-wise minimum
                proximity_matrix_values = np.minimum(p_j_given_i, p_i_given_j)
            
            elapsed = time.time() - start_time
            logger.debug(f"Calculated proximity matrix without acceleration in {elapsed:.4f}s")
        
        # Convert to DataFrame
        proximity_df_wide = pd.DataFrame(
            proximity_matrix_values, 
            index=valid_products, 
            columns=valid_products
        )
        
        # Convert to long format
        # Clear index/column names to avoid potential conflicts
        proximity_df_wide.index.name = None
        proximity_df_wide.columns.name = None
        
        # Stack and reset index to create long format
        proximity_df_long = proximity_df_wide.stack().reset_index()
        proximity_df_long.columns = ['product1', 'product2', 'proximity']
        
        # Filter out zero proximities
        proximity_df_long = proximity_df_long[proximity_df_long['proximity'] > 0]
        
        logger.info(f"Product proximity calculated for {len(valid_products)} products")
        return proximity_df_long
        
    finally:
        # Restore original acceleration type if we changed it
        if original_accel_type:
            hw_manager.set_acceleration_type(original_accel_type)
            logger.debug(f"Restored original acceleration type: {original_accel_type.value}")

@memoize(level=StorageTier.MEMORY)
@log_execution_time(logger=logger)
def get_product_space_chart_data_gpu(
    product_space_df: pd.DataFrame,
    proximity_df: Optional[pd.DataFrame] = None,
    product_col_name: str = 'hs2',
    top_n_products: int = 50,
    min_proximity_link: float = 0.1
) -> Dict[str, Any]:
    """
    Generate chart data for product space visualization using GPU acceleration.
    
    Args:
        product_space_df: DataFrame with product space data
        proximity_df: Optional DataFrame with proximity data
        product_col_name: Column name for product code
        top_n_products: Number of top products to include in the chart
        min_proximity_link: Minimum proximity value for links
        
    Returns:
        Dictionary with chart data
    """
    logger.info(f"Generating product space chart data with GPU acceleration for top {top_n_products} products")
    
    if product_space_df.empty:
        logger.warning("Product space data is empty for chart")
        raise ValueError("Input product_space_df cannot be empty for chart data generation")
    
    # Get hardware manager and check for acceleration availability
    hw_manager = get_hardware_manager()
    use_acceleration = hw_manager.is_hardware_acceleration_available()
    
    # Get top N products by export value
    if use_acceleration:
        try:
            # Extract values and indices
            export_values = product_space_df['yemen_exports'].values
            
            # Get indices of top N products
            top_indices = np.argsort(-export_values)[:top_n_products]
            
            # Select top products
            top_products_df = product_space_df.iloc[top_indices].reset_index(drop=True)
            
            logger.debug(f"Selected top {len(top_indices)} products with acceleration")
        except Exception as e:
            logger.warning(f"Accelerated top product selection failed: {e}. Falling back to standard selection.")
            top_products_df = product_space_df.nlargest(top_n_products, 'yemen_exports')
    else:
        # Standard pandas nlargest
        top_products_df = product_space_df.nlargest(top_n_products, 'yemen_exports')
    
    if top_products_df.empty:
        logger.warning("No top products found for chart")
        raise ValueError("Could not determine top products from product_space_df")
    
    # Create base chart data
    chart_data = {
        'product': top_products_df[product_col_name].tolist(),
        'rca': top_products_df['rca'].tolist(),
        'yemen_exports': top_products_df['yemen_exports'].tolist(),
        'category': top_products_df['category'].tolist(),
        'advantage_type': top_products_df['advantage_type'].tolist() if 'advantage_type' in top_products_df else ['Unknown'] * len(top_products_df)
    }
    
    # Add links if proximity data is available
    if proximity_df is not None and not proximity_df.empty:
        top_product_list = top_products_df[product_col_name].tolist()
        
        if use_acceleration:
            try:
                # Extract links where both products are in top product list
                products1 = proximity_df['product1'].values
                products2 = proximity_df['product2'].values
                proximities = proximity_df['proximity'].values
                
                # Create sets for faster lookups
                top_products_set = set(top_product_list)
                
                # Prepare filtered links
                filtered_links = []
                
                # Filter links
                for i in range(len(products1)):
                    p1 = products1[i]
                    p2 = products2[i]
                    prox = proximities[i]
                    
                    if (p1 in top_products_set and 
                        p2 in top_products_set and 
                        p1 != p2 and  # Avoid self-loops
                        prox >= min_proximity_link):
                        filtered_links.append({
                            'product1': p1,
                            'product2': p2,
                            'proximity': float(prox)
                        })
                
                chart_data['links'] = filtered_links
                
                logger.debug(f"Filtered links with acceleration: {len(filtered_links)} links")
            except Exception as e:
                logger.warning(f"Accelerated link filtering failed: {e}. Falling back to standard filtering.")
                
                # Standard pandas filtering
                filtered_proximity = proximity_df[
                    proximity_df['product1'].isin(top_product_list) &
                    proximity_df['product2'].isin(top_product_list) &
                    (proximity_df['product1'] != proximity_df['product2']) & # Avoid self-loops
                    (proximity_df['proximity'] >= min_proximity_link)
                ]
                chart_data['links'] = filtered_proximity.to_dict(orient='records')
        else:
            # Standard pandas filtering
            filtered_proximity = proximity_df[
                proximity_df['product1'].isin(top_product_list) &
                proximity_df['product2'].isin(top_product_list) &
                (proximity_df['product1'] != proximity_df['product2']) & # Avoid self-loops
                (proximity_df['proximity'] >= min_proximity_link)
            ]
            chart_data['links'] = filtered_proximity.to_dict(orient='records')
    else:
        chart_data['links'] = []
    
    logger.info(f"Product space chart data prepared: {len(chart_data['product'])} products, {len(chart_data['links'])} links")
    return chart_data

class GPUAcceleratedProductSpace:
    """
    GPU-accelerated implementation of the product space model.
    
    This class provides a unified interface for working with the product space model
    using GPU acceleration for significantly improved performance.
    """
    
    def __init__(self):
        """Initialize the GPU-accelerated product space model."""
        self.logger = get_logger(f"{__name__}.GPUAcceleratedProductSpace")
        self.hw_manager = get_hardware_manager()
        
        # Check for GPU availability and log accordingly
        if self.hw_manager.is_hardware_acceleration_available(AccelerationType.GPU):
            self.logger.info("GPU acceleration is available for product space model")
        else:
            self.logger.info("GPU acceleration is not available, will use CPU vectorization")
    
    @protect("calculate_product_space", OperationType.COMPUTATION)
    @log_execution_time(logger=logger)
    def calculate_product_space(
        self,
        exports_df: pd.DataFrame,
        rca_data_df: pd.DataFrame,
        year: int,
        country_code: int = 887,
        product_col_name: str = 'hs2',
        value_col_name: str = 'value',
        exporter_col_name: str = 'exporter',
        min_export_val: float = 1000
    ) -> pd.DataFrame:
        """
        Calculate product space data using GPU acceleration.
        
        Args:
            exports_df: DataFrame with export data
            rca_data_df: DataFrame with RCA data
            year: Year to calculate product space for
            country_code: Country code to calculate product space for
            product_col_name: Column name for product code
            value_col_name: Column name for export value
            exporter_col_name: Column name for exporter
            min_export_val: Minimum export value to include
            
        Returns:
            DataFrame with product space data
        """
        return calculate_product_space_data_gpu(
            exports_df=exports_df,
            rca_data_df=rca_data_df,
            year=year,
            country_code=country_code,
            product_col_name=product_col_name,
            value_col_name=value_col_name,
            exporter_col_name=exporter_col_name,
            min_export_val=min_export_val
        )
    
    @protect("calculate_proximity", OperationType.COMPUTATION)
    @log_execution_time(logger=logger)
    def calculate_proximity(
        self,
        rca_data_all_countries: pd.DataFrame,
        year: int,
        product_col_name: str = 'hs2',
        exporter_col_name: str = 'exporter',
        rca_col_name: str = 'rca',
        min_countries_for_rca: int = 5,
        rca_threshold_proximity: float = 1.0
    ) -> pd.DataFrame:
        """
        Calculate product proximity matrix using GPU acceleration.
        
        Args:
            rca_data_all_countries: DataFrame with RCA data for all countries
            year: Year to calculate proximity for
            product_col_name: Column name for product code
            exporter_col_name: Column name for exporter
            rca_col_name: Column name for RCA value
            min_countries_for_rca: Minimum number of countries with RCA > threshold
            rca_threshold_proximity: RCA threshold for proximity calculation
            
        Returns:
            DataFrame with product proximity data
        """
        return calculate_product_proximity_gpu(
            rca_data_all_countries=rca_data_all_countries,
            year=year,
            product_col_name=product_col_name,
            exporter_col_name=exporter_col_name,
            rca_col_name=rca_col_name,
            min_countries_for_rca=min_countries_for_rca,
            rca_threshold_proximity=rca_threshold_proximity
        )
    
    @log_execution_time(logger=logger)
    def get_chart_data(
        self,
        product_space_df: pd.DataFrame,
        proximity_df: Optional[pd.DataFrame] = None,
        product_col_name: str = 'hs2',
        top_n_products: int = 50,
        min_proximity_link: float = 0.1
    ) -> Dict[str, Any]:
        """
        Generate chart data for product space visualization using GPU acceleration.
        
        Args:
            product_space_df: DataFrame with product space data
            proximity_df: Optional DataFrame with proximity data
            product_col_name: Column name for product code
            top_n_products: Number of top products to include
            min_proximity_link: Minimum proximity value for links
            
        Returns:
            Dictionary with chart data
        """
        return get_product_space_chart_data_gpu(
            product_space_df=product_space_df,
            proximity_df=proximity_df,
            product_col_name=product_col_name,
            top_n_products=top_n_products,
            min_proximity_link=min_proximity_link
        )

def get_gpu_product_space_model() -> GPUAcceleratedProductSpace:
    """
    Factory function to create a new GPU-accelerated product space model.
    
    Returns:
        Configured GPUAcceleratedProductSpace instance
    """
    return GPUAcceleratedProductSpace()

# Standalone functions for simplified usage
@protect("calculate_product_space_gpu", OperationType.COMPUTATION)
def calculate_product_space_gpu(
    exports_df: pd.DataFrame,
    rca_data_df: pd.DataFrame,
    year: int,
    country_code: int = 887
) -> pd.DataFrame:
    """
    Simplified function to calculate product space data with GPU acceleration.
    
    Args:
        exports_df: DataFrame with export data
        rca_data_df: DataFrame with RCA data
        year: Year to calculate product space for
        country_code: Country code to calculate product space for
        
    Returns:
        DataFrame with product space data
    """
    model = get_gpu_product_space_model()
    return model.calculate_product_space(
        exports_df=exports_df,
        rca_data_df=rca_data_df,
        year=year,
        country_code=country_code
    )

@protect("calculate_proximity_gpu", OperationType.COMPUTATION)
def calculate_proximity_gpu(
    rca_data_all_countries: pd.DataFrame,
    year: int
) -> pd.DataFrame:
    """
    Simplified function to calculate product proximity with GPU acceleration.
    
    Args:
        rca_data_all_countries: DataFrame with RCA data for all countries
        year: Year to calculate proximity for
        
    Returns:
        DataFrame with product proximity data
    """
    model = get_gpu_product_space_model()
    return model.calculate_proximity(
        rca_data_all_countries=rca_data_all_countries,
        year=year
    )

if __name__ == "__main__":
    # Set up logging
    from yemen_trade_diagnostic.interfaces.logging_interface import configure_logging, LogLevel
    configure_logging(log_level=LogLevel.DEBUG, log_to_console=True)
    logger.info("--- Testing GPU-Accelerated Product Space Model ---")
    
    # Create sample data
    exports_sample = pd.DataFrame({
        'year': [2020, 2020, 2020, 2020, 2021, 2021],
        'exporter': [887, 887, 887, 123, 123, 887],
        'hs2': ['01', '02', '03', '01', '02', '01'],
        'value': [10000, 50000, 2000, 15000, 6000, 12000]
    })
    
    rca_sample = pd.DataFrame({
        'year': [2020, 2020, 2020, 2020, 2021, 2021],
        'exporter': [887, 887, 887, 123, 123, 887],
        'hs2': ['01', '02', '03', '01', '02', '01'],
        'rca': [1.5, 0.8, 2.5, 0.5, 1.2, 1.8],
        'advantage_type': ['Slight Advantage', 'Slight Disadvantage', 'Strong Advantage', 'Strong Disadvantage', 'Slight Advantage', 'Slight Advantage']
    })
    
    rca_all_countries_sample = pd.concat([
        rca_sample,
        pd.DataFrame({
            'year': [2020, 2020, 2020],
            'exporter': [456, 456, 456],
            'hs2': ['01', '02', '04'],
            'rca': [2.2, 0.1, 3.0],
            'advantage_type': ['SA', 'SD', 'SA']
        })
    ], ignore_index=True)
    
    # Initialize model
    model = get_gpu_product_space_model()
    
    # Test product space calculation
    logger.info("Testing product space calculation with GPU acceleration")
    ps_data = model.calculate_product_space(
        exports_df=exports_sample,
        rca_data_df=rca_sample,
        year=2020,
        country_code=887
    )
    logger.info(f"Product Space Data (GPU-accelerated):\n{ps_data}")
    
    # Test proximity calculation
    logger.info("Testing proximity calculation with GPU acceleration")
    prox_data = model.calculate_proximity(
        rca_data_all_countries=rca_all_countries_sample,
        year=2020
    )
    logger.info(f"Proximity Data (GPU-accelerated):\n{prox_data}")
    
    # Test chart data generation
    logger.info("Testing chart data generation with GPU acceleration")
    chart_data = model.get_chart_data(
        product_space_df=ps_data,
        proximity_df=prox_data
    )
    logger.info(f"Chart Data Nodes: {len(chart_data.get('product', []))}, Links: {len(chart_data.get('links', []))}")
    logger.info("--- GPU-Accelerated Product Space Model Test Completed ---")