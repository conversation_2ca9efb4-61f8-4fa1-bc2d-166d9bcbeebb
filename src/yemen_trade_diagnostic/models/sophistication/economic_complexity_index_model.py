"""
Economic Complexity Index (ECI) Model for Yemen Trade Diagnostic (V2)

This module calculates Economic Complexity Index (ECI) using V2 interfaces and
optimized implementations. It leverages hardware acceleration where available.
"""
# Standard library imports
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

# Third-party imports
import numpy as np
import pandas as pd

# Project imports
from yemen_trade_diagnostic.interfaces.cache_interface import StorageTier, memoize
from yemen_trade_diagnostic.interfaces.hardware_interface import (
    AccelerationType, 
    get_hardware_manager
)
from yemen_trade_diagnostic.interfaces.logging_interface import (
    LogLevel, 
    configure_logging, 
    get_logger, 
    log_execution_time
)
from yemen_trade_diagnostic.models.rca import calculate_rca_by_year_v2_standalone
from yemen_trade_diagnostic.errors import protect, OperationType, MODEL_CONFIG

logger = get_logger(__name__)

@protect("calculate_eci_v2_standalone", OperationType.MODEL_CALCULATION)
@memoize(ttl=7200, level=StorageTier.MEMORY)
@log_execution_time(logger=logger)
def calculate_eci_v2_standalone(
    rca_data: pd.DataFrame,
    year_col: str = 'year',
    country_col: str = 'country',
    product_col: str = 'product',
    rca_col: str = 'rca',
    rca_threshold: float = 1.0,
    iterations: int = 20
) -> pd.DataFrame:
    """
    Calculate Economic Complexity Index (ECI) using the method of reflections.
    
    Args:
        rca_data: DataFrame with RCA values (country, product, year, rca)
        year_col: Name of the column containing year values
        country_col: Name of the column containing country identifiers
        product_col: Name of the column containing product identifiers
        rca_col: Name of the column containing RCA values
        rca_threshold: Threshold for considering a country has comparative advantage
        iterations: Number of iterations for the method of reflections
        
    Returns:
        DataFrame with year, country, and eci columns
    """
    logger.info(f"Calculating ECI for data with shape {rca_data.shape}")
    
    # Check required columns
    required_cols = [year_col, country_col, product_col, rca_col]
    missing_cols = [col for col in required_cols if col not in rca_data.columns]
    if missing_cols:
        raise ValueError(f"Required columns missing from RCA data: {missing_cols}")
    
    # Check for empty dataframe
    if rca_data.empty:
        return pd.DataFrame(columns=[year_col, country_col, 'eci'])
    
    # Get unique years
    years = rca_data[year_col].unique()
    logger.info(f"Processing {len(years)} years: {sorted(years)}")
    
    result_dfs = []
    
    for year in years:
        try:
            # Filter data for the current year
            year_data = rca_data[rca_data[year_col] == year].copy()
            
            # Skip if no data for this year
            if year_data.empty:
                logger.warning(f"No data for year {year}, skipping")
                continue
            
            # Create a binary RCA matrix (1 if RCA >= threshold, 0 otherwise)
            year_data['has_advantage'] = (year_data[rca_col] >= rca_threshold).astype(int)
            
            # Create matrices for the method of reflections
            # M_cp (country-product matrix): 1 if country c has advantage in product p
            try:
                mcp_matrix = year_data.pivot_table(
                    index=country_col,
                    columns=product_col,
                    values='has_advantage',
                    fill_value=0
                )
            except Exception as e:
                msg = f"Error creating MCP matrix for year {year}: {e}"
                logger.error(msg, exc_info=True)
                continue
            
            # Convert to numpy arrays for faster computation
            mcp = mcp_matrix.values.astype(np.float32)
            
            # Calculate diversity (k_c) and ubiquity (k_p)
            k_c = mcp.sum(axis=1)  # Diversity: number of products exported by country c
            k_p = mcp.sum(axis=0)  # Ubiquity: number of countries exporting product p
            
            # Handle zero values to avoid division by zero
            k_c_inv = np.zeros_like(k_c)
            k_c_inv[k_c > 0] = 1.0 / k_c[k_c > 0]
            
            k_p_inv = np.zeros_like(k_p)
            k_p_inv[k_p > 0] = 1.0 / k_p[k_p > 0]
            
            # Try to use hardware acceleration for matrix operations
            hw_manager = get_hardware_manager()
            use_hw_accel = hw_manager.is_hardware_acceleration_available()
            
            # Method of reflections
            if use_hw_accel:
                try:
                    logger.info("Using hardware acceleration for ECI calculation")
                    
                    # Create Mcc' matrix
                    # Mcc' = sum_p (Mcp / kp) * (Mc'p / kp)
                    # First, normalize columns by k_p
                    mcp_norm = mcp.copy()
                    for j in range(mcp.shape[1]):
                        if k_p[j] > 0:
                            mcp_norm[:, j] = mcp[:, j] / k_p[j]
                    
                    # Then compute Mcc'
                    mcc = hw_manager.accelerate_matrix(
                        mcp_norm.astype(np.float32),
                        "matmul",
                        mcp_norm.T.astype(np.float32)
                    )
                    if mcc is None:
                        raise RuntimeError("Hardware acceleration failed for matrix multiplication")
                    
                    # Method of reflections
                    # Start with k_c (diversity) as first approximation
                    eci = k_c.copy().astype(np.float32)
                    
                    for _ in range(iterations):
                        # Normalize
                        eci = (eci - np.mean(eci)) / np.std(eci)
                        
                        # Update using Mcc'
                        eci_new = hw_manager.accelerate_matrix(
                            mcc.astype(np.float32),
                            "matmul",
                            eci.reshape(-1, 1).astype(np.float32)
                        )
                        if eci_new is None:
                            raise RuntimeError("Hardware acceleration failed for ECI iteration")
                        
                        eci = eci_new.flatten()
                    
                except Exception as e:
                    logger.warning(f"Hardware acceleration failed: {e}, falling back to CPU")
                    use_hw_accel = False
            
            if not use_hw_accel:
                # Create M_cc' matrix (country-country matrix)
                # M_cc' = sum_p (M_cp / k_p) * (M_c'p / k_p)
                mcc = np.zeros((mcp.shape[0], mcp.shape[0]), dtype=np.float32)
                
                for j in range(mcp.shape[1]):  # For each product
                    if k_p[j] > 0:
                        # Outer product of product j's exporters, normalized by ubiquity
                        mcc += np.outer(mcp[:, j], mcp[:, j]) / (k_p[j] * k_p[j])
                
                # Set diagonal to zero
                np.fill_diagonal(mcc, 0)
                
                # Method of reflections
                # Start with k_c (diversity) as first approximation
                eci = k_c.copy()
                
                for _ in range(iterations):
                    # Normalize
                    eci = (eci - np.mean(eci)) / np.std(eci)
                    
                    # Update using M_cc'
                    eci = mcc @ eci
            
            # Normalize the final ECI
            eci = (eci - np.mean(eci)) / np.std(eci)
            
            # Convert back to DataFrame
            eci_df = pd.DataFrame({
                country_col: mcp_matrix.index,
                'eci': eci
            })
            
            # Add year column
            eci_df[year_col] = year
            
            result_dfs.append(eci_df)
            
            logger.info(f"Completed ECI calculation for year {year}")
            
        except Exception as e:
            msg = f"Error calculating ECI for year {year}: {e}"
            logger.error(msg, exc_info=True)
            # Continue with other years
    
    if not result_dfs:
        logger.warning("No ECI results for any year")
        return pd.DataFrame(columns=[year_col, country_col, 'eci'])
    
    # Combine results from all years
    combined_result = pd.concat(result_dfs, ignore_index=True)
    
    # Reorder columns
    column_order = [year_col, country_col, 'eci']
    combined_result = combined_result[column_order]
    
    logger.info(f"ECI calculation completed. Output shape: {combined_result.shape}")
    return combined_result

@protect("calculate_eci_from_exports_v2", OperationType.MODEL_CALCULATION)
@memoize(ttl=7200, level=StorageTier.MEMORY)
@log_execution_time(logger=logger)
def calculate_eci_from_exports_v2(
    export_data: pd.DataFrame,
    year_col: str = 'year',
    country_col: str = 'country',
    product_col: str = 'product',
    value_col: str = 'value',
    rca_threshold: float = 1.0,
    iterations: int = 20
) -> pd.DataFrame:
    """
    Calculate Economic Complexity Index (ECI) from export data.
    First calculates RCA, then uses the RCA to compute ECI.
    
    Args:
        export_data: DataFrame with export data
        year_col: Name of the column containing year values
        country_col: Name of the column containing country identifiers
        product_col: Name of the column containing product identifiers
        value_col: Name of the column containing export values
        rca_threshold: Threshold for considering a country has comparative advantage
        iterations: Number of iterations for the method of reflections
        
    Returns:
        DataFrame with year, country, and eci columns
    """
    logger.info(f"Calculating ECI from export data with shape {export_data.shape}")
    
    # Calculate RCA
    rca_data = calculate_rca_by_year_v2_standalone(
        data=export_data,
        year_col=year_col,
        country_col=country_col,
        product_col=product_col,
        value_col=value_col
    )
    # Calculate ECI from RCA
    return calculate_eci_v2_standalone(
        rca_data=rca_data,
        year_col=year_col,
        country_col=country_col,
        product_col=product_col,
        rca_threshold=rca_threshold,
        iterations=iterations
    )

if __name__ == "__main__":
    # Example usage
    configure_logging(log_level=LogLevel.DEBUG, log_to_console=True)
    logger.info("--- Running ECI Model Example ---")
    
    # Create sample data
    countries = ['A', 'B', 'C', 'D', 'E']
    products = ['P1', 'P2', 'P3', 'P4', 'P5']
    years = [2018, 2019]
    
    # Create all combinations
    data = []
    for year in years:
        for country in countries:
            for product in products:
                # Create some patterns in the data
                if country in ['A', 'B'] and product in ['P1', 'P2']:
                    rca = 2.0  # A and B have advantage in P1 and P2
                elif country in ['C', 'D'] and product in ['P3', 'P4']:
                    rca = 1.5  # C and D have advantage in P3 and P4
                else:
                    rca = 0.5  # No advantage for other combinations
                
                data.append({
                    'year': year,
                    'country': country,
                    'product': product,
                    'rca': rca
                })
    
    rca_df = pd.DataFrame(data)
    
    # Calculate ECI
    eci_result = calculate_eci_v2_standalone(rca_df)
    logger.info(f"ECI Result:\n{eci_result}")