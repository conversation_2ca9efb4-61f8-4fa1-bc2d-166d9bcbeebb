"""
Product Complexity and Proximity Validation for Yemen Trade Diagnostic (V2)

This module provides validation functions for product complexity calculations
and proximity matrices used by the Sophistication pipeline.
"""
# Standard library imports
from typing import Any, Dict, List, Optional, Tuple, Union

# Third-party imports
import logging

import numpy as np
import pandas as pd

# Project imports
from yemen_trade_diagnostic.interfaces.logging_interface import get_logger
from yemen_trade_diagnostic.interfaces.validation_interface import (
    ValidationIssueLevel, 
    ValidationResult, 
    get_validation_manager
)
from yemen_trade_diagnostic.errors import protect, OperationType

logger = get_logger(__name__)

# Validation schemas
PRODUCT_COMPLEXITY_SCHEMA_V2 = {
    'required_columns': ['hs2', 'complexity'],
    'column_types': {'hs2': 'string', 'complexity': 'numeric'}
}

PRODY_SCHEMA_V2 = {
    'required_columns': ['hs2', 'prody', 'year'],
    'column_types': {'hs2': 'string', 'prody': 'numeric', 'year': 'integer'}
}

EXPY_SCHEMA_V2 = {
    'required_columns': ['country_code', 'expy', 'year'],
    'column_types': {'country_code': 'string', 'expy': 'numeric', 'year': 'integer'}
}

GLOBAL_RCA_SCHEMA_V2 = {
    'required_columns': ['year', 'country', 'product', 'rca'],
    'column_types': {'year': 'integer', 'country': 'numeric', 'product': 'string', 'rca': 'numeric'}
}

PRODUCT_PROXIMITY_SCHEMA_V2 = {
    'required_columns': ['product1', 'product2', 'proximity'],
    'column_types': {'product1': 'string', 'product2': 'string', 'proximity': 'numeric'}
}

def validate_global_rca_data(rca_df: pd.DataFrame) -> Tuple[bool, List[str]]:
    """
    Validates global RCA data needed for proximity calculations.
    
    Args:
        rca_df: DataFrame containing global RCA data
        
    Returns:
        Tuple of (is_valid, error_messages)
    """
    validation_manager = get_validation_manager()
    error_messages = []
    
    # Basic DataFrame validation
    if rca_df is None or not isinstance(rca_df, pd.DataFrame):
        error_messages.append("Global RCA data is None or not a DataFrame")
        return False, error_messages
        
    if rca_df.empty:
        error_messages.append("Global RCA data is empty")
        return False, error_messages
    
    # Schema validation
    result = validation_manager.validate_schema(rca_df, GLOBAL_RCA_SCHEMA_V2)
    if not result.is_valid:
        for issue in result.issues:
            error_messages.append(str(issue))
        return False, error_messages
    
    # Check for minimum data requirements
    num_countries = rca_df['country'].nunique()
    if num_countries < 5:
        error_messages.append(f"Insufficient country coverage: {num_countries} countries (minimum 5 required)")
    
    num_products = rca_df['product'].nunique()
    if num_products < 10:
        error_messages.append(f"Insufficient product coverage: {num_products} products (minimum 10 required)")
    
    # Check RCA values are in a reasonable range
    if (rca_df['rca'] < 0).any():
        error_messages.append("RCA values contain negative numbers, which are invalid")
    
    if (rca_df['rca'] > 1000).any():
        error_messages.append("RCA values contain extremely high values (>1000), which may indicate data issues")
    
    return len(error_messages) == 0, error_messages

def validate_proximity_matrix(proximity_df: pd.DataFrame) -> Tuple[bool, List[str]]:
    """
    Validates product proximity matrix.
    
    Args:
        proximity_df: DataFrame containing product proximity data
        
    Returns:
        Tuple of (is_valid, error_messages)
    """
    validation_manager = get_validation_manager()
    error_messages = []
    
    # Basic DataFrame validation
    if proximity_df is None or not isinstance(proximity_df, pd.DataFrame):
        error_messages.append("Proximity matrix is None or not a DataFrame")
        return False, error_messages
        
    if proximity_df.empty:
        error_messages.append("Proximity matrix is empty")
        return False, error_messages
    
    # Schema validation
    result = validation_manager.validate_schema(proximity_df, PRODUCT_PROXIMITY_SCHEMA_V2)
    if not result.is_valid:
        for issue in result.issues:
            error_messages.append(str(issue))
        return False, error_messages
    
    # Check proximity values are in valid range [0, 1]
    if (proximity_df['proximity'] < 0).any() or (proximity_df['proximity'] > 1).any():
        error_messages.append("Proximity values outside valid range [0, 1]")
    
    # Ensure we have a reasonable number of links
    if len(proximity_df) < 10:
        error_messages.append(f"Too few proximity links: {len(proximity_df)} (minimum 10 expected)")
    
    # Check uniqueness of products
    num_products = len(set(proximity_df['product1'].tolist() + proximity_df['product2'].tolist()))
    if num_products < 5:
        error_messages.append(f"Too few unique products in proximity matrix: {num_products} (minimum 5 expected)")
    
    return len(error_messages) == 0, error_messages

def preprocess_global_rca_for_proximity(
    global_rca_df: pd.DataFrame, 
    year: int,
    country_col: str = 'country',
    product_col: str = 'product',
    rca_col: str = 'rca',
    year_col: str = 'year'
) -> pd.DataFrame:
    """
    Preprocess global RCA data to ensure it's ready for proximity calculation.
    Handles common issues that could cause proximity calculation to fail.
    
    Args:
        global_rca_df: DataFrame with global RCA data
        year: Year to filter data for
        country_col: Column name for country identifiers
        product_col: Column name for product codes
        rca_col: Column name for RCA values
        year_col: Column name for year values
        
    Returns:
        Processed DataFrame ready for proximity calculation, or empty DataFrame if processing fails
    """
    if global_rca_df is None or global_rca_df.empty:
        logger.warning("Empty global RCA data provided for preprocessing")
        return pd.DataFrame()
        
    # Check and rename columns if needed to match standard names
    rename_mapping = {}
    
    # Drop any duplicates on key columns
    key_columns = [country_col, product_col]
    if year_col in global_rca_df.columns:
        key_columns.append(year_col)
    
    processed_df = global_rca_df.drop_duplicates(subset=key_columns)
    
    # Filter for the specific year if year column exists
    if year_col in processed_df.columns:
        processed_df = processed_df[processed_df[year_col] == year]
    
    # Handle null RCA values
    if rca_col in processed_df.columns:
        processed_df = processed_df[~processed_df[rca_col].isna()]
    
    # Ensure we have the necessary columns
    required_cols = [country_col, product_col, rca_col]
    if not all(col in processed_df.columns for col in required_cols):
        missing_cols = [col for col in required_cols if col not in processed_df.columns]
        logger.error(f"Missing columns in global RCA data: {missing_cols}")
        return pd.DataFrame()
    
    # Ensure numeric RCA values
    if not pd.api.types.is_numeric_dtype(processed_df[rca_col]):
        try:
            processed_df[rca_col] = pd.to_numeric(processed_df[rca_col], errors='coerce')
            processed_df = processed_df[~processed_df[rca_col].isna()]
        except Exception as e:
            logger.error(f"Failed to convert RCA values to numeric: {e}")
            return pd.DataFrame()
    
    # Basic product cleaning - ensure strings
    if not pd.api.types.is_string_dtype(processed_df[product_col]):
        processed_df[product_col] = processed_df[product_col].astype(str)
    
    # Check if we have enough data after processing
    if processed_df.empty or processed_df[country_col].nunique() < 3:
        logger.warning(f"Insufficient data after preprocessing: {len(processed_df)} rows, {processed_df[country_col].nunique()} countries")
        return pd.DataFrame()
        
    return processed_df