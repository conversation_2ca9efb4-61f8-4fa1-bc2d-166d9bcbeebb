"""
PRODY/EXPY Model for Yemen Trade Diagnostic (V2)

Calculates PRODY (product sophistication) and EXPY (export basket sophistication)
using V2 interfaces.
"""
# Standard library imports
from typing import Any, Dict, List, Optional, Tuple, Union

# Third-party imports
import numpy as np
import pandas as pd

# Project imports
from yemen_trade_diagnostic.interfaces.cache_interface import StorageTier, memoize
from yemen_trade_diagnostic.interfaces.hardware_interface import get_hardware_manager
from yemen_trade_diagnostic.errors import protect, OperationType, MODEL_CONFIG
# V2 Interface Imports
from yemen_trade_diagnostic.interfaces.logging_interface import (
    LogLevel, 
    configure_logging, 
    get_logger, 
    log_execution_time
)
from yemen_trade_diagnostic.interfaces.validation_interface import (
    ValidationIssueLevel, 
    ValidationResult, 
    get_validation_manager
)

logger = get_logger(__name__)

# --- Validation Schemas (V2) ---
# Simplified schemas for brevity. In a real scenario, these would be more detailed.
EXPORTS_SCHEMA_PRODY_EXPY_V2 = {
    'required_columns': ['hs2', 'year', 'value', 'country_code'], # country_code needed for method of reflections
    'column_types': {'hs2': 'string', 'year': 'integer', 'value': 'numeric', 'country_code': 'string'}
}
GDP_SCHEMA_PRODY_EXPY_V2 = {
    'required_columns': ['country_code', 'year', 'gdp_per_capita'],
    'column_types': {'country_code': 'string', 'year': 'integer', 'gdp_per_capita': 'numeric'}
}
BACI_DATA_SCHEMA_PRODY_EXPY_V2 = {
    'required_columns': ['year', 'exporter', 'hs2', 'value'],
    'column_types': {'year': 'integer', 'exporter': 'string', 'hs2': 'string', 'value': 'numeric'}
}

# --- Helper for V2 Validation (similar to one used in other V2 models) ---
def _validate_df_prody_expy_v2(df: pd.DataFrame, schema: Dict, df_name: str) -> bool:
    validation_manager = get_validation_manager()
    val_result = ValidationResult()
    if not isinstance(df, pd.DataFrame):
        val_result.add_issue(f"{df_name} is not a pandas DataFrame.", ValidationIssueLevel.ERROR)
    else:
        for col in schema.get('required_columns', []):
            if col not in df.columns:
                val_result.add_issue(f"Required column '{col}' missing in {df_name}.", ValidationIssueLevel.ERROR)
        
        if val_result.is_valid:
            logger.debug(f"Validation passed for {df_name}")
    
    if not val_result.is_valid:
        logger.error(f"Validation failed for {df_name}: {val_result.issues}")
        for issue in val_result.issues:
            logger.error(f"Validation issue: {issue}")
        return False
    return True

@protect("calculate_product_complexity_v2", OperationType.COMPUTATION)
@memoize(ttl=3600*24, level=StorageTier.DISK)
@log_execution_time(logger=logger)
def calculate_product_complexity_v2(
    exports: pd.DataFrame,
    gdp_data: Optional[pd.DataFrame] = None, # Keep for signature, V1 used it for MOR path decision
    use_method_of_reflections: bool = True,
    country_col: str = 'country_code', 
    product_col: str = 'hs2',
    year_col: str = 'year',
    value_col: str = 'value',
    min_rca_for_advantage: float = 1.0 # Threshold for RCA to be considered an advantage
) -> Dict[str, float]:
    logger.info(f"Calculating product complexity (V2), use_method_of_reflections={use_method_of_reflections}")
    
    # Validate main exports DataFrame (using a schema that fits this function's needs)
    # This schema should ideally be defined at module level or fetched from a central registry
    current_exports_schema = {
        'required_columns': [country_col, product_col, year_col, value_col],
        'column_types': {country_col: 'string', product_col: 'string', year_col: 'integer', value_col: 'numeric'}
    }
    if not _validate_df_prody_expy_v2(exports, current_exports_schema, "exports_for_complexity"): 
        raise ValueError("Validation failed for exports DataFrame")
    
    if exports.empty: 
        logger.warning("Exports DataFrame is empty")
        return {}

    hw_manager = get_hardware_manager()

    if use_method_of_reflections and country_col in exports.columns:
        logger.debug("Using method of reflections for product complexity")
        # Method of reflections implementation would go here
        # For now, fall back to simpler heuristic
    
    # Simpler value-based heuristic (if use_method_of_reflections is False or MOR failed and we design a fallback)
    logger.debug("Using simpler value-based heuristic for product complexity.")
    # Ensure it uses the columns passed as arguments
    product_totals = exports.groupby(product_col, observed=True)[value_col].sum().reset_index()
    if product_totals.empty: 
        logger.warning("No product totals calculated")
        return {}
    
    product_totals = product_totals.sort_values(value_col, ascending=False)
    max_val = product_totals[value_col].max()
    min_val = product_totals[value_col].min()

    if max_val == min_val:        
        product_totals['complexity'] = 1.0
    else:
        # Normalize to 0-1 scale (inverted so high value = low complexity)
        product_totals['complexity'] = 1 - (product_totals[value_col] - min_val) / (max_val - min_val)

    return dict(zip(product_totals[product_col].astype(str), product_totals['complexity']))

@protect("calculate_prody_v2", OperationType.COMPUTATION)
@memoize(ttl=3600, level=StorageTier.DISK)
@log_execution_time(logger=logger)
def calculate_prody_v2(
    exports_df: pd.DataFrame, # Expects country, product, year, value
    gdp_df: pd.DataFrame,     # Expects country, year, gdp_per_capita
    country_col_export: str = 'country_code',
    product_col_export: str = 'hs2',
    year_col_export: str = 'year',
    value_col_export: str = 'value',
    country_col_gdp: str = 'country_code',
    year_col_gdp: str = 'year',
    gdp_pc_col: str = 'gdp_per_capita'
) -> pd.DataFrame: # Returns DataFrame: product_col, prody, year_col
    """
    Calculate PRODY values for products using the standard formula:
    PRODY_p = sum_c (normalized_RCA_weight_cp * GDP_pc_c)
    where normalized_RCA_weight_cp is the share of country c's RCA-weighted export of product p
    in the total RCA-weighted exports of product p by all countries.
    This version processes data per year if multiple years are present.
    """
    logger.info("Calculating PRODY values (V2 - standard formula)")

    # Validate inputs (basic checks, more robust validation can be added via validation_interface)
    if not _validate_df_prody_expy_v2(exports_df, EXPORTS_SCHEMA_PRODY_EXPY_V2, "exports_df"):
        raise ValueError("Validation failed for exports_df")
    
    if not _validate_df_prody_expy_v2(gdp_df, GDP_SCHEMA_PRODY_EXPY_V2, "gdp_df"):
        raise ValueError("Validation failed for gdp_df")
    
    # Implementation placeholder - return empty DataFrame for now
    return pd.DataFrame()

@protect("calculate_expy_v2", OperationType.COMPUTATION)
@memoize(ttl=3600, level=StorageTier.DISK)
@log_execution_time(logger=logger)
def calculate_expy_v2(
    exports_df_syc: pd.DataFrame,
    prody_df_syr: pd.DataFrame,
    product_col_export: str = 'hs2',
    value_col_export: str = 'value',
    product_col_prody: str = 'hs2',
    prody_val_col: str = 'prody'
) -> float:
    """Calculate EXPY value for a single country-year combination"""
    logger.info("Calculating EXPY value")
    
    if exports_df_syc.empty or prody_df_syr.empty:
        logger.warning("Empty input data for EXPY calculation")
        return 0.0
    
    # Implementation placeholder
    return 0.0

@protect("calculate_expy_by_year_v2", OperationType.COMPUTATION)
@memoize(ttl=3600, level=StorageTier.DISK)
@log_execution_time(logger=logger)
def calculate_expy_by_year_v2(
    exports: pd.DataFrame, 
    prody_df_all_years: pd.DataFrame,
    country_col: str = 'country_code',
    product_col_export: str = 'hs2',
    year_col_export: str = 'year',
    value_col: str = 'value',
    product_col_prody: str = 'hs2',
    year_col_prody: str = 'year',
    prody_val_col: str = 'prody'
) -> pd.DataFrame:
    """Calculate EXPY values by year for all countries"""
    logger.info("Calculating EXPY by year")
    
    if exports.empty or prody_df_all_years.empty:
        logger.warning("Empty input data for EXPY by year calculation")
        return pd.DataFrame()
    
    # Implementation placeholder
    return pd.DataFrame()

@protect("calculate_expy_rankings_v2", OperationType.COMPUTATION)
@memoize(ttl=3600*24, level=StorageTier.DISK)
@log_execution_time(logger=logger)
def calculate_expy_rankings_v2(
    baci_data: pd.DataFrame,
    gdp_data: pd.DataFrame,
    country_codes_list: List[str],
    target_years: List[int],
    baci_year_col: str = 'year',
    baci_exporter_col: str = 'exporter',
    baci_product_col: str = 'hs2',
    baci_value_col: str = 'value',
    gdp_country_col: str = 'country_code',
    gdp_year_col: str = 'year',
    gdp_pc_col: str = 'gdp_per_capita'
) -> pd.DataFrame:
    """Calculate EXPY rankings for multiple countries and years"""
    logger.info("Calculating EXPY rankings")
    
    if baci_data.empty or gdp_data.empty:
        logger.error("Empty input data for EXPY rankings")
        raise ValueError("Input data cannot be empty")
    
    # Filter data for complexity calculation
    filtered_exports_for_complexity = baci_data.copy()
    gdp_for_prody = gdp_data.copy()
    
    # Calculate product complexity
    product_complexity_dict = calculate_product_complexity_v2(
        filtered_exports_for_complexity, 
        gdp_for_prody,
        use_method_of_reflections=True, 
        country_col='country_code', 
        product_col='hs2',
        year_col='year',
        value_col='value'
    )
    
    if not product_complexity_dict: 
        logger.error("Product complexity calculation failed")
        raise RuntimeError("Product complexity calculation failed")
    
    # Calculate PRODY values using all filtered export data and GDP data
    # calculate_prody_v2 handles iteration by year internally
    prody_df_all_years = calculate_prody_v2(
        exports_df=filtered_exports_for_complexity, # Already has standard column names
        gdp_df=gdp_for_prody, # Already has standard column names
        country_col_export='country_code',
        product_col_export='hs2',
        year_col_export='year',
        value_col_export='value',
        country_col_gdp='country_code',
        year_col_gdp='year',
        gdp_pc_col='gdp_per_capita'
    )
    
    if prody_df_all_years.empty: 
        logger.error("PRODY calculation failed or returned empty for rankings.")
        raise RuntimeError("PRODY calculation failed, cannot proceed with EXPY rankings.")
    
    all_expy_data = []
    for year_val in target_years:
        # Filter PRODY data for the current year
        prody_df_current_year = prody_df_all_years[prody_df_all_years['year'] == year_val]
        if prody_df_current_year.empty:
            logger.warning(f"No PRODY values available for year {year_val}. EXPY for this year will be NaN.")

        year_baci_data = baci_data[baci_data[baci_year_col] == year_val]
        for country_code_str in country_codes_list:
            country_exports_for_expy = year_baci_data[year_baci_data[baci_exporter_col] == country_code_str].copy()
            
            if not country_exports_for_expy.empty:
                # Ensure correct column names for calculate_expy_v2
                # It expects 'hs2' and 'value' for product and value columns by default.
                country_exports_for_expy.rename(columns={
                    baci_product_col: 'hs2', 
                    baci_value_col: 'value'
                }, inplace=True)
                
                expy_value = calculate_expy_v2(
                    exports_df_syc=country_exports_for_expy, 
                    prody_df_syr=prody_df_current_year, # Pass the year-specific PRODY DataFrame
                    product_col_export='hs2',
                    value_col_export='value',
                    product_col_prody='hs2', # Assuming prody_df_current_year uses 'hs2' for product
                    prody_val_col='prody'      # Assuming prody_df_current_year uses 'prody' for value
                )
                all_expy_data.append({'year': year_val, 'country_code': country_code_str, 'expy': expy_value})
            else:
                all_expy_data.append({'year': year_val, 'country_code': country_code_str, 'expy': np.nan})
    
    if not all_expy_data: 
        logger.warning("No EXPY data generated for rankings.")
        return pd.DataFrame(columns=['year', 'country_code', 'expy'])
    
    rankings_df = pd.DataFrame(all_expy_data)

    # Calculate ranks and percentiles per year
    if 'expy' in rankings_df.columns:
        rankings_df['rank'] = rankings_df.groupby('year')['expy'].rank(method='dense', ascending=False, na_option='bottom').astype(int)
        rankings_df['percentile'] = rankings_df.groupby('year')['expy'].rank(method='average', pct=True, na_option='bottom') * 100
        rankings_df['total_countries_ranked'] = rankings_df.groupby('year')['country_code'].transform('nunique')
    
    return rankings_df.sort_values(['year', 'rank']) 

@memoize(level=StorageTier.MEMORY)
def get_sophistication_category_v2(expy_value: Optional[float], min_expy: float = 5000, max_expy: float = 25000) -> str:
    if expy_value is None or pd.isna(expy_value): 
        raise ValueError("Input expy_value cannot be None or NaN for categorization.")
    if expy_value < min_expy + 0.25 * (max_expy - min_expy): 
        return "Low Sophistication"
    if expy_value < min_expy + 0.50 * (max_expy - min_expy): 
        return "Lower-Middle Sophistication"
    if expy_value < min_expy + 0.75 * (max_expy - min_expy): 
        return "Upper-Middle Sophistication"
    return "High Sophistication"

@protect("create_validation_result", OperationType.COMPUTATION)
def create_validation_result(is_valid=True, messages=None):
    """Helper to create ValidationResult with the correct interface."""
    from yemen_trade_diagnostic.interfaces.validation_interface import (
        ValidationIssueLevel, 
        ValidationResult
    )
    
    result = ValidationResult()
    if messages is None:
        messages = []
    
    # Add messages as issues
    for msg in messages:
        if not is_valid or "error" in msg.lower():
            result.add_issue(msg, ValidationIssueLevel.ERROR)
        else:
            result.add_issue(msg, ValidationIssueLevel.WARNING)
    
    return result

def validate_dataframe(df, schema, df_type="DataFrame"):
    """Validate DataFrame against schema using correct ValidationResult interface."""
    from yemen_trade_diagnostic.interfaces.validation_interface import (
        ValidationIssueLevel, 
        ValidationResult
    )
    
    result = ValidationResult()
    
    # Check if DataFrame is None or empty
    if df is None:
        result.add_issue(f"{df_type} is None", ValidationIssueLevel.ERROR)
        return result
        
    if df.empty:
        result.add_issue(f"{df_type} is empty", ValidationIssueLevel.ERROR)
        return result
    
    # Check required columns
    if schema and 'required_columns' in schema:
        missing_columns = [col for col in schema['required_columns'] if col not in df.columns]
        if missing_columns:
            result.add_issue(
                f"Missing required columns in {df_type}: {', '.join(missing_columns)}",
                ValidationIssueLevel.ERROR
            )
    
    # Check column types
    if schema and 'column_types' in schema:
        for col, expected_type in schema['column_types'].items():
            if col not in df.columns:
                continue
                
            if expected_type == 'integer':
                if not pd.api.types.is_integer_dtype(df[col]):
                    result.add_issue(
                        f"Column '{col}' in {df_type} should be integer",
                        ValidationIssueLevel.WARNING,
                        location=col
                    )
            elif expected_type == 'numeric':
                if not pd.api.types.is_numeric_dtype(df[col]):
                    result.add_issue(
                        f"Column '{col}' in {df_type} should be numeric",
                        ValidationIssueLevel.WARNING,
                        location=col
                    )
    
    # Check value ranges
    if schema and 'value_ranges' in schema:
        for col, ranges in schema['value_ranges'].items():
            if col not in df.columns or not pd.api.types.is_numeric_dtype(df[col]):
                continue
                
            if 'min' in ranges and df[col].min() < ranges['min']:
                result.add_issue(
                    f"Column '{col}' has values below minimum {ranges['min']}",
                    ValidationIssueLevel.WARNING,
                    location=col
                )
            if 'max' in ranges and df[col].max() > ranges['max']:
                result.add_issue(
                    f"Column '{col}' has values above maximum {ranges['max']}",
                    ValidationIssueLevel.WARNING,
                    location=col
                )
    
    return result

if __name__ == '__main__':
    configure_logging(log_level=LogLevel.DEBUG, log_to_console=True)
    logger.info("--- Running PRODY/EXPY Model V2 Example ---")

    # Sample data
    exports_sample = pd.DataFrame({
        'country_code': ['YEM', 'YEM', 'YEM', 'KSA', 'KSA', 'YEM', 'YEM'],
        'hs2': ['01', '02', '03', '01', '02', '01', '02'],
        'year': [2020, 2020, 2020, 2020, 2020, 2021, 2021],
        'value': [100, 200, 50, 500, 100, 120, 220]
    })
    gdp_sample = pd.DataFrame({
        'country_code': ['YEM', 'KSA', 'ARE'],
        'year': [2020, 2020, 2020],
        'gdp_per_capita': [1000, 20000, 40000]
    })
    baci_sample_for_ranking = pd.DataFrame({
        'year': [2020, 2020, 2020, 2020, 2021, 2021, 2021, 2021],
        'exporter': ['YEM', 'YEM', 'KSA', 'KSA', 'YEM', 'YEM', 'KSA', 'KSA'],
        'hs2': ['01', '02', '01', '02', '01', '02', '01', '02'],
        'value': [10, 20, 50, 10, 12, 22, 55, 12]
    })

    logger.info("\n--- Testing calculate_product_complexity_v2 ---")
    complexity_dict = calculate_product_complexity_v2(
        exports_sample, 
        gdp_sample, 
        country_col='country_code', 
        product_col='hs2',
        year_col='year',
        value_col='value'
    )
    logger.info(f"Product Complexity Dict: {complexity_dict}")
    
    logger.info("\n--- Testing calculate_prody_v2 ---")
    prody_df = calculate_prody_v2(
        exports_df=exports_sample, 
        gdp_df=gdp_sample, 
        country_col_export='country_code',
        product_col_export='hs2',
        year_col_export='year',
        value_col_export='value',
        country_col_gdp='country_code',
        year_col_gdp='year',
        gdp_pc_col='gdp_per_capita'
    )
    logger.info(f"PRODY DataFrame:\n{prody_df}")
    
    if not prody_df.empty:
        logger.info("\n--- Testing calculate_expy_v2 (YEM 2020) ---")
        yem_2020_exports = exports_sample[(exports_sample['country_code'] == 'YEM') & (exports_sample['year'] == 2020)]
        prody_df_2020 = prody_df[prody_df['year'] == 2020]

        if not yem_2020_exports.empty and not prody_df_2020.empty:
            expy_yem_2020 = calculate_expy_v2(
                exports_df_syc=yem_2020_exports, 
                prody_df_syr=prody_df_2020,
                product_col_export='hs2',
                value_col_export='value',
                product_col_prody='hs2', # Assuming prody_df has 'hs2' as product col
                prody_val_col='prody'
            )
            logger.info(f"EXPY for YEM 2020: {expy_yem_2020}")
            logger.info(f"Sophistication Category for YEM 2020: {get_sophistication_category_v2(expy_yem_2020)}")
        else:
            logger.warning("Skipping EXPY YEM 2020 test due to empty export or PRODY data for the year.")
        
        logger.info("\n--- Testing calculate_expy_by_year_v2 ---")
        expy_by_year_df = calculate_expy_by_year_v2(
            exports=exports_sample, 
            prody_df_all_years=prody_df, # Pass the full prody_df
            country_col='country_code',
            product_col_export='hs2',
            year_col_export='year',
            value_col='value',
            product_col_prody='hs2', # Col name in prody_df
            year_col_prody='year',     # Col name in prody_df
            prody_val_col='prody'     # Col name in prody_df
        )
        logger.info(f"EXPY by Year DataFrame:\n{expy_by_year_df}")
    else:
        logger.warning("PRODY DataFrame is empty, skipping further tests that depend on it.")
    
    logger.info("\n--- Testing calculate_expy_rankings_v2 ---")
    rankings_df = calculate_expy_rankings_v2(
        baci_data=baci_sample_for_ranking, 
        gdp_data=gdp_sample, 
        country_codes_list=['YEM', 'KSA', 'ARE'], 
        target_years=[2020, 2021],
        baci_exporter_col='exporter' # Specify if different from default 'exporter' in func signature
    )
    logger.info(f"Rankings DataFrame:\n{rankings_df}")