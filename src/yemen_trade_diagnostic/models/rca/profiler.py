"""
RCA Calculator Performance Profiler

This module provides a wrapper for RCA calculators to profile their performance
and collect metrics for adaptive optimization.
"""
# Standard library imports
import os
import time
import tracemalloc
import psutil
from functools import wraps
from typing import Any, Dict, Optional, Callable

# Third-party imports
import pandas as pd

# Project imports
from yemen_trade_diagnostic.models.rca.interface import RCACalculator
from yemen_trade_diagnostic.interfaces.logging_interface import get_logger
from yemen_trade_diagnostic.monitoring.decorators import monitor_performance
from yemen_trade_diagnostic.errors import protect, OperationType

# Configure logger
logger = get_logger(__name__)

class PerformanceProfiledCalculator(RCACalculator):
    """
    Wrapper class that profiles any RCA calculator implementation.
    
    This class wraps an existing RCA calculator and collects performance
    metrics during execution, including execution time, memory usage,
    and accuracy when reference data is available.
    """
    
    def __init__(self, calculator: RCACalculator):
        """
        Initialize the profiled calculator.
        
        Args:
            calculator: The RCA calculator to profile
        """
        self.calculator = calculator
        self.calculator_type = calculator.__class__.__name__
        self.metrics_history = []
        self.process = psutil.Process(os.getpid())
        logger.info(f"Created profiled wrapper for {self.calculator_type}")
    
    @monitor_performance(track_args=False)
    def calculate_rca(self, exports_df: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """
        Calculate RCA with performance profiling.
        
        Args:
            exports_df: DataFrame with export data
            **kwargs: Additional parameters for the calculator
            
        Returns:
            DataFrame with RCA values
        """
        metrics = {}
        
        # Record initial state
        initial_memory = self.process.memory_info().rss / 1024 / 1024  # MB
        
        # Start memory tracking
        tracemalloc.start()
        
        # Time the execution
        start_time = time.time()
        
        try:
            result = self.calculator.calculate_rca(exports_df, **kwargs)
            execution_time = time.time() - start_time
            
            # Get memory statistics
            current, peak = tracemalloc.get_traced_memory()
            tracemalloc.stop()
            
            # Record metrics
            metrics = {
                'method': 'calculate_rca',
                'execution_time': execution_time,
                'initial_memory_mb': initial_memory,
                'peak_memory_mb': peak / 1024 / 1024,
                'current_memory_mb': current / 1024 / 1024,
                'input_rows': len(exports_df),
                'output_rows': len(result) if result is not None else 0,
                'timestamp': time.time()
            }
            
            self.metrics_history.append(metrics)
            
            # Update selector metrics if available
            self._update_selector_metrics(metrics)
            
            logger.info(
                f"{self.calculator_type}.calculate_rca completed in {execution_time:.2f}s, "
                f"peak memory: {peak / 1024 / 1024:.2f}MB"
            )
            
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            tracemalloc.stop()
            
            metrics = {
                'method': 'calculate_rca',
                'execution_time': execution_time,
                'error': str(e),
                'input_rows': len(exports_df),
                'timestamp': time.time()
            }
            
            self.metrics_history.append(metrics)
            logger.error(f"Error in {self.calculator_type}.calculate_rca: {str(e)}")
            raise
            
        finally:
            # Ensure tracemalloc is stopped
            if tracemalloc.is_tracing():
                tracemalloc.stop()
    
    @monitor_performance(track_args=False)
    def create_proximity_matrix(self, rca_df: pd.DataFrame, threshold: float = 1.0, **kwargs) -> pd.DataFrame:
        """
        Create proximity matrix with performance profiling.
        
        Args:
            rca_df: DataFrame with RCA values
            threshold: RCA threshold for advantage
            **kwargs: Additional parameters for the calculator
            
        Returns:
            DataFrame with proximity matrix
        """
        metrics = {}
        
        # Record initial state
        initial_memory = self.process.memory_info().rss / 1024 / 1024  # MB
        
        # Start memory tracking
        tracemalloc.start()
        
        # Time the execution
        start_time = time.time()
        
        try:
            result = self.calculator.create_proximity_matrix(rca_df, threshold, **kwargs)
            execution_time = time.time() - start_time
            
            # Get memory statistics
            current, peak = tracemalloc.get_traced_memory()
            tracemalloc.stop()
            
            # Record metrics
            metrics = {
                'method': 'create_proximity_matrix',
                'execution_time': execution_time,
                'initial_memory_mb': initial_memory,
                'peak_memory_mb': peak / 1024 / 1024,
                'current_memory_mb': current / 1024 / 1024,
                'input_rows': len(rca_df),
                'output_rows': len(result) if result is not None else 0,
                'threshold': threshold,
                'timestamp': time.time()
            }
            
            self.metrics_history.append(metrics)
            
            # Update selector metrics if available
            self._update_selector_metrics(metrics)
            
            logger.info(
                f"{self.calculator_type}.create_proximity_matrix completed in {execution_time:.2f}s, "
                f"peak memory: {peak / 1024 / 1024:.2f}MB"
            )
            
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            tracemalloc.stop()
            
            metrics = {
                'method': 'create_proximity_matrix',
                'execution_time': execution_time,
                'error': str(e),
                'input_rows': len(rca_df),
                'threshold': threshold,
                'timestamp': time.time()
            }
            
            self.metrics_history.append(metrics)
            logger.error(f"Error in {self.calculator_type}.create_proximity_matrix: {str(e)}")
            raise
            
        finally:
            # Ensure tracemalloc is stopped
            if tracemalloc.is_tracing():
                tracemalloc.stop()
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """
        Get summary of collected performance metrics.
        
        Returns:
            Dictionary with performance statistics
        """
        if not self.metrics_history:
            return {
                'calculator_type': self.calculator_type,
                'total_calls': 0,
                'metrics': {}
            }
        
        # Separate by method
        method_metrics = {}
        for metric in self.metrics_history:
            method = metric.get('method', 'unknown')
            if method not in method_metrics:
                method_metrics[method] = []
            method_metrics[method].append(metric)
        
        # Calculate statistics
        summary = {
            'calculator_type': self.calculator_type,
            'total_calls': len(self.metrics_history),
            'metrics': {}
        }
        
        for method, metrics_list in method_metrics.items():
            execution_times = [m['execution_time'] for m in metrics_list if 'execution_time' in m and 'error' not in m]
            memory_peaks = [m['peak_memory_mb'] for m in metrics_list if 'peak_memory_mb' in m]
            
            if execution_times:
                summary['metrics'][method] = {
                    'calls': len(metrics_list),
                    'avg_execution_time': sum(execution_times) / len(execution_times),
                    'min_execution_time': min(execution_times),
                    'max_execution_time': max(execution_times),
                    'errors': len([m for m in metrics_list if 'error' in m])
                }
                
                if memory_peaks:
                    summary['metrics'][method].update({
                        'avg_peak_memory_mb': sum(memory_peaks) / len(memory_peaks),
                        'max_peak_memory_mb': max(memory_peaks)
                    })
        
        return summary
    
    def reset_metrics(self):
        """Reset collected metrics history."""
        self.metrics_history = []
        logger.info(f"Reset metrics for {self.calculator_type}")
    
    def _update_selector_metrics(self, metrics: Dict):
        """Update the RCA selector with performance metrics."""
        try:
            # Import here to avoid circular dependency
            from yemen_trade_diagnostic.models.rca.interface import RCACalculatorFactory
            
            if hasattr(RCACalculatorFactory, '_selector') and RCACalculatorFactory._selector:
                calculator_name = self.calculator_type.lower().replace('rcacalculator', '')
                
                selector_metrics = {
                    'execution_time': metrics.get('execution_time'),
                    'memory_usage': metrics.get('peak_memory_mb'),
                    'accuracy': metrics.get('accuracy')  # Will be None if not calculated
                }
                
                RCACalculatorFactory._selector.update_performance_history(
                    calculator_name, 
                    selector_metrics
                )
        except Exception as e:
            logger.debug(f"Could not update selector metrics: {str(e)}")

def profile_calculator_method(method: Callable) -> Callable:
    """
    Decorator to profile individual calculator methods.
    
    Args:
        method: Method to profile
        
    Returns:
        Wrapped method with profiling
    """
    @wraps(method)
    def wrapper(self, *args, **kwargs):
        start_time = time.time()
        tracemalloc.start()
        
        try:
            result = method(self, *args, **kwargs)
            execution_time = time.time() - start_time
            current, peak = tracemalloc.get_traced_memory()
            
            logger.info(
                f"{method.__name__} completed in {execution_time:.2f}s, "
                f"peak memory: {peak / 1024 / 1024:.2f}MB"
            )
            
            return result
        finally:
            tracemalloc.stop()
    
    return wrapper