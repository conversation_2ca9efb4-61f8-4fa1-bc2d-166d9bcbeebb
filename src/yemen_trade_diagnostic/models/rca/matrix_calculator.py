"""
Matrix-Accelerated RCA Calculator

This module provides a high-performance implementation of the RCA calculator
using matrix operations with hardware acceleration.
"""
# Standard library imports
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union

# Third-party imports
import numpy as np
import pandas as pd

# Project imports
from yemen_trade_diagnostic.interfaces.cache_interface import StorageTier, memoize
from yemen_trade_diagnostic.interfaces.hardware_interface import AccelerationType, get_hardware_manager
from yemen_trade_diagnostic.interfaces.logging_interface import <PERSON>g<PERSON><PERSON><PERSON>, get_logger, log_execution_time
from yemen_trade_diagnostic.interfaces.validation_interface import Validation<PERSON>ssueLevel, ValidationResult, get_validation_manager
from yemen_trade_diagnostic.models.rca.interface import RCACalculator, RCACalculatorFactory
from yemen_trade_diagnostic.models.rca.utils.matrix_operations import accelerated_proximity_matrix_calculation, accelerated_rca_matrix_calculation, get_optimal_hardware_type
from yemen_trade_diagnostic.errors import protect, OperationType, MODEL_CONFIG

# Configure logger
logger = get_logger(__name__)

class MatrixRCACalculator(RCACalculator):
    """
    High-performance RCA implementation using matrix operations with hardware acceleration.

    This implementation provides:
    - Fastest calculation for dense datasets
    - Optimized for GPU acceleration
    - Matrix operations for both RCA and proximity calculations
    - Minimal memory footprint when GPU acceleration is available
    """

    def __init__(self,
                use_acceleration: bool = True,
                acceleration_type: Optional[AccelerationType] = None):
        """
        Initialize the matrix-accelerated RCA calculator.

        Args:
            use_acceleration: Whether to use hardware acceleration
            acceleration_type: Specific acceleration type to use, or None for auto-selection
        """
        self.use_acceleration = use_acceleration
        self.acceleration_type = acceleration_type
        self.hw_manager = get_hardware_manager()
        self.logger = get_logger(f"{__name__}.{self.__class__.__name__}")

        # Check if hardware acceleration is available
        if self.use_acceleration and not self.hw_manager.is_hardware_acceleration_available():
            self.logger.warning("Hardware acceleration requested but not available, falling back to CPU")
            self.use_acceleration = False

        # Auto-select acceleration type if not specified
        if self.use_acceleration and self.acceleration_type is None:
            self.acceleration_type = get_optimal_hardware_type()
            self.logger.info(f"Auto-selected acceleration type: {self.acceleration_type}")

        self.logger.info(f"Initialized MatrixRCACalculator (use_acceleration={self.use_acceleration}, "
                       f"acceleration_type={self.acceleration_type})")

    def _create_pivot_matrix(self,
                            exports_df: pd.DataFrame,
                            country_col: str,
                            product_col: str,
                            value_col: str) -> Tuple[np.ndarray, List[Any], List[Any]]:
        """
        Create a pivot matrix from exports data.

        Args:
            exports_df: DataFrame with export data
            country_col: Column name for country
            product_col: Column name for product
            value_col: Column name for trade value

        Returns:
            Tuple of (pivot_matrix, country_index, product_index)
        """
        self.logger.info("Creating pivot matrix from exports data")

        # Create pivot table
        pivot_df = exports_df.pivot_table(
            index=country_col,
            columns=product_col,
            values=value_col,
            aggfunc='sum',
            fill_value=0
        )

        # Get country and product indices
        country_index = pivot_df.index.tolist()
        product_index = pivot_df.columns.tolist()

        # Convert to numpy array
        pivot_matrix = pivot_df.values

        self.logger.info(f"Created pivot matrix with shape {pivot_matrix.shape}")

        return pivot_matrix, country_index, product_index

    @protect("calculate_rca", OperationType.COMPUTATION)
    @memoize(ttl=3600, level=StorageTier.MEMORY)
    @log_execution_time(logger=logger)
    def calculate_rca(self,
                     exports_df: pd.DataFrame,
                     country_col: str = 'country',
                     product_col: str = 'product',
                     value_col: str = 'value',
                     **kwargs) -> pd.DataFrame:
        """
        Calculate RCA values using matrix operations.

        Args:
            exports_df: DataFrame with export data
            country_col: Column name for country
            product_col: Column name for product
            value_col: Column name for trade value
            **kwargs: Additional parameters

        Returns:
            DataFrame with RCA values
        """
        self.logger.info(f"Calculating matrix RCA for data with shape {exports_df.shape}")

        # Validate inputs
        if exports_df.empty:
            self.logger.warning("Empty exports DataFrame provided")
            return pd.DataFrame(columns=[country_col, product_col, 'rca', 'advantage_type'])

        # Check if required columns exist
        required_cols = [country_col, product_col, value_col]
        missing_cols = [col for col in required_cols if col not in exports_df.columns]
        if missing_cols:
            err_msg = f"Required columns missing from exports data: {missing_cols}"
            self.logger.error(err_msg)
            raise ValueError(err_msg)

        # Create pivot matrix
        pivot_matrix, country_index, product_index = self._create_pivot_matrix(
            exports_df=exports_df,
            country_col=country_col,
            product_col=product_col,
            value_col=value_col
        )

        # Calculate RCA matrix
        if self.use_acceleration:
            self.logger.info(f"Using accelerated RCA matrix calculation with {self.acceleration_type}")
            rca_matrix = accelerated_rca_matrix_calculation(
                export_matrix=pivot_matrix,
                acceleration_type=self.acceleration_type
            )
        else:
            self.logger.info("Using standard RCA matrix calculation")
            # Calculate country totals (sum across products)
            country_totals = np.sum(pivot_matrix, axis=1, keepdims=True)
            # Calculate product totals (sum across countries)
            product_totals = np.sum(pivot_matrix, axis=0, keepdims=True)

            # Calculate global total
            global_total = np.sum(product_totals)

            # Calculate country shares
            country_shares = np.divide(
                pivot_matrix,
                country_totals,
                out=np.zeros_like(pivot_matrix, dtype=float),
                where=country_totals!=0
            )

            # Calculate world shares
            world_shares = np.divide(
                product_totals,
                global_total,
                out=np.zeros_like(product_totals, dtype=float),
                where=global_total!=0
            )

            # Calculate RCA
            rca_matrix = np.divide(
                country_shares,
                world_shares,
                out=np.zeros_like(pivot_matrix, dtype=float),
                where=world_shares!=0
            )

            # Handle NaN and Inf values
            rca_matrix = np.nan_to_num(rca_matrix, nan=0.0, posinf=0.0, neginf=0.0)

        # Convert RCA matrix back to DataFrame
        self.logger.info("Converting RCA matrix to DataFrame")
        rca_df = pd.DataFrame(
            index=country_index,
            columns=product_index,
            data=rca_matrix
        )

        # Melt the DataFrame to long format
        # Reset index to make the country column a regular column
        reset_df = rca_df.reset_index()

        # Rename the index column to match the expected country column name
        if reset_df.columns[0] != country_col:
            reset_df = reset_df.rename(columns={reset_df.columns[0]: country_col})

        result_df = pd.melt(
            reset_df,
            id_vars=[country_col],
            var_name=product_col,
            value_name='rca'
        )

        # Add advantage_type column
        result_df['advantage_type'] = result_df['rca'].apply(self.get_advantage_type)

        # Add detailed_advantage_type column
        result_df['detailed_advantage_type'] = result_df['rca'].apply(self.get_detailed_advantage_type)

        self.logger.info(f"Matrix RCA calculation completed with {len(result_df)} results")

        return result_df

    @protect("create_proximity_matrix", OperationType.COMPUTATION)
    @memoize(ttl=3600, level=StorageTier.MEMORY)
    @log_execution_time(logger=logger)
    def create_proximity_matrix(self,
                               rca_df: pd.DataFrame,
                               threshold: float = 1.0,
                               country_col: str = 'country',
                               product_col: str = 'product',
                               rca_col: str = 'rca',
                               **kwargs) -> pd.DataFrame:
        """
        Create a product proximity matrix from RCA values using matrix operations.

        Args:
            rca_df: DataFrame with RCA values
            threshold: RCA threshold for considering a product as having comparative advantage
            country_col: Column name for country
            product_col: Column name for product
            rca_col: Column name for RCA value
            **kwargs: Additional parameters

        Returns:
            DataFrame with product proximity matrix
        """
        self.logger.info(f"Creating proximity matrix from RCA data with shape {rca_df.shape}")

        # Validate inputs
        if rca_df.empty:
            self.logger.warning("Empty RCA DataFrame provided")
            return pd.DataFrame(columns=['product1', 'product2', 'proximity'])

        # Check if required columns exist
        required_cols = [country_col, product_col, rca_col]
        missing_cols = [col for col in required_cols if col not in rca_df.columns]
        if missing_cols:
            err_msg = f"Required columns missing from RCA data: {missing_cols}"
            self.logger.error(err_msg)
            raise ValueError(err_msg)

        # Create pivot table of RCA values
        pivot_df = rca_df.pivot_table(
            index=country_col,
            columns=product_col,
            values=rca_col,
            fill_value=0
        )

        # Get product index
        product_index = pivot_df.columns.tolist()

        # Create binary MCP matrix (1 if RCA >= threshold, 0 otherwise)
        mcp_matrix = (pivot_df.values >= threshold).astype(float)

        # Calculate proximity matrix
        if self.use_acceleration:
            self.logger.info(f"Using accelerated proximity matrix calculation with {self.acceleration_type}")
            proximity_matrix = accelerated_proximity_matrix_calculation(
                mcp_matrix=mcp_matrix,
                acceleration_type=self.acceleration_type
            )
        else:
            self.logger.info("Using standard proximity matrix calculation")
            # Calculate product ubiquity (number of countries with RCA > threshold for each product)
            product_ubiquity = np.sum(mcp_matrix, axis=0)
            # Calculate co-occurrence matrix
            co_occurrence = np.dot(mcp_matrix.T, mcp_matrix)

            # Calculate proximity matrix
            denominator = np.maximum(
                np.outer(product_ubiquity, np.ones_like(product_ubiquity)),
                np.outer(np.ones_like(product_ubiquity), product_ubiquity)
            )
            proximity_matrix = np.divide(
                co_occurrence,
                denominator,
                out=np.zeros_like(co_occurrence, dtype=float),
                where=denominator!=0
            )

            # Handle NaN and Inf values
            proximity_matrix = np.nan_to_num(proximity_matrix, nan=0.0, posinf=0.0, neginf=0.0)

        # Convert proximity matrix to DataFrame
        proximity_list = []

        for i, product1 in enumerate(product_index):
            for j, product2 in enumerate(product_index):
                proximity_list.append({
                    'product1': product1,
                    'product2': product2,
                    'proximity': proximity_matrix[i, j]
                })

        proximity_df = pd.DataFrame(proximity_list)

        self.logger.info(f"Proximity matrix calculation completed with {len(proximity_df)} entries")

        return proximity_df

# Register the calculator with the factory
RCACalculatorFactory.register_calculator('matrix', MatrixRCACalculator)
