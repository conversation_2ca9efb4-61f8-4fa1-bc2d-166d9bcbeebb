"""
RCA Models (V2)

This package contains V2 models for Revealed Comparative Advantage analysis.
"""

# Standard library imports
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

# Third-party imports
import pandas as pd

# Project imports
from yemen_trade_diagnostic.models.rca.exact_calculator import ExactRCACalculator

# Import base interface and factory
from yemen_trade_diagnostic.models.rca.interface import (
    RCACalculator, 
    RCACalculatorFactory, 
    get_advantage_type, 
    get_detailed_advantage_type
)
from yemen_trade_diagnostic.models.rca.matrix_calculator import MatrixRCACalculator
from yemen_trade_diagnostic.models.rca.optimized_calculator import OptimizedRCACalculator

# Import transition model
from yemen_trade_diagnostic.models.rca.rca_transition_model import (
    AdvantageCategoryV2,
    RcaAnalysisConfigV2,
    RCATransitionModelV2,
    calculate_rca_transitions_v2,
    get_advantage_category_v2 as get_transition_advantage_category_v2,
    prepare_transition_matrix_data_v2
)

# Import calculator implementations
from yemen_trade_diagnostic.models.rca.standard_calculator import StandardRCACalculator

# Import verification tools
from yemen_trade_diagnostic.models.rca.verification import RCAVerifier

# Import configuration and benchmarking tools
from yemen_trade_diagnostic.models.rca.config import (
    RCACalculatorConfig,
    RCACalculatorSelector,
    DatasetSize,
    CalculatorType
)
from yemen_trade_diagnostic.models.rca.benchmark import (
    RCABenchmark,
    BenchmarkResult
)
from yemen_trade_diagnostic.models.rca.profiler import (
    PerformanceProfiledCalculator
)

# Convenience functions
def calculate_rca(exports_df, calculator_type='standard', **kwargs):
    """
    Calculate RCA using the specified calculator type.

    Args:
        exports_df: DataFrame with export data
        calculator_type: Type of calculator to use ('standard', 'optimized', 'matrix', 'exact')
        **kwargs: Additional parameters for the specific calculator type

    Returns:
        DataFrame with RCA values
    """
    # Extract parameters that should only go to the calculator creation
    calculator_params = {}
    calculation_params = {}

    # Parameters that should only go to the calculator creation
    calculator_only_params = ['use_acceleration', 'acceleration_type', 'aggregates_path', 'processed_data_dir']

    # Split parameters
    for key, value in kwargs.items():
        if key in calculator_only_params:
            calculator_params[key] = value
        else:
            calculation_params[key] = value

    # Create calculator with appropriate parameters
    calculator = RCACalculatorFactory.create_calculator(calculator_type, **calculator_params)

    # Calculate RCA with remaining parameters
    return calculator.calculate_rca(exports_df, **calculation_params)

def create_proximity_matrix(rca_df, calculator_type='standard', **kwargs):
    """
    Create a product proximity matrix using the specified calculator type.

    Args:
        rca_df: DataFrame with RCA values
        calculator_type: Type of calculator to use ('standard', 'optimized', 'matrix', 'exact')
        **kwargs: Additional parameters for the specific calculator type

    Returns:
        DataFrame with proximity matrix
    """
    calculator = RCACalculatorFactory.create_calculator(calculator_type, **kwargs)
    return calculator.create_proximity_matrix(rca_df, **kwargs)

def create_optimal_calculator(**kwargs):
    """
    Create the optimal RCA calculator based on available hardware and requirements.

    Args:
        **kwargs: Parameters including precision_required, hardware_acceleration, etc.

    Returns:
        RCACalculator instance
    """
    return RCACalculatorFactory.create_optimal_calculator(**kwargs)

# Legacy functions for backward compatibility
# These functions provide backward compatibility with the old API
# but use the new calculator implementations internally

def calculate_rca_v2(exports_df, world_exports_df=None, raw_data_dir=None, processed_data_dir=None,
                    country_col='exporter_iso', product_col='product_code', year_col='year', value_col='trade_value_usd'):
    """
    Legacy function for backward compatibility.
    Calculates RCA using the StandardRCACalculator.

    Args:
        exports_df: DataFrame with export data
        world_exports_df: Optional DataFrame with world export data
        raw_data_dir: Optional path to raw data directory
        processed_data_dir: Optional path to processed data directory
        country_col: Column name for country identifiers
        product_col: Column name for product identifiers
        year_col: Column name for year
        value_col: Column name for trade values

    Returns:
        DataFrame with RCA values
    """
    # Use the new calculate_rca convenience function with the standard calculator
    return calculate_rca(
        exports_df=exports_df,
        calculator_type='standard',
        processed_data_dir=processed_data_dir,
        country_col=country_col,
        product_col=product_col,
        year_col=year_col,
        value_col=value_col
    )

def get_advantage_type_v2(rca_value, threshold=1.0):
    """
    Legacy function for backward compatibility.
    Determines the advantage type based on RCA value.

    Args:
        rca_value: RCA value
        threshold: Threshold for advantage (default: 1.0)

    Returns:
        String indicating advantage type ('advantaged' or 'disadvantaged')
    """
    # Use the new get_advantage_type function
    return get_advantage_type(rca_value, threshold)

def load_world_exports_v2(path=None, years=None):
    """
    Legacy function for backward compatibility.
    Loads world exports data.

    Args:
        path: Path to world exports data
        years: Optional year to filter by

    Returns:
        DataFrame with world exports data
    """
    # This functionality is now handled by the data loaders
    # For backward compatibility, we'll use a simple implementation
    from yemen_trade_diagnostic.data import DataSource, load_data

    if path is None:
        # Use default path
        path = "data/processed/world_exports"

    try:
        data = load_data("world_exports", years=years)
        return data
    except:
        # If the loader is not available, return an empty DataFrame
        return pd.DataFrame()

def optimize_dataframe_memory_v2(df):
    """
    Legacy function for backward compatibility.
    Optimizes DataFrame memory usage.

    Args:
        df: DataFrame to optimize

    Returns:
        Optimized DataFrame
    """
    # This functionality is now handled by the memory optimizer
    # For backward compatibility, we'll use a simple implementation
    from yemen_trade_diagnostic.utils.memory_optimizer import optimize_dataframe_dtypes

    return optimize_dataframe_dtypes(df)

# Backward compatibility for exact RCA implementation
def calculate_exact_rca(exports_df, aggregates_path=None, years=None,
                       country_col='exporter_iso', product_col='product_code', value_col='trade_value_usd'):
    """
    Legacy function for backward compatibility.
    Calculates RCA using the ExactRCACalculator.

    Args:
        exports_df: DataFrame with export data
        aggregates_path: Path to aggregates directory
        years: Year for RCA calculation
        country_col: Column name for country identifiers
        product_col: Column name for product identifiers
        value_col: Column name for trade values

    Returns:
        DataFrame with RCA values
    """
    # Use the new calculate_rca convenience function with the exact calculator
    return calculate_rca(
        exports_df=exports_df,
        calculator_type='exact',
        aggregates_path=aggregates_path,
        years=years,
        country_col=country_col,
        product_col=product_col,
        value_col=value_col
    )

def create_proximity_matrix_exact(rca_df, threshold=1.0,
                                country_col='exporter_iso', product_col='product_code', rca_col='rca'):
    """
    Legacy function for backward compatibility.
    Creates a proximity matrix using the ExactRCACalculator.

    Args:
        rca_df: DataFrame with RCA values
        threshold: Threshold for advantage (default: 1.0)
        country_col: Column name for country identifiers
        product_col: Column name for product identifiers
        rca_col: Column name for RCA values

    Returns:
        DataFrame with proximity matrix
    """
    # Use the new create_proximity_matrix convenience function with the exact calculator
    return create_proximity_matrix(
        rca_df=rca_df,
        calculator_type='exact',
        threshold=threshold,
        country_col=country_col,
        product_col=product_col,
        rca_col=rca_col
    )

# Backward compatibility for matrix-accelerated RCA implementation
def calculate_matrix_rca(exports_df, use_acceleration=True,
                        country_col='exporter_iso', product_col='product_code',
                        year_col='year', value_col='trade_value_usd'):
    """
    Legacy function for backward compatibility.
    Calculates RCA using the MatrixRCACalculator.

    Args:
        exports_df: DataFrame with export data
        use_acceleration: Whether to use hardware acceleration
        country_col: Column name for country identifiers
        product_col: Column name for product identifiers
        year_col: Column name for year
        value_col: Column name for trade values

    Returns:
        DataFrame with RCA values
    """
    # Use the new calculate_rca convenience function with the matrix calculator
    return calculate_rca(
        exports_df=exports_df,
        calculator_type='matrix',
        use_acceleration=use_acceleration,
        country_col=country_col,
        product_col=product_col,
        year_col=year_col,
        value_col=value_col
    )

# Backward compatibility for optimized RCA implementation
def calculate_rca_optimized(exports_df, aggregates_path=None, years=None,
                           country_col='exporter_iso', product_col='product_code', value_col='trade_value_usd'):
    """
    Legacy function for backward compatibility.
    Calculates RCA using the OptimizedRCACalculator.

    Args:
        exports_df: DataFrame with export data
        aggregates_path: Path to aggregates directory
        years: Year for RCA calculation
        country_col: Column name for country identifiers
        product_col: Column name for product identifiers
        value_col: Column name for trade values

    Returns:
        DataFrame with RCA values
    """
    # Use the new calculate_rca convenience function with the optimized calculator
    return calculate_rca(
        exports_df=exports_df,
        calculator_type='optimized',
        aggregates_path=aggregates_path,
        years=years,
        country_col=country_col,
        product_col=product_col,
        value_col=value_col
    )

class RCAModelV2:
    """
    Legacy class for backward compatibility.
    Provides the same interface as the old RCAModelV2 class
    but uses the new calculator implementations internally.
    """

    def __init__(self, raw_data_dir=None, processed_data_dir=None):
        """
        Initialize the RCA model.

        Args:
            raw_data_dir: Path to raw data directory
            processed_data_dir: Path to processed data directory
        """
        self.raw_data_dir = raw_data_dir
        self.processed_data_dir = processed_data_dir
        self.calculator = create_optimal_calculator(
            processed_data_dir=processed_data_dir
        )

    def calculate_rca_for_country_data(self, exports_df, world_exports_df=None,
                                      country_col='exporter_iso', product_col='product_code',
                                      year_col='year', value_col='trade_value_usd'):
        """
        Calculate RCA for country data.

        Args:
            exports_df: DataFrame with export data
            world_exports_df: Optional DataFrame with world export data
            country_col: Column name for country identifiers
            product_col: Column name for product identifiers
            year_col: Column name for year
            value_col: Column name for trade values

        Returns:
            DataFrame with RCA values
        """
        return self.calculator.calculate_rca(
            exports_df=exports_df,
            country_col=country_col,
            product_col=product_col,
            year_col=year_col,
            value_col=value_col
        )

    def create_proximity_matrix(self, rca_df, threshold=1.0):
        """
        Create a product proximity matrix.

        Args:
            rca_df: DataFrame with RCA values
            threshold: Threshold for advantage (default: 1.0)

        Returns:
            DataFrame with proximity matrix
        """
        return self.calculator.create_proximity_matrix(
            rca_df=rca_df,
            threshold=threshold
        )

__all__ = [
    # Base classes and factory
    "RCACalculator",
    "RCACalculatorFactory",
    "get_advantage_type",
    "get_detailed_advantage_type",

    # Calculator implementations
    "StandardRCACalculator",
    "OptimizedRCACalculator",
    "MatrixRCACalculator",
    "ExactRCACalculator",
    
    # Configuration and selection
    "RCACalculatorConfig",
    "RCACalculatorSelector",
    "DatasetSize",
    "CalculatorType",
    
    # Benchmarking and profiling
    "RCABenchmark",
    "BenchmarkResult",
    "PerformanceProfiledCalculator",

    # Verification
    "RCAVerifier",

    # Transition model
    "calculate_rca_transitions_v2",
    "prepare_transition_matrix_data_v2",
    "get_transition_advantage_category_v2",
    "AdvantageCategoryV2",
    "RcaAnalysisConfigV2",
    "RCATransitionModelV2",

    # Convenience functions
    "calculate_rca",
    "create_proximity_matrix",
    "create_optimal_calculator",

    # Legacy functions
    "calculate_rca_v2",
    "get_advantage_type_v2",
    "load_world_exports_v2",
    "optimize_dataframe_memory_v2",
    "RCAModelV2",

    # Legacy functions for exact RCA
    "calculate_exact_rca",
    "create_proximity_matrix_exact",

    # Legacy functions for matrix RCA
    "calculate_matrix_rca",

    # Legacy functions for optimized RCA
    "calculate_rca_optimized"
]