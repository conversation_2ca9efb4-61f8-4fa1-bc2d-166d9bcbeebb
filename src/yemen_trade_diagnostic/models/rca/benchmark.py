"""
RCA Calculator Benchmarking Framework

This module provides comprehensive benchmarking capabilities for comparing
different RCA calculator implementations.
"""
# Standard library imports
import time
import tracemalloc
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Tuple, Any, Union
import json
from pathlib import Path
from datetime import datetime

# Third-party imports
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

# Project imports
from yemen_trade_diagnostic.models.rca.interface import RCACalculatorFactory
from yemen_trade_diagnostic.models.rca.config import RCACalculatorConfig, DatasetSize
from yemen_trade_diagnostic.interfaces.logging_interface import get_logger
from yemen_trade_diagnostic.interfaces.hardware_interface import get_hardware_manager
from yemen_trade_diagnostic.errors import protect, OperationType

# Configure logger
logger = get_logger(__name__)

@dataclass
class BenchmarkResult:
    """Results from a single benchmark run."""
    calculator_type: str
    dataset_size: int
    execution_time: float
    memory_peak: float
    memory_allocated: float
    accuracy: Optional[float] = None
    hardware_info: Dict = field(default_factory=dict)
    error: Optional[str] = None
    timestamp: str = field(default_factory=lambda: datetime.now().isoformat())
    
    def to_dict(self) -> Dict:
        """Convert to dictionary for serialization."""
        return {
            'calculator_type': self.calculator_type,
            'dataset_size': self.dataset_size,
            'execution_time': self.execution_time,
            'memory_peak': self.memory_peak,
            'memory_allocated': self.memory_allocated,
            'accuracy': self.accuracy,
            'hardware_info': self.hardware_info,
            'error': self.error,
            'timestamp': self.timestamp
        }

class RCABenchmark:
    """
    Comprehensive benchmarking suite for RCA calculators.
    
    This class provides tools to benchmark and compare different RCA
    calculator implementations across various metrics and scenarios.
    """
    
    def __init__(self, results_dir: Optional[Path] = None):
        """
        Initialize the benchmark suite.
        
        Args:
            results_dir: Directory to save benchmark results
        """
        self.results_dir = results_dir or Path("data/benchmarks/rca")
        self.results_dir.mkdir(parents=True, exist_ok=True)
        self.hw_manager = get_hardware_manager()
        self.results: List[BenchmarkResult] = []
    
    def generate_test_data(self, size: DatasetSize) -> pd.DataFrame:
        """
        Generate synthetic test data for benchmarking.
        
        Args:
            size: Dataset size category
            
        Returns:
            DataFrame with synthetic export data
        """
        size_map = {
            DatasetSize.SMALL: (10, 50, 5),      # 10 countries, 50 products, 5 years
            DatasetSize.MEDIUM: (50, 500, 10),   # 50 countries, 500 products, 10 years
            DatasetSize.LARGE: (200, 2000, 20)   # 200 countries, 2000 products, 20 years
        }
        
        n_countries, n_products, n_years = size_map[size]
        
        # Generate data
        countries = [f"COUNTRY_{i:03d}" for i in range(n_countries)]
        products = [f"PROD_{i:04d}" for i in range(n_products)]
        years = list(range(2000, 2000 + n_years))
        
        data = []
        for year in years:
            for country in countries:
                # Each country exports a subset of products
                exported_products = np.random.choice(
                    products, 
                    size=np.random.randint(10, min(len(products), 100)),
                    replace=False
                )
                for product in exported_products:
                    data.append({
                        'year': year,
                        'exporter_iso': country,
                        'product_code': product,
                        'trade_value_usd': np.random.lognormal(10, 2) * 1000
                    })
        
        df = pd.DataFrame(data)
        logger.info(f"Generated test data: {len(df)} rows, {df.memory_usage(deep=True).sum() / 1024 / 1024:.2f} MB")
        
        return df
    
    @protect("benchmark_calculator", OperationType.COMPUTATION)
    def benchmark_calculator(self, 
                           calculator_type: str, 
                           test_data: pd.DataFrame,
                           reference_result: Optional[pd.DataFrame] = None) -> BenchmarkResult:
        """
        Benchmark a single calculator implementation.
        
        Args:
            calculator_type: Type of calculator to benchmark
            test_data: Test data for benchmarking
            reference_result: Optional reference result for accuracy comparison
            
        Returns:
            BenchmarkResult object
        """
        logger.info(f"Benchmarking {calculator_type} calculator with {len(test_data)} rows")
        
        # Start memory tracking
        tracemalloc.start()
        
        try:
            # Create calculator
            calculator = RCACalculatorFactory.create_calculator(calculator_type)
            
            # Time the execution
            start_time = time.time()
            result = calculator.calculate_rca(test_data)
            execution_time = time.time() - start_time
            
            # Get memory statistics
            current, peak = tracemalloc.get_traced_memory()
            tracemalloc.stop()
            
            # Calculate accuracy if reference provided
            accuracy = None
            if reference_result is not None:
                accuracy = self._calculate_accuracy(result, reference_result)
            
            # Get hardware info
            hardware_info = {
                'hardware_available': self.hw_manager.is_hardware_acceleration_available(),
                'acceleration_type': self.hw_manager.get_available_acceleration_types()
            }
            
            return BenchmarkResult(
                calculator_type=calculator_type,
                dataset_size=len(test_data),
                execution_time=execution_time,
                memory_peak=peak / 1024 / 1024,  # Convert to MB
                memory_allocated=current / 1024 / 1024,  # Convert to MB
                accuracy=accuracy,
                hardware_info=hardware_info
            )
        except Exception as e:
            tracemalloc.stop()
            logger.error(f"Error benchmarking {calculator_type}: {str(e)}")
            return BenchmarkResult(
                calculator_type=calculator_type,
                dataset_size=len(test_data),
                execution_time=-1,
                memory_peak=-1,
                memory_allocated=-1,
                error=str(e)
            )
    
    def benchmark_all_calculators(self, test_data: pd.DataFrame) -> List[BenchmarkResult]:
        """
        Benchmark all registered calculator implementations.
        
        Args:
            test_data: Test data for benchmarking
            
        Returns:
            List of BenchmarkResult objects
        """
        calculator_types = ['standard', 'optimized', 'exact', 'matrix']
        results = []
        
        # Use exact calculator as reference for accuracy
        logger.info("Calculating reference result using exact calculator")
        reference_calculator = RCACalculatorFactory.create_calculator('exact')
        reference_result = reference_calculator.calculate_rca(test_data)
        
        for calc_type in calculator_types:
            result = self.benchmark_calculator(calc_type, test_data, reference_result)
            results.append(result)
            self.results.append(result)
        
        return results
    
    def run_comprehensive_benchmark(self) -> Dict[str, List[BenchmarkResult]]:
        """
        Run comprehensive benchmarks across all dataset sizes.
        
        Returns:
            Dictionary mapping dataset sizes to results
        """
        all_results = {}
        
        for size in DatasetSize:
            logger.info(f"Running benchmarks for {size.value} dataset")
            test_data = self.generate_test_data(size)
            results = self.benchmark_all_calculators(test_data)
            all_results[size.value] = results
        
        # Save results
        self._save_results()
        
        return all_results
    
    def create_performance_report(self, output_path: Optional[Path] = None) -> Path:
        """
        Create a comprehensive performance report with visualizations.
        
        Args:
            output_path: Optional path for the report
            
        Returns:
            Path to the generated report
        """
        if not self.results:
            logger.warning("No benchmark results to report")
            return None
        
        output_path = output_path or self.results_dir / f"benchmark_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
        
        # Create visualizations
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # Prepare data
        df = pd.DataFrame([r.to_dict() for r in self.results if r.error is None])
        
        # 1. Execution time comparison
        ax = axes[0, 0]
        pivot_time = df.pivot_table(values='execution_time', index='dataset_size', columns='calculator_type')
        pivot_time.plot(kind='bar', ax=ax)
        ax.set_title('Execution Time by Calculator Type and Dataset Size')
        ax.set_ylabel('Time (seconds)')
        ax.legend(title='Calculator Type')
        
        # 2. Memory usage comparison
        ax = axes[0, 1]
        pivot_memory = df.pivot_table(values='memory_peak', index='dataset_size', columns='calculator_type')
        pivot_memory.plot(kind='bar', ax=ax)
        ax.set_title('Peak Memory Usage by Calculator Type and Dataset Size')
        ax.set_ylabel('Memory (MB)')
        ax.legend(title='Calculator Type')
        
        # 3. Speed improvement relative to standard
        ax = axes[1, 0]
        if 'standard' in df['calculator_type'].values:
            standard_times = df[df['calculator_type'] == 'standard'].set_index('dataset_size')['execution_time']
            speedup_data = []
            for calc_type in df['calculator_type'].unique():
                if calc_type != 'standard':
                    calc_times = df[df['calculator_type'] == calc_type].set_index('dataset_size')['execution_time']
                    for size in calc_times.index:
                        if size in standard_times.index:
                            speedup = standard_times[size] / calc_times[size]
                            speedup_data.append({
                                'calculator_type': calc_type,
                                'dataset_size': size,
                                'speedup': speedup
                            })
            
            if speedup_data:
                speedup_df = pd.DataFrame(speedup_data)
                pivot_speedup = speedup_df.pivot_table(values='speedup', index='dataset_size', columns='calculator_type')
                pivot_speedup.plot(kind='bar', ax=ax)
                ax.set_title('Speed Improvement Relative to Standard Calculator')
                ax.set_ylabel('Speedup Factor')
                ax.axhline(y=1, color='r', linestyle='--', alpha=0.5)
                ax.legend(title='Calculator Type')
        
        # 4. Accuracy comparison (if available)
        ax = axes[1, 1]
        if 'accuracy' in df.columns and df['accuracy'].notna().any():
            pivot_accuracy = df.pivot_table(values='accuracy', index='dataset_size', columns='calculator_type')
            pivot_accuracy.plot(kind='bar', ax=ax)
            ax.set_title('Accuracy Comparison')
            ax.set_ylabel('Accuracy Score')
            ax.legend(title='Calculator Type')
        else:
            ax.text(0.5, 0.5, 'No accuracy data available', ha='center', va='center', transform=ax.transAxes)
        
        plt.tight_layout()
        
        # Save plot
        plot_path = output_path.parent / f"{output_path.stem}_plots.png"
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        # Generate HTML report
        html_content = self._generate_html_report(df, plot_path)
        
        with open(output_path, 'w') as f:
            f.write(html_content)
        
        logger.info(f"Performance report saved to {output_path}")
        return output_path
    
    def _calculate_accuracy(self, result: pd.DataFrame, reference: pd.DataFrame) -> Optional[float]:
        """Calculate accuracy score between result and reference."""
        try:
            # Merge results on common columns
            merged = result.merge(
                reference, 
                on=['exporter_iso', 'product_code', 'year'],
                suffixes=('_test', '_ref')
            )
            # Calculate correlation
            if 'rca_test' in merged.columns and 'rca_ref' in merged.columns:
                correlation = merged['rca_test'].corr(merged['rca_ref'])
                return correlation
            
            return None
        except Exception as e:
            logger.error(f"Error calculating accuracy: {str(e)}")
            return None
    
    def _save_results(self):
        """Save benchmark results to JSON file."""
        results_file = self.results_dir / f"benchmark_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(results_file, 'w') as f:
            json.dump(
                [r.to_dict() for r in self.results],
                f,
                indent=2
            )
        logger.info(f"Results saved to {results_file}")
    
    def _generate_html_report(self, df: pd.DataFrame, plot_path: Path) -> str:
        """Generate HTML report content."""
        return f"""
<!DOCTYPE html>
<html>
<head>
    <title>RCA Calculator Benchmark Report</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        h1, h2 {{ color: #333; }}
        table {{ border-collapse: collapse; width: 100%; margin: 20px 0; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f2f2f2; }}
        .summary {{ background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin: 20px 0; }}
        img {{ max-width: 100%; height: auto; margin: 20px 0; }}
    </style>
</head>
<body>
    <h1>RCA Calculator Benchmark Report</h1>
    <p>Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    
    <div class="summary">
        <h2>Summary</h2>
        <ul>
            <li>Total benchmarks run: {len(self.results)}</li>
            <li>Calculator types tested: {df['calculator_type'].nunique()}</li>
            <li>Dataset sizes tested: {df['dataset_size'].nunique()}</li>
            <li>Hardware acceleration available: {self.hw_manager.is_hardware_acceleration_available()}</li>
        </ul>
    </div>
    
    <h2>Performance Metrics</h2>
    {df.to_html(index=False)}
    
    <h2>Visual Analysis</h2>
    <img src="{plot_path.name}" alt="Performance Analysis Charts">
    
    <h2>Recommendations</h2>
    <ul>
        <li><strong>For small datasets:</strong> {self._get_recommendation(df, 'small')}</li>
        <li><strong>For medium datasets:</strong> {self._get_recommendation(df, 'medium')}</li>
        <li><strong>For large datasets:</strong> {self._get_recommendation(df, 'large')}</li>
    </ul>
</body>
</html>
"""
    
    def _get_recommendation(self, df: pd.DataFrame, size: str) -> str:
        """Get calculator recommendation for dataset size."""
        size_df = df[df['dataset_size'].astype(str).str.contains(size, case=False)]
        if size_df.empty:
            return "No data available"
        
        # Find fastest calculator
        fastest = size_df.loc[size_df['execution_time'].idxmin(), 'calculator_type']
        
        # Find most memory efficient
        memory_efficient = size_df.loc[size_df['memory_peak'].idxmin(), 'calculator_type']
        
        if fastest == memory_efficient:
            return f"Use {fastest} calculator (best overall performance)"
        else:
            return f"Use {fastest} for speed or {memory_efficient} for memory efficiency"