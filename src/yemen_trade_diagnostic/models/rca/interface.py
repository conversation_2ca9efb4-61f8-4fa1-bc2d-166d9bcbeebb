"""
RCA Calculator Interface and Factory

This module defines the base interface for all RCA calculation implementations
and provides a factory for creating RCA calculator instances.
"""
# Standard library imports
from abc import ABC, abstractmethod
from pathlib import Path
from typing import Any, Dict, List, Optional, Type, Union

# Third-party imports
import pandas as pd

# Project imports
from yemen_trade_diagnostic.interfaces.cache_interface import StorageTier, memoize
from yemen_trade_diagnostic.interfaces.hardware_interface import AccelerationType, get_hardware_manager
from yemen_trade_diagnostic.interfaces.logging_interface import Log<PERSON><PERSON>l, get_logger, log_execution_time
from yemen_trade_diagnostic.interfaces.validation_interface import ValidationIssueLevel, ValidationResult, get_validation_manager
from yemen_trade_diagnostic.errors import protect, OperationType

# Configure logger
logger = get_logger(__name__)

class RCACalculator(ABC):
    """Base interface for all RCA calculation implementations."""
    
    @protect("calculate_rca", OperationType.COMPUTATION)
    @abstractmethod
    def calculate_rca(self, 
                     exports_df: pd.DataFrame,
                     **kwargs) -> pd.DataFrame:
        """
        Calculate RCA values for the given exports data.
        
        Args:
            exports_df: DataFrame with export data
            **kwargs: Additional parameters specific to the implementation
            
        Returns:
            DataFrame with RCA values
        """
        pass
    
    @protect("create_proximity_matrix", OperationType.COMPUTATION)
    @abstractmethod
    def create_proximity_matrix(self, 
                               rca_df: pd.DataFrame,
                               threshold: float = 1.0,
                               **kwargs) -> pd.DataFrame:
        """
        Create a product proximity matrix from RCA values.
        
        Args:
            rca_df: DataFrame with RCA values
            threshold: RCA threshold for considering a product as having comparative advantage
            **kwargs: Additional parameters specific to the implementation
            
        Returns:
            DataFrame with product proximity matrix
        """
        pass
    
    @classmethod
    def get_advantage_type(cls, rca_value: float) -> str:
        """
        Determine advantage type based on RCA value.
        
        Args:
            rca_value: RCA value
            
        Returns:
            String indicating advantage type ('advantaged' or 'disadvantaged')
        """
        return 'advantaged' if rca_value >= 1.0 else 'disadvantaged'
    
    @classmethod
    def get_detailed_advantage_type(cls, rca_value: float) -> str:
        """
        Get a more detailed advantage type based on RCA value ranges.
        
        Args:
            rca_value: RCA value
            
        Returns:
            String indicating detailed advantage type
        """
        if rca_value >= 4.0:
            return 'Strong Advantage'
        elif rca_value >= 2.0:
            return 'Moderate Advantage'
        elif rca_value >= 1.0:
            return 'Slight Advantage'
        elif rca_value >= 0.5:
            return 'Slight Disadvantage'
        elif rca_value >= 0.25:
            return 'Moderate Disadvantage'
        else:
            return 'Strong Disadvantage'

class RCACalculatorFactory:
    """Factory for creating RCA calculator instances."""
    
    _calculator_registry: Dict[str, Type[RCACalculator]] = {}
    _performance_profiler = None
    _selector = None
    
    @classmethod
    def register_calculator(cls, name: str, calculator_class: Type[RCACalculator]) -> None:
        """Register a calculator implementation."""
        cls._calculator_registry[name] = calculator_class
        logger.info(f"Registered calculator: {name}")
    
    @protect("create_calculator", OperationType.COMPUTATION)
    @classmethod
    def create_calculator(cls, calculator_type: str = 'standard', **kwargs) -> RCACalculator:
        """Create a calculator instance."""
        if calculator_type not in cls._calculator_registry:
            logger.error(f"Unknown calculator type: {calculator_type}")
            raise ValueError(f"Unknown calculator type: {calculator_type}")
        
        calculator_class = cls._calculator_registry[calculator_type]
        return calculator_class(**kwargs)
    
    @protect("create_calculator_with_config", OperationType.COMPUTATION)
    @classmethod
    def create_calculator_with_config(cls, 
                                    config: Optional['RCACalculatorConfig'] = None,
                                    df: Optional[pd.DataFrame] = None,
                                    **kwargs) -> RCACalculator:
        """Create calculator with configuration."""
        if cls._selector is None:
            from yemen_trade_diagnostic.models.rca.config import RCACalculatorSelector
            cls._selector = RCACalculatorSelector()
        
        # Create or update configuration
        if config is None:
            if df is not None:
                from yemen_trade_diagnostic.models.rca.config import RCACalculatorConfig
                config = RCACalculatorConfig.from_data(df, **kwargs)
            else:
                from yemen_trade_diagnostic.models.rca.config import RCACalculatorConfig
                config = RCACalculatorConfig(**kwargs)
        
        # Select calculator type
        calculator_type = cls._selector.select_calculator(config, df)
        
        # Create calculator
        return cls.create_calculator(calculator_type, **kwargs)
    
    @protect("create_optimal_calculator", OperationType.COMPUTATION)
    @classmethod
    def create_optimal_calculator(cls, **kwargs) -> RCACalculator:
        """Create optimal calculator based on system characteristics."""
        return cls.create_calculator_with_config(**kwargs)
    
    @classmethod
    def benchmark_calculators(cls, test_data: Optional[pd.DataFrame] = None) -> Dict:
        """Benchmark all registered calculators."""
        from yemen_trade_diagnostic.models.rca.benchmark import RCABenchmark
        benchmark = RCABenchmark()
        
        if test_data is None:
            from yemen_trade_diagnostic.models.rca.config import DatasetSize
            test_data = benchmark.generate_test_data(DatasetSize.MEDIUM)
        
        return benchmark.benchmark_all_calculators(test_data)
    
    @classmethod
    def get_performance_summary(cls) -> Dict:
        """Get performance summary from profiler."""
        if cls._performance_profiler is None:
            return {}
        return cls._performance_profiler.get_performance_summary()
    
    @classmethod
    def _wrap_with_profiler(cls, calculator: RCACalculator) -> RCACalculator:
        """Wrap calculator with performance profiler."""
        if cls._performance_profiler is None:
            from yemen_trade_diagnostic.models.rca.profiler import PerformanceProfiledCalculator
            cls._performance_profiler = PerformanceProfiledCalculator(calculator)
        return cls._performance_profiler

# Convenience functions for backward compatibility
def get_advantage_type(rca_value: float) -> str:
    """
    Determine advantage type based on RCA value.
    
    Args:
        rca_value: RCA value
        
    Returns:
        String indicating advantage type ('advantaged' or 'disadvantaged')
    """
    return RCACalculator.get_advantage_type(rca_value)

def get_detailed_advantage_type(rca_value: float) -> str:
    """
    Get a more detailed advantage type based on RCA value ranges.
    
    Args:
        rca_value: RCA value
        
    Returns:
        String indicating detailed advantage type
    """
    return RCACalculator.get_detailed_advantage_type(rca_value)
