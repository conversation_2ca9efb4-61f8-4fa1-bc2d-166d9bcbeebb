"""
RCA Aggregates Utilities

This module provides utility functions for working with RCA aggregates.
"""
# Standard library imports
import json
from pathlib import Path
from typing import Any, Dict, Optional, Union

# Third-party imports
import pandas as pd

# Project imports
from yemen_trade_diagnostic.interfaces.cache_interface import StorageTier, memoize
from yemen_trade_diagnostic.interfaces.logging_interface import Log<PERSON><PERSON><PERSON>, get_logger, log_execution_time
from yemen_trade_diagnostic.errors import protect, OperationType

# Configure logger
logger = get_logger(__name__)

@memoize(ttl=3600, level=StorageTier.MEMORY)
@log_execution_time(logger=logger)
@protect("load_rca_aggregates", OperationType.DATA_LOADING)
def load_rca_aggregates(
    aggregates_path: Union[str, Path],
    year: int
) -> Dict[str, Any]:
    """
    Load pre-computed RCA aggregates from file.
    
    Args:
        aggregates_path: Path to the aggregates directory
        year: Year for which to load aggregates
        
    Returns:
        Dictionary containing aggregates data
    """
    aggregates_path = Path(aggregates_path)
    aggregates_file = aggregates_path / f"rca_aggregates_{year}.json"
    
    if not aggregates_file.exists():
        logger.warning(f"Aggregates file not found: {aggregates_file}")
        raise FileNotFoundError(f"Aggregates file not found: {aggregates_file}")
    
    logger.info(f"Loading RCA aggregates from {aggregates_file}")
    
    with open(aggregates_file, 'r') as f:
        aggregates = json.load(f)
    
    return aggregates

@log_execution_time(logger=logger)
@protect("prepare_rca_aggregates", OperationType.COMPUTATION)
def prepare_rca_aggregates(
    exports_df: pd.DataFrame,
    country_col: str = 'exporter_iso',
    product_col: str = 'product_code',
    value_col: str = 'trade_value_usd'
) -> Dict[str, Any]:
    """
    Prepare RCA aggregates from exports data.
    
    Args:
        exports_df: DataFrame with export data
        country_col: Column name for country identifiers
        product_col: Column name for product identifiers
        value_col: Column name for trade values
        
    Returns:
        Dictionary containing aggregates data
    """
    logger.info(f"Preparing RCA aggregates from data with shape {exports_df.shape}")
    
    # Calculate country totals
    country_totals = exports_df.groupby(country_col)[value_col].sum().to_dict()
    
    # Calculate product totals
    product_totals = exports_df.groupby(product_col)[value_col].sum().to_dict()
    
    # Calculate global total
    global_total = exports_df[value_col].sum()
    
    aggregates = {
        'country_totals': country_totals,
        'product_totals': product_totals,
        'global_total': float(global_total),
        'num_countries': len(country_totals),
        'num_products': len(product_totals),
        'total_records': len(exports_df)
    }
    
    logger.info(f"Prepared aggregates: {aggregates['num_countries']} countries, "
                f"{aggregates['num_products']} products, total: ${aggregates['global_total']:,.0f}")
    
    return aggregates

@log_execution_time(logger=logger)
@protect("save_rca_aggregates", OperationType.DATA_LOADING)
def save_rca_aggregates(
    aggregates: Dict[str, Any],
    aggregates_path: Union[str, Path],
    year: int
) -> None:
    """
    Save RCA aggregates to file.
    
    Args:
        aggregates: Dictionary containing aggregates data
        aggregates_path: Path to the aggregates directory
        year: Year for which to save aggregates
    """
    aggregates_path = Path(aggregates_path)
    aggregates_path.mkdir(parents=True, exist_ok=True)
    
    aggregates_file = aggregates_path / f"rca_aggregates_{year}.json"
    
    logger.info(f"Saving RCA aggregates to {aggregates_file}")
    
    with open(aggregates_file, 'w') as f:
        json.dump(aggregates, f, indent=2)
    
    logger.info(f"Aggregates saved successfully to {aggregates_file}")
