"""
Matrix Operations for RCA Calculations

This module provides hardware-accelerated matrix operations for RCA calculations.
"""
# Standard library imports
from typing import Optional

# Third-party imports
import numpy as np

# Project imports
from yemen_trade_diagnostic.interfaces.hardware_interface import AccelerationType, get_hardware_manager
from yemen_trade_diagnostic.interfaces.logging_interface import get_logger
from yemen_trade_diagnostic.errors import protect, OperationType

# Configure logger
logger = get_logger(__name__)

def get_optimal_hardware_type() -> AccelerationType:
    """
    Get the optimal hardware acceleration type for the current system.
    
    Returns:
        AccelerationType enum value for optimal acceleration
    """
    hw_manager = get_hardware_manager()
    
    if hw_manager.is_hardware_acceleration_available():
        available_types = hw_manager.get_available_acceleration_types()
        
        # Prefer GPU over other types
        if AccelerationType.GPU in available_types:
            return AccelerationType.GPU
        elif AccelerationType.NEURAL_ENGINE in available_types:
            return AccelerationType.NEURAL_ENGINE
        elif AccelerationType.CPU_VECTORIZED in available_types:
            return AccelerationType.CPU_VECTORIZED
        else:
            return available_types[0] if available_types else AccelerationType.CPU
    
    return AccelerationType.CPU

@protect("accelerated_rca_matrix_calculation", OperationType.COMPUTATION)
def accelerated_rca_matrix_calculation(
    export_matrix: np.ndarray,
    acceleration_type: AccelerationType = AccelerationType.CPU
) -> np.ndarray:
    """
    Calculate RCA matrix using hardware acceleration.
    
    Args:
        export_matrix: Matrix of export values (countries x products)
        acceleration_type: Type of acceleration to use
        
    Returns:
        RCA matrix with same dimensions as input
    """
    logger.info(f"Calculating RCA matrix with {acceleration_type} acceleration")
    
    hw_manager = get_hardware_manager()
    
    try:
        if acceleration_type != AccelerationType.CPU and hw_manager.is_hardware_acceleration_available():
            # Use hardware acceleration
            result = hw_manager.accelerate_matrix_operation(
                operation="rca_calculation",
                matrices=[export_matrix],
                acceleration_type=acceleration_type
            )
            return result
        else:
            # Fallback to CPU calculation
            return _cpu_rca_matrix_calculation(export_matrix)
    except Exception as e:
        logger.warning(f"Hardware acceleration failed: {e}, falling back to CPU")
        return _cpu_rca_matrix_calculation(export_matrix)

def _cpu_rca_matrix_calculation(export_matrix: np.ndarray) -> np.ndarray:
    """
    Calculate RCA matrix using CPU operations.
    
    Args:
        export_matrix: Matrix of export values (countries x products)
        
    Returns:
        RCA matrix with same dimensions as input
    """
    # Calculate country totals (sum across products)
    country_totals = np.sum(export_matrix, axis=1, keepdims=True)
    
    # Calculate product totals (sum across countries)
    product_totals = np.sum(export_matrix, axis=0, keepdims=True)
    
    # Calculate global total
    global_total = np.sum(product_totals)
    
    # Calculate country shares
    country_shares = np.divide(
        export_matrix,
        country_totals,
        out=np.zeros_like(export_matrix, dtype=float),
        where=country_totals!=0
    )
    
    # Calculate world shares
    world_shares = np.divide(
        product_totals,
        global_total,
        out=np.zeros_like(product_totals, dtype=float),
        where=global_total!=0
    )
    
    # Calculate RCA
    rca_matrix = np.divide(
        country_shares,
        world_shares,
        out=np.zeros_like(export_matrix, dtype=float),
        where=world_shares!=0
    )
    
    # Handle NaN and Inf values
    rca_matrix = np.nan_to_num(rca_matrix, nan=0.0, posinf=0.0, neginf=0.0)
    
    return rca_matrix

@protect("accelerated_proximity_matrix_calculation", OperationType.COMPUTATION)
def accelerated_proximity_matrix_calculation(
    mcp_matrix: np.ndarray,
    acceleration_type: AccelerationType = AccelerationType.CPU
) -> np.ndarray:
    """
    Calculate proximity matrix using hardware acceleration.
    
    Args:
        mcp_matrix: Binary matrix indicating comparative advantage (countries x products)
        acceleration_type: Type of acceleration to use
        
    Returns:
        Proximity matrix (products x products)
    """
    logger.info(f"Calculating proximity matrix with {acceleration_type} acceleration")
    
    hw_manager = get_hardware_manager()
    
    try:
        if acceleration_type != AccelerationType.CPU and hw_manager.is_hardware_acceleration_available():
            # Use hardware acceleration
            result = hw_manager.accelerate_matrix_operation(
                operation="proximity_calculation",
                matrices=[mcp_matrix],
                acceleration_type=acceleration_type
            )
            return result
        else:
            # Fallback to CPU calculation
            return _cpu_proximity_matrix_calculation(mcp_matrix)
    except Exception as e:
        logger.warning(f"Hardware acceleration failed: {e}, falling back to CPU")
        return _cpu_proximity_matrix_calculation(mcp_matrix)

def _cpu_proximity_matrix_calculation(mcp_matrix: np.ndarray) -> np.ndarray:
    """
    Calculate proximity matrix using CPU operations.
    
    Args:
        mcp_matrix: Binary matrix indicating comparative advantage (countries x products)
        
    Returns:
        Proximity matrix (products x products)
    """
    # Calculate product ubiquity (number of countries with advantage for each product)
    product_ubiquity = np.sum(mcp_matrix, axis=0)
    
    # Calculate co-occurrence matrix (products x products)
    co_occurrence = np.dot(mcp_matrix.T, mcp_matrix)
    
    # Calculate proximity matrix
    # proximity[i,j] = co_occurrence[i,j] / min(ubiquity[i], ubiquity[j])
    denominator = np.minimum(
        np.outer(product_ubiquity, np.ones_like(product_ubiquity)),
        np.outer(np.ones_like(product_ubiquity), product_ubiquity)
    )
    
    proximity_matrix = np.divide(
        co_occurrence,
        denominator,
        out=np.zeros_like(co_occurrence, dtype=float),
        where=denominator!=0
    )
    
    # Handle NaN and Inf values
    proximity_matrix = np.nan_to_num(proximity_matrix, nan=0.0, posinf=0.0, neginf=0.0)
    
    return proximity_matrix
