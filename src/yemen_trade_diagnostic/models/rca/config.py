"""
RCA Calculator Configuration System

This module provides configuration management for RCA calculator selection
and performance optimization.
"""
# Standard library imports
from dataclasses import dataclass
from enum import Enum
from typing import Dict, Optional, Union

# Third-party imports
import pandas as pd

# Project imports
from yemen_trade_diagnostic.interfaces.hardware_interface import get_hardware_manager
from yemen_trade_diagnostic.interfaces.logging_interface import get_logger
from yemen_trade_diagnostic.errors import protect, OperationType

# Configure logger
logger = get_logger(__name__)

class DatasetSize(Enum):
    """Dataset size categories for optimization decisions."""
    SMALL = "small"      # < 10K rows
    MEDIUM = "medium"    # 10K - 1M rows
    LARGE = "large"      # > 1M rows
    
    @classmethod
    def from_row_count(cls, row_count: int) -> 'DatasetSize':
        """Determine dataset size from row count."""
        if row_count < 10_000:
            return cls.SMALL
        elif row_count < 1_000_000:
            return cls.MEDIUM
        else:
            return cls.LARGE

class CalculatorType(Enum):
    """Available RCA calculator types."""
    STANDARD = "standard"
    OPTIMIZED = "optimized"
    EXACT = "exact"
    MATRIX = "matrix"
    AUTO = "auto"

@dataclass
class RCACalculatorConfig:
    """
    Configuration for RCA calculator selection and optimization.
    
    Attributes:
        precision_required: Whether high precision is required
        hardware_acceleration: Whether to use hardware acceleration
        memory_constrained: Whether system is memory constrained
        dataset_size: Expected dataset size (small, medium, large)
        real_time: Whether real-time performance is required
        aggregates_available: Whether pre-computed aggregates are available
        batch_processing: Whether processing multiple calculations
        fallback_enabled: Whether to fallback to simpler calculator on failure
    """
    precision_required: bool = False
    hardware_acceleration: bool = True
    memory_constrained: bool = False
    dataset_size: Union[DatasetSize, str] = DatasetSize.MEDIUM
    real_time: bool = False
    aggregates_available: bool = True
    batch_processing: bool = False
    fallback_enabled: bool = True
    
    def __post_init__(self):
        """Convert string dataset_size to enum if needed."""
        if isinstance(self.dataset_size, str):
            self.dataset_size = DatasetSize(self.dataset_size)
    
    @classmethod
    def from_data(cls, df: pd.DataFrame, **kwargs) -> 'RCACalculatorConfig':
        """
        Create configuration from data characteristics.
        
        Args:
            df: DataFrame to analyze
            **kwargs: Additional configuration parameters
            
        Returns:
            RCACalculatorConfig instance
        """
        row_count = len(df)
        dataset_size = DatasetSize.from_row_count(row_count)
        
        # Check hardware availability
        hw_manager = get_hardware_manager()
        hardware_available = hw_manager.is_hardware_acceleration_available()
        
        # Check memory constraints
        memory_usage_mb = df.memory_usage(deep=True).sum() / 1024 / 1024
        memory_constrained = memory_usage_mb > 1000  # > 1GB indicates potential constraint
        
        return cls(
            dataset_size=dataset_size,
            hardware_acceleration=kwargs.get('hardware_acceleration', hardware_available),
            memory_constrained=kwargs.get('memory_constrained', memory_constrained),
            **{k: v for k, v in kwargs.items() 
               if k not in ['dataset_size', 'hardware_acceleration', 'memory_constrained']}
        )
    
    def get_recommended_calculator(self) -> str:
        """
        Get recommended calculator type based on configuration.
        
        Returns:
            Recommended calculator type
        """
        # Priority 1: Precision requirements
        if self.precision_required:
            logger.info("Selecting exact calculator for precision requirements")
            return CalculatorType.EXACT.value
        
        # Priority 2: Real-time with hardware acceleration
        if self.real_time and self.hardware_acceleration:
            hw_manager = get_hardware_manager()
            if hw_manager.is_hardware_acceleration_available():
                logger.info("Selecting matrix calculator for real-time hardware acceleration")
                return CalculatorType.MATRIX.value
        
        # Priority 3: Memory constraints or large datasets
        if self.memory_constrained or self.dataset_size == DatasetSize.LARGE:
            if self.aggregates_available:
                logger.info("Selecting optimized calculator for memory efficiency")
                return CalculatorType.OPTIMIZED.value
        
        # Priority 4: Batch processing with available hardware
        if self.batch_processing and self.hardware_acceleration:
            hw_manager = get_hardware_manager()
            if hw_manager.is_hardware_acceleration_available():
                logger.info("Selecting matrix calculator for batch hardware acceleration")
                return CalculatorType.MATRIX.value
        
        # Default: Standard calculator
        logger.info("Selecting standard calculator as default")
        return CalculatorType.STANDARD.value
    
    def to_dict(self) -> Dict:
        """Convert configuration to dictionary."""
        return {
            'precision_required': self.precision_required,
            'hardware_acceleration': self.hardware_acceleration,
            'memory_constrained': self.memory_constrained,
            'dataset_size': self.dataset_size.value,
            'real_time': self.real_time,
            'aggregates_available': self.aggregates_available,
            'batch_processing': self.batch_processing,
            'fallback_enabled': self.fallback_enabled,
            'recommended_calculator': self.get_recommended_calculator()
        }

class RCACalculatorSelector:
    """
    Advanced selector for RCA calculator implementations.
    
    This class provides intelligent selection of RCA calculators based on
    multiple factors including data characteristics, system resources, and
    performance requirements.
    """
    
    def __init__(self):
        """Initialize the calculator selector."""
        self.hw_manager = get_hardware_manager()
        self._performance_history: Dict[str, Dict] = {}
    
    @protect("select_calculator", OperationType.COMPUTATION)
    def select_calculator(self, 
                         config: Optional[RCACalculatorConfig] = None,
                         df: Optional[pd.DataFrame] = None,
                         **kwargs) -> str:
        """
        Select the optimal calculator based on configuration and data.
        
        Args:
            config: Optional configuration object
            df: Optional DataFrame to analyze
            **kwargs: Additional parameters for configuration
            
        Returns:
            Selected calculator type
        """
        # Create configuration if not provided
        if config is None:
            if df is not None:
                config = RCACalculatorConfig.from_data(df, **kwargs)
            else:
                config = RCACalculatorConfig(**kwargs)
        
        # Get recommendation
        calculator_type = config.get_recommended_calculator()
        
        # Log selection
        logger.info(f"Selected {calculator_type} calculator based on configuration: {config.to_dict()}")
        
        return calculator_type
    
    def update_performance_history(self, calculator_type: str, metrics: Dict):
        """
        Update performance history for adaptive selection.
        
        Args:
            calculator_type: Type of calculator
            metrics: Performance metrics (execution_time, memory_usage, accuracy)
        """
        if calculator_type not in self._performance_history:
            self._performance_history[calculator_type] = {
                'execution_times': [],
                'memory_usage': [],
                'accuracy': []
            }
        
        history = self._performance_history[calculator_type]
        if 'execution_time' in metrics:
            history['execution_times'].append(metrics['execution_time'])
        if 'memory_usage' in metrics:
            history['memory_usage'].append(metrics['memory_usage'])
        if 'accuracy' in metrics:
            history['accuracy'].append(metrics['accuracy'])
        
        # Keep only recent history (last 100 entries)
        for key in history:
            if len(history[key]) > 100:
                history[key] = history[key][-100:]
    
    def get_performance_summary(self) -> Dict[str, Dict]:
        """
        Get performance summary for all calculators.
        
        Returns:
            Dictionary with performance statistics per calculator
        """
        summary = {}
        for calc_type, history in self._performance_history.items():
            summary[calc_type] = {}
            
            if history['execution_times']:
                summary[calc_type]['avg_execution_time'] = sum(history['execution_times']) / len(history['execution_times'])
                summary[calc_type]['min_execution_time'] = min(history['execution_times'])
                summary[calc_type]['max_execution_time'] = max(history['execution_times'])
            
            if history['memory_usage']:
                summary[calc_type]['avg_memory_usage'] = sum(history['memory_usage']) / len(history['memory_usage'])
                summary[calc_type]['max_memory_usage'] = max(history['memory_usage'])
            
            if history['accuracy']:
                summary[calc_type]['avg_accuracy'] = sum(history['accuracy']) / len(history['accuracy'])
        
        return summary