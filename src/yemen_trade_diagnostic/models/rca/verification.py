"""
RCA Verification Tools

This module provides utilities for verifying RCA calculations against expected values.
"""
# Standard library imports
import time
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union

# Third-party imports
import pandas as pd

# Project imports
from yemen_trade_diagnostic.interfaces.logging_interface import (
    LogLevel,
    get_logger,
    log_execution_time,
)
from yemen_trade_diagnostic.interfaces.validation_interface import (
    ValidationIssueLevel,
    ValidationResult,
    get_validation_manager,
)
from yemen_trade_diagnostic.models.rca.interface import RCACalculator, RCACalculatorFactory
from yemen_trade_diagnostic.models.rca.utils.aggregates import (
    prepare_rca_aggregates,
    save_rca_aggregates,
)
from yemen_trade_diagnostic.errors import protect, OperationType, MODEL_CONFIG

# Configure logger
logger = get_logger(__name__)

class RCAVerifier:
    """Utility for verifying RCA calculations against expected values."""

    def __init__(self,
                reference_data_path: Optional[Path] = None,
                tolerance: float = 0.01):
        """
        Initialize the RCA verifier.

        Args:
            reference_data_path: Path to the reference data file
            tolerance: Maximum allowed difference between calculated and expected values
        """
        self.logger = get_logger(f"{__name__}.{self.__class__.__name__}")
        self.reference_data_path = reference_data_path
        self.tolerance = tolerance
        self.reference_data = self._load_reference_data()
        self.logger.info("Initialized RCAVerifier")

    def _load_reference_data(self) -> Dict[str, Any]:
        """
        Load reference data from file if provided, otherwise use defaults.

        Returns:
            Dictionary with reference data
        """
        if self.reference_data_path and Path(self.reference_data_path).exists():
            try:
                import json
                with open(self.reference_data_path, 'r') as f:
                    data = json.load(f)
                self.logger.info(f"Loaded reference data from {self.reference_data_path}")
                return data
            except Exception as e:
                self.logger.warning(f"Failed to load reference data: {e}")

        self.logger.info("Using default reference data")
        return self._get_default_reference_data()

    def _get_default_reference_data(self) -> Dict[str, Any]:
        """
        Get default reference data.

        Returns:
            Dictionary with default reference data
        """
        return {
            "expected_rca": {
                "887_010101": 2.500,
                "887_010102": 0.900,
                "887_020101": 1.750,
                "840_010101": 1.350,
                "840_010102": 1.200,
                "276_020101": 1.286
            }
        }

    @protect("create_sample_data", OperationType.COMPUTATION)
    @log_execution_time(logger=logger)
    def create_sample_data(self) -> Tuple[pd.DataFrame, Dict[str, Any], Dict[Tuple[str, str], float], Path, Path]:
        """
        Create sample data for verification.
        
        Returns:
            Tuple of (exports_df, aggregates, expected_rca, temp_dir, aggregates_dir)
        """
        import tempfile
        
        # Create temporary directory
        temp_dir = Path(tempfile.mkdtemp())
        aggregates_dir = temp_dir / "aggregates"
        aggregates_dir.mkdir(exist_ok=True)
        
        # Create sample export data
        exports_data = [
            {'exporter_iso': '887', 'product_code': '010101', 'trade_value_usd': 1000000, 'year': 2023},
            {'exporter_iso': '887', 'product_code': '010102', 'trade_value_usd': 500000, 'year': 2023},
            {'exporter_iso': '887', 'product_code': '020101', 'trade_value_usd': 750000, 'year': 2023},
            {'exporter_iso': '840', 'product_code': '010101', 'trade_value_usd': 2000000, 'year': 2023},
            {'exporter_iso': '840', 'product_code': '010102', 'trade_value_usd': 1500000, 'year': 2023},
            {'exporter_iso': '276', 'product_code': '020101', 'trade_value_usd': 800000, 'year': 2023},
        ]
        
        exports_df = pd.DataFrame(exports_data)
        
        # Prepare aggregates
        aggregates = prepare_rca_aggregates(
            exports_df=exports_df,
            country_col='exporter_iso',
            product_col='product_code',
            value_col='trade_value_usd'
        )
        
        # Save aggregates
        save_rca_aggregates(
            aggregates=aggregates,
            output_path=aggregates_dir,
            year=2023
        )
        
        # Expected RCA values
        expected_rca = {
            ('887', '010101'): 2.500,
            ('887', '010102'): 0.900,
            ('887', '020101'): 1.750,
            ('840', '010101'): 1.350,
            ('840', '010102'): 1.200,
            ('276', '020101'): 1.286
        }

        self.logger.info(f"Created sample data with {len(exports_df)} export records")

        return exports_df, aggregates, expected_rca, temp_dir, aggregates_dir

    @log_execution_time(logger=logger)
    def verify_rca_calculation(self,
                              calculator: RCACalculator,
                              test_data: pd.DataFrame,
                              expected_rca: Optional[Dict[Tuple[str, str], float]] = None,
                              country_col: str = 'exporter_iso',
                              product_col: str = 'product_code',
                              rca_col: str = 'rca') -> Dict[str, Any]:
        """
        Verify that the calculator produces expected RCA values.

        Args:
            calculator: RCA calculator to verify
            test_data: Test data to use for verification
            expected_rca: Dictionary of expected RCA values (optional)
            country_col: Column name for country
            product_col: Column name for product
            rca_col: Column name for RCA value

        Returns:
            Dictionary with verification results
        """
        self.logger.info(f"Verifying RCA calculation using {calculator.__class__.__name__}")

        # Use default expected RCA if not provided
        if expected_rca is None:
            expected_rca = {
                tuple(key.split('_')): value
                for key, value in self.reference_data.get('expected_rca', {}).items()
            }

        # Calculate RCA
        start_time = time.time()
        try:
            calculated_rca = calculator.calculate_rca(
                exports_df=test_data,
                country_col=country_col,
                product_col=product_col,
                value_col='trade_value_usd',
                year=2023  # Provide year parameter for optimized and exact calculators
            )
        except TypeError:
            # If the calculator doesn't accept year parameter, try without it
            calculated_rca = calculator.calculate_rca(
                exports_df=test_data,
                country_col=country_col,
                product_col=product_col,
                value_col='trade_value_usd'
            )
        elapsed_time = time.time() - start_time

        # Convert calculated RCA to a dictionary for easier comparison
        calculated_dict = {}
        for _, row in calculated_rca.iterrows():
            key = (row[country_col], row[product_col])
            calculated_dict[key] = row[rca_col]

        # Compare calculated vs expected
        self.logger.info("Comparing calculated vs expected RCA values:")
        self.logger.info(f"{'Country':<8} {'Product':<10} {'Calculated':>10} {'Expected':>10} {'Diff':>10} {'Status':>10}")
        self.logger.info("-" * 60)

        all_match = True
        max_diff = 0.0
        discrepancies = []
        comparison_results = []

        for key, expected in expected_rca.items():
            country, product = key
            calculated = calculated_dict.get(key, 0.0)
            diff = abs(calculated - expected)
            max_diff = max(max_diff, diff)

            status = "✓" if diff <= self.tolerance else "✗"
            if diff > self.tolerance:
                all_match = False
                discrepancies.append({
                    "country": country,
                    "product": product,
                    "calculated": calculated,
                    "expected": expected,
                    "difference": diff
                })

            self.logger.info(f"{country:<8} {product:<10} {calculated:>10.3f} {expected:>10.3f} {diff:>10.3f} {status:>10}")

            comparison_results.append({
                "country": country,
                "product": product,
                "calculated_rca": float(calculated),
                "expected_rca": expected,
                "difference": float(diff),
                "within_tolerance": diff <= self.tolerance,
                "advantage_type": calculated_rca[
                    (calculated_rca[country_col] == country) &
                    (calculated_rca[product_col] == product)
                ].iloc[0]['advantage_type'] if not calculated_rca[
                    (calculated_rca[country_col] == country) &
                    (calculated_rca[product_col] == product)
                ].empty else None
            })

        self.logger.info("-" * 60)
        self.logger.info(f"Maximum difference: {max_diff:.6f}")

        if all_match:
            self.logger.info("✅ VERIFICATION SUCCESSFUL: All RCA values match expected values within tolerance.")
        else:
            self.logger.info("❌ VERIFICATION FAILED: Some RCA values differ from expected values.")

        verification_results = {
            "success": all_match,
            "tolerance": self.tolerance,
            "max_difference": float(max_diff),
            "comparison_results": comparison_results,
            "discrepancies": discrepancies if not all_match else [],
            "calculator_type": calculator.__class__.__name__,
            "calculation_time_seconds": elapsed_time
        }

        return verification_results

    @log_execution_time(logger=logger)
    def verify_proximity_matrix(self,
                               calculator: RCACalculator,
                               rca_df: pd.DataFrame,
                               threshold: float = 1.0) -> Dict[str, Any]:
        """
        Verify proximity matrix calculation.

        Args:
            calculator: RCA calculator to verify
            rca_df: DataFrame with RCA values
            threshold: RCA threshold for comparative advantage

        Returns:
            Dictionary with verification results
        """
        self.logger.info(f"Verifying proximity matrix calculation using {calculator.__class__.__name__}")

        try:
            proximity_matrix = calculator.create_proximity_matrix(
                rca_df=rca_df,
                threshold=threshold
            )

            # Basic validation checks
            issues = []
            
            # Check if matrix is symmetric
            products = proximity_matrix['product1'].unique()
            for product1 in products:
                for product2 in products:
                    p12 = proximity_matrix[
                        (proximity_matrix['product1'] == product1) &
                        (proximity_matrix['product2'] == product2)
                    ]['proximity'].values
                    
                    p21 = proximity_matrix[
                        (proximity_matrix['product1'] == product2) &
                        (proximity_matrix['product2'] == product1)
                    ]['proximity'].values
                    
                    if len(p12) > 0 and len(p21) > 0:
                        if abs(p12[0] - p21[0]) > 0.001:
                            issues.append(f"Asymmetric proximity for {product1}-{product2}")

            # Check diagonal values (should be 1.0)
            for product in products:
                diagonal_val = proximity_matrix[
                    (proximity_matrix['product1'] == product) &
                    (proximity_matrix['product2'] == product)
                ]['proximity'].values
                
                if len(diagonal_val) > 0 and abs(diagonal_val[0] - 1.0) > 0.001:
                    issues.append(f"Diagonal value for {product} is not 1.0: {diagonal_val[0]}")

            success = len(issues) == 0
            
            self.logger.info(f"Proximity matrix verification: {'PASSED' if success else 'FAILED'}")
            if issues:
                for issue in issues:
                    self.logger.warning(issue)

            return {
                "success": success,
                "issues": issues,
                "matrix_size": len(proximity_matrix),
                "unique_products": len(products)
            }

        except Exception as e:
            self.logger.error(f"Error during proximity matrix verification: {e}")
            return {
                "success": False,
                "error": str(e)
            }
