"""
RCA Transition Model for Yemen Trade Diagnostic (V2)

Analyzes product movement between RCA categories over time using V2 interfaces.
"""
# Standard library imports
from dataclasses import dataclass, field
from enum import Enum
from typing import Any, Dict, List, Optional, Union

# Third-party imports
import pandas as pd
import numpy as np

# Project imports
from yemen_trade_diagnostic.interfaces.cache_interface import StorageTier, memoize
from yemen_trade_diagnostic.interfaces.hardware_interface import get_hardware_manager
from yemen_trade_diagnostic.errors import protect, OperationType, MODEL_CONFIG
from yemen_trade_diagnostic.interfaces.logging_interface import LogLevel, configure_logging, get_logger, log_execution_time
from yemen_trade_diagnostic.interfaces.validation_interface import ValidationIssueLevel, ValidationResult, get_validation_manager

logger = get_logger(__name__)

# --- Constants & Enums ---
YEMEN_COUNTRY_CODE_V2 = 887
DEFAULT_RCA_THRESHOLD_V2 = 1.0

class AdvantageCategoryV2(Enum):
    """RCA advantage categories for transition analysis."""
    STRONG_ADVANTAGE = "strong_advantage"
    MODERATE_ADVANTAGE = "moderate_advantage"
    SLIGHT_ADVANTAGE = "slight_advantage"
    SLIGHT_DISADVANTAGE = "slight_disadvantage"
    MODERATE_DISADVANTAGE = "moderate_disadvantage"
    STRONG_DISADVANTAGE = "strong_disadvantage"

@dataclass
class RcaAnalysisConfigV2:
    """Configuration for RCA transition analysis."""
    threshold: float = DEFAULT_RCA_THRESHOLD_V2
    years: List[int] = field(default_factory=list)
    country_code: int = YEMEN_COUNTRY_CODE_V2
    include_world_comparison: bool = True
    min_trade_value: float = 1000.0

# --- Validation Schemas ---
TRANSITION_DATA_SCHEMA = {
    'required_columns': ['year', 'product_code', 'rca_value', 'advantage_category'],
    'data_types': {
        'year': 'int64',
        'product_code': 'object',
        'rca_value': 'float64',
        'advantage_category': 'object'
    }
}

@protect("get_advantage_category_v2", OperationType.COMPUTATION)
def get_advantage_category_v2(rca_value: float) -> AdvantageCategoryV2:
    """
    Categorize RCA value into advantage category.
    
    Args:
        rca_value: RCA value to categorize
        
    Returns:
        AdvantageCategoryV2 enum value
    """
    if rca_value >= 4.0:
        return AdvantageCategoryV2.STRONG_ADVANTAGE
    elif rca_value >= 2.0:
        return AdvantageCategoryV2.MODERATE_ADVANTAGE
    elif rca_value >= 1.0:
        return AdvantageCategoryV2.SLIGHT_ADVANTAGE
    elif rca_value >= 0.5:
        return AdvantageCategoryV2.SLIGHT_DISADVANTAGE
    elif rca_value >= 0.25:
        return AdvantageCategoryV2.MODERATE_DISADVANTAGE
    else:
        return AdvantageCategoryV2.STRONG_DISADVANTAGE

@protect("prepare_transition_matrix_data_v2", OperationType.DATA_LOADING)
def prepare_transition_matrix_data_v2(
    rca_df: pd.DataFrame,
    config: RcaAnalysisConfigV2
) -> pd.DataFrame:
    """
    Prepare data for transition matrix analysis.
    
    Args:
        rca_df: DataFrame with RCA values over time
        config: Configuration for analysis
        
    Returns:
        DataFrame prepared for transition analysis
    """
    logger.info("Preparing transition matrix data")
    
    # Validate input data
    validator = get_validation_manager()
    validation_result = validator.validate_dataframe(
        rca_df, 
        TRANSITION_DATA_SCHEMA,
        "transition_data"
    )
    
    if not validation_result.is_valid:
        logger.error(f"Data validation failed: {validation_result.summary}")
        raise ValueError(f"Invalid transition data: {validation_result.summary}")
    
    # Filter data based on config
    filtered_df = rca_df.copy()
    
    if config.years:
        filtered_df = filtered_df[filtered_df['year'].isin(config.years)]
    
    # Add advantage categories
    filtered_df['advantage_category'] = filtered_df['rca_value'].apply(
        lambda x: get_advantage_category_v2(x).value
    )
    
    return filtered_df

class RCATransitionModelV2:
    """
    Model for analyzing RCA transitions over time.
    """
    
    def __init__(self, config: Optional[RcaAnalysisConfigV2] = None):
        """
        Initialize the transition model.
        
        Args:
            config: Configuration for analysis
        """
        self.config = config or RcaAnalysisConfigV2()
        self.hw_manager = get_hardware_manager()
        self.logger = get_logger(f"{__name__}.{self.__class__.__name__}")
    
    @protect("calculate_transitions", OperationType.COMPUTATION)
    @memoize(ttl=3600, level=StorageTier.MEMORY)
    def calculate_transitions(self, rca_df: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate transition matrix between advantage categories.
        
        Args:
            rca_df: DataFrame with RCA values over time
            
        Returns:
            DataFrame with transition probabilities
        """
        self.logger.info("Calculating RCA transitions")
        
        # Prepare data
        prepared_data = prepare_transition_matrix_data_v2(rca_df, self.config)
        
        # Create transition matrix
        transitions = []
        
        for product in prepared_data['product_code'].unique():
            product_data = prepared_data[
                prepared_data['product_code'] == product
            ].sort_values('year')
            
            if len(product_data) < 2:
                continue
            
            for i in range(len(product_data) - 1):
                current_row = product_data.iloc[i]
                next_row = product_data.iloc[i + 1]
                
                transitions.append({
                    'product_code': product,
                    'from_year': current_row['year'],
                    'to_year': next_row['year'],
                    'from_category': current_row['advantage_category'],
                    'to_category': next_row['advantage_category'],
                    'from_rca': current_row['rca_value'],
                    'to_rca': next_row['rca_value']
                })
        
        return pd.DataFrame(transitions)

@protect("calculate_rca_transitions_v2", OperationType.COMPUTATION)
def calculate_rca_transitions_v2(
    rca_df: pd.DataFrame,
    config: Optional[RcaAnalysisConfigV2] = None
) -> Dict[str, Any]:
    """
    Calculate comprehensive RCA transition analysis.
    
    Args:
        rca_df: DataFrame with RCA values over time
        config: Configuration for analysis
        
    Returns:
        Dictionary with transition analysis results
    """
    logger.info("Starting RCA transition analysis")
    
    if config is None:
        config = RcaAnalysisConfigV2()
    
    model = RCATransitionModelV2(config)
    transitions_df = model.calculate_transitions(rca_df)
    
    # Calculate summary statistics
    transition_counts = transitions_df.groupby(['from_category', 'to_category']).size().reset_index(name='count')
    
    # Calculate transition probabilities
    category_totals = transitions_df.groupby('from_category').size()
    transition_probs = []
    
    for _, row in transition_counts.iterrows():
        from_cat = row['from_category']
        prob = row['count'] / category_totals[from_cat] if category_totals[from_cat] > 0 else 0
        transition_probs.append({
            'from_category': from_cat,
            'to_category': row['to_category'],
            'count': row['count'],
            'probability': prob
        })
    
    results = {
        'transitions': transitions_df,
        'transition_counts': transition_counts,
        'transition_probabilities': pd.DataFrame(transition_probs),
        'config': config,
        'summary': {
            'total_transitions': len(transitions_df),
            'unique_products': transitions_df['product_code'].nunique(),
            'year_range': [transitions_df['from_year'].min(), transitions_df['to_year'].max()]
        }
    }
    
    logger.info(f"Completed transition analysis: {results['summary']}")
    return results