"""
Standard RCA Calculator

This module provides the standard implementation of the RCA calculator.
"""
# Standard library imports
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

# Third-party imports
import numpy as np
import pandas as pd

# Project imports
from yemen_trade_diagnostic.interfaces.cache_interface import StorageTier, memoize
from yemen_trade_diagnostic.errors import protect, OperationType, MODEL_CONFIG
from yemen_trade_diagnostic.interfaces.hardware_interface import (
    AccelerationType,
    get_hardware_manager,
)
from yemen_trade_diagnostic.interfaces.logging_interface import (
    LogLevel,
    get_logger,
    log_execution_time,
)
from yemen_trade_diagnostic.interfaces.validation_interface import (
    ValidationIssueLevel,
    ValidationResult,
    get_validation_manager,
)
from yemen_trade_diagnostic.models.rca.interface import RCACalculator, RCACalculatorFactory

# Configure logger
logger = get_logger(__name__)

class StandardRCACalculator(RCACalculator):
    """
    Standard RCA implementation with basic optimizations.
    
    This implementation provides:
    - Full compatibility with all data formats
    - Detailed validation and error handling
    - Hardware acceleration as available
    - Standard memory optimizations
    """
    
    def __init__(self, 
                raw_data_dir: Optional[Path] = None,
                processed_data_dir: Optional[Path] = None):
        """
        Initialize the standard RCA calculator.
        
        Args:
            raw_data_dir: Path to raw data directory
            processed_data_dir: Path to processed data directory
        """
        self.raw_data_dir = raw_data_dir
        self.processed_data_dir = processed_data_dir
        self.hw_manager = get_hardware_manager()
        self.logger = get_logger(f"{__name__}.{self.__class__.__name__}")
        
        self.logger.info("Initialized StandardRCACalculator")
    
    @protect("calculate_rca", OperationType.COMPUTATION)
    @memoize(ttl=3600, level=StorageTier.MEMORY)
    @log_execution_time(logger=logger)
    def calculate_rca(self, 
                     exports_df: pd.DataFrame,
                     world_exports_df: Optional[pd.DataFrame] = None,
                     country_col: str = 'country',
                     product_col: str = 'product',
                     year_col: str = 'year',
                     value_col: str = 'value',
                     **kwargs) -> pd.DataFrame:
        """
        Calculate RCA values for the given exports data.
        
        Args:
            exports_df: DataFrame with export data
            world_exports_df: DataFrame with world export data (optional)
            country_col: Column name for country
            product_col: Column name for product
            year_col: Column name for year
            value_col: Column name for trade value
            **kwargs: Additional parameters
            
        Returns:
            DataFrame with RCA values
        """
        self.logger.info(f"Calculating RCA for data with shape {exports_df.shape}")
        
        # Validate inputs
        if exports_df.empty:
            self.logger.warning("Empty exports DataFrame provided")
            return pd.DataFrame(columns=[country_col, product_col, 'rca', 'advantage_type'])
        
        # Check if required columns exist
        required_cols = [country_col, product_col, value_col]
        if year_col in exports_df.columns:
            required_cols.append(year_col)
        
        missing_cols = [col for col in required_cols if col not in exports_df.columns]
        if missing_cols:
            err_msg = f"Required columns missing from exports data: {missing_cols}"
            self.logger.error(err_msg)
            raise ValueError(err_msg)
        
        # Load world exports if not provided
        if world_exports_df is None:
            world_exports_df = exports_df.copy()
            self.logger.info("Using exports data as world exports data")
        
        # Calculate country totals
        country_totals = exports_df.groupby(country_col)[value_col].sum().reset_index()
        country_totals = dict(zip(country_totals[country_col], country_totals[value_col]))
        
        # Calculate product totals from world exports
        product_totals = world_exports_df.groupby(product_col)[value_col].sum().reset_index()
        product_totals = dict(zip(product_totals[product_col], product_totals[value_col]))
        
        # Calculate global total
        global_total = float(world_exports_df[value_col].sum())
        
        # Prepare result DataFrame
        result_df = exports_df[[country_col, product_col, value_col]].copy()
        
        # Try to use hardware acceleration for RCA calculation
        try:
            if self.hw_manager.is_hardware_acceleration_available():
                self.logger.info("Using hardware acceleration for RCA calculation")
                # Create arrays for vectorized calculation
                country_total_values = result_df[country_col].map(lambda c: float(country_totals.get(c, 0.0)))
                product_total_values = result_df[product_col].map(lambda p: float(product_totals.get(p, 0.0)))
                
                # Calculate numerator and denominator
                numerator = result_df[value_col] / country_total_values
                denominator = product_total_values / global_total
                
                # Use hardware acceleration for the division
                rca_values = self.hw_manager.accelerate_array_operation(
                    numerator / denominator
                )
                result_df['rca'] = rca_values
            else:
                self.logger.info("Hardware acceleration not available, using standard calculation")
                result_df['rca'] = (
                    (result_df[value_col] / 
                     result_df[country_col].map(lambda c: float(country_totals.get(c, 0.0)))) /
                    (result_df[product_col].map(lambda p: float(product_totals.get(p, 0.0))) / 
                     global_total)
                )
        except Exception as e:
            self.logger.warning(f"Error during accelerated calculation: {e}. Falling back to standard calculation.")
            # Calculate RCA directly
            result_df['rca'] = (
                (result_df[value_col] / 
                 result_df[country_col].map(lambda c: float(country_totals.get(c, 0.0)))) /
                (result_df[product_col].map(lambda p: float(product_totals.get(p, 0.0))) / 
                 global_total)
            )
        
        # Handle NaN and infinite values
        result_df['rca'] = result_df['rca'].fillna(0.0)
        result_df['rca'] = result_df['rca'].replace([np.inf, -np.inf], 0.0)
        
        # Add advantage type columns
        result_df['advantage_type'] = result_df['rca'].apply(self.get_advantage_type)
        result_df['detailed_advantage_type'] = result_df['rca'].apply(self.get_detailed_advantage_type)
        
        self.logger.info(f"RCA calculation completed with {len(result_df)} results")
        
        return result_df
    
    @protect("create_proximity_matrix", OperationType.COMPUTATION)
    @memoize(ttl=3600, level=StorageTier.MEMORY)
    @log_execution_time(logger=logger)
    def create_proximity_matrix(self, 
                               rca_df: pd.DataFrame,
                               threshold: float = 1.0,
                               country_col: str = 'country',
                               product_col: str = 'product',
                               rca_col: str = 'rca',
                               **kwargs) -> pd.DataFrame:
        """
        Create a product proximity matrix from RCA values.
        
        Args:
            rca_df: DataFrame with RCA values
            threshold: RCA threshold for considering a product as having comparative advantage
            country_col: Column name for country
            product_col: Column name for product
            rca_col: Column name for RCA value
            **kwargs: Additional parameters
            
        Returns:
            DataFrame with product proximity matrix
        """
        self.logger.info(f"Creating proximity matrix from RCA data with shape {rca_df.shape}")
        
        # Validate inputs
        if rca_df.empty:
            self.logger.warning("Empty RCA DataFrame provided")
            return pd.DataFrame(columns=['product1', 'product2', 'proximity'])
        
        # Check if required columns exist
        required_cols = [country_col, product_col, rca_col]
        missing_cols = [col for col in required_cols if col not in rca_df.columns]
        if missing_cols:
            err_msg = f"Required columns missing from RCA data: {missing_cols}"
            self.logger.error(err_msg)
            raise ValueError(err_msg)
        
        # Create binary RCA matrix (1 if RCA >= threshold, 0 otherwise)
        self.logger.info(f"Creating binary RCA matrix with threshold {threshold}")
        
        # Get unique products with RCA >= threshold
        products_with_advantage = rca_df[rca_df[rca_col] >= threshold][product_col].unique()
        
        # Create product-country matrix
        self.logger.info("Creating product-country matrix")
        product_country_matrix = pd.DataFrame(index=products_with_advantage)
        
        # For each product, add a column indicating which countries have RCA >= threshold
        for product in products_with_advantage:
            countries_with_advantage = rca_df[
                (rca_df[product_col] == product) & 
                (rca_df[rca_col] >= threshold)
            ][country_col].tolist()
            
            product_country_matrix.loc[product, 'countries'] = countries_with_advantage
        
        # Calculate proximity matrix
        self.logger.info("Calculating proximity matrix")
        proximity_list = []
        
        for i, product1 in enumerate(products_with_advantage):
            countries1 = set(product_country_matrix.loc[product1, 'countries'])
            
            for j, product2 in enumerate(products_with_advantage[i:], i):
                if product1 == product2:
                    proximity_list.append({
                        'product1': product1,
                        'product2': product2,
                        'proximity': 1.0
                    })
                    continue
                
                countries2 = set(product_country_matrix.loc[product2, 'countries'])
                intersection = countries1.intersection(countries2)
                min_countries = min(len(countries1), len(countries2))
                proximity = len(intersection) / min_countries if min_countries > 0 else 0.0
                
                proximity_list.append({
                    'product1': product1,
                    'product2': product2,
                    'proximity': proximity
                })
                
                # Add symmetric entry
                proximity_list.append({
                    'product1': product2,
                    'product2': product1,
                    'proximity': proximity
                })
        
        proximity_df = pd.DataFrame(proximity_list)
        
        self.logger.info(f"Proximity matrix calculation completed with {len(proximity_df)} entries")
        
        return proximity_df

# Register the calculator with the factory
RCACalculatorFactory.register_calculator('standard', StandardRCACalculator)
