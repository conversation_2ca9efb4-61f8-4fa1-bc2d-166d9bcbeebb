"""
Optimized RCA Calculator

This module provides a memory-optimized implementation of the RCA calculator.
"""
# Standard library imports
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

# Third-party imports
import numpy as np
import pandas as pd

# Project imports
from yemen_trade_diagnostic.interfaces.cache_interface import StorageTier, memoize
from yemen_trade_diagnostic.interfaces.hardware_interface import AccelerationType, get_hardware_manager
from yemen_trade_diagnostic.interfaces.logging_interface import LogLevel, get_logger, log_execution_time
from yemen_trade_diagnostic.interfaces.validation_interface import ValidationI<PERSON>ueLevel, ValidationResult, get_validation_manager
from yemen_trade_diagnostic.models.rca.interface import RCACalculator, RCACalculatorFactory
from yemen_trade_diagnostic.models.rca.utils.aggregates import load_rca_aggregates, prepare_rca_aggregates
from yemen_trade_diagnostic.errors import protect, OperationType, MODEL_CONFIG

# Configure logger
logger = get_logger(__name__)

class OptimizedRCACalculator(RCACalculator):
    """
    Memory-optimized RCA implementation using pre-computed aggregates.

    This implementation provides:
    - Significantly faster calculation for large datasets
    - Memory optimization through pre-computed aggregates
    - Detailed verification reports
    - Support for both RCA calculation and proximity matrix generation
    """

    def __init__(self,
                processed_data_dir: Optional[Union[str, Path]] = None,
                aggregates_path: Optional[Union[str, Path]] = None):
        """
        Initialize the optimized RCA calculator.

        Args:
            processed_data_dir: Path to processed data directory
            aggregates_path: Path to aggregates directory
        """
        self.processed_data_dir = Path(processed_data_dir) if processed_data_dir else None
        self.aggregates_path = Path(aggregates_path) if aggregates_path else None
        self.hw_manager = get_hardware_manager()
        self.logger = get_logger(f"{__name__}.{self.__class__.__name__}")

        # Set default aggregates path if not provided
        if self.aggregates_path is None and self.processed_data_dir:
            self.aggregates_path = self.processed_data_dir / "aggregates"

        self.logger.info("Initialized OptimizedRCACalculator")

    @protect("calculate_rca", OperationType.COMPUTATION)
    @memoize(ttl=3600, level=StorageTier.MEMORY)
    @log_execution_time(logger=logger)
    def calculate_rca(self,
                     exports_df: pd.DataFrame,
                     aggregates_path: Optional[Union[str, Path]] = None,
                     year: Optional[int] = None,
                     country_col: str = 'exporter_iso',
                     product_col: str = 'product_code',
                     value_col: str = 'trade_value_usd',
                     **kwargs) -> pd.DataFrame:
        """
        Calculate RCA using optimized approach with pre-computed aggregates.

        Args:
            exports_df: DataFrame with export data
            aggregates_path: Path to aggregates directory
            year: Year for which to load aggregates
            country_col: Column name for country
            product_col: Column name for product
            value_col: Column name for trade value
            **kwargs: Additional parameters

        Returns:
            DataFrame with RCA values
        """
        start_time = pd.Timestamp.now()
        self.logger.info(f"Calculating optimized RCA for data with shape {exports_df.shape}")

        # Validate inputs
        if exports_df.empty:
            self.logger.warning("Empty exports DataFrame provided")
            return pd.DataFrame(columns=[country_col, product_col, 'rca', 'advantage_type'])

        # Check if required columns exist
        required_cols = [country_col, product_col, value_col]
        missing_cols = [col for col in required_cols if col not in exports_df.columns]
        if missing_cols:
            err_msg = f"Required columns missing from exports data: {missing_cols}"
            self.logger.error(err_msg)
            raise ValueError(err_msg)

        # Determine aggregates path
        if aggregates_path is None:
            aggregates_path = self.aggregates_path

        # Load aggregates
        if aggregates_path and year:
            self.logger.info(f"Loading aggregates for year {year} from {aggregates_path}")
            aggregates = load_rca_aggregates(aggregates_path=aggregates_path, year=year)
        else:
            self.logger.info("No aggregates path or year provided, preparing from data")
            aggregates = prepare_rca_aggregates(
                exports_df=exports_df,
                country_col=country_col,
                product_col=product_col,
                value_col=value_col
            )

        # Extract aggregates
        country_totals = aggregates['country_totals']
        product_totals = aggregates['product_totals']
        global_total = float(aggregates['global_total'])

        # Create result DataFrame based on exports_df structure
        result_df = exports_df[[country_col, product_col, value_col]].copy()

        # Calculate RCA efficiently using vectorized operations where possible
        self.logger.info("Calculating RCA values using optimized approach")

        # Handle missing values by ensuring all keys are strings
        result_df[country_col] = result_df[country_col].astype(str)
        result_df[product_col] = result_df[product_col].astype(str)

        # Calculate RCA using optimized approach
        if global_total > 0:
            result_df['rca'] = (
                (result_df[value_col] / 
                 result_df[country_col].map(lambda c: float(country_totals.get(str(c), 0.0)))) /
                (result_df[product_col].map(lambda p: float(product_totals.get(str(p), 0.0))) / 
                 global_total)
            )
        else:
            result_df['rca'] = 0.0

        # Handle NaN and infinite values
        result_df['rca'] = result_df['rca'].fillna(0.0)
        result_df['rca'] = result_df['rca'].replace([np.inf, -np.inf], 0.0)

        # Add advantage type columns
        result_df['advantage_type'] = result_df['rca'].apply(self.get_advantage_type)
        result_df['detailed_advantage_type'] = result_df['rca'].apply(self.get_detailed_advantage_type)
        
        elapsed_time = (pd.Timestamp.now() - start_time).total_seconds()
        self.logger.info(f"Optimized RCA calculation completed in {elapsed_time:.2f} seconds")
        self.logger.info(f"Results shape: {result_df.shape}")
        
        return result_df
    
    @protect("create_proximity_matrix", OperationType.COMPUTATION)
    @memoize(ttl=3600, level=StorageTier.MEMORY)
    @log_execution_time(logger=logger)
    def create_proximity_matrix(self, 
                               rca_df: pd.DataFrame,
                               threshold: float = 1.0,
                               country_col: str = 'exporter_iso',
                               product_col: str = 'product_code',
                               rca_col: str = 'rca',
                               **kwargs) -> pd.DataFrame:
        """
        Create a product proximity matrix from RCA values.
        
        Args:
            rca_df: DataFrame with RCA values
            threshold: RCA threshold for considering a product as having comparative advantage
            country_col: Column name for country
            product_col: Column name for product
            rca_col: Column name for RCA value
            **kwargs: Additional parameters
            
        Returns:
            DataFrame with product proximity matrix
        """
        self.logger.info(f"Creating proximity matrix from RCA data with shape {rca_df.shape}")
        if rca_df.empty:
            self.logger.warning("Empty RCA DataFrame provided")
            return pd.DataFrame(columns=['product1', 'product2', 'proximity'])

        required_cols = [country_col, product_col, rca_col]
        missing_cols = [col for col in required_cols if col not in rca_df.columns]
        if missing_cols:
            err_msg = f"Required columns missing from RCA data: {missing_cols}"
            self.logger.error(err_msg)
            raise ValueError(err_msg)

        self.logger.info(f"Creating binary RCA matrix with threshold {threshold}")
        
        # Get products with advantage
        products_with_advantage = rca_df[rca_df[rca_col] >= threshold][product_col].unique()
        
        self.logger.info("Creating product-country matrix")
        product_country_matrix = pd.DataFrame(index=products_with_advantage)
        
        for product in products_with_advantage:
            countries_with_advantage = rca_df[
                (rca_df[product_col] == product) & 
                (rca_df[rca_col] >= threshold)
            ][country_col].tolist()
            
            product_country_matrix.loc[product, 'countries'] = countries_with_advantage

        self.logger.info("Calculating proximity matrix")
        proximity_list = []
        
        for i, product1 in enumerate(products_with_advantage):
            countries1 = set(product_country_matrix.loc[product1, 'countries'])
            
            for j, product2 in enumerate(products_with_advantage[i:], i):
                if product1 == product2:
                    proximity_list.append({
                        'product1': product1,
                        'product2': product2,
                        'proximity': 1.0
                    })
                    continue
                
                countries2 = set(product_country_matrix.loc[product2, 'countries'])
                intersection = countries1.intersection(countries2)
                min_countries = min(len(countries1), len(countries2))
                proximity = len(intersection) / min_countries if min_countries > 0 else 0.0
                
                proximity_list.append({
                    'product1': product1,
                    'product2': product2,
                    'proximity': proximity
                })
                proximity_list.append({
                    'product1': product2,
                    'product2': product1,
                    'proximity': proximity
                })
        
        proximity_df = pd.DataFrame(proximity_list)
        
        self.logger.info(f"Proximity matrix calculation completed with {len(proximity_df)} entries")
        
        return proximity_df

# Register the calculator with the factory
RCACalculatorFactory.register_calculator('optimized', OptimizedRCACalculator)
