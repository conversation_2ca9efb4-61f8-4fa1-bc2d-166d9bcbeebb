"""
Top Trade Model for Yemen Trade Diagnostic (V2)

This module provides V2 functions for analyzing top exports and imports by value and volume.
"""
# Standard library imports
import time
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union

# Third-party imports
import numpy as np
import pandas as pd

# Project imports
# V2 Unified data loader
from yemen_trade_diagnostic.data import DataSource, load_data # Keep for potential future use, but not directly used in V2 functions here
from yemen_trade_diagnostic.interfaces.cache_interface import StorageTier, memoize
from yemen_trade_diagnostic.interfaces.hardware_interface import AccelerationType, get_hardware_manager # V2 Interface Imports
# Schema definitions for validation
TRADE_DATA_SCHEMA = {
    'required_columns': ['year', 'product', 'partner', 'value', 'quantity'],
    'column_types': {
        'year': 'integer', 
        'product': 'string', 
        'partner': 'string', 
        'value': 'numeric',
        'quantity': 'numeric'
    }
}

PRODUCT_METADATA_SCHEMA = {
    'required_columns': ['product_code', 'product_name', 'sector'],
    'column_types': {
        'product_code': 'string',
        'product_name': 'string',
        'sector': 'string'
    }
}

COUNTRY_METADATA_SCHEMA = {
    'required_columns': ['country_code', 'country_name', 'region'],
    'column_types': {
        'country_code': 'string',
        'country_name': 'string',
        'region': 'string'
    }
}

logger = get_logger(__name__)

def _validate_trade_data(df: pd.DataFrame, schema: Dict = None) -> ValidationResult:
    """Validate trade data for top trade analysis."""
    schema = schema or TRADE_DATA_SCHEMA
    validation_manager = get_validation_manager()
    
    result = ValidationResult()
    if not isinstance(df, pd.DataFrame) or df.empty:
        result.add_issue("Trade data is empty or not a DataFrame", ValidationIssueLevel.ERROR)
        return result
    
    # Check required columns
    required_cols = schema.get('required_columns', [])
    missing_cols = [col for col in required_cols if col not in df.columns]
    if missing_cols:
        result.add_issue(f"Missing required columns: {missing_cols}", ValidationIssueLevel.ERROR)
        
    # Check column types if all required columns are present
    if not missing_cols:
        column_types = schema.get('column_types', {})
        for col, expected_type in column_types.items():
            if col in df.columns:
                # Check data type
                if expected_type == 'numeric' and not pd.api.types.is_numeric_dtype(df[col]):
                    result.add_issue(f"Column '{col}' should be numeric but is {df[col].dtype}", ValidationIssueLevel.WARNING)
                elif expected_type == 'string' and not (pd.api.types.is_object_dtype(df[col]) or pd.api.types.is_string_dtype(df[col])):
                    result.add_issue(f"Column '{col}' should be string but is {df[col].dtype}", ValidationIssueLevel.WARNING)
                elif expected_type == 'integer' and not pd.api.types.is_integer_dtype(df[col]):
                    result.add_issue(f"Column '{col}' should be integer but is {df[col].dtype}", ValidationIssueLevel.WARNING)
                    
    # Check for negative values in value and quantity columns
    if 'value' in df.columns and (df['value'] < 0).any():
        result.add_issue("Negative values found in 'value' column", ValidationIssueLevel.WARNING)
    if 'quantity' in df.columns and (df['quantity'] < 0).any():
        result.add_issue("Negative values found in 'quantity' column", ValidationIssueLevel.WARNING)
        
    return result

@memoize(ttl=3600, level=StorageTier.MEMORY)
@log_execution_time(logger=logger)
def _optimize_dataframe_for_trade_analysis(df: pd.DataFrame) -> pd.DataFrame:
    """Optimize a DataFrame for trade analysis using hardware acceleration if available."""
    if df.empty:
        return df
        
    # Use hardware acceleration if available
    hw_manager = get_hardware_manager()
    if hw_manager.is_hardware_acceleration_available():
        try:
            logger.debug("Using hardware acceleration for DataFrame optimization")
            optimized_df = hw_manager.accelerate_dataframe(df, optimize_for="memory")
            if optimized_df is not None:
                logger.debug("Hardware acceleration successful for DataFrame optimization")
                return optimized_df
            else:
                logger.warning("Hardware acceleration returned None for DataFrame optimization")
        except Exception as e:
            logger.warning(f"Hardware acceleration failed for DataFrame optimization: {e}")
                
    # Fallback to CPU optimization
    logger.debug("Using CPU optimization for DataFrame")
    
    # Copy to avoid modifying the original
    optimized_df = df.copy()
    
    # Optimize numeric columns
    for col in optimized_df.select_dtypes(include=['number']).columns:
        if optimized_df[col].min() >= 0:
            # For unsigned integers
            max_val = optimized_df[col].max()
            if max_val <= 255:
                optimized_df[col] = optimized_df[col].astype(np.uint8)
            elif max_val <= 65535:
                optimized_df[col] = optimized_df[col].astype(np.uint16)
            elif max_val <= 4294967295:
                optimized_df[col] = optimized_df[col].astype(np.uint32)
        else:
            # For signed integers
            max_val = abs(optimized_df[col].max())
            min_val = abs(optimized_df[col].min())
            max_abs = max(max_val, min_val)
            if max_abs <= 127:
                optimized_df[col] = optimized_df[col].astype(np.int8)
            elif max_abs <= 32767:
                optimized_df[col] = optimized_df[col].astype(np.int16)
            elif max_abs <= 2147483647:
                optimized_df[col] = optimized_df[col].astype(np.int32)
    
    # Optimize float columns to float32 if precision allows
    for col in optimized_df.select_dtypes(include=['float64']).columns:
        optimized_df[col] = optimized_df[col].astype(np.float32)
    
    # Optimize string columns using category for high-cardinality columns
    for col in optimized_df.select_dtypes(include=['object']).columns:
        if optimized_df[col].nunique() / len(optimized_df) < 0.5:  # If fewer than 50% unique values
            optimized_df[col] = optimized_df[col].astype('category')
    
    return optimized_df

@memoize(ttl=3600*24, level=StorageTier.DISK)
@log_execution_time(logger=logger)
def get_top_exports_by_value_v2(
    trade_data: pd.DataFrame,
    product_metadata: Optional[pd.DataFrame] = None,
    country_metadata: Optional[pd.DataFrame] = None,
    year: int = 2023,
    country_code: str = "887",  # Yemen by default
    top_n: int = 10,
    include_sectors: bool = True,
    year_col: str = "year",
    product_col: str = "product",
    partner_col: str = "partner",
    value_col: str = "value",
    direction_col: str = "direction"
) -> pd.DataFrame:
    """
    Get top exports by value for a specific country and year.
    
    Args:
        trade_data: DataFrame with trade data (must have year, product, partner, value columns)
        product_metadata: Optional DataFrame with product metadata (product_code, product_name, sector)
        country_metadata: Optional DataFrame with country metadata (country_code, country_name, region)
        year: Year to analyze
        country_code: Country code to analyze (default: "887" for Yemen)
        top_n: Number of top products to return
        include_sectors: Whether to include sector information
        year_col: Name of the year column in trade_data
        product_col: Name of the product column in trade_data
        partner_col: Name of the partner column in trade_data
        value_col: Name of the value column in trade_data
        direction_col: Name of the direction column in trade_data
        
    Returns:
        DataFrame with top exports by value
    """
    logger.info(f"Getting top {top_n} exports by value for {country_code} in {year}")
    
    # Validate inputs
    validation_result = _validate_trade_data(trade_data)
    if not validation_result.is_valid:
        for issue in validation_result.issues:
            logger.warning(f"Trade data validation issue: {issue}")
        if any(issue.level == ValidationIssueLevel.ERROR for issue in validation_result.issues):
            return pd.DataFrame()
    
    # Filter data for the specified year and country (as exporter)
    if direction_col in trade_data.columns:
        # If direction column exists, use it to filter exports
        filtered_data = trade_data[
            (trade_data[year_col] == year) & 
            (trade_data[direction_col] == "export") &
            (trade_data.get("country", trade_data.get("exporter", "")) == country_code)
        ]
    else:
        # Otherwise, assume exporter/importer columns
        exporter_col = "exporter" if "exporter" in trade_data.columns else "i"
        filtered_data = trade_data[
            (trade_data[year_col] == year) & 
            (trade_data[exporter_col] == country_code)
        ]
    
    if filtered_data.empty:
        logger.warning(f"No export data found for {country_code} in {year}")
        return pd.DataFrame()
    
    # Optimize the filtered DataFrame
    filtered_data = _optimize_dataframe_for_trade_analysis(filtered_data)
    
    # Group by product and aggregate values
    hw_manager = get_hardware_manager()
    if hw_manager.is_hardware_acceleration_available(AccelerationType.CPU):
        try:
            logger.debug("Using hardware acceleration for aggregation")
            result = hw_manager.accelerate_groupby(
                filtered_data, 
                by=product_col, 
                agg={value_col: 'sum'}
            ) # Added closing parenthesis
            if result is not None:
                product_totals = result.reset_index()
                logger.debug("Hardware acceleration successful for aggregation")
            else:
                logger.warning("Hardware acceleration returned None for aggregation")
                product_totals = filtered_data.groupby(product_col)[value_col].sum().reset_index()
        except Exception as e:
            logger.warning(f"Hardware acceleration failed for aggregation: {e}") # Corrected indentation
            product_totals = filtered_data.groupby(product_col)[value_col].sum().reset_index()
    else:
        product_totals = filtered_data.groupby(product_col)[value_col].sum().reset_index()
    # Sort by value in descending order and take top N
    product_totals = product_totals.sort_values(by=value_col, ascending=False).head(top_n)
    
    # Calculate percentage of total exports
    total_exports = product_totals[value_col].sum()
    product_totals['percent_of_total'] = (product_totals[value_col] / total_exports * 100) if total_exports > 0 else 0
    
    # Merge with product metadata if provided
    if product_metadata is not None and not product_metadata.empty:
        # Validate product metadata
        validation_result = _validate_trade_data(product_metadata, PRODUCT_METADATA_SCHEMA)
        if validation_result.is_valid or not any(issue.level == ValidationIssueLevel.ERROR for issue in validation_result.issues):
            # Align column names for merge
            product_code_col = "product_code" if "product_code" in product_metadata.columns else "code"
            product_name_col = "product_name" if "product_name" in product_metadata.columns else "name"
            
            # Merge with product metadata
            product_totals = pd.merge(
                product_totals,
                product_metadata[[product_code_col, product_name_col] + (["sector"] if "sector" in product_metadata.columns and include_sectors else [])],
                left_on=product_col,
                right_on=product_code_col,
                how="left"
            ) # Added closing parenthesis
            # Clean up column names if needed
            if product_code_col != product_col and product_code_col in product_totals.columns:
                product_totals = product_totals.drop(columns=[product_code_col])
                
            # Rename product name column for consistency
            if product_name_col in product_totals.columns and product_name_col != "product_name":
                product_totals = product_totals.rename(columns={product_name_col: "product_name"})
    
    # Add rank column
    product_totals['rank'] = range(1, len(product_totals) + 1)
    
    # Reorder columns
    base_columns = ['rank', product_col, value_col, 'percent_of_total']
    metadata_columns = ["product_name"] + (["sector"] if include_sectors and "sector" in product_totals.columns else [])
    product_totals = product_totals[base_columns + [col for col in metadata_columns if col in product_totals.columns]]
    
    return product_totals

@memoize(ttl=3600*24, level=StorageTier.DISK)
@log_execution_time(logger=logger)
def get_top_imports_by_value_v2(
    # Corrected indentation
    trade_data: pd.DataFrame,
    product_metadata: Optional[pd.DataFrame] = None,
    country_metadata: Optional[pd.DataFrame] = None,
    year: int = 2023,
    country_code: str = "887",  # Yemen by default
    top_n: int = 10,
    include_sectors: bool = True,
    year_col: str = "year",
    product_col: str = "product",
    partner_col: str = "partner",
    value_col: str = "value",
    direction_col: str = "direction"
) -> pd.DataFrame:
    """
    Get top imports by value for a specific country and year.
    
    Args:
        trade_data: DataFrame with trade data (must have year, product, partner, value columns)
        product_metadata: Optional DataFrame with product metadata (product_code, product_name, sector)
        country_metadata: Optional DataFrame with country metadata (country_code, country_name, region)
        year: Year to analyze
        country_code: Country code to analyze (default: "887" for Yemen)
        top_n: Number of top products to return
        include_sectors: Whether to include sector information
        year_col: Name of the year column in trade_data
        product_col: Name of the product column in trade_data
        partner_col: Name of the partner column in trade_data
        value_col: Name of the value column in trade_data
        direction_col: Name of the direction column in trade_data
        
    Returns:
        DataFrame with top imports by value
    """
    logger.info(f"Getting top {top_n} imports by value for {country_code} in {year}")
    
    # Validate inputs
    validation_result = _validate_trade_data(trade_data)
    if not validation_result.is_valid:
        for issue in validation_result.issues:
            logger.warning(f"Trade data validation issue: {issue}")
        if any(issue.level == ValidationIssueLevel.ERROR for issue in validation_result.issues):
            return pd.DataFrame()
    
    # Filter data for the specified year and country (as importer)
    if direction_col in trade_data.columns:
        # If direction column exists, use it to filter imports
        filtered_data = trade_data[
            (trade_data[year_col] == year) & 
            (trade_data[direction_col] == "import") &
            (trade_data.get("country", trade_data.get("importer", "")) == country_code)
        ]
    else:
        # Otherwise, assume exporter/importer columns
        importer_col = "importer" if "importer" in trade_data.columns else "j"
        filtered_data = trade_data[
            (trade_data[year_col] == year) & 
            (trade_data[importer_col] == country_code)
        ]
    
    if filtered_data.empty:
        logger.warning(f"No import data found for {country_code} in {year}")
        return pd.DataFrame()
    
    # Optimize the filtered DataFrame
    filtered_data = _optimize_dataframe_for_trade_analysis(filtered_data)
    
    # Group by product and aggregate values
    hw_manager = get_hardware_manager()
    if hw_manager.is_hardware_acceleration_available(AccelerationType.CPU):
        try:
            logger.debug("Using hardware acceleration for aggregation")
            result = hw_manager.accelerate_groupby(
                filtered_data, 
                by=product_col, 
                agg={value_col: 'sum'}
            ) # Added closing parenthesis
            if result is not None:
                product_totals = result.reset_index()
                logger.debug("Hardware acceleration successful for aggregation")
            else:
                logger.warning("Hardware acceleration returned None for aggregation")
                product_totals = filtered_data.groupby(product_col)[value_col].sum().reset_index()
        except Exception as e:
            logger.warning(f"Hardware acceleration failed for aggregation: {e}") # Corrected indentation
            product_totals = filtered_data.groupby(product_col)[value_col].sum().reset_index()
    else:
        product_totals = filtered_data.groupby(product_col)[value_col].sum().reset_index()
    # Sort by value in descending order and take top N
    product_totals = product_totals.sort_values(by=value_col, ascending=False).head(top_n)
    
    # Calculate percentage of total imports
    total_imports = product_totals[value_col].sum()
    product_totals['percent_of_total'] = (product_totals[value_col] / total_imports * 100) if total_imports > 0 else 0
    
    # Merge with product metadata if provided
    if product_metadata is not None and not product_metadata.empty:
        # Validate product metadata
        validation_result = _validate_trade_data(product_metadata, PRODUCT_METADATA_SCHEMA)
        if validation_result.is_valid or not any(issue.level == ValidationIssueLevel.ERROR for issue in validation_result.issues):
            # Align column names for merge
            product_code_col = "product_code" if "product_code" in product_metadata.columns else "code"
            product_name_col = "product_name" if "product_name" in product_metadata.columns else "name"
            
            # Merge with product metadata
            product_totals = pd.merge(
                product_totals,
                product_metadata[[product_code_col, product_name_col] + (["sector"] if "sector" in product_metadata.columns and include_sectors else [])],
                left_on=product_col,
                right_on=product_code_col,
                how="left"
            ) # Added closing parenthesis
            # Clean up column names if needed
            if product_code_col != product_col and product_code_col in product_totals.columns:
                product_totals = product_totals.drop(columns=[product_code_col])
                
            # Rename product name column for consistency
            if product_name_col in product_totals.columns and product_name_col != "product_name":
                product_totals = product_totals.rename(columns={product_name_col: "product_name"})
    
    # Add rank column
    product_totals['rank'] = range(1, len(product_totals) + 1)
    
    # Reorder columns
    base_columns = ['rank', product_col, value_col, 'percent_of_total']
    metadata_columns = ["product_name"] + (["sector"] if include_sectors and "sector" in product_totals.columns else [])
    product_totals = product_totals[base_columns + [col for col in metadata_columns if col in product_totals.columns]]
    
    return product_totals

@memoize(ttl=3600*24, level=StorageTier.DISK)
@log_execution_time(logger=logger)
def get_top_exports_by_volume_v2(
    # Corrected indentation
    trade_data: pd.DataFrame,
    product_metadata: Optional[pd.DataFrame] = None,
    country_metadata: Optional[pd.DataFrame] = None,
    year: int = 2023,
    country_code: str = "887",  # Yemen by default
    top_n: int = 10,
    include_sectors: bool = True,
    year_col: str = "year",
    product_col: str = "product",
    partner_col: str = "partner",
    quantity_col: str = "quantity",
    direction_col: str = "direction"
) -> pd.DataFrame:
    """
    Get top exports by volume/quantity for a specific country and year.
    
    Args:
        trade_data: DataFrame with trade data (must have year, product, partner, quantity columns)
        product_metadata: Optional DataFrame with product metadata (product_code, product_name, sector)
        country_metadata: Optional DataFrame with country metadata (country_code, country_name, region)
        year: Year to analyze
        country_code: Country code to analyze (default: "887" for Yemen)
        top_n: Number of top products to return
        include_sectors: Whether to include sector information
        year_col: Name of the year column in trade_data
        product_col: Name of the product column in trade_data
        partner_col: Name of the partner column in trade_data
        quantity_col: Name of the quantity column in trade_data
        direction_col: Name of the direction column in trade_data
        
    Returns:
        DataFrame with top exports by volume
    """
    logger.info(f"Getting top {top_n} exports by volume for {country_code} in {year}")
    
    # Check if quantity column exists
    if quantity_col not in trade_data.columns:
        logger.error(f"Quantity column '{quantity_col}' not found in trade data")
        raise ValueError(f"Required quantity column '{quantity_col}' not found.")
    
    # Validate inputs (modify schema to require quantity column)
    modified_schema = TRADE_DATA_SCHEMA.copy()
    modified_schema['required_columns'] = [year_col, product_col, partner_col, quantity_col]
    
    validation_result = _validate_trade_data(trade_data, modified_schema)
    if not validation_result.is_valid:
        for issue in validation_result.issues:
            logger.warning(f"Trade data validation issue: {issue}")
        if any(issue.level == ValidationIssueLevel.ERROR for issue in validation_result.issues):
            return pd.DataFrame()
    
    # Filter data for the specified year and country (as exporter)
    if direction_col in trade_data.columns:
        # If direction column exists, use it to filter exports
        filtered_data = trade_data[
            (trade_data[year_col] == year) & 
            (trade_data[direction_col] == "export") &
            (trade_data.get("country", trade_data.get("exporter", "")) == country_code)
        ]
    else:
        # Otherwise, assume exporter/importer columns
        exporter_col = "exporter" if "exporter" in trade_data.columns else "i"
        filtered_data = trade_data[
            (trade_data[year_col] == year) & 
            (trade_data[exporter_col] == country_code)
        ]
    
    if filtered_data.empty:
        logger.warning(f"No export data found for {country_code} in {year}")
        return pd.DataFrame()
    
    # Filter out records with missing or zero quantity
    filtered_data = filtered_data[filtered_data[quantity_col].notna() & (filtered_data[quantity_col] > 0)]
    
    if filtered_data.empty:
        logger.warning(f"No export data with valid quantity found for {country_code} in {year}")
        return pd.DataFrame()
    
    # Optimize the filtered DataFrame
    filtered_data = _optimize_dataframe_for_trade_analysis(filtered_data)
    
    # Group by product and aggregate quantities
    hw_manager = get_hardware_manager()
    if hw_manager.is_hardware_acceleration_available(AccelerationType.CPU):
        try:
            logger.debug("Using hardware acceleration for aggregation")
            result = hw_manager.accelerate_groupby(
                filtered_data, 
                by=product_col, 
                agg={quantity_col: 'sum'}
            ) # Added closing parenthesis
            if result is not None:
                product_totals = result.reset_index()
                logger.debug("Hardware acceleration successful for aggregation")
            else:
                logger.warning("Hardware acceleration returned None for aggregation")
                product_totals = filtered_data.groupby(product_col)[quantity_col].sum().reset_index()
        except Exception as e:
            logger.warning(f"Hardware acceleration failed for aggregation: {e}") # Corrected indentation
            product_totals = filtered_data.groupby(product_col)[quantity_col].sum().reset_index()
    else:
        product_totals = filtered_data.groupby(product_col)[quantity_col].sum().reset_index()
    # Sort by quantity in descending order and take top N
    product_totals = product_totals.sort_values(by=quantity_col, ascending=False).head(top_n)
    
    # Calculate percentage of total exports by volume
    total_exports_volume = product_totals[quantity_col].sum()
    product_totals['percent_of_total'] = (product_totals[quantity_col] / total_exports_volume * 100) if total_exports_volume > 0 else 0
    
    # Merge with product metadata if provided
    if product_metadata is not None and not product_metadata.empty:
        # Validate product metadata
        validation_result = _validate_trade_data(product_metadata, PRODUCT_METADATA_SCHEMA)
        if validation_result.is_valid or not any(issue.level == ValidationIssueLevel.ERROR for issue in validation_result.issues):
            # Align column names for merge
            product_code_col = "product_code" if "product_code" in product_metadata.columns else "code"
            product_name_col = "product_name" if "product_name" in product_metadata.columns else "name"
            
            # Merge with product metadata
            product_totals = pd.merge(
                product_totals,
                product_metadata[[product_code_col, product_name_col] + (["sector"] if "sector" in product_metadata.columns and include_sectors else [])],
                left_on=product_col,
                right_on=product_code_col,
                how="left"
            ) # Added closing parenthesis
            # Clean up column names if needed
            if product_code_col != product_col and product_code_col in product_totals.columns:
                product_totals = product_totals.drop(columns=[product_code_col])
                
            # Rename product name column for consistency
            if product_name_col in product_totals.columns and product_name_col != "product_name":
                product_totals = product_totals.rename(columns={product_name_col: "product_name"})
    
    # Add rank column
    product_totals['rank'] = range(1, len(product_totals) + 1)
    
    # Add value column from original data if available
    if 'value' in filtered_data.columns:
        value_by_product = filtered_data.groupby(product_col)['value'].sum().reset_index()
        product_totals = pd.merge(product_totals, value_by_product, on=product_col, how='left')
    
    # Reorder columns
    base_columns = ['rank', product_col, quantity_col, 'percent_of_total']
    value_columns = ['value'] if 'value' in product_totals.columns else []
    metadata_columns = ["product_name"] + (["sector"] if include_sectors and "sector" in product_totals.columns else [])
    product_totals = product_totals[base_columns + value_columns + [col for col in metadata_columns if col in product_totals.columns]]
    
    return product_totals

@memoize(ttl=3600*24, level=StorageTier.DISK)
@log_execution_time(logger=logger)
def get_top_imports_by_volume_v2(
    # Corrected indentation
    trade_data: pd.DataFrame,
    product_metadata: Optional[pd.DataFrame] = None,
    country_metadata: Optional[pd.DataFrame] = None,
    year: int = 2023,
    country_code: str = "887",  # Yemen by default
    top_n: int = 10,
    include_sectors: bool = True,
    year_col: str = "year",
    product_col: str = "product",
    partner_col: str = "partner",
    quantity_col: str = "quantity",
    direction_col: str = "direction"
) -> pd.DataFrame:
    """
    Get top imports by volume/quantity for a specific country and year.
    
    Args:
        trade_data: DataFrame with trade data (must have year, product, partner, quantity columns)
        product_metadata: Optional DataFrame with product metadata (product_code, product_name, sector)
        country_metadata: Optional DataFrame with country metadata (country_code, country_name, region)
        year: Year to analyze
        country_code: Country code to analyze (default: "887" for Yemen)
        top_n: Number of top products to return
        include_sectors: Whether to include sector information
        year_col: Name of the year column in trade_data
        product_col: Name of the product column in trade_data
        partner_col: Name of the partner column in trade_data
        quantity_col: Name of the quantity column in trade_data
        direction_col: Name of the direction column in trade_data
        
    Returns:
        DataFrame with top imports by volume
    """
    logger.info(f"Getting top {top_n} imports by volume for {country_code} in {year}")
    
    # Check if quantity column exists
    if quantity_col not in trade_data.columns:
        logger.error(f"Quantity column '{quantity_col}' not found in trade data")
        raise ValueError(f"Required quantity column '{quantity_col}' not found.")
    
    # Validate inputs (modify schema to require quantity column)
    modified_schema = TRADE_DATA_SCHEMA.copy()
    modified_schema['required_columns'] = [year_col, product_col, partner_col, quantity_col]
    
    validation_result = _validate_trade_data(trade_data, modified_schema)
    if not validation_result.is_valid:
        for issue in validation_result.issues:
            logger.warning(f"Trade data validation issue: {issue}")
        if any(issue.level == ValidationIssueLevel.ERROR for issue in validation_result.issues):
            return pd.DataFrame()
    
    # Filter data for the specified year and country (as importer)
    if direction_col in trade_data.columns:
        # If direction column exists, use it to filter imports
        filtered_data = trade_data[
            (trade_data[year_col] == year) & 
            (trade_data[direction_col] == "import") &
            (trade_data.get("country", trade_data.get("importer", "")) == country_code)
        ]
    else:
        # Otherwise, assume exporter/importer columns
        importer_col = "importer" if "importer" in trade_data.columns else "j"
        filtered_data = trade_data[
            (trade_data[year_col] == year) & 
            (trade_data[importer_col] == country_code)
        ]
    
    if filtered_data.empty:
        logger.warning(f"No import data found for {country_code} in {year}")
        return pd.DataFrame()
    
    # Filter out records with missing or zero quantity
    filtered_data = filtered_data[filtered_data[quantity_col].notna() & (filtered_data[quantity_col] > 0)]
    
    if filtered_data.empty:
        logger.warning(f"No import data with valid quantity found for {country_code} in {year}")
        return pd.DataFrame()
    
    # Optimize the filtered DataFrame
    filtered_data = _optimize_dataframe_for_trade_analysis(filtered_data)
    
    # Group by product and aggregate quantities
    hw_manager = get_hardware_manager()
    if hw_manager.is_hardware_acceleration_available(AccelerationType.CPU):
        try:
            logger.debug("Using hardware acceleration for aggregation")
            result = hw_manager.accelerate_groupby(
                filtered_data, 
                by=product_col, 
                agg={quantity_col: 'sum'}
            ) # Added closing parenthesis
            if result is not None:
                product_totals = result.reset_index()
                logger.debug("Hardware acceleration successful for aggregation")
            else:
                logger.warning("Hardware acceleration returned None for aggregation")
                product_totals = filtered_data.groupby(product_col)[quantity_col].sum().reset_index()
        except Exception as e:
            logger.warning(f"Hardware acceleration failed for aggregation: {e}") # Corrected indentation
            product_totals = filtered_data.groupby(product_col)[quantity_col].sum().reset_index()
    else:
        product_totals = filtered_data.groupby(product_col)[quantity_col].sum().reset_index()
    # Sort by quantity in descending order and take top N
    product_totals = product_totals.sort_values(by=quantity_col, ascending=False).head(top_n)
    
    # Calculate percentage of total imports by volume
    total_imports_volume = product_totals[quantity_col].sum()
    product_totals['percent_of_total'] = (product_totals[quantity_col] / total_imports_volume * 100) if total_imports_volume > 0 else 0
    
    # Merge with product metadata if provided
    if product_metadata is not None and not product_metadata.empty:
        # Validate product metadata
        validation_result = _validate_trade_data(product_metadata, PRODUCT_METADATA_SCHEMA)
        if validation_result.is_valid or not any(issue.level == ValidationIssueLevel.ERROR for issue in validation_result.issues):
            # Align column names for merge
            product_code_col = "product_code" if "product_code" in product_metadata.columns else "code"
            product_name_col = "product_name" if "product_name" in product_metadata.columns else "name"
            
            # Merge with product metadata
            product_totals = pd.merge(
                product_totals,
                product_metadata[[product_code_col, product_name_col] + (["sector"] if "sector" in product_metadata.columns and include_sectors else [])],
                left_on=product_col,
                right_on=product_code_col,
                how="left"
            ) # Added closing parenthesis
            # Clean up column names if needed
            if product_code_col != product_col and product_code_col in product_totals.columns:
                product_totals = product_totals.drop(columns=[product_code_col])
                
            # Rename product name column for consistency
            if product_name_col in product_totals.columns and product_name_col != "product_name":
                product_totals = product_totals.rename(columns={product_name_col: "product_name"})
    
    # Add rank column
    product_totals['rank'] = range(1, len(product_totals) + 1)
    
    # Add value column from original data if available
    if 'value' in filtered_data.columns:
        value_by_product = filtered_data.groupby(product_col)['value'].sum().reset_index()
        product_totals = pd.merge(product_totals, value_by_product, on=product_col, how='left')
    
    # Reorder columns
    base_columns = ['rank', product_col, quantity_col, 'percent_of_total']
    value_columns = ['value'] if 'value' in product_totals.columns else []
    metadata_columns = ["product_name"] + (["sector"] if include_sectors and "sector" in product_totals.columns else [])
    product_totals = product_totals[base_columns + value_columns + [col for col in metadata_columns if col in product_totals.columns]]
    
    return product_totals

@memoize(ttl=3600*24, level=StorageTier.DISK)
@log_execution_time(logger=logger)
def get_top_trade_partners_v2(
    trade_data: pd.DataFrame,
    country_metadata: Optional[pd.DataFrame] = None,
    year: int = 2023,
    country_code: str = "887",  # Yemen by default
    top_n: int = 10,
    direction: str = "export",  # "export" or "import"
    include_regions: bool = True,
    year_col: str = "year",
    partner_col: str = "partner",
    value_col: str = "value",
    direction_col: str = "direction"
) -> pd.DataFrame:
    """
    Get top trade partners (destinations for exports or sources for imports) for a specific country and year.
    
    Args:
        trade_data: DataFrame with trade data (must have year, partner, value columns)
        country_metadata: Optional DataFrame with country metadata (country_code, country_name, region)
        year: Year to analyze
        country_code: Country code to analyze (default: "887" for Yemen)
        top_n: Number of top partners to return
        direction: Trade direction, "export" for destinations or "import" for sources
        include_regions: Whether to include region information for partners
        year_col: Name of the year column in trade_data
        partner_col: Name of the partner column in trade_data
        value_col: Name of the value column in trade_data
        direction_col: Name of the direction column in trade_data
        
    Returns:
        DataFrame with top trade partners
    """
    logger.info(f"Getting top {top_n} {direction} partners for {country_code} in {year}")
    
    # Validate direction
    if direction not in ["export", "import"]:
        logger.error(f"Invalid direction: {direction}. Must be 'export' or 'import'.")
        raise ValueError(f"Invalid trade direction specified: {direction}. Must be 'export' or 'import'.")
    
    # Validate inputs
    validation_result = _validate_trade_data(trade_data)
    if not validation_result.is_valid:
        for issue in validation_result.issues:
            logger.warning(f"Trade data validation issue: {issue}")
        if any(issue.level == ValidationIssueLevel.ERROR for issue in validation_result.issues):
            return pd.DataFrame()
    
    # Filter data for the specified year, country, and direction
    if direction_col in trade_data.columns:
        # If direction column exists, use it to filter
        filtered_data = trade_data[
            (trade_data[year_col] == year) & 
            (trade_data[direction_col] == direction) &
            (trade_data.get("country", trade_data.get("exporter" if direction == "export" else "importer", "")) == country_code)
        ]
    else:
        # Otherwise, assume exporter/importer columns
        if direction == "export":
            exporter_col = "exporter" if "exporter" in trade_data.columns else "i"
            filtered_data = trade_data[
                (trade_data[year_col] == year) & 
                (trade_data[exporter_col] == country_code)
            ]
        else:  # import
            importer_col = "importer" if "importer" in trade_data.columns else "j"
            filtered_data = trade_data[
                (trade_data[year_col] == year) & 
                (trade_data[importer_col] == country_code)
            ]
    
    if filtered_data.empty:
        logger.warning(f"No {direction} data found for {country_code} in {year}")
        return pd.DataFrame()
    
    # Optimize the filtered DataFrame
    filtered_data = _optimize_dataframe_for_trade_analysis(filtered_data)
    
    # Group by partner and aggregate values
    hw_manager = get_hardware_manager()
    if hw_manager.is_hardware_acceleration_available(AccelerationType.CPU):
        try:
            logger.debug("Using hardware acceleration for aggregation")
            result = hw_manager.accelerate_groupby(
                filtered_data, 
                by=partner_col, 
                agg={value_col: 'sum'}
            ) # Added closing parenthesis
            if result is not None:
                partner_totals = result.reset_index()
                logger.debug("Hardware acceleration successful for aggregation")
            else:
                logger.warning("Hardware acceleration returned None for aggregation")
                partner_totals = filtered_data.groupby(partner_col)[value_col].sum().reset_index()
        except Exception as e:
            logger.warning(f"Hardware acceleration failed for aggregation: {e}") # Corrected indentation
            partner_totals = filtered_data.groupby(partner_col)[value_col].sum().reset_index()
    else:
        partner_totals = filtered_data.groupby(partner_col)[value_col].sum().reset_index()
    # Sort by value in descending order and take top N
    partner_totals = partner_totals.sort_values(by=value_col, ascending=False).head(top_n)
    
    # Calculate percentage of total
    total_value = partner_totals[value_col].sum()
    partner_totals['percent_of_total'] = (partner_totals[value_col] / total_value * 100) if total_value > 0 else 0
    
    # Merge with country metadata if provided
    if country_metadata is not None and not country_metadata.empty:
        # Validate country metadata
        validation_result = _validate_trade_data(country_metadata, COUNTRY_METADATA_SCHEMA)
        if validation_result.is_valid or not any(issue.level == ValidationIssueLevel.ERROR for issue in validation_result.issues):
            # Align column names for merge
            country_code_col = "country_code" if "country_code" in country_metadata.columns else "code"
            country_name_col = "country_name" if "country_name" in country_metadata.columns else "name"
            
            # Merge with country metadata
            partner_totals = pd.merge(
                partner_totals,
                country_metadata[[country_code_col, country_name_col] + (["region"] if "region" in country_metadata.columns and include_regions else [])],
                left_on=partner_col,
                right_on=country_code_col,
                how="left"
            ) # Added closing parenthesis
            # Clean up column names if needed
            if country_code_col != partner_col and country_code_col in partner_totals.columns:
                partner_totals = partner_totals.drop(columns=[country_code_col])
                
            # Rename country name column for consistency
            if country_name_col in partner_totals.columns and country_name_col != "partner_name":
                partner_totals = partner_totals.rename(columns={country_name_col: "partner_name"})
    
    # Add rank column
    partner_totals['rank'] = range(1, len(partner_totals) + 1)
    
    # Add trade direction
    partner_totals['direction'] = direction
    
    # Reorder columns
    base_columns = ['rank', partner_col, value_col, 'percent_of_total', 'direction']
    metadata_columns = ["partner_name"] + (["region"] if include_regions and "region" in partner_totals.columns else [])
    partner_totals = partner_totals[base_columns + [col for col in metadata_columns if col in partner_totals.columns]]
    
    return partner_totals

# Class-based API for the model
class TopTradeModelV2:
    """Model for top trade analysis (V2)."""
    
    def __init__(self, data_dir: Optional[Path] = None):
        """
        Initialize the model.
        
        Args:
            data_dir: Optional base directory for data files
        """
        self.logger = get_logger(f"{__name__}.TopTradeModelV2")
        self.data_dir = data_dir
        self.hw_manager = get_hardware_manager()
        self.logger.info("TopTradeModelV2 initialized")
    
    def get_top_exports_by_value(self, trade_data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """Get top exports by value using the function-based API."""
        return get_top_exports_by_value_v2(trade_data, **kwargs)
    
    def get_top_imports_by_value(self, trade_data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """Get top imports by value using the function-based API."""
        return get_top_imports_by_value_v2(trade_data, **kwargs)
    
    def get_top_exports_by_volume(self, trade_data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """Get top exports by volume using the function-based API."""
        return get_top_exports_by_volume_v2(trade_data, **kwargs)
    
    def get_top_imports_by_volume(self, trade_data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """Get top imports by volume using the function-based API."""
        return get_top_imports_by_volume_v2(trade_data, **kwargs)
    
    def get_top_trade_partners(self, trade_data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """Get top trade partners using the function-based API."""
        return get_top_trade_partners_v2(trade_data, **kwargs)

# Example usage (Keep configure_logging for example)
if __name__ == "__main__":
    configure_logging(log_level=LogLevel.INFO, log_to_console=True)
    logger.info("--- Running Top Trade Model V2 Example ---")
    
    # Create sample data
    sample_trade_data = pd.DataFrame({
        'year': [2023, 2023, 2023, 2023, 2023, 2023],
        'product': ['010101', '010102', '020101', '010101', '010102', '020101'],
        'partner': ['156', '156', '156', '840', '840', '840'],
        'value': [100, 200, 150, 300, 250, 50],
        'quantity': [500, 1000, 750, 1500, 1250, 250],
        'direction': ['export', 'export', 'export', 'import', 'import', 'import'],
        'country': ['887', '887', '887', '887', '887', '887']
    })
    
    sample_product_metadata = pd.DataFrame({
        'product_code': ['010101', '010102', '020101'],
        'product_name': ['Live horses', 'Live donkeys', 'Beef'],
        'sector': ['Animals', 'Animals', 'Meat']
    })
    
    sample_country_metadata = pd.DataFrame({
        'country_code': ['156', '840', '887'],
        'country_name': ['China', 'United States', 'Yemen'],
        'region': ['Asia', 'North America', 'Middle East']
    })
    
    # Create model instance
    model = TopTradeModelV2()
    
    # Test top exports by value
    top_exports_value = model.get_top_exports_by_value(
        sample_trade_data,
        product_metadata=sample_product_metadata,
        country_metadata=sample_country_metadata,
        year=2023,
        country_code='887',
        top_n=10
    ) # Added closing parenthesis
    logger.info(f"Top exports by value:\n{top_exports_value}")
    # Test top imports by value
    top_imports_value = model.get_top_imports_by_value(
        sample_trade_data,
        product_metadata=sample_product_metadata,
        country_metadata=sample_country_metadata,
        year=2023,
        country_code='887',
        top_n=10
    )
    logger.info(f"Top imports by value:\n{top_imports_value}")
    # Test top exports by volume
    top_exports_volume = model.get_top_exports_by_volume(
        sample_trade_data,
        product_metadata=sample_product_metadata,
        country_metadata=sample_country_metadata,
        year=2023,
        country_code='887',
        top_n=10
    ) # Added closing parenthesis
    logger.info(f"Top exports by volume:\n{top_exports_volume}")
    # Test top trade partners
    top_destinations = model.get_top_trade_partners(
        sample_trade_data,
        country_metadata=sample_country_metadata,
        year=2023,
        country_code='887',
        direction='export',
        top_n=10
    ) # Added closing parenthesis
    logger.info(f"Top destinations:\n{top_destinations}")
    logger.info("--- Top Trade Model V2 Example Finished ---")