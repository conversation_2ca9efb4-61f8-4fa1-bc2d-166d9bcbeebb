"""
Gravity Model for Yemen Trade Diagnostic (V2)

This module provides V2 functions for analyzing trade flows using the gravity model.
"""
# Standard library imports
from typing import Any, Dict, List, Optional, Tuple, Union

# Third-party imports
import numpy as np
import pandas as pd
import statsmodels.api as sm  # Retained for OLS regression

# Project imports
from yemen_trade_diagnostic.data import DataSource, load_data  # Use unified API
from yemen_trade_diagnostic.interfaces.cache_interface import StorageTier, DataLifetime, memoize
from yemen_trade_diagnostic.interfaces.hardware_interface import AccelerationType, get_hardware_manager
from yemen_trade_diagnostic.interfaces.logging_interface import LogLevel, get_logger, log_execution_time
from yemen_trade_diagnostic.interfaces.validation_interface import (
    SchemaValidationRule,
    ValidationIssueLevel,
    ValidationResult,
    get_validation_manager,
    validate_schema,
)
from yemen_trade_diagnostic.errors import protect, OperationType

logger = get_logger(__name__)

# Constants - standardizing column names where possible
LOG_TRADE_OFFSET = 1

# V2 Version of TypedDict if Python version supports it, else basic Dict
# Using basic Dict for broader compatibility in this example refactor.
GravityModelResultV2 = Dict[str, Any] # Replacement for TypedDict for now

# --- V2 Adapted Validation Helper (from trade_flow_model_v2) ---
def _validate_gravity_input_df_v2(df: pd.DataFrame, required_cols: List[str], df_name: str) -> ValidationResult:
    res = ValidationResult()
    if not isinstance(df, pd.DataFrame) or df.empty:
        res.add_issue(f"{df_name} is not a valid or non-empty DataFrame.", ValidationIssueLevel.ERROR)
        return res
    missing = [col for col in required_cols if col not in df.columns]
    if missing:
        res.add_issue(f"Missing columns in {df_name}: {missing}", ValidationIssueLevel.ERROR)
    # Add more specific type/value checks as needed for gravity model inputs
    return res

# --- Helper Functions Refactored for V2 ---

def _filter_trade_by_year_v2(
    trade_data: pd.DataFrame,
    year_col: str,
    year: Optional[int] = None,
    min_year: Optional[int] = None,
    max_year: Optional[int] = None
) -> pd.DataFrame:
    logger.debug(f"Filtering trade data by year. Period: {year or (min_year, max_year)}")
    filtered_trade = trade_data.copy()
    if year is not None:
        filtered_trade = filtered_trade[filtered_trade[year_col] == year]
    elif min_year is not None and max_year is not None:
        filtered_trade = filtered_trade[(filtered_trade[year_col] >= min_year) & (filtered_trade[year_col] <= max_year)]
    elif min_year is not None:
        filtered_trade = filtered_trade[filtered_trade[year_col] >= min_year]
    elif max_year is not None:
        filtered_trade = filtered_trade[filtered_trade[year_col] <= max_year]
    return filtered_trade

@log_execution_time(logger=logger)
def _prepare_merged_dataset_v2(
    trade_data: pd.DataFrame, gdp_data: pd.DataFrame, distance_data: pd.DataFrame,
    exporter_col: str, importer_col: str, year_col: str, value_col: str, # Added year_col here
    exporter_gdp_col: str, importer_gdp_col: str, distance_col: str, gdp_col: str = "gdp"
) -> pd.DataFrame:
    logger.debug("Preparing merged dataset for gravity model.")
    # Aggregate trade data by exporter-importer-year triple
    trade_flows = trade_data.groupby([exporter_col, importer_col, year_col], observed=True)[value_col].sum().reset_index()

    # Prepare GDP data: rename GDP column and set index for merging
    gdp_data_exp = gdp_data.rename(columns={gdp_col: exporter_gdp_col, 'country_code': exporter_col}) # Assuming gdp_data has 'country_code'
    gdp_data_imp = gdp_data.rename(columns={gdp_col: importer_gdp_col, 'country_code': importer_col})

    # Merge trade flows with exporter GDP
    merged_data = pd.merge(trade_flows, gdp_data_exp[[exporter_col, year_col, exporter_gdp_col]], on=[exporter_col, year_col], how="inner")
    # Merge with importer GDP
    merged_data = pd.merge(merged_data, gdp_data_imp[[importer_col, year_col, importer_gdp_col]], on=[importer_col, year_col], how="inner")
    # Merge with distance data
    # Assuming distance_data has exporter_col, importer_col, distance_col and is static (not year-specific)
    merged_data = pd.merge(merged_data, distance_data[[exporter_col, importer_col, distance_col]], on=[exporter_col, importer_col], how="inner")

    if merged_data.empty:
        logger.warning("Merged dataset is empty after joins.")
        raise ValueError("No data after merging required datasets for gravity model preparation.")

    # Log-transform variables, handling zeros appropriately
    merged_data[f"ln_{value_col}"] = np.log(merged_data[value_col] + LOG_TRADE_OFFSET)
    merged_data[f"ln_{exporter_gdp_col}"] = np.log(merged_data[exporter_gdp_col].replace(0, np.nan) + LOG_TRADE_OFFSET) # Avoid log(0)
    merged_data[f"ln_{importer_gdp_col}"] = np.log(merged_data[importer_gdp_col].replace(0, np.nan) + LOG_TRADE_OFFSET)
    merged_data[f"ln_{distance_col}"] = np.log(merged_data[distance_col].replace(0, np.nan)) # Distance shouldn't be 0
    
    # Drop rows with NaN in log-transformed cols, as they can't be used in OLS
    log_cols = [f"ln_{value_col}", f"ln_{exporter_gdp_col}", f"ln_{importer_gdp_col}", f"ln_{distance_col}"]
    merged_data.dropna(subset=log_cols, inplace=True)
    logger.debug("Merged dataset prepared with log-transformed variables.")
    return merged_data

def _run_gravity_regression_v2(data: pd.DataFrame, value_col: str, exporter_gdp_col: str, importer_gdp_col: str, distance_col: str) -> Optional[GravityModelResultV2]:
    logger.debug("Running OLS regression for gravity model.")
    ln_trade_col = f"ln_{value_col}"
    ln_exp_gdp_col = f"ln_{exporter_gdp_col}"
    ln_imp_gdp_col = f"ln_{importer_gdp_col}"
    ln_dist_col = f"ln_{distance_col}"

    if not all(c in data.columns for c in [ln_trade_col, ln_exp_gdp_col, ln_imp_gdp_col, ln_dist_col]):
        logger.error("Missing required log-transformed columns for regression.")
        raise ValueError("Missing required log-transformed columns for OLS regression.")

    X = data[[ln_exp_gdp_col, ln_imp_gdp_col, ln_dist_col]]
    X = sm.add_constant(X) # Adds a constant term to the predictor
    y = data[ln_trade_col]

    if X.empty or y.empty or len(X) != len(y):
        logger.error("Independent (X) or dependent (y) variables are empty or mismatched for OLS.")
        raise ValueError("Independent (X) or dependent (y) variables are empty or mismatched for OLS regression.")

    model = sm.OLS(y, X)
    results = model.fit()
    logger.info(f"Gravity model OLS regression completed. R-squared: {results.rsquared:.4f}")
    
    return {
        "parameters": results.params.to_dict(),
        "statistics": {"r_squared": results.rsquared, "adj_r_squared": results.rsquared_adj,
                       "num_observations": results.nobs, "f_statistic": results.fvalue,
                       "p_value_f_statistic": results.f_pvalue},
        "data": data, # Return the data used for regression (contains log-transformed vars)
        "r_squared": results.rsquared, 
        "adj_r_squared": results.rsquared_adj
    }

# --- Main Functions Refactored for V2 ---

@memoize(ttl=DataLifetime.DAILY.value if hasattr(DataLifetime, 'DAILY') else 3600*24, level=StorageTier.MEMORY)
@log_execution_time(logger=logger)
def estimate_gravity_model_v2(
    trade_data: pd.DataFrame, gdp_data: pd.DataFrame, distance_data: pd.DataFrame,
    exporter_col: str = "i", importer_col: str = "j", year_col: str = "t", value_col: str = "v",
    gdp_country_col: str = "country_code", # Assuming this is the country identifier in gdp_data
    exporter_gdp_col: str = "gdp_exporter", # Name for exporter GDP column in merged_data
    importer_gdp_col: str = "gdp_importer", # Name for importer GDP column in merged_data
    gdp_value_col: str = "gdp_value", # Actual GDP value column in gdp_data
    distance_col: str = "dist",
    year: Optional[int] = None, min_year: Optional[int] = None, max_year: Optional[int] = None
) -> Optional[GravityModelResultV2]:
    """Estimate gravity model parameters (V2)."""
    logger.info(f"Estimating gravity model for period: {year or (min_year, max_year)}.")
    # Basic validation of input DataFrames
    if (_validate_gravity_input_df_v2(trade_data, [exporter_col, importer_col, year_col, value_col], "trade_data").is_valid is False or
       _validate_gravity_input_df_v2(gdp_data, [gdp_country_col, year_col, gdp_value_col], "gdp_data").is_valid is False or
       _validate_gravity_input_df_v2(distance_data, [exporter_col, importer_col, distance_col], "distance_data").is_valid is False):
        logger.error("Input data validation failed for estimate_gravity_model_v2.")
        raise ValueError("Input data validation failed for gravity model estimation.")

    filtered_trade = _filter_trade_by_year_v2(trade_data, year_col, year, min_year, max_year)
    if filtered_trade.empty: 
        logger.warning("No trade data after year filtering for gravity model.")
        raise ValueError("No trade data available for the specified period to estimate gravity model.")
    
    # Rename GDP columns for clarity before merge
    gdp_data_renamed = gdp_data.rename(columns={gdp_country_col: 'country_code', gdp_value_col: 'gdp'})

    merged_data = _prepare_merged_dataset_v2(
        filtered_trade, gdp_data_renamed, distance_data,
        exporter_col, importer_col, year_col, value_col,
        exporter_gdp_col, importer_gdp_col, distance_col
    )
    
    if merged_data.empty: 
        logger.warning("Merged dataset is empty, cannot estimate gravity model.")
        raise RuntimeError("Data preparation resulted in an empty dataset before regression.")
    return _run_gravity_regression_v2(merged_data, value_col, exporter_gdp_col, importer_gdp_col, distance_col)

@memoize(ttl=3600, level=StorageTier.MEMORY)
@log_execution_time(logger=logger)
def predict_trade_flows_v2(
    model_estimation_result: Optional[GravityModelResultV2],
    country_code_to_analyze: int = 887, # Typically Yemen
    value_col: str = "v",
    exporter_col: str = "i", importer_col: str = "j", 
    exporter_gdp_col: str = "gdp_exporter", 
    importer_gdp_col: str = "gdp_importer", 
    distance_col: str = "dist"
) -> pd.DataFrame:
    """Predict trade flows using estimated gravity model (V2)."""
    logger.info(f"Predicting trade flows for country {country_code_to_analyze}.")
    if not model_estimation_result or not model_estimation_result.get("parameters") or not isinstance(model_estimation_result.get("data"), pd.DataFrame):
        logger.error("Invalid or incomplete model estimation result provided for prediction.")
        raise ValueError("Invalid or incomplete model estimation result provided for prediction.")

    params = model_estimation_result["parameters"]
    data_used_for_estimation = model_estimation_result["data"]
    
    # Filter the data used in estimation for the specific country
    country_specific_data = data_used_for_estimation[
        (data_used_for_estimation[exporter_col] == country_code_to_analyze) | 
        (data_used_for_estimation[importer_col] == country_code_to_analyze)
    ].copy()

    if country_specific_data.empty:
        logger.warning(f"No data for country {country_code_to_analyze} in the estimation dataset.")
        return pd.DataFrame()
    
    # Log-transformed column names used in regression
    ln_exp_gdp_col = f"ln_{exporter_gdp_col}"
    ln_imp_gdp_col = f"ln_{importer_gdp_col}"
    ln_dist_col = f"ln_{distance_col}"

    # Calculate ln(predicted_trade)
    const_term = params.get("const", 0)
    country_specific_data["ln_predicted_trade"] = (
        const_term +
        params.get(ln_exp_gdp_col, 0) * country_specific_data[ln_exp_gdp_col] +
        params.get(ln_imp_gdp_col, 0) * country_specific_data[ln_imp_gdp_col] +
        params.get(ln_dist_col, 0) * country_specific_data[ln_dist_col]
    )
    
    country_specific_data["predicted_trade"] = np.exp(country_specific_data["ln_predicted_trade"]) - LOG_TRADE_OFFSET
    country_specific_data["predicted_trade"] = country_specific_data["predicted_trade"].clip(lower=0) # Ensure non-negative
    # Actual trade is already in 'value_col'. ln_value_col was used for estimation.
    country_specific_data["trade_intensity"] = (country_specific_data[value_col] + LOG_TRADE_OFFSET) / (country_specific_data["predicted_trade"] + LOG_TRADE_OFFSET)
    country_specific_data["trade_intensity"] = country_specific_data["trade_intensity"].fillna(0)

    country_specific_data["direction"] = np.where(country_specific_data[exporter_col] == country_code_to_analyze, "export", "import")
    
    cols_to_return = [exporter_col, importer_col, "direction", value_col, "predicted_trade", "trade_intensity"]
    # Add partner name cols if they exist from merge step in _prepare_merged_dataset_v2 (if partner_df was used)
    if "partner_name" in country_specific_data.columns:
        cols_to_return.insert(2, "partner_name")
        
    return country_specific_data[cols_to_return].reset_index(drop=True)

@protect("analyze_trade_relationships_v2", OperationType.COMPUTATION)
@memoize(ttl=DataLifetime.DAILY.value if hasattr(DataLifetime, 'DAILY') else 3600*24, level=StorageTier.MEMORY)
def analyze_trade_relationships_v2(
    trade_data: pd.DataFrame,
    country_pairs: List[Tuple[str, str]] = None,
    reference_years: List[int] = None,
    distance_data: Optional[pd.DataFrame] = None,
    gdp_data: Optional[pd.DataFrame] = None,
    exporter_col: str = "exporter_iso",
    importer_col: str = "importer_iso",
    year_col: str = "year",
    value_col: str = "trade_value_usd"
) -> Dict[str, Any]:
    """
    Analyze bilateral trade relationships using gravity model principles (V2).
    
    Args:
        trade_data: DataFrame containing trade flow data
        country_pairs: List of (exporter, importer) tuples to analyze. If None, top pairs are selected
        reference_years: List of years to analyze. If None, most recent available years are used
        distance_data: Optional DataFrame with distance between countries
        gdp_data: Optional DataFrame with GDP data for countries
        exporter_col: Column name for exporter country codes
        importer_col: Column name for importer country codes
        year_col: Column name for years
        value_col: Column name for trade values
        
    Returns:
        Dictionary with analysis results for each country pair
    """
    logger.info(f"Analyzing trade relationships for {len(country_pairs) if country_pairs else 'top'} country pairs.")
    
    # Validate inputs
    validation_result = _validate_gravity_input_df_v2(
        trade_data, 
        [exporter_col, importer_col, year_col, value_col],
        "trade_data"
    )
    
    if not validation_result.is_valid:
        logger.error(f"Trade data validation failed: {validation_result.issues}")
        raise ValueError(f"Invalid trade data for relationship analysis: {validation_result.issues}")
    
    # Select country pairs if not provided
    if country_pairs is None or len(country_pairs) == 0:
        country_pairs = _identify_top_country_pairs_v2(
            trade_data, exporter_col, importer_col, value_col, top_n=5
        )
        logger.info(f"Selected top {len(country_pairs)} country pairs for analysis.")
    
    # Select reference years if not provided
    if reference_years is None:
        available_years = sorted(trade_data[year_col].unique())
        if not available_years:
            logger.error("No years available in trade data.")
            raise ValueError("No years available in trade data for relationship analysis.")
        # Use last 5 years or all if fewer
        reference_years = available_years[-min(5, len(available_years)):]
        logger.info(f"Selected years for analysis: {reference_years}")
    
    # Load additional data if needed
    if distance_data is None:
        try:
            distance_data = load_data(source_name="country_distances")
            logger.info("Loaded distance data from data source.")
        except Exception as e:
            logger.warning(f"Failed to load distance data: {e}. Analysis will proceed without distance information.")
    
    if gdp_data is None:
        try:
            gdp_data = load_data(source_name="world_bank", indicator="NY.GDP.MKTP.CD")
            logger.info("Loaded GDP data from data source.")
        except Exception as e:
            logger.warning(f"Failed to load GDP data: {e}. Analysis will proceed without GDP information.")
    
    # Prepare results container
    results = {}
    
    # Analyze each country pair
    for exporter, importer in country_pairs:
        logger.debug(f"Analyzing trade relationship: {exporter} -> {importer}")
        
        # Filter trade data for this pair
        pair_data = trade_data[
            (trade_data[exporter_col] == exporter) & 
            (trade_data[importer_col] == importer) &
            (trade_data[year_col].isin(reference_years))
        ].copy()
        
        if pair_data.empty:
            logger.warning(f"No trade data found for {exporter} -> {importer} in years {reference_years}")
            results[f"{exporter}_{importer}"] = {"status": "No data available"}
            continue
        
        # Calculate basic trade statistics
        total_trade = pair_data[value_col].sum()
        avg_annual_trade = pair_data.groupby(year_col, observed=True)[value_col].sum().mean()
        year_on_year_growth = pair_data.groupby(year_col, observed=True)[value_col].sum().pct_change().mean() * 100
        
        # Get distance if available
        distance = None
        if distance_data is not None:
            try:
                distance_row = distance_data[
                    ((distance_data["origin"] == exporter) & (distance_data["destination"] == importer)) |
                    ((distance_data["origin"] == importer) & (distance_data["destination"] == exporter))
                ]
                if not distance_row.empty and "distance" in distance_row.columns:
                    distance = distance_row["distance"].iloc[0]
            except Exception as e:
                logger.warning(f"Error retrieving distance for {exporter}-{importer}: {e}")
        
        # Get GDP data if available
        exporter_gdp = importer_gdp = None
        if gdp_data is not None:
            try:
                for country, gdp_var in [(exporter, "exporter_gdp"), (importer, "importer_gdp")]:
                    country_gdp = gdp_data[
                        (gdp_data["country_code"] == country) & 
                        (gdp_data[year_col].isin(reference_years))
                    ]
                    if not country_gdp.empty and "value" in country_gdp.columns:
                        locals()[gdp_var] = country_gdp["value"].mean()
            except Exception as e:
                logger.warning(f"Error retrieving GDP data: {e}")
        
        # Store results
        results[f"{exporter}_{importer}"] = {
            "exporter": exporter,
            "importer": importer,
            "years": sorted(pair_data[year_col].unique()),
            "total_trade": total_trade,
            "avg_annual_trade": avg_annual_trade,
            "year_on_year_growth": year_on_year_growth,
            "distance": distance,
            "exporter_gdp": exporter_gdp,
            "importer_gdp": importer_gdp,
            # Calculate expected trade based on gravity model if all data is available
            "trade_potential": _calculate_trade_potential_v2(
                exporter_gdp, importer_gdp, distance, total_trade
            ) if all(v is not None for v in [exporter_gdp, importer_gdp, distance]) else None
        }
    
    logger.info(f"Analyzed {len(results)} trade relationships.")
    return results

def _identify_top_country_pairs_v2(
    trade_data: pd.DataFrame,
    exporter_col: str,
    importer_col: str,
    value_col: str,
    top_n: int = 5
) -> List[Tuple[str, str]]:
    """Helper to identify top trading country pairs by volume."""
    # Group by exporter-importer pair and sum trade values
    pair_totals = trade_data.groupby([exporter_col, importer_col], observed=True)[value_col].sum().reset_index()
    
    # Sort by total trade value and select top pairs
    top_pairs = pair_totals.sort_values(value_col, ascending=False).head(top_n)
    
    # Convert to list of tuples
    return [(row[exporter_col], row[importer_col]) for _, row in top_pairs.iterrows()]

def _calculate_trade_potential_v2(
    exporter_gdp: Optional[float],
    importer_gdp: Optional[float],
    distance: Optional[float],
    actual_trade: float
) -> Dict[str, float]:
    """
    Calculate trade potential based on gravity model (V2).
    
    Returns dict with expected trade and over/under-trading percentage.
    """
    if any(v is None for v in [exporter_gdp, importer_gdp, distance]) or distance == 0:
        return None
    
    # Simple gravity model: Trade ~ (GDP_i * GDP_j) / Distance
    # Constants derived from empirical research on gravity models
    gdp_elasticity = 0.9  # Typical elasticity coefficient for GDP
    distance_elasticity = -0.7  # Typical elasticity coefficient for distance
    
    # Calculate expected trade (millions of USD)
    expected_trade = (
        (exporter_gdp ** gdp_elasticity) * 
        (importer_gdp ** gdp_elasticity) / 
        (distance ** abs(distance_elasticity))
    ) * 1e-6  # Scale factor
    
    # Calculate over/under-trading
    if expected_trade > 0:
        trade_ratio = (actual_trade / expected_trade)
        over_under_percent = (trade_ratio - 1) * 100
    else:
        trade_ratio = 0
        over_under_percent = 0
    
    return {
        "expected_trade": expected_trade,
        "trade_ratio": trade_ratio,
        "over_under_percent": over_under_percent,
        "assessment": "Over-trading" if over_under_percent > 20 else 
                      "Under-trading" if over_under_percent < -20 else 
                      "Trading at expected level"
    }

@memoize(ttl=3600, level=StorageTier.MEMORY)
def prepare_gravity_model_visualization_data_v2(
    model_result: GravityModelResultV2,
    actual_trade_data: pd.DataFrame = None,
    country_focus: str = None,
    exporter_col: str = "exporter_iso",
    importer_col: str = "importer_iso",
    year_col: str = "year",
    value_col: str = "trade_value_usd",
    prediction_col: str = "predicted_trade"
) -> pd.DataFrame:
    """
    Prepare gravity model results for visualization (V2).
    
    Args:
        model_result: Results from estimate_gravity_model_v2
        actual_trade_data: Optional raw trade data for comparison
        country_focus: Optional country code to focus analysis on
        exporter_col: Column name for exporter country codes
        importer_col: Column name for importer country codes
        year_col: Column name for years
        value_col: Column name for actual trade values
        prediction_col: Column name for predicted trade values
        
    Returns:
        DataFrame with actual vs. predicted trade for visualization
    """
    logger.info("Preparing gravity model data for visualization.")
    
    # Check if model_result is valid
    if not isinstance(model_result, dict) or 'data' not in model_result:
        logger.error("Invalid model_result for visualization.")
        return pd.DataFrame()
    
    # Get the model data
    model_data = model_result['data']
    if not isinstance(model_data, pd.DataFrame) or model_data.empty:
        logger.error("Empty or invalid model data for visualization.")
        return pd.DataFrame()
    
    # Initialize visualization DataFrame
    viz_data = model_data.copy()
    
    # Filter by country_focus if provided
    if country_focus:
        viz_data = viz_data[
            (viz_data[exporter_col] == country_focus) | 
            (viz_data[importer_col] == country_focus)
        ]
        if viz_data.empty:
            logger.warning(f"No data found for country_focus: {country_focus}")
            return pd.DataFrame()
    
    # Check if predicted values exist in the model data
    if prediction_col not in viz_data.columns and 'predicted_log_trade' in viz_data.columns:
        # Convert from log scale if needed
        viz_data[prediction_col] = np.exp(viz_data['predicted_log_trade']) - LOG_TRADE_OFFSET
        logger.debug("Converted predicted_log_trade to predicted_trade for visualization.")
    elif prediction_col not in viz_data.columns:
        logger.error(f"No prediction column found in model data: {viz_data.columns}")
        return pd.DataFrame()
    
    # Add residuals (actual - predicted)
    if value_col in viz_data.columns:
        viz_data['residual'] = viz_data[value_col] - viz_data[prediction_col]
        viz_data['residual_pct'] = (viz_data['residual'] / viz_data[prediction_col]) * 100
        
        # Categorize over/under trading
        viz_data['trading_status'] = pd.cut(
            viz_data['residual_pct'],
            bins=[-float('inf'), -30, -10, 10, 30, float('inf')],
            labels=['Severe under-trading', 'Moderate under-trading', 'Expected level', 
                    'Moderate over-trading', 'Severe over-trading']
        )
    
    # Add country pair column for better visualization
    viz_data['country_pair'] = viz_data[exporter_col] + ' → ' + viz_data[importer_col]
    
    # Sort by the absolute residual to highlight largest deviations
    if 'residual' in viz_data.columns:
        viz_data['abs_residual'] = viz_data['residual'].abs()
        viz_data = viz_data.sort_values('abs_residual', ascending=False).reset_index(drop=True)
        viz_data = viz_data.drop(columns=['abs_residual'])
    
    logger.info(f"Prepared visualization data with {len(viz_data)} rows.")
    return viz_data

if __name__ == '__main__':
    # Project imports
    from yemen_trade_diagnostic.interfaces.logging_interface import configure_logging

    configure_logging(log_level=LogLevel.DEBUG, log_to_console=True, log_to_file=False)
    logger.info("Starting Gravity Model V2 example.")

    # Create Sample Data (Mimicking BACI and supplemental data)
    sample_trade = pd.DataFrame({
        't': [2020, 2020, 2021, 2021],      # year
        'i': [887, 156, 887, 4],    # exporter_iso (Yemen=887, China=156, Afghanistan=4)
        'j': [156, 887, 4, 887],    # importer_iso 
        'k': ['TOTAL', 'TOTAL', 'TOTAL', 'TOTAL'], # product_code (using TOTAL for aggregate example)
        'v': [100, 200, 150, 50]         # value
    })
    sample_gdp = pd.DataFrame({
        'country_code': [887, 156, 784, 4], # Assuming a column with country codes that match i and j
        't': [2020, 2020, 2020, 2020], # Assuming GDP is for a single year or needs merging by year
        'gdp_value': [10e9, 14e12, 2e12, 20e9] # GDP values
    })
    # For a multi-year model, GDP data should also be multi-year.
    # Simplified: Using 2020 GDP for all trade years in this example.
    sample_gdp_2020 = sample_gdp[sample_gdp['t'] == 2020][['country_code', 'gdp_value']]
    
    sample_distance = pd.DataFrame({
        'i': [887, 887, 156, 156, 4, 4],
        'j': [156, 4, 887, 4, 887, 156],
        'dist': [5000, 3000, 5000, 6000, 3000, 6000] # Distance in km
    })

    logger.debug(f"Sample trade data:\n{sample_trade}")
    logger.debug(f"Sample GDP data (2020):\n{sample_gdp_2020}")
    logger.debug(f"Sample distance data:\n{sample_distance}")

    # Estimate model for a specific year
    model_res_2020 = estimate_gravity_model_v2(
        sample_trade, sample_gdp_2020, sample_distance, 
        year=2020, 
        exporter_gdp_col='gdp_exp', importer_gdp_col='gdp_imp', # Custom names for merged GDP
        gdp_country_col='country_code', gdp_value_col='gdp_value'
    )
    
    if model_res_2020 and model_res_2020.get("parameters"):
        logger.info(f"Gravity Model Estimated Parameters (2020): {model_res_2020['parameters']}")
        logger.info(f"Gravity Model Statistics (2020): {model_res_2020['statistics']}")

        # Predict trade flows for Yemen (887)
        predicted_flows_yem = predict_trade_flows_v2(model_res_2020, country_code_to_analyze=887,
                                                     exporter_gdp_col='gdp_exp', importer_gdp_col='gdp_imp')
        logger.info(f"Predicted Trade Flows for Yemen (887) based on 2020 model:\n{predicted_flows_yem}")
    else:
        logger.error("Gravity model estimation failed for 2020.")

    logger.info("Gravity Model V2 example finished.")