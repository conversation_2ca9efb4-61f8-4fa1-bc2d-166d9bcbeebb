"""
Trade Flow Model for Yemen Trade Diagnostic (V2)

This module provides V2 functions for analyzing trade flows, including top exports,
top imports, top destinations, top import sources, and regional composition.
"""

# Standard library imports
from typing import Any, Dict, List, Optional, Tuple, Union

# Third-party imports
import numpy as np
import pandas as pd

# Project imports
from yemen_trade_diagnostic.data import DataSource, load_data
from yemen_trade_diagnostic.interfaces.cache_interface import StorageTier, memoize
from yemen_trade_diagnostic.interfaces.hardware_interface import AccelerationType, get_hardware_manager
from yemen_trade_diagnostic.interfaces.logging_interface import LogLevel, get_logger, log_execution_time
from yemen_trade_diagnostic.interfaces.validation_interface import (
    SchemaValidationRule, 
    ValidationIssueLevel, 
    ValidationResult, 
    get_validation_manager, 
    validate_schema
)
from yemen_trade_diagnostic.errors import protect, OperationType

logger = get_logger(__name__)

# --- V2 Adapted Validation Helpers ---
def _validate_trade_data_format_v2(df: pd.DataFrame, required_cols: List[str], df_name: str) -> ValidationResult:
    """
    Validate that a DataFrame has the required columns for trade data analysis.

    Args:
        df: DataFrame to validate
        required_cols: List of column names that must be present
        df_name: Name of the DataFrame for error messages

    Returns:
        ValidationResult with any validation issues
    """
    validation_result = ValidationResult()

    # Check if DataFrame is valid and non-empty
    if not isinstance(df, pd.DataFrame):
        validation_result.add_issue(f"{df_name} is not a valid DataFrame.", ValidationIssueLevel.ERROR)
        return validation_result

    if df.empty:
        logger.warning(f"{df_name} is empty. This may be valid if no data exists for the requested period.")
        # Empty DataFrame is not necessarily an error, but we should log it
        # We don't add this as an issue as empty DataFrames are handled gracefully

    # Check for missing columns
    missing = [col for col in required_cols if col not in df.columns]
    if missing:
        # Log the actual columns present to help with debugging
        logger.error(f"Missing columns in {df_name}: {missing}. Columns present: {df.columns.tolist()}")
        validation_result.add_issue(f"Missing columns in {df_name}: {missing}", ValidationIssueLevel.ERROR)

    return validation_result

@protect("analyze_trade_flows_v2", OperationType.COMPUTATION)
@memoize(ttl=3600*24, level=StorageTier.MEMORY)
@log_execution_time(logger=logger)
def analyze_trade_flows_v2(
    trade_df: pd.DataFrame,
    country_code: int = 887,
    value_col: str = "v",
    year_col: str = "t",
    product_col: str = "k",
    exporter_col: str = "i",
    importer_col: str = "j",
    trade_type: str = "export",
    time_period: Union[int, List[int], str] = "latest",
    partner_df: Optional[pd.DataFrame] = None,
    partner_code_col_in_trade: Optional[str] = None,
    partner_code_col_in_partner_df: Optional[str] = None,
    partner_name_col: Optional[str] = "partner_name"
) -> Dict[str, Any]:
    """Analyze trade flows with V2 interfaces."""
    logger.info(f"Analyzing {trade_type} flows for country {country_code} for period '{time_period}'.")
    hw_manager = get_hardware_manager()

    # Import here to avoid circular imports
    from yemen_trade_diagnostic.utils.column_mapping import map_dataframe_columns

    # Map columns to expected names
    trade_df = map_dataframe_columns(
        trade_df,
        [exporter_col, importer_col, year_col, product_col, value_col],
        # Add other potential columns like 'quantity' if needed for future extensions
        inplace=False,
        create_missing=True
    )

    # If we still don't have the expected columns, try to map from reporter_code/partner_code
    if exporter_col not in trade_df.columns and 'reporter_code' in trade_df.columns:
        logger.info(f"Mapping 'reporter_code' to '{exporter_col}'")
        trade_df[exporter_col] = trade_df['reporter_code']

    if importer_col not in trade_df.columns and 'partner_code' in trade_df.columns:
        logger.info(f"Mapping 'partner_code' to '{importer_col}'")
        trade_df[importer_col] = trade_df['partner_code']

    # Store original parameters to check if they were defaults
    param_exporter_col, param_importer_col, param_year_col, param_product_col, param_value_col = exporter_col, importer_col, year_col, product_col, value_col

    # Initialize effective column names with parameter values
    eff_exporter_col, eff_importer_col, eff_year_col, eff_product_col, eff_value_col = exporter_col, importer_col, year_col, product_col, value_col

    raw_to_semantic = {
        't': 'year', 'i': 'exporter_iso', 'j': 'importer_iso',
        'k': 'product_code', 'v': 'trade_value_usd', 'q': 'quantity'
    }
    semantic_to_raw = {v: k for k, v in raw_to_semantic.items()}

    # Set up column mapping to handle both raw and semantic column names
    column_mapping = {
        # Raw column names
        't': ['t', 'year', 'year_col'],
        'i': ['i', 'exporter_iso', 'exporter', 'exporter_code'],
        'j': ['j', 'importer_iso', 'importer', 'importer_code'],
        'k': ['k', 'product_code', 'product', 'hs_code', 'hs6'],
        'v': ['v', 'value', 'trade_value_usd', 'trade_value'],
        # Semantic column names (for reverse mapping if needed)
        'year': ['year', 't', 'year_col'],
        'exporter_iso': ['exporter_iso', 'i', 'exporter', 'exporter_code'],
        'importer_iso': ['importer_iso', 'j', 'importer', 'importer_code'],
        'product_code': ['product_code', 'k', 'product', 'hs_code', 'hs6'],
        'trade_value_usd': ['trade_value_usd', 'v', 'value', 'trade_value']
    }

    if isinstance(trade_df, pd.DataFrame) and not trade_df.empty:
        df_cols = trade_df.columns.tolist()
        # Check if parameters are raw defaults AND trade_df has corresponding semantic columns
        params_are_raw_defaults = (param_exporter_col == "i" and param_importer_col == "j" and
                                   param_year_col == "t" and param_product_col == "k" and param_value_col == "v")

        df_has_all_semantic = all(raw_to_semantic.get(raw_col) in df_cols for raw_col in ["i", "j", "t", "k", "v"])

        # Create a set of current parameter column names for validation
        current_param_cols_set = {param_exporter_col, param_importer_col, param_year_col, param_product_col, param_value_col}

        # Map requested columns to actual DataFrame columns if they don't exist directly
        # This handles both semantic and raw column naming conventions

        # Map exporter column
        if exporter_col not in df_cols and exporter_col in column_mapping:
            for alt in column_mapping[exporter_col]:
                if alt in df_cols:
                    eff_exporter_col = alt
                    logger.info(f"Mapped exporter column from '{exporter_col}' to '{eff_exporter_col}'")
                    break

        # Map importer column
        if importer_col not in df_cols and importer_col in column_mapping:
            for alt in column_mapping[importer_col]:
                if alt in df_cols:
                    eff_importer_col = alt
                    logger.info(f"Mapped importer column from '{importer_col}' to '{eff_importer_col}'")
                    break

        # Map year column
        if year_col not in df_cols and year_col in column_mapping:
            for alt in column_mapping[year_col]:
                if alt in df_cols:
                    eff_year_col = alt
                    logger.info(f"Mapped year column from '{year_col}' to '{eff_year_col}'")
                    break

        # Map product column
        if product_col not in df_cols and product_col in column_mapping:
            for alt in column_mapping[product_col]:
                if alt in df_cols:
                    eff_product_col = alt
                    logger.info(f"Mapped product column from '{product_col}' to '{eff_product_col}'")
                    break

        # Map value column
        if value_col not in df_cols and value_col in column_mapping:
            for alt in column_mapping[value_col]:
                if alt in df_cols:
                    eff_value_col = alt
                    logger.info(f"Mapped value column from '{value_col}' to '{eff_value_col}'")
                    break

        # Legacy column mapping for compatibility with older code
        if params_are_raw_defaults and df_has_all_semantic:
            logger.warning("Parameters are raw defaults and DataFrame has semantic columns. Adapting parameters to semantic.")
            eff_exporter_col = raw_to_semantic["i"]
            eff_importer_col = raw_to_semantic["j"]
            eff_year_col = raw_to_semantic["t"]
            eff_product_col = raw_to_semantic["k"]
            eff_value_col = raw_to_semantic["v"]
        # Else, if parameters are semantic AND trade_df has raw columns (less likely but for completeness)
        elif not params_are_raw_defaults and all(sem_col in current_param_cols_set for sem_col in raw_to_semantic.values()) and \
             all(raw_col in df_cols for raw_col in ["i", "j", "t", "k", "v"]):
            logger.warning("Parameters are semantic and DataFrame has raw columns. Adapting parameters to raw (this case might need review).")
            eff_exporter_col = semantic_to_raw.get(param_exporter_col, param_exporter_col)
            eff_importer_col = semantic_to_raw.get(param_importer_col, param_importer_col)
            eff_year_col = semantic_to_raw.get(param_year_col, param_year_col)
            eff_product_col = semantic_to_raw.get(param_product_col, param_product_col)
            eff_value_col = semantic_to_raw.get(param_value_col, param_value_col)
        # Otherwise, use the mapped columns from above

    required_trade_cols = [eff_exporter_col, eff_importer_col, eff_year_col, eff_product_col, eff_value_col]
    logger.info(f"Effective columns for validation and operations: {required_trade_cols}")

    validation_res = _validate_trade_data_format_v2(trade_df, required_trade_cols, "trade_df")

    if not validation_res.is_valid:
        # More descriptive error logging with detailed information
        issues_formatted = []
        for issue in validation_res.issues:
            issues_formatted.append(f"{issue.level.value}: {issue.message}")

        issues_str = "; ".join(issues_formatted)
        error_msg = f"Trade data validation failed: {issues_str}"

        # Log detailed column information for debugging
        logger.error(error_msg)
        logger.error(f"Expected columns: {required_trade_cols}")
        if isinstance(trade_df, pd.DataFrame):
            logger.error(f"Actual columns: {trade_df.columns.tolist()}")

        # Raise with a more detailed error message
        raise ValueError(error_msg)

    if trade_type.lower() == "export":
        flow_df = trade_df[trade_df[eff_exporter_col] == country_code].copy()
        actual_partner_col_in_trade = eff_importer_col
    elif trade_type.lower() == "import":
        flow_df = trade_df[trade_df[eff_importer_col] == country_code].copy()
        actual_partner_col_in_trade = eff_exporter_col
    else:
        raise ValueError(f"Invalid trade_type specified: {trade_type}")

    if partner_code_col_in_trade:
        actual_partner_col_in_trade = partner_code_col_in_trade

    if flow_df.empty:
        logger.warning(f"No {trade_type} data for country {country_code}.")
        return {
            "time_period_analyzed": str(time_period),
            "total_trade_value": 0.0,
            "product_aggregation": pd.DataFrame(),
            "top_products": pd.DataFrame(),
            "partner_aggregation": pd.DataFrame(),
            "top_partners": pd.DataFrame(),
            "total_trade_growth": pd.DataFrame()
        }

    time_period_display = str(time_period)
    if isinstance(time_period, str) and time_period.lower() == "latest":
        if not flow_df[eff_year_col].empty:
            latest_year = flow_df[eff_year_col].max()
            flow_df = flow_df[flow_df[eff_year_col] == latest_year]
            time_period_display = str(latest_year)
        else:
            time_period_display = "latest (no data)"
    elif isinstance(time_period, int):
        flow_df = flow_df[flow_df[eff_year_col] == time_period]
    elif isinstance(time_period, list) and all(isinstance(yr, int) for yr in time_period):
        flow_df = flow_df[flow_df[eff_year_col].isin(time_period)]
        time_period_display = ", ".join(map(str, time_period))
    else:
        raise ValueError(f"Invalid time_period format: {time_period}")

    if flow_df.empty:
        logger.warning(f"No data for country {country_code} in period {time_period_display}.")
        return {
            "time_period_analyzed": time_period_display,
            "total_trade_value": 0.0,
            "product_aggregation": pd.DataFrame(),
            "top_products": pd.DataFrame(),
            "partner_aggregation": pd.DataFrame(),
            "top_partners": pd.DataFrame(),
            "total_trade_growth": pd.DataFrame()
        }

    results = {"time_period_analyzed": time_period_display}

    display_partner_col = actual_partner_col_in_trade
    if partner_df is not None and not partner_df.empty and partner_code_col_in_partner_df and partner_name_col:
        if partner_code_col_in_partner_df in partner_df.columns and partner_name_col in partner_df.columns:
            flow_df = pd.merge(flow_df, partner_df[[partner_code_col_in_partner_df, partner_name_col]],
                               left_on=actual_partner_col_in_trade, right_on=partner_code_col_in_partner_df, how='left')
            display_partner_col = partner_name_col if partner_name_col in flow_df.columns else actual_partner_col_in_trade
        else:
            logger.warning("Partner DF missing specified code/name columns. Using partner codes.")
    elif partner_name_col and partner_name_col not in flow_df.columns: # if partner_df was None but name col expected
            flow_df[partner_name_col] = flow_df[actual_partner_col_in_trade] # map codes to names initially

    results['total_trade_value'] = flow_df[eff_value_col].sum()

    product_agg = flow_df.groupby([eff_product_col, eff_year_col], observed=True)[eff_value_col].sum().reset_index()
    if results['total_trade_value'] > 0:
        product_agg['share'] = (product_agg[eff_value_col] / results['total_trade_value']) * 100
    else:
        product_agg['share'] = 0.0
    results['product_aggregation'] = product_agg.sort_values(by=[eff_year_col, eff_value_col], ascending=[True, False])
    results['top_products'] = results['product_aggregation'].groupby(eff_year_col, group_keys=False).head(10) if not results['product_aggregation'].empty else pd.DataFrame()

    partner_agg_cols = [display_partner_col, eff_year_col] if display_partner_col != eff_year_col else [display_partner_col]
    partner_agg = flow_df.groupby(partner_agg_cols, observed=True)[eff_value_col].sum().reset_index()
    if results['total_trade_value'] > 0:
        partner_agg['share'] = (partner_agg[eff_value_col] / results['total_trade_value']) * 100
    else:
        partner_agg['share'] = 0.0
    results['partner_aggregation'] = partner_agg.sort_values(by=[eff_year_col, eff_value_col], ascending=[True, False])
    results['top_partners'] = results['partner_aggregation'].groupby(eff_year_col, group_keys=False).head(10) if not results['partner_aggregation'].empty else pd.DataFrame()

    if flow_df[eff_year_col].nunique() > 1:
        total_trade_yearly = flow_df.groupby(eff_year_col, observed=True)[eff_value_col].sum().reset_index()
        total_trade_yearly = total_trade_yearly.sort_values(by=eff_year_col)
        total_trade_yearly['yoy_growth_pct'] = total_trade_yearly[eff_value_col].pct_change() * 100
        results['total_trade_growth'] = total_trade_yearly
    else:
        results['total_trade_growth'] = pd.DataFrame()

    logger.info("Trade flow analysis completed.")
    return results

@memoize(ttl=3600*24, level=StorageTier.MEMORY)
def get_top_products_v2(trade_df: pd.DataFrame, country_code: int = 887, is_export: bool = True, top_n: int = 10, year: Optional[int] = None, **kwargs) -> pd.DataFrame:
    """Get top N products (V2). kwargs passed to analyze_trade_flows_v2."""
    trade_type = "export" if is_export else "import"
    time_period = year if year is not None else "latest"
    analysis = analyze_trade_flows_v2(trade_df, country_code=country_code, trade_type=trade_type, time_period=time_period, **kwargs)
    if "top_products" in analysis and isinstance(analysis["top_products"], pd.DataFrame):
        return analysis["top_products"].head(top_n)
    logger.warning("Could not retrieve 'top_products' DataFrame from analysis result.")
    return pd.DataFrame()

@memoize(ttl=3600*24, level=StorageTier.MEMORY)
def get_top_partners_v2(trade_df: pd.DataFrame, country_code: int = 887, is_export: bool = True, top_n: int = 10, year: Optional[int] = None, **kwargs) -> pd.DataFrame:
    """Get top N partners (V2). kwargs passed to analyze_trade_flows_v2."""
    trade_type = "export" if is_export else "import"
    time_period = year if year is not None else "latest"
    analysis = analyze_trade_flows_v2(trade_df, country_code=country_code, trade_type=trade_type, time_period=time_period, **kwargs)
    if "top_partners" in analysis and isinstance(analysis["top_partners"], pd.DataFrame):
        return analysis["top_partners"].head(top_n)
    logger.warning("Could not retrieve 'top_partners' DataFrame from analysis result.")
    return pd.DataFrame()

@protect("calculate_trade_flow_intensity_v2", OperationType.COMPUTATION)
@memoize(ttl=3600*24*7, level=StorageTier.DISK)
@log_execution_time(logger=logger)
def calculate_trade_flow_intensity_v2(
    trade_df: pd.DataFrame,
    country_code: int = 887,
    value_col: str = "v",
    year_col: str = "t",
    product_col: str = "k",
    exporter_col: str = "i",
    importer_col: str = "j",
    trade_type: str = "export",
    time_period: Union[int, List[int], str] = "latest",
    partner_df: Optional[pd.DataFrame] = None,
    partner_code_col_in_trade: Optional[str] = None,
    partner_code_col_in_partner_df: Optional[str] = None,
    partner_name_col: Optional[str] = "partner_name"
) -> pd.DataFrame:
    """Calculate trade flow intensity with V2 interfaces."""
    logger.info(f"Calculating trade flow intensity for country {country_code} for period '{time_period}'.")
    hw_manager = get_hardware_manager()

    required_trade_cols = [exporter_col, importer_col, year_col, product_col, value_col]
    validation_res = _validate_trade_data_format_v2(trade_df, required_trade_cols, "trade_df")
    if not validation_res.is_valid:
        raise ValueError(f"Trade data validation failed: {validation_res.issues}")

    if trade_type.lower() == "export":
        flow_df = trade_df[trade_df[exporter_col] == country_code].copy()
        actual_partner_col_in_trade = exporter_col
    elif trade_type.lower() == "import":
        flow_df = trade_df[trade_df[importer_col] == country_code].copy()
        actual_partner_col_in_trade = exporter_col
    else:
        raise ValueError(f"Invalid trade_type: {trade_type}")

    if partner_code_col_in_trade:
        actual_partner_col_in_trade = partner_code_col_in_trade

    if flow_df.empty:
        logger.warning(f"No {trade_type} data for country {country_code}.")
        return pd.DataFrame()

    time_period_display = str(time_period)
    if isinstance(time_period, str) and time_period.lower() == "latest":
        if not flow_df[year_col].empty:
            latest_year = flow_df[year_col].max()
            flow_df = flow_df[flow_df[year_col] == latest_year]
            time_period_display = str(latest_year)
        else:
            time_period_display = "latest (no data)"
    elif isinstance(time_period, int):
        flow_df = flow_df[flow_df[year_col] == time_period]
    elif isinstance(time_period, list) and all(isinstance(yr, int) for yr in time_period):
        flow_df = flow_df[flow_df[year_col].isin(time_period)]
        time_period_display = ", ".join(map(str, time_period))
    else:
        raise ValueError(f"Invalid time_period format: {time_period}")

    if flow_df.empty:
        logger.warning(f"No data for country {country_code} in period {time_period_display}.")
        return pd.DataFrame()

    # Calculate intensity (normalized trade values)
    total_trade = flow_df[value_col].sum()
    if total_trade > 0:
        flow_df['trade_intensity'] = flow_df[value_col] / total_trade
    else:
        flow_df['trade_intensity'] = 0.0

    logger.info("Trade flow intensity calculation completed.")
    return flow_df

@protect("calculate_trade_flow_v2", OperationType.COMPUTATION)
@log_execution_time(logger=logger)
def calculate_trade_flow_v2(
    trade_data: pd.DataFrame,
    analysis_year: Optional[int] = None,
    exporter_col: str = 'exporter_iso',
    importer_col: str = 'importer_iso',
    product_col: str = 'product_code',
    value_col: str = 'value',
    year_col: str = 'year'
) -> pd.DataFrame:
    """
    Calculates total trade flow values for each exporter-importer-product combination for a given year.
    Args:
        trade_data: Input DataFrame with trade data.
        analysis_year: Optional year to filter the data for.
        exporter_col: Name of the exporter column.
        importer_col: Name of the importer column.
        product_col: Name of the product code column.
        value_col: Name of the trade value column.
        year_col: Name of the year column.
    Returns:
        DataFrame with [exporter_col, importer_col, product_col, year_col (if not filtered), 'total_flow_value']
    """
    logger.info(f"Calculating trade flows. Analysis year: {analysis_year or 'all available'}")

    required_cols = [exporter_col, importer_col, product_col, value_col, year_col]
    # Log the expected columns
    logger.info(f"Required columns for trade flow calculation: {required_cols}")

    # Log actual columns for debugging
    if isinstance(trade_data, pd.DataFrame):
        logger.info(f"Actual columns in trade_data: {trade_data.columns.tolist()}")

        # Try to automatically adapt to semantic column names if needed
        raw_to_semantic = {
            't': 'year',
            'i': 'exporter_iso',
            'j': 'importer_iso',
            'k': 'product_code',
            'v': 'trade_value_usd',
            'q': 'quantity'
        }

        # Check if data seems to be in semantic format but we're using raw column names
        if (all(col not in trade_data.columns for col in [exporter_col, importer_col, product_col, value_col, year_col]) and
            all(raw_to_semantic.get(col, '') in trade_data.columns for col in [exporter_col, importer_col, product_col, value_col, year_col])):
            logger.warning("Detected semantic column names but using raw column names in parameters. Adapting automatically.")
            # Update column references to use semantic names
            exporter_col = raw_to_semantic.get(exporter_col, exporter_col)
            importer_col = raw_to_semantic.get(importer_col, importer_col)
            product_col = raw_to_semantic.get(product_col, product_col)
            value_col = raw_to_semantic.get(value_col, value_col)
            year_col = raw_to_semantic.get(year_col, year_col)
            # Update required columns
            required_cols = [exporter_col, importer_col, product_col, value_col, year_col]
            logger.info(f"Adapted to semantic column names: {required_cols}")

    # Perform validation with potentially updated column names
    validation_res = _validate_trade_data_format_v2(trade_data, required_cols, "trade_data_for_flow_calc")
    if not validation_res.is_valid:
        # More descriptive error logging with detailed information
        issues_formatted = []
        for issue in validation_res.issues:
            issues_formatted.append(f"{issue.level.value}: {issue.message}")
        issues_str = "; ".join(issues_formatted)
        error_msg = f"Trade data validation for flow calculation failed: {issues_str}"
        # Log detailed column information for debugging
        logger.error(error_msg)
        logger.error(f"Expected columns: {required_cols}")
        if isinstance(trade_data, pd.DataFrame):
            logger.error(f"Actual columns: {trade_data.columns.tolist()}")
        # Raise with a more detailed error message
        raise ValueError(error_msg)

    df_to_process = trade_data.copy()

    if analysis_year is not None:
        df_to_process = df_to_process[df_to_process[year_col] == analysis_year]
        if df_to_process.empty:
            logger.warning(f"No trade data for analysis year {analysis_year}.")
            raise ValueError(f"No trade data found for the specified analysis year: {analysis_year}.")
        group_by_cols = [exporter_col, importer_col, product_col]
    else:
        group_by_cols = [exporter_col, importer_col, product_col, year_col]

    flow_summary = df_to_process.groupby(group_by_cols, observed=True, as_index=False)[value_col].sum()
    flow_summary.rename(columns={value_col: 'total_flow_value'}, inplace=True)

    logger.info(f"Trade flow calculation complete. Shape: {flow_summary.shape}")
    return flow_summary

if __name__ == '__main__':
    # Project imports
    from yemen_trade_diagnostic.interfaces.logging_interface import configure_logging
    configure_logging(log_level=LogLevel.DEBUG, log_to_console=True, log_to_file=False)

    logger.info("Starting Trade Flow Model V2 example.")

    sample_trade_data = pd.DataFrame({
        't': [2020, 2020, 2021, 2021, 2020, 2021],
        'i': [887, 887, 887, 156, 887, 156],
        'j': [156, 784, 156, 887, 784, 887],
        'k': ['010101', '020101', '010101', '030101', '020101', '030101'],
        'v': [1000, 1500, 1200, 800, 1800, 900]
    })

    sample_partner_data = pd.DataFrame({
        'country_code_iso3_num': [156, 784],
        'country_name_full': ['China', 'United Arab Emirates']
    })

    logger.debug(f"Sample trade data:\n{sample_trade_data}")
    logger.debug(f"Sample partner data:\n{sample_partner_data}")

    yemen_exports_latest = analyze_trade_flows_v2(
        sample_trade_data.copy(), country_code=887, trade_type="export", time_period="latest",
        partner_df=sample_partner_data.copy(),
        partner_code_col_in_partner_df='country_code_iso3_num',
        partner_name_col='country_name_full'
    )

    if isinstance(yemen_exports_latest, dict):
        logger.info(f"  Time Period: {yemen_exports_latest.get('time_period_analyzed')}")
        logger.info(f"  Total Value: {yemen_exports_latest.get('total_trade_value')}")
        logger.info(f"  Top Products:\n{yemen_exports_latest.get('top_products')}")
        logger.info(f"  Top Partners:\n{yemen_exports_latest.get('top_partners')}")

    top_imports_2020 = get_top_products_v2(sample_trade_data.copy(), country_code=887, is_export=False, top_n=5, year=2020, product_col='k')
    logger.info(f"Top 5 Yemen Imports (2020):\n{top_imports_2020}")

    top_china_export_partners_2021 = get_top_partners_v2(
        sample_trade_data.copy(), country_code=156, is_export=True, top_n=3, year=2021,
        partner_df=sample_partner_data.copy(),
        partner_code_col_in_partner_df='country_code_iso3_num',
        partner_name_col='country_name_full'
    )
    logger.info(f"Top 3 China Export Partners (2021):\n{top_china_export_partners_2021}")

    logger.info("Trade Flow Model V2 example finished.")
