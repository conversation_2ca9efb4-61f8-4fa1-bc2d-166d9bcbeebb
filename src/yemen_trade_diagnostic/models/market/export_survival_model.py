"""
Export Survival Model for Yemen Trade Diagnostic (V2)

This module provides V2 functions for analyzing the survival of export relationships,
using V2 interfaces for logging, error handling, caching, validation, and hardware acceleration.
"""
# Standard library imports
import json  # For pretty printing dicts in example
from typing import Any, Dict, List, Optional, Tuple, Union

# Third-party imports
import numpy as np
import pandas as pd

# Project imports
from yemen_trade_diagnostic.interfaces.cache_interface import StorageTier, memoize
from yemen_trade_diagnostic.interfaces.hardware_interface import AccelerationType, get_hardware_manager
from yemen_trade_diagnostic.interfaces.logging_interface import LogLevel, get_logger, log_execution_time
from yemen_trade_diagnostic.interfaces.validation_interface import SchemaValidationRule, ValidationIssueLevel, ValidationResult, get_validation_manager
from yemen_trade_diagnostic.errors import protect, OperationType

logger = get_logger(__name__)

# --- Constants (can be moved to a config file/module) ---
DEFAULT_COUNTRY_CODE_V2 = 887  # Yemen's country code
DEFAULT_CONFLICT_ONSET_YEAR_V2 = 2015
DEFAULT_PRE_CONFLICT_YEARS_V2 = 3
DEFAULT_MIN_EXPORT_VALUE_V2 = 100000  # $100,000
DEFAULT_SURVIVAL_THRESHOLD_V2 = 0.25  # Allow for 75% decline
DEFAULT_TOP_ITEMS_COUNT_V2 = 8
YEARS_TO_ANALYZE_AFTER_CONFLICT_V2 = 6
VALUE_QUARTILE_LABELS_V2 = ['Q1 (Lowest)', 'Q2', 'Q3', 'Q4 (Highest)']
PRODUCT_DIVERSITY_BINS_V2 = [0, 1, 3, 5, float('inf')]
PRODUCT_DIVERSITY_LABELS_V2 = ['Single Partner', '2-3 Partners', '4-5 Partners', '6+ Partners']

# --- Validation Schema --- (Example - can be more detailed)
BACI_DATA_SCHEMA_V2 = {
    'required_columns': ['exporter', 'year', 'product', 'value', 'importer'],
    'column_types': {
        'exporter': 'integer',
        'year': 'integer',
        'product': 'string', # Or more specific if HS codes are always int-like strings
        'value': 'numeric',
        'importer': 'integer'
    },
    'no_missing': ['exporter', 'year', 'product', 'value', 'importer']
}

SURVIVAL_DF_SCHEMA_V2 = {
    'required_columns': ['importer', 'hs2', 'pre_conflict_value', 'year', 'years_since_onset', 'current_value', 'survived', 'value_ratio'],
    'column_types': {
        'importer': 'integer',
        'hs2': 'string',
        'pre_conflict_value': 'numeric',
        'year': 'integer',
        'years_since_onset': 'integer',
        'current_value': 'numeric',
        'survived': 'boolean',
        'value_ratio': 'numeric'
    }
}

class ExportRelationshipV2:
    """Represents an export relationship (V2)."""
    def __init__(self, importer: int, hs2: str, pre_conflict_value: float,
                 partner_name: str, product_name: str):
        self.importer = importer
        self.hs2 = hs2
        self.pre_conflict_value = pre_conflict_value
        self.partner_name = partner_name
        self.product_name = product_name

    def check_survival(self, year: int, conflict_onset_year: int, 
                      current_value: float, min_export_value: float, survival_threshold: float) -> dict:
        """Check if relationship survived and return survival data (V2)."""
        if self.pre_conflict_value < 0: # Basic sanity check
            logger.warning("Pre-conflict value cannot be negative.")
            # Decide on handling, e.g. return as not survived or raise specific error caught by decorator

        years_since_onset = year - conflict_onset_year
        survived = current_value >= min_export_value * survival_threshold

        return {
            'importer': self.importer,
            'partner_name': self.partner_name,
            'hs2': self.hs2,
            'product_name': self.product_name,
            'pre_conflict_value': self.pre_conflict_value,
            'year': year,
            'years_since_onset': years_since_onset,
            'current_value': current_value,
            'survived': survived,
            'value_ratio': current_value / self.pre_conflict_value if self.pre_conflict_value > 0 else 0
        }

class ExportSurvivalAnalyzerV2:
    """Analyzes export relationship survival (V2)."""

    def __init__(self, baci_data: pd.DataFrame, 
                 country_code: int = DEFAULT_COUNTRY_CODE_V2,
                 conflict_onset_year: int = DEFAULT_CONFLICT_ONSET_YEAR_V2,
                 pre_conflict_years: int = DEFAULT_PRE_CONFLICT_YEARS_V2,
                 min_export_value: float = DEFAULT_MIN_EXPORT_VALUE_V2,
                 survival_threshold: float = DEFAULT_SURVIVAL_THRESHOLD_V2,
                 iso_to_name: Optional[Dict[str, str]] = None,
                 hs2_to_description: Optional[Dict[str, str]] = None):
        
        self.logger = get_logger(f"{__name__}.ExportSurvivalAnalyzerV2") # Instance specific logger
        self.hw_manager = get_hardware_manager()
        self.validation_manager = get_validation_manager()

        self.logger.debug(f"Initializing ExportSurvivalAnalyzerV2 for country {country_code}, conflict year {conflict_onset_year}")

        # Validate baci_data
        val_result = self.validation_manager.validate_schema(baci_data, BACI_DATA_SCHEMA_V2)
        if not val_result.is_valid:
            error_msg = f"Invalid baci_data provided: {val_result}"
            self.logger.error(error_msg)
            # report_error(ValueError(error_msg), component="ExportSurvivalAnalyzerV2.__init__", severity=ErrorSeverity.CRITICAL) # Handled by decorator
            # self.baci_data = pd.DataFrame() # Set to empty to prevent processing
            raise ValueError(error_msg) # Raise if validation fails critically
        else:
            self.baci_data = baci_data
        
        self.country_code = country_code
        self.conflict_onset_year = conflict_onset_year
        self.pre_conflict_years = pre_conflict_years
        self.min_export_value = min_export_value
        self.survival_threshold = survival_threshold
        self.iso_to_name = iso_to_name or {}
        self.hs2_to_description = hs2_to_description or {}

    @protect("process_export_survival_data", OperationType.COMPUTATION)
    @memoize(ttl=3600, level=StorageTier.MEMORY) # Cache results for 1 hour
    @log_execution_time(logger=logger, level=LogLevel.INFO)
    def process_export_survival_data(self) -> Optional[Dict[str, Any]]:
        self.logger.info("Processing export survival data (V2)")
        if self.baci_data.empty:
            self.logger.warning("BACI data is empty or invalid. Cannot perform survival analysis.")
            # return None # No data to analyze, this should probably be an error if called.
            raise ValueError("BACI data is empty, cannot perform survival analysis.")

        country_exports = self._extract_country_exports()
        if country_exports.empty:
            self.logger.warning(f"No export data found for country {self.country_code}. Cannot perform survival analysis.")
            # return None
            # This could be a valid state (no exports), so return a result indicating that.
            return {'significant_relationships_count': 0, 'overall': pd.DataFrame(), 'by_product': pd.DataFrame(), 'top_products': pd.DataFrame(), 'by_partner': pd.DataFrame(), 'top_partners': pd.DataFrame()}

        significant_relationships = self._get_significant_relationships(country_exports)
        if not significant_relationships:
            self.logger.warning("No significant export relationships found for survival analysis.")
            return {'significant_relationships_count': 0, 'overall': pd.DataFrame(), 'by_product': pd.DataFrame(), 'top_products': pd.DataFrame(), 'by_partner': pd.DataFrame(), 'top_partners': pd.DataFrame()} 

        survival_df = self._track_relationship_survival(country_exports, significant_relationships)
        if survival_df.empty:
            self.logger.warning("No survival data could be calculated (e.g., no post-conflict data for significant relationships).")
            # Return partial results if needed, or a structure indicating no survival data
            return {
                'significant_relationships_count': len(significant_relationships), 
                'overall': pd.DataFrame(), 
                'by_product': pd.DataFrame(), 
                'top_products': pd.DataFrame(), 
                'by_partner': pd.DataFrame(), 
                'top_partners': pd.DataFrame(),
                'status': 'No post-conflict survival data for identified relationships'
            } 

        results = self._calculate_survival_metrics(survival_df, len(significant_relationships))
        self.logger.info(f"Export survival data processed with {len(significant_relationships)} significant pre-conflict relationships.")
        return results

    @protect("_extract_country_exports", OperationType.COMPUTATION)
    def _extract_country_exports(self) -> pd.DataFrame:
        self.logger.debug("Extracting country exports.")
        country_exports = self.baci_data[self.baci_data['exporter'] == self.country_code].copy()
        if country_exports.empty:
            return pd.DataFrame()
            
        country_exports['hs2'] = country_exports['product'].astype(str).str[:2]
        if self.hw_manager.is_hardware_acceleration_available():
            try:
                country_exports = self.hw_manager.accelerate_dataframe(country_exports, operations=['filter', 'assign'])
                self.logger.debug("Applied DataFrame acceleration to country_exports.")
            except Exception as e:
                logger.warning(f"DataFrame acceleration failed: {e}")
        return country_exports

    @protect("_get_significant_relationships", OperationType.COMPUTATION)
    def _get_significant_relationships(self, country_exports: pd.DataFrame) -> List[ExportRelationshipV2]:
        self.logger.debug("Identifying significant pre-conflict relationships.")
        pre_conflict_start = self.conflict_onset_year - self.pre_conflict_years
        pre_conflict_end = self.conflict_onset_year - 1

        pre_conflict_exports = country_exports[
            (country_exports['year'] >= pre_conflict_start) & 
            (country_exports['year'] <= pre_conflict_end)
        ]
        if pre_conflict_exports.empty: return []

        relationship_data = pre_conflict_exports.groupby(['importer', 'hs2'], observed=True)['value'].sum().reset_index()
        significant_data = relationship_data[relationship_data['value'] >= self.min_export_value]

        relationships = []
        for _, row in significant_data.iterrows():
            importer = int(row['importer'])
            hs2 = str(row['hs2'])
            partner_name = self.iso_to_name.get(str(importer), f"Partner {importer}")
            product_name = self.hs2_to_description.get(hs2, f"HS {hs2}")
            relationships.append(ExportRelationshipV2(importer, hs2, float(row['value']), partner_name, product_name))
        self.logger.debug(f"Identified {len(relationships)} significant relationships.")
        return relationships

    @protect("_track_relationship_survival", OperationType.COMPUTATION)
    def _track_relationship_survival(self, country_exports: pd.DataFrame, 
                                    relationships: List[ExportRelationshipV2]) -> pd.DataFrame:
        self.logger.debug("Tracking relationship survival.")
        years_to_check = list(range(self.conflict_onset_year, self.conflict_onset_year + YEARS_TO_ANALYZE_AFTER_CONFLICT_V2))
        available_years = sorted([y for y in years_to_check if y in country_exports['year'].unique()])
        if not available_years: return pd.DataFrame()

        all_survival_data = []
        
        # Prepare a lookup for faster year data access
        country_exports_grouped_small = country_exports[country_exports['year'].isin(available_years)]
        value_lookup = country_exports_grouped_small.groupby(['year', 'importer', 'hs2'], observed=True)['value'].sum()

        for rel in relationships:
            for year_val in available_years:
                current_val = value_lookup.get((year_val, rel.importer, rel.hs2), 0)
                all_survival_data.append(rel.check_survival(year_val, self.conflict_onset_year, current_val, self.min_export_value, self.survival_threshold))
        
        if not all_survival_data: return pd.DataFrame()
        result_df = pd.DataFrame(all_survival_data)
        if self.hw_manager.is_hardware_acceleration_available():
            try:
                result_df = self.hw_manager.accelerate_dataframe(result_df, operations=['general'])
                self.logger.debug("Applied DataFrame acceleration to survival tracking results.")
            except Exception as e:
                logger.warning(f"DataFrame acceleration failed: {e}")
        return result_df

    @protect("_calculate_survival_metrics", OperationType.COMPUTATION)
    def _calculate_survival_metrics(self, survival_df: pd.DataFrame, relationship_count: int) -> Dict[str, Any]:
        self.logger.debug("Calculating survival metrics.")
        # Basic validation of survival_df
        val_res = self.validation_manager.validate_schema(survival_df, SURVIVAL_DF_SCHEMA_V2) # Assuming SURVIVAL_DF_SCHEMA_V2 exists
        if not val_res.is_valid:
            self.logger.error(f"Survival DataFrame is invalid for metrics calculation: {val_res}")
            # return {'error': "Invalid survival_df", 'significant_relationships_count': relationship_count}
            # Validator with reraise=True should handle this if schema validation is strict
            # Or, raise specific error here.
            raise ValueError(f"Invalid survival_df for metrics calculation: {val_res}")

        return {
            'overall': self._calculate_overall_survival(survival_df),
            'by_product': self._calculate_product_survival(survival_df),
            'top_products': self._get_top_product_survival(survival_df),
            'by_partner': self._calculate_partner_survival(survival_df),
            'top_partners': self._get_top_partner_survival(survival_df),
            'significant_relationships_count': relationship_count
        }

    def _aggregate_survival_data(self, df: pd.DataFrame, group_by_cols: List[str], agg_dict: Dict) -> pd.DataFrame:
        # Helper for consistent aggregation, potentially with hardware acceleration hint
        if df.empty: return pd.DataFrame()
        # In V2, specific `fast_groupby_agg` might not exist directly in hw_manager.
        # Standard groupby is used, relying on prior df acceleration or future specific hw_manager methods.
        return df.groupby(group_by_cols, observed=True).agg(agg_dict).reset_index()

    def _calculate_overall_survival(self, survival_df: pd.DataFrame) -> pd.DataFrame:
        self.logger.debug("Calculating overall survival.")
        agg_dict = {'survived': 'mean', 'value_ratio': 'mean', 'importer': 'nunique', 'hs2': 'nunique'}
        df = self._aggregate_survival_data(survival_df, ['years_since_onset'], agg_dict)
        return df.rename(columns={'survived': 'survival_rate', 'value_ratio': 'avg_value_ratio', 'importer': 'partner_count', 'hs2': 'product_count'})

    def _calculate_product_survival(self, survival_df: pd.DataFrame) -> pd.DataFrame:
        self.logger.debug("Calculating product survival.")
        agg_dict = {'survived': 'mean', 'value_ratio': 'mean', 'importer': 'nunique'}
        df = self._aggregate_survival_data(survival_df, ['hs2', 'product_name', 'years_since_onset'], agg_dict)
        return df.rename(columns={'survived': 'survival_rate', 'value_ratio': 'avg_value_ratio', 'importer': 'partner_count'})

    def _get_top_product_survival(self, survival_df: pd.DataFrame) -> pd.DataFrame:
        self.logger.debug("Getting top product survival.")
        if survival_df.empty or 'years_since_onset' not in survival_df.columns: return pd.DataFrame()
        first_year_data = survival_df[survival_df['years_since_onset'] == 0]
        if first_year_data.empty: return pd.DataFrame()
        
        product_partner_counts = self._aggregate_survival_data(first_year_data, ['hs2', 'product_name'], {'importer': 'nunique'})
        product_partner_counts = product_partner_counts.rename(columns={'importer': 'partner_count'})
        top_products = product_partner_counts.sort_values('partner_count', ascending=False).head(DEFAULT_TOP_ITEMS_COUNT_V2)
        
        survival_by_product = self._calculate_product_survival(survival_df)
        return survival_by_product[survival_by_product['hs2'].isin(top_products['hs2'])]

    def _calculate_partner_survival(self, survival_df: pd.DataFrame) -> pd.DataFrame:
        self.logger.debug("Calculating partner survival.")
        agg_dict = {'survived': 'mean', 'value_ratio': 'mean', 'hs2': 'nunique'}
        df = self._aggregate_survival_data(survival_df, ['importer', 'partner_name', 'years_since_onset'], agg_dict)
        return df.rename(columns={'survived': 'survival_rate', 'value_ratio': 'avg_value_ratio', 'hs2': 'product_count'})

    def _get_top_partner_survival(self, survival_df: pd.DataFrame) -> pd.DataFrame:
        self.logger.debug("Getting top partner survival.")
        if survival_df.empty or 'years_since_onset' not in survival_df.columns: return pd.DataFrame()
        first_year_data = survival_df[survival_df['years_since_onset'] == 0]
        if first_year_data.empty: return pd.DataFrame()

        partner_product_counts = self._aggregate_survival_data(first_year_data, ['importer', 'partner_name'], {'hs2': 'nunique'})
        partner_product_counts = partner_product_counts.rename(columns={'hs2': 'product_count'})
        top_partners = partner_product_counts.sort_values('product_count', ascending=False).head(DEFAULT_TOP_ITEMS_COUNT_V2)

        survival_by_partner = self._calculate_partner_survival(survival_df)
        return survival_by_partner[survival_by_partner['importer'].isin(top_partners['importer'])]

class SurvivalDataVisualizerV2:
    """Prepares export survival data for visualization (V2)."""

    @staticmethod
    @memoize(level=StorageTier.MEMORY)
    @protect("analyze_survival_by_importer", OperationType.COMPUTATION)
    @log_execution_time(logger=logger, level=LogLevel.DEBUG)
    def get_survival_chart_data_v2(survival_results: Optional[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        logger.debug("Getting survival chart data (V2).")
        if not survival_results or not isinstance(survival_results.get('overall'), pd.DataFrame) or survival_results['overall'].empty:
            logger.warning("No valid survival results for chart data extraction.")
            return None

        overall_survival = survival_results['overall']
        chart_data = {
            'years_survived': overall_survival['years_since_onset'].tolist(),
            'survival_rate': overall_survival['survival_rate'].tolist(),
            'avg_value_ratio': overall_survival['avg_value_ratio'].tolist(),
            'partner_count': overall_survival['partner_count'].tolist(),
            'product_count': overall_survival['product_count'].tolist(),
            'significant_relationships_count': survival_results.get('significant_relationships_count', 0)
        }
        top_products_df = survival_results.get('top_products')
        product_data = {}
        if isinstance(top_products_df, pd.DataFrame) and not top_products_df.empty:
            for hs2_code, group in top_products_df.groupby('hs2', observed=True):
                product_name = group['product_name'].iloc[0] if not group['product_name'].empty else f"HS {hs2_code}"
                product_data[str(hs2_code)] = {
                    'name': product_name,
                    'years_survived': group['years_since_onset'].tolist(),
                    'survival_rate': group['survival_rate'].tolist()
                }
        chart_data['top_products'] = product_data
        logger.debug("Survival chart data prepared.")
        return chart_data

class SurvivalFactorAnalyzerV2:
    """Analyzes factors affecting export relationship survival (V2)."""

    @protect("analyze_survival_factors_v2", OperationType.MODEL_CALCULATION)
    @staticmethod
    @memoize(level=StorageTier.MEMORY)
    @protect("analyze_survival_factors", OperationType.COMPUTATION)
    @log_execution_time(logger=logger, level=LogLevel.DEBUG)
    def analyze_survival_factors_v2(survival_df: pd.DataFrame) -> Dict[str, Any]:
        logger.debug("Analyzing survival factors (V2).")
        validation_manager = get_validation_manager()
        val_res = validation_manager.validate_schema(survival_df, SURVIVAL_DF_SCHEMA_V2)
        if not val_res.is_valid or survival_df.empty:
            logger.error(f"Invalid survival_df for factor analysis: {val_res}")
            # return {'error': "Invalid survival_df for factor analysis"}
            raise ValueError(f"Invalid or empty survival_df provided for factor analysis: {val_res}")

        hw_manager = get_hardware_manager()
        first_year_df = survival_df[survival_df['years_since_onset'] == 1].copy()
        if first_year_df.empty: 
            logger.warning("No data for first year after conflict in survival_df for factor analysis.")
            # return {'error': "No data for first year after conflict"}
            # Return empty results for factors if this key data is missing
            return {
                'value_survival_correlation': np.nan,
                'survival_by_value': [],
                'survival_by_diversity': []
            }

        if hw_manager.is_hardware_acceleration_available():
            try: 
                first_year_df = hw_manager.accelerate_dataframe(first_year_df, operations=['assign', 'filter'])
                logger.debug("Applied DataFrame acceleration to first_year_df.")
            except Exception as e: 
                logger.warning(f"DataFrame acceleration failed: {e}")
                        
        first_year_df['log_pre_value'] = np.log1p(first_year_df['pre_conflict_value'])
        value_survival_corr = first_year_df['log_pre_value'].corr(first_year_df['survived'])
        if pd.isna(value_survival_corr): value_survival_corr = 0.0 # Handle NaN correlation if data is sparse

        try:
            first_year_df['value_quartile'] = pd.qcut(first_year_df['pre_conflict_value'], 4, labels=VALUE_QUARTILE_LABELS_V2, duplicates='drop')
            survival_by_value = first_year_df.groupby('value_quartile', observed=True)['survived'].mean().reset_index()
        except Exception as e: # Handle cases where qcut fails (e.g. not enough unique values)
            logger.warning(f"Value quartile calculation failed: {e}")
            survival_by_value = pd.DataFrame() # Empty or default structure

        product_partner_counts = first_year_df.groupby('hs2', observed=True)['importer'].nunique().reset_index().rename(columns={'importer': 'partner_count'})
        first_year_df = pd.merge(first_year_df, product_partner_counts, on='hs2', how='left')
        first_year_df['partner_count'] = first_year_df['partner_count'].fillna(0)
        
        try:
            first_year_df['product_diversity'] = pd.cut(first_year_df['partner_count'], bins=PRODUCT_DIVERSITY_BINS_V2, labels=PRODUCT_DIVERSITY_LABELS_V2, right=False, duplicates='drop')
            survival_by_diversity = first_year_df.groupby('product_diversity', observed=True)['survived'].mean().reset_index()
        except Exception as e: # Handle cases where cut fails
            logger.warning(f"Product diversity calculation failed: {e}")
            survival_by_diversity = pd.DataFrame()

        logger.debug("Survival factor analysis complete.")
        return {
            'value_survival_correlation': round(value_survival_corr, 4),
            'survival_by_value': survival_by_value.to_dict(orient='records'),
            'survival_by_diversity': survival_by_diversity.to_dict(orient='records')
        }

# --- V2 Standalone/Legacy Wrapper Functions ---
@protect("process_export_survival_data_v2", OperationType.MODEL_CALCULATION)
@memoize(ttl=3600, level=StorageTier.MEMORY) # Applied here as well for direct calls
@protect("process_export_survival_data_v2", OperationType.COMPUTATION)
@log_execution_time(logger=logger)
def process_export_survival_data_v2(
    baci_data: pd.DataFrame,
    country_code: int = DEFAULT_COUNTRY_CODE_V2,
    conflict_onset_year: int = DEFAULT_CONFLICT_ONSET_YEAR_V2,
    pre_conflict_years: int = DEFAULT_PRE_CONFLICT_YEARS_V2,
    min_export_value: float = DEFAULT_MIN_EXPORT_VALUE_V2,
    survival_threshold: float = DEFAULT_SURVIVAL_THRESHOLD_V2,
    iso_to_name: Optional[Dict[str, str]] = None,
    hs2_to_description: Optional[Dict[str, str]] = None,
    exporter_col: str = 'exporter',
    importer_col: str = 'importer',
    product_col: str = 'product',
    value_col: str = 'value',
    year_col: str = 'year'
) -> Optional[Dict[str, Any]]:
    logger.info("Calling process_export_survival_data_v2 (standalone wrapper)")
    # Rename the columns in baci_data to match what the analyzer expects
    column_mapping = {
        year_col: 'year',
        exporter_col: 'exporter',
        importer_col: 'importer',
        product_col: 'product',
        value_col: 'value'
    }

    # First check if we have all the required columns or need to look for alternatives
    required_cols = ['exporter', 'importer', 'product', 'value', 'year']
    missing_cols = [col for col in required_cols if col not in baci_data.columns]

    # Try to find alternatives for missing columns
    if missing_cols:
        logger.warning(f"Missing required columns for ExportSurvivalAnalyzerV2: {missing_cols}")

        # Look for alternative columns - extensive mapping to catch common variants
        alt_column_map = {
            'exporter': ['reporter_code', 'reporter', 'i', 'exporter_iso', 'country_code', 'source'],
            'importer': ['partner_code', 'partner', 'j', 'importer_iso', 'destination'],
            'product': ['product_code', 'k', 'hs_code', 'hs6', 'commodity_code', 'item_code'],
            'value': ['trade_value', 'trade_value_usd', 'v', 'export_value', 'import_value'],
            'year': ['t', 'time', 'period', 'date', 'trade_year']
        }

        # Create a copy of the data to modify
        modified_data = baci_data.copy()

        # Try to find alternatives for each missing column
        for missing_col in missing_cols:
            found = False
            if missing_col in alt_column_map:
                for alt_col in alt_column_map[missing_col]:
                    if alt_col in baci_data.columns:
                        logger.info(f"Using '{alt_col}' as alternative for missing '{missing_col}'")
                        modified_data[missing_col] = baci_data[alt_col]
                        found = True
                        break

            if not found:
                logger.error(f"Could not find alternative for required column '{missing_col}'")
                return None  # Return None if we can't find all required columns

        # Now use the modified data with the added columns
        baci_data = modified_data

    # Rename any remaining columns that need to be renamed
    renamed_cols = {old: new for old, new in column_mapping.items() if old != new and old in baci_data.columns and new not in baci_data.columns}
    if renamed_cols:
        logger.info(f"Renaming columns for ExportSurvivalAnalyzerV2: {renamed_cols}")
        baci_data = baci_data.rename(columns=renamed_cols)

    # Verify all required columns exist now
    missing_after_fix = [col for col in required_cols if col not in baci_data.columns]
    if missing_after_fix:
        logger.error(f"Still missing required columns after fixes: {missing_after_fix}")
        return None

    # Convert column types to match expected schema
    try:
        # Convert column types to those expected by the schema validator
        type_conversion = {
            'exporter': 'int32',  # Changed from int64 to int32 to match expected schema
            'importer': 'int32',  # Changed from int64 to int32 to match expected schema
            'product': 'object',  # String type
            'value': 'float64',
            'year': 'int32'       # Changed from int64 to int32 to match expected schema
        }

        for col, dtype in type_conversion.items():
            if col in baci_data.columns:
                # Log the current dtype for debugging
                logger.info(f"Converting column '{col}' from {baci_data[col].dtype} to {dtype}")
                if col == 'product':
                    # Ensure product codes are strings
                    baci_data[col] = baci_data[col].astype(str)
                else:
                    # Convert numeric columns to appropriate types
                    try:
                        # Explicitly use numpy types for better compatibility with the validation schema
                        if dtype == 'int32':
                            # Third-party imports
                            import numpy as np
                            baci_data[col] = pd.to_numeric(baci_data[col], errors='coerce').fillna(0).astype(np.int32)
                        elif dtype == 'float64':
                            # Third-party imports
                            import numpy as np
                            baci_data[col] = pd.to_numeric(baci_data[col], errors='coerce').fillna(0.0).astype(np.float64)
                        else:
                            baci_data[col] = baci_data[col].astype(dtype)
                    except Exception as conversion_error:
                        logger.warning(f"Error converting {col} to {dtype}: {conversion_error}. Attempting fallback conversion.")
                        # Try a more robust conversion
                        if dtype in ('int32', 'int64'):
                            # Third-party imports
                            import numpy as np
                            baci_data[col] = pd.to_numeric(baci_data[col], errors='coerce').fillna(0).astype(np.int32)
                        elif dtype == 'float64':
                            # Third-party imports
                            import numpy as np
                            baci_data[col] = pd.to_numeric(baci_data[col], errors='coerce').fillna(0.0).astype(np.float64)

        # Verify data types after conversion
        for col in baci_data.columns:
            logger.debug(f"Column '{col}' final dtype: {baci_data[col].dtype}")

        analyzer = ExportSurvivalAnalyzerV2(
            baci_data, country_code, conflict_onset_year, pre_conflict_years,
            min_export_value, survival_threshold, iso_to_name, hs2_to_description
        )
    except ValueError as e:
        logger.error(f"Failed to initialize ExportSurvivalAnalyzerV2: {e}")
        return None
    return analyzer.process_export_survival_data()
@protect("get_survival_chart_data_v2", OperationType.COMPUTATION)
def get_survival_chart_data_v2(survival_results: Optional[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
    logger.info("Calling get_survival_chart_data_v2 (standalone wrapper)")
    return SurvivalDataVisualizerV2.get_survival_chart_data_v2(survival_results)

@protect("analyze_survival_factors_v2", OperationType.MODEL_CALCULATION)
@protect("analyze_survival_factors_v2", OperationType.COMPUTATION)
def analyze_survival_factors_v2(survival_df: pd.DataFrame) -> Dict[str, Any]:
    logger.info("Calling analyze_survival_factors_v2 (standalone wrapper)")
    return SurvivalFactorAnalyzerV2.analyze_survival_factors_v2(survival_df)

if __name__ == '__main__':
    # Project imports
    from yemen_trade_diagnostic.interfaces.logging_interface import configure_logging
    configure_logging(log_level=LogLevel.DEBUG, log_to_console=True, log_to_file=False)

    logger.info("--- Running Export Survival Model V2 Example ---")

    # Sample data (simplified BACI-like structure)
    sample_data = pd.DataFrame({
        'year': [2012, 2013, 2014, 2015, 2016, 2017] * 4, # Years before and after 2015 conflict
        'exporter': [DEFAULT_COUNTRY_CODE_V2] * 24,
        'importer': ([1]*6 + [2]*6 + [3]*6 + [4]*6), # Four import partners
        'product': (['010101'] * 12 + ['020202'] * 12), # Two products (HS6), will be truncated to HS2
        'value': [
            # Product 1, Partner 1 (Survives well)
            200000, 220000, 210000, 180000, 190000, 200000,
            # Product 1, Partner 2 (Declines, doesn't survive based on threshold)
            150000, 160000, 140000, 50000, 20000, 10000, # min_export_value=100k, threshold=0.25 -> needs 25k
            # Product 2, Partner 3 (Survives initially, then drops)
            300000, 320000, 280000, 250000, 30000, 15000,
            # Product 2, Partner 4 (New relation post-conflict - won't be in pre-conflict significant)
            0, 0, 0, 120000, 130000, 110000,
        ]
    })

    iso_map_sample = {'1': 'Country A', '2': 'Country B', '3': 'Country C', '4': 'Country D'}
    hs2_map_sample = {'01': 'Live Animals', '02': 'Meat'}

    logger.info(f"Sample BACI data created with shape: {sample_data.shape}")

    # Test with the standalone wrapper function
    logger.info("\n--- Testing process_export_survival_data_v2 (standalone) ---")
    try:
        survival_analysis_results = process_export_survival_data_v2(
            baci_data=sample_data,
            iso_to_name=iso_map_sample,
            hs2_to_description=hs2_map_sample
        )
        if survival_analysis_results:
            logger.info(f"Survival Analysis Results (processed relationships: {survival_analysis_results.get('significant_relationships_count')})")
            # Print overall for quick check, others can be large
            if 'overall' in survival_analysis_results and isinstance(survival_analysis_results['overall'], pd.DataFrame):
                 logger.info(f"Overall Survival:\n{survival_analysis_results['overall']}")
            # Example: logger.info(json.dumps(survival_analysis_results, indent=2, default=str)) # if DataFrames are not directly printable in a useful way here
        else:
            logger.warning("Export survival analysis returned no results or failed.")
        # Test chart data generation
        if survival_analysis_results:
            logger.info("\n--- Testing get_survival_chart_data_v2 ---")
            chart_data = get_survival_chart_data_v2(survival_analysis_results)
            if chart_data:
                logger.info(f"Chart Data (first few keys): {{ \n    'years_survived': {chart_data.get('years_survived')}, \n    'survival_rate': {chart_data.get('survival_rate')}, \n    ...}}") # Keep log concise
                # logger.info(json.dumps(chart_data, indent=2, default=str)) # For full data
            else:
                logger.warning("Chart data generation failed or returned None.")

        # Test factor analysis - requires survival_df from inside the analyzer, or a more complex setup.
        # For this example, we'll create a dummy survival_df if the main analysis ran.
        # Normally, you'd pass the survival_df obtained from the analyzer's internal steps.
        if survival_analysis_results and isinstance(survival_analysis_results.get('overall'), pd.DataFrame):
             # This is a simplification. Actual survival_df is more detailed.
             # We'll try to get it from the structure if ExportSurvivalAnalyzerV2._track_relationship_survival was successful
             # This part is tricky without exposing internal DataFrames directly or re-running parts of the logic.
             # For a true test of analyze_survival_factors_v2, one would need the detailed survival_df.
             logger.info("\n--- Testing analyze_survival_factors_v2 (conceptual) ---")
             # Construct a simplified dummy survival_df for the example based on sample_data and expected structure
             # This is NOT the real survival_df that would be generated internally by ExportSurvivalAnalyzerV2.
             dummy_survival_for_factors = pd.DataFrame({
                 'importer': [1,1,2,2,3,3],
                 'hs2': ['01','01','01','01','02','02'],
                 'partner_name': ['Country A'] * 2 + ['Country B'] * 2 + ['Country C'] * 2,
                 'product_name': ['Live Animals'] * 4 + ['Meat'] * 2,
                 'pre_conflict_value': [210000, 210000, 140000, 140000, 280000, 280000],
                 'year': [2015, 2016, 2015, 2016, 2015, 2016],
                 'years_since_onset': [1,2,1,2,1,2],
                 'current_value': [180000,190000, 50000, 20000, 250000, 30000],
                 'survived': [True, True, True, False, True, True], # Adjusted for threshold (25k for P2)
                 'value_ratio': [0.85, 0.90, 0.35, 0.14, 0.89, 0.10]
             })
             factor_analysis_results = analyze_survival_factors_v2(dummy_survival_for_factors)
             if factor_analysis_results:
                 logger.info(f"Factor Analysis Results:\n{json.dumps(factor_analysis_results, indent=2)}")
             else:
                 logger.warning("Factor analysis returned no results or failed.")

    except Exception as e:
                logger.error(f"An error occurred in the Export Survival Model V2 example: {e}", exc_info=True)

    logger.info("--- Export Survival Model V2 Example Finished ---") 