"""
Import Dependency Model for Yemen Trade Diagnostic (V2)

This module provides V2 functions for analyzing import dependency for key product categories.
"""
# Standard library imports
from typing import Any, Dict, List, Optional, Tuple, Union

# Third-party imports
import numpy as np
import pandas as pd

# Project imports
from yemen_trade_diagnostic.data import DataSource, load_data
from yemen_trade_diagnostic.interfaces.cache_interface import StorageTier, memoize
from yemen_trade_diagnostic.interfaces.hardware_interface import AccelerationType, get_hardware_manager
from yemen_trade_diagnostic.interfaces.logging_interface import Log<PERSON>evel, get_logger
from yemen_trade_diagnostic.interfaces.validation_interface import SchemaValidationRule, ValidationIssueLevel, ValidationResult, get_validation_manager, validate_schema
from yemen_trade_diagnostic.models.concentration.hhi_model import calculate_hhi_v2
from yemen_trade_diagnostic.errors import protect, OperationType

logger = get_logger(__name__)

# Adapted validation helper (consider promoting to a shared util if widely used)
def _validate_trade_dataframe_v2(df: pd.DataFrame, required_cols: List[str], df_name: str) -> ValidationResult:
    res = ValidationResult()
    if not isinstance(df, pd.DataFrame) or df.empty:
        res.add_issue(f"{df_name} is not a valid or non-empty DataFrame.", ValidationIssueLevel.ERROR)
        return res
    missing = [col for col in required_cols if col not in df.columns]
    if missing:
        res.add_issue(f"Missing columns in {df_name}: {missing}", ValidationIssueLevel.ERROR)
    return res

class TradeAnalyzerV2:
    """Class for analyzing trade data with focus on import dependency and vulnerability (V2)."""

    def __init__(
        self,
        trade_data: pd.DataFrame,
        country_code: int = 887,
        value_col: str = "v",
        year_col: str = "t",
        product_col: str = "k",
        importer_col: str = "j", # For general trade data, importer is j
        exporter_col: str = "i"  # Exporter is i
    ):
        logger.debug(f"Initializing TradeAnalyzerV2 for country {country_code}.")
        self.trade_data = trade_data
        self.country_code = country_code
        self.value_col = value_col
        self.year_col = year_col
        self.product_col = product_col
        # Note: The original model had product_name_col, but it's often derived or merged.
        # For dependency, importer_col will be the self.country_code
        # For diversification of imports, exporter_col will be the partner dimension.
        self.importer_col = importer_col
        self.exporter_col = exporter_col
        self.hw_manager = get_hardware_manager()

    def _filter_country_imports_v2(
        self,
        year: Optional[int] = None,
        start_year: Optional[int] = None,
        end_year: Optional[int] = None
    ) -> pd.DataFrame:
        """Filter import data for a specific country (or range of years).

        Args:
            year: Specific year to filter for
            start_year: Start year for range filtering
            end_year: End year for range filtering

        Returns:
            Filtered DataFrame containing import data for specified country and year(s)
        """
        filtered_data = self.trade_data.copy()

        # Log columns for debugging
        logger.debug(f"Columns in trade_data for filtering: {filtered_data.columns.tolist()}")
        logger.debug(f"Expected importer column: {self.importer_col}, exporter column: {self.exporter_col}")
        logger.debug(f"Country code for filtering: {self.country_code}")

        # If the data has already been pre-filtered for our country of interest
        if hasattr(self, 'is_country_specific') and self.is_country_specific:
            logger.info("Data is already pre-filtered for the specific country.")
            pass
        # Use importer column if available (default for country imports data)
        elif self.importer_col in filtered_data.columns:
            logger.info(f"Filtering by importer column '{self.importer_col}' for country code {self.country_code}")
            filtered_data = filtered_data[filtered_data[self.importer_col] == self.country_code]
        # Use exporter column if available (for checking outbound data)
        elif self.exporter_col in filtered_data.columns:
            if filtered_data[self.exporter_col].nunique() > 0:  # Check if there are exporters
                logger.info(f"Filtering by exporter column '{self.exporter_col}' for country code {self.country_code}")
                filtered_data = filtered_data[filtered_data[self.exporter_col] == self.country_code]
        # Try alternative columns for filtering
        else:
            # Check common alternative columns that might represent country
            alternative_country_cols = ['source', 'reporter', 'reporter_code', 'partner', 'partner_code', 'i', 'j', 'country', 'country_code']

            # For Yemen imports data, we need to check if this is already Yemen-specific data
            # Check if the data is already filtered for Yemen (e.g., from YemenImportsLoader)
            if 'partner_code' in filtered_data.columns and 'reporter_code' in filtered_data.columns:
                # Check if this is Yemen imports data (where partner_code is Yemen)
                if any(filtered_data['partner_code'] == self.country_code):
                    logger.info(f"Data appears to be Yemen imports (partner_code = {self.country_code})")
                    # This is already Yemen imports data, no need to filter further
                    return filtered_data
                # Check if this is Yemen exports data (where reporter_code is Yemen)
                elif any(filtered_data['reporter_code'] == self.country_code):
                    logger.info(f"Data appears to be Yemen exports (reporter_code = {self.country_code})")
                    # This is already Yemen exports data, no need to filter further
                    return filtered_data

            for col in alternative_country_cols:
                if col in filtered_data.columns:
                    # Handle numeric vs string representation of country code/name
                    if filtered_data[col].dtype.kind in 'ifu':  # numeric column
                        logger.info(f"Filtering by numeric alternative column '{col}' for country code {self.country_code}")
                        filtered_data = filtered_data[filtered_data[col] == self.country_code]
                        break
                    elif filtered_data[col].dtype == 'object':  # string column
                        # Check if 'yemen' is in the values (case insensitive)
                        if any(isinstance(val, str) and 'yemen' in val.lower() for val in filtered_data[col].unique()):
                            logger.info(f"Filtering by string alternative column '{col}' containing 'yemen'")
                            filtered_data = filtered_data[filtered_data[col].str.lower().str.contains('yemen', na=False)]
                            break

        # Log filtered data size
        logger.info(f"After country filtering: {len(filtered_data)} rows remaining")

        # Filter by year if specified
        if year is not None:
            # Try to find a usable year column if the specified one doesn't exist
            working_year_col = self.year_col
            if self.year_col not in filtered_data.columns:
                alternative_year_cols = ['year', 't', 'time', 'date', 'period', 'trade_year']
                for col in alternative_year_cols:
                    if col in filtered_data.columns:
                        logger.info(f"Using alternative year column '{col}' instead of '{self.year_col}'")
                        working_year_col = col
                        break
                else:
                    logger.warning(f"No suitable year column found (tried {alternative_year_cols}). Skipping year filtering.")
                    return filtered_data

            logger.info(f"Filtering by year: {year} using column '{working_year_col}'")
            filtered_data = filtered_data[filtered_data[working_year_col] == year]
        elif start_year is not None and end_year is not None:
            # Try to find a usable year column if the specified one doesn't exist
            working_year_col = self.year_col
            if self.year_col not in filtered_data.columns:
                alternative_year_cols = ['year', 't', 'time', 'date', 'period', 'trade_year']
                for col in alternative_year_cols:
                    if col in filtered_data.columns:
                        logger.info(f"Using alternative year column '{col}' instead of '{self.year_col}'")
                        working_year_col = col
                        break
                else:
                    logger.warning(f"No suitable year column found (tried {alternative_year_cols}). Skipping year filtering.")
                    return filtered_data

            logger.info(f"Filtering by year range: {start_year}-{end_year} using column '{working_year_col}'")
            filtered_data = filtered_data[
                (filtered_data[working_year_col] >= start_year) &
                (filtered_data[working_year_col] <= end_year)
            ]

        # Log final filtered data size
        logger.info(f"After all filtering: {len(filtered_data)} rows remaining")
        return filtered_data

    @protect("calculate_import_dependency_v2", OperationType.COMPUTATION)
    @memoize(ttl=3600, level=StorageTier.MEMORY)
    def calculate_import_dependency_v2(
        self,
        essentiality_scores: Dict[str, float], # Product code (str) to score (float)
        year: Optional[int] = None,
        start_year: Optional[int] = None,
        end_year: Optional[int] = None,
        product_name_col: Optional[str] = "product_name" # If available in input data
    ) -> pd.DataFrame:
        logger.info("Calculating import dependency scores.")
        country_imports = self._filter_country_imports_v2(year, start_year, end_year)
        if country_imports.empty:
            return pd.DataFrame()

        group_by_cols = [self.product_col]
        if product_name_col and product_name_col in country_imports.columns:
            group_by_cols.append(product_name_col)

        product_data = country_imports.groupby(group_by_cols, observed=True)[self.value_col].sum().reset_index()
        if product_data.empty:
             return pd.DataFrame()

        total_imports = product_data[self.value_col].sum()
        if total_imports == 0:
            product_data["import_share"] = 0.0
        else:
            product_data["import_share"] = product_data[self.value_col] / total_imports

        # Map essentiality scores with more robust handling
        # First, try direct mapping with string conversion
        product_data["essentiality_score"] = product_data[self.product_col].astype(str).map(essentiality_scores)

        # For products without direct matches, try to find matches with different formats
        missing_scores = product_data["essentiality_score"].isna()
        if missing_scores.any():
            logger.info(f"Found {missing_scores.sum()} products without direct essentiality score matches")

            # Try with zero-padding to 6 digits
            for idx in product_data[missing_scores].index:
                product_code = product_data.loc[idx, self.product_col]
                product_code_str = str(product_code)

                # Try with zero-padding
                padded_code = product_code_str.zfill(6)
                if padded_code in essentiality_scores:
                    product_data.loc[idx, "essentiality_score"] = essentiality_scores[padded_code]
                    continue

                # Try with first 6 digits if longer
                if len(product_code_str) > 6:
                    truncated_code = product_code_str[:6]
                    if truncated_code in essentiality_scores:
                        product_data.loc[idx, "essentiality_score"] = essentiality_scores[truncated_code]
                        continue

                # Try with first 4 digits (HS4)
                if len(product_code_str) >= 4:
                    hs4_code = product_code_str[:4]
                    # Look for any keys that start with this HS4 code
                    hs4_matches = [k for k in essentiality_scores.keys() if k.startswith(hs4_code)]
                    if hs4_matches:
                        # Use the average score of all matches
                        avg_score = sum(essentiality_scores[k] for k in hs4_matches) / len(hs4_matches)
                        product_data.loc[idx, "essentiality_score"] = avg_score
                        continue

                # Try with first 2 digits (HS2)
                if len(product_code_str) >= 2:
                    hs2_code = product_code_str[:2]
                    # Look for any keys that start with this HS2 code
                    hs2_matches = [k for k in essentiality_scores.keys() if k.startswith(hs2_code)]
                    if hs2_matches:
                        # Use the average score of all matches
                        avg_score = sum(essentiality_scores[k] for k in hs2_matches) / len(hs2_matches)
                        product_data.loc[idx, "essentiality_score"] = avg_score
                        continue

        # Fill any remaining NaN values with default score
        missing_after_processing = product_data["essentiality_score"].isna().sum()
        if missing_after_processing > 0:
            logger.info(f"After processing, still have {missing_after_processing} products without essentiality scores, using default")

            # Find the min and max scores to determine the middle
            if essentiality_scores:
                min_score = min(essentiality_scores.values())
                max_score = max(essentiality_scores.values())
                default_score = (min_score + max_score) / 2
            else:
                default_score = 5.0  # Default middle score if no scores available

            product_data["essentiality_score"] = product_data["essentiality_score"].fillna(default_score)
        
        product_data["dependency_score"] = product_data["import_share"] * product_data["essentiality_score"]

        return product_data.sort_values(by="dependency_score", ascending=False).reset_index(drop=True)

    def categorize_products_by_sector_v2(
        self,
        dependency_data: pd.DataFrame, # Output from calculate_import_dependency_v2
        sector_mapping: Dict[str, str] # HS2 code (str) to Sector Name (str)
    ) -> pd.DataFrame:
        logger.info("Categorizing products by sector.")
        if dependency_data.empty:
            logger.warning("Dependency data is empty for sector categorization.")
            return pd.DataFrame() # Valid empty if input is empty
        if self.product_col not in dependency_data.columns: # Check on original input
            logger.error(f"Product column '{self.product_col}' not found in dependency_data for sector categorization.")
            raise ValueError(f"Product column '{self.product_col}' not found in dependency_data.")

        # Assumes product_col in dependency_data contains full HS codes
        data_with_hs2 = dependency_data.copy()
        data_with_hs2["_hs2_temp"] = data_with_hs2[self.product_col].astype(str).str.slice(0, 2)
        data_with_hs2["sector"] = data_with_hs2["_hs2_temp"].map(sector_mapping).fillna("Other")
        data_with_hs2.drop(columns=["_hs2_temp"], inplace=True)

        agg_cols = {self.value_col: "sum", "import_share": "sum",
                    "essentiality_score": "mean", "dependency_score": "sum"}
        # Filter out columns not present in data_with_hs2 before aggregation
        valid_agg_cols = {k: v for k,v in agg_cols.items() if k in data_with_hs2.columns}
        if not valid_agg_cols:
            logger.warning("No valid columns to aggregate for sector categorization. Returning data with sector column only.")
            # Return with sector column but no aggregation if no valid agg columns present
            # This might happen if dependency_data was very minimal.
            return data_with_hs2

        sector_data = data_with_hs2.groupby("sector", observed=True).agg(valid_agg_cols).reset_index()
        # The check below was incorrect as self.product_col would be grouped out.
        # if sector_data.empty:
        #     logger.warning("Dependency data is empty for sector categorization.") # This log message is misleading here
        #     return pd.DataFrame()
        # if self.product_col not in sector_data.columns:
        #     logger.warning(f"Product column '{self.product_col}' not found for sector categorization.")
        #     raise ValueError(f"Product column '{self.product_col}' not found in dependency_data.")

        if sector_data.empty:
            logger.warning("Sector data is empty after grouping, though dependency data was present.")
            # This implies all products mapped to "Other" and then something else went wrong or no valid agg values.
            # Or simply no groups were formed, which is an empty result.
            return pd.DataFrame() # Valid empty result if grouping leads to empty

        return sector_data.sort_values(by="dependency_score", ascending=False).reset_index(drop=True)

    @protect("calculate_import_diversification_v2", OperationType.COMPUTATION)
    @memoize(ttl=3600, level=StorageTier.MEMORY)
    def calculate_import_diversification_v2(
        self,
        year: Optional[int] = None,
        start_year: Optional[int] = None,
        end_year: Optional[int] = None
    ) -> pd.DataFrame:
        logger.info("Calculating import diversification (HHI of sources per product).")
        country_imports = self._filter_country_imports_v2(year, start_year, end_year)
        if country_imports.empty:
            logger.warning("Country imports data is empty for diversification calculation.")
            return pd.DataFrame()

        # Debugging: Log column information
        logger.debug(f"Columns in country_imports for diversification: {country_imports.columns.tolist()}")
        logger.debug(f"Product column for groupby: {self.product_col}, Exporter column for groupby: {self.exporter_col}")

        # Check and handle missing required columns
        if self.product_col not in country_imports.columns:
            logger.error(f"Product column '{self.product_col}' missing from country_imports for diversification.")
            return pd.DataFrame()
        if self.value_col not in country_imports.columns:
            logger.error(f"Value column '{self.value_col}' missing from country_imports for diversification.")
            return pd.DataFrame()

        # Special handling for exporter column - try alternative column names if not found
        working_exporter_col = self.exporter_col
        if self.exporter_col not in country_imports.columns:
            # Look for common alternative exporter columns, especially in yemen_imports
            alternative_exporter_cols = ['source', 'partner', 'i', 'partner_iso', 'reporter_code', 'partner_code', 'reporter', 'exporter']

            for alt_col in alternative_exporter_cols:
                if alt_col in country_imports.columns:
                    logger.info(f"Using alternative column '{alt_col}' instead of missing exporter column '{self.exporter_col}'")
                    working_exporter_col = alt_col
                    break
            else:
                # If we couldn't find any alternative, log error and return empty
                logger.error(f"Exporter column '{self.exporter_col}' (expected partner country) missing from country_imports for diversification and no suitable alternative found.")
                return pd.DataFrame()

        # Group by product and exporter to get value from each source for each product
        product_exporter_data = country_imports.groupby([self.product_col, working_exporter_col], observed=True)[self.value_col].sum().reset_index()

        # Calculate HHI of sources for each product
        hhi_results = []
        for product_code, group in product_exporter_data.groupby(self.product_col, observed=True):
            # Here, group_col for HHI is working_exporter_col, value_col is self.value_col for shares of that product
            hhi_val = calculate_hhi_v2(group, group_col=working_exporter_col, value_col=self.value_col, validate=False)
            num_suppliers = group[working_exporter_col].nunique()
            total_product_value = group[self.value_col].sum()
            hhi_results.append({
                self.product_col: product_code,
                "hhi_sources": hhi_val,
                "supplier_count": num_suppliers,
                "total_value_product": total_product_value
            })

        if not hhi_results:
            return pd.DataFrame()

        hhi_df = pd.DataFrame(hhi_results)
        hhi_df["diversification_score"] = 1 - hhi_df["hhi_sources"] # Higher is more diversified
        return hhi_df.sort_values(by="total_value_product", ascending=False).reset_index(drop=True)

    @protect("calculate_import_vulnerability_v2", OperationType.COMPUTATION)
    def calculate_import_vulnerability_v2(
        self,
        dependency_data: pd.DataFrame, # Output from calculate_import_dependency_v2
        diversification_data: pd.DataFrame # Output from calculate_import_diversification_v2
    ) -> pd.DataFrame:
        logger.info("Calculating import vulnerability.")
        if dependency_data.empty:
            logger.warning("Dependency data is empty for vulnerability calculation.")
            return pd.DataFrame()

        # Debug information
        logger.debug(f"Dependency data columns: {dependency_data.columns.tolist()}")
        logger.debug(f"Diversification data columns: {diversification_data.columns.tolist() if not diversification_data.empty else 'Empty DataFrame'}")

        # Check if product column exists in dependency data
        if self.product_col not in dependency_data.columns:
            # Try finding alternative product column
            alt_product_cols = ['k', 'product', 'product_code', 'hs_code', 'hs6', 'commodity_code']
            for alt_col in alt_product_cols:
                if alt_col in dependency_data.columns:
                    logger.info(f"Using alternative product column '{alt_col}' instead of missing '{self.product_col}' in dependency data")
                    # Create a copy in the expected column name
                    dependency_data[self.product_col] = dependency_data[alt_col]
                    break
            else:
                logger.error(f"Product column '{self.product_col}' missing in dependency_data and no suitable alternative found")
                return pd.DataFrame()

        # Check that dependency_score exists in dependency data
        if "dependency_score" not in dependency_data.columns:
            logger.error("dependency_score column missing in dependency_data")
            return pd.DataFrame()

        # Handle empty or missing diversification data
        if diversification_data.empty:
            logger.warning("Diversification data is empty. Vulnerability will be based solely on dependency.")
            # Return dependency data, but add vulnerability score based on zero diversification
            vulnerability_data = dependency_data.copy()
            vulnerability_data["diversification_score"] = 0.0 # Max concentration (minimum diversification)
            vulnerability_data["hhi_sources"] = 1.0 # Max HHI
            vulnerability_data["supplier_count"] = 0 # Or 1, if no diversification means one dominant/unknown source
        else:
            # Check if product column exists in diversification data
            if self.product_col not in diversification_data.columns:
                # Try finding alternative product column
                alt_product_cols = ['k', 'product', 'product_code', 'hs_code', 'hs6', 'commodity_code']
                for alt_col in alt_product_cols:
                    if alt_col in diversification_data.columns:
                        logger.info(f"Using alternative product column '{alt_col}' instead of missing '{self.product_col}' in diversification data")
                        # Create a copy in the expected column name
                        diversification_data[self.product_col] = diversification_data[alt_col]
                        break
                else:
                    logger.warning(f"Product column '{self.product_col}' missing in diversification_data and no suitable alternative found. Using dependency data only.")
                    vulnerability_data = dependency_data.copy()
                    vulnerability_data["diversification_score"] = 0.0
                    vulnerability_data["hhi_sources"] = 1.0
                    vulnerability_data["supplier_count"] = 0
                    vulnerability_data["vulnerability_score"] = vulnerability_data["dependency_score"] # When no diversification info, vulnerability = dependency
                    return vulnerability_data.sort_values(by="vulnerability_score", ascending=False).reset_index(drop=True)

            # Check if diversification_score exists in diversification data
            if "diversification_score" not in diversification_data.columns:
                if "hhi_sources" in diversification_data.columns:
                    # Can derive diversification score from HHI
                    logger.info("Calculating diversification_score from hhi_sources")
                    diversification_data["diversification_score"] = 1.0 - diversification_data["hhi_sources"]
                else:
                    logger.warning("Neither diversification_score nor hhi_sources found in diversification_data. Using dependency data only.")
                    vulnerability_data = dependency_data.copy()
                    vulnerability_data["diversification_score"] = 0.0
                    vulnerability_data["hhi_sources"] = 1.0
                    vulnerability_data["supplier_count"] = 0
                    vulnerability_data["vulnerability_score"] = vulnerability_data["dependency_score"]
                    return vulnerability_data.sort_values(by="vulnerability_score", ascending=False).reset_index(drop=True)

            # Prepare columns for merge
            div_columns = [self.product_col]
            for col in ["hhi_sources", "supplier_count", "diversification_score"]:
                if col in diversification_data.columns:
                    div_columns.append(col)

            # Perform merge
            try:
                logger.info(f"Merging dependency and diversification data on '{self.product_col}'")
                vulnerability_data = pd.merge(
                    dependency_data,
                    diversification_data[div_columns],
                    on=self.product_col,
                    how="left"
                )
                logger.info(f"Merge complete. Result has {len(vulnerability_data)} rows")
            except Exception as e:
                logger.error(f"Error merging dependency and diversification data: {e}")
                vulnerability_data = dependency_data.copy()
                for col in ["diversification_score", "hhi_sources", "supplier_count"]:
                    if col not in vulnerability_data.columns:
                        vulnerability_data[col] = 0.0 if col != "supplier_count" else 0
        
        # Fill NaNs that might result from merge (e.g. product in dependency but not diversification)
        vulnerability_data["diversification_score"] = vulnerability_data["diversification_score"].fillna(0.0) # Assume 0 diversification (max concentration) if no data
        vulnerability_data["dependency_score"] = vulnerability_data["dependency_score"].fillna(0.0)

        # Fill other columns from diversification_data that might be NaN after left merge
        for col_from_div in ["hhi_sources", "supplier_count"]:
            if col_from_div in vulnerability_data.columns:
                 vulnerability_data[col_from_div] = vulnerability_data[col_from_div].fillna(0) # Or appropriate default

        # Calculate vulnerability score - Vulnerability = Dependency * (1 - Diversification) = Dependency * Concentration
        vulnerability_data["vulnerability_score"] = vulnerability_data["dependency_score"] * (1 - vulnerability_data["diversification_score"])

        logger.info(f"Vulnerability calculation complete with {len(vulnerability_data)} products")
        return vulnerability_data.sort_values(by="vulnerability_score", ascending=False).reset_index(drop=True)

    @memoize(ttl=3600, level=StorageTier.MEMORY)
    def prepare_visualization_data_v2(
        self,
        essentiality_scores: Dict[str, float],
        sector_mapping: Optional[Dict[str, str]] = None,
        top_n: int = 20,
        year: Optional[int] = None,
        start_year: Optional[int] = None,
        end_year: Optional[int] = None,
        product_name_col: Optional[str] = "product_name" # If available in input trade_data
    ) -> pd.DataFrame:
        logger.info("Preparing data for import dependency visualization.")
        dependency_data = self.calculate_import_dependency_v2(essentiality_scores, year, start_year, end_year, product_name_col)
        if dependency_data.empty: 
            return pd.DataFrame()

        diversification_data = self.calculate_import_diversification_v2(year, start_year, end_year)
        # If diversification calculation fails or returns empty, vulnerability_data will be just dependency_data with NaNs for diversification cols

        viz_data = self.calculate_import_vulnerability_v2(dependency_data, diversification_data)
        if viz_data.empty: 
            return pd.DataFrame()

        if sector_mapping:
            viz_data = self.categorize_products_by_sector_v2(viz_data, sector_mapping)

        # Ensure product_name column exists for display, using product_col as fallback
        if product_name_col and product_name_col in viz_data.columns:
            viz_data["display_name"] = viz_data[product_name_col].fillna(viz_data[self.product_col])
        else:
            viz_data["display_name"] = viz_data[self.product_col]

        return viz_data.head(top_n)

# Standalone wrapper functions
@protect("calculate_import_dependency_v2_standalone", OperationType.COMPUTATION)
def calculate_import_dependency_v2_standalone(trade_data: pd.DataFrame, essentiality_scores: Dict[str, float], **kwargs) -> pd.DataFrame:
    analyzer = TradeAnalyzerV2(trade_data, **{k:v for k,v in kwargs.items() if k not in ['essentiality_scores']}) # Pass relevant kwargs
    return analyzer.calculate_import_dependency_v2(essentiality_scores, **{k:v for k,v in kwargs.items() if k not in ['trade_data']})

@protect("calculate_import_diversification_v2_standalone", OperationType.COMPUTATION)
def calculate_import_diversification_v2_standalone(trade_data: pd.DataFrame, **kwargs) -> pd.DataFrame:
    analyzer = TradeAnalyzerV2(trade_data, **kwargs)
    return analyzer.calculate_import_diversification_v2(**{k:v for k,v in kwargs.items() if k not in ['trade_data']})

@protect("calculate_import_vulnerability_v2_standalone", OperationType.COMPUTATION)
def calculate_import_vulnerability_v2_standalone(dependency_data: pd.DataFrame, diversification_data: pd.DataFrame, **kwargs) -> pd.DataFrame:
    analyzer = TradeAnalyzerV2(pd.DataFrame(), **kwargs) # trade_data not strictly needed for this specific method if others provide product_col
    return analyzer.calculate_import_vulnerability_v2(dependency_data, diversification_data)

def prepare_import_dependency_visualization_data_v2_standalone(trade_data: pd.DataFrame, essentiality_scores: Dict[str, float], **kwargs) -> pd.DataFrame:
    analyzer = TradeAnalyzerV2(trade_data, **{k:v for k,v in kwargs.items() if k not in ['essentiality_scores']})
    return analyzer.prepare_visualization_data_v2(essentiality_scores, **{k:v for k,v in kwargs.items() if k not in ['trade_data']})

if __name__ == '__main__':
    # Project imports
    from yemen_trade_diagnostic.interfaces.logging_interface import configure_logging
    configure_logging(log_level=LogLevel.DEBUG, log_to_console=True, log_to_file=False)
    logger.info("Starting Import Dependency Model V2 example.")

    sample_trade = pd.DataFrame({
        't': [2021, 2021, 2021, 2021, 2021],
        'i': [156, 784, 4, 156, 784],      # Exporter (CN, AE, AFG)
        'j': [887, 887, 887, 887, 887],      # Importer (Yemen)
        'k': ['010101', '010101', '010101', '020101', '020101'], # Product
        'product_name': ['Live Horses', 'Live Horses', 'Live Horses', 'Meat of Bovine', 'Meat of Bovine'],
        'v': [100, 200, 50, 300, 250]       # Value
    })
    essentiality = {'010101': 8.0, '020101': 7.5, '030101': 6.0} # HS code string to score
    sectors = {'01': 'Live Animals', '02': 'Meat Products', '03': 'Fish'}

    logger.debug(f"Sample trade data for import dependency:\n{sample_trade}")

    # Using standalone function
    dependency_df = calculate_import_dependency_v2_standalone(
        sample_trade.copy(), 
        essentiality_scores=essentiality, 
        year=2021, 
        country_code=887, 
        product_name_col='product_name'
    )
    logger.info(f"Import Dependency Scores (2021):\n{dependency_df}")

    if not dependency_df.empty:
        diversification_df = calculate_import_diversification_v2_standalone(
            sample_trade.copy(), 
            year=2021, 
            country_code=887
        )
        logger.info(f"Import Diversification (2021):\n{diversification_df}")

        if not diversification_df.empty:
            vulnerability_df = calculate_import_vulnerability_v2_standalone(
                dependency_df.copy(), 
                diversification_df.copy(), 
                product_col='k'
            )
            logger.info(f"Import Vulnerability (2021):\n{vulnerability_df}")

            viz_data = prepare_import_dependency_visualization_data_v2_standalone(
                sample_trade.copy(), 
                essentiality_scores=essentiality, 
                sector_mapping=sectors, 
                year=2021, 
                country_code=887, 
                product_name_col='product_name'
            )
            logger.info(f"Visualization Data (Top 20, 2021):\n{viz_data}")
        else:
            logger.warning("Diversification data was empty, skipping further tests.")
    else:
        logger.warning("Dependency data was empty, skipping further tests.")
    
    logger.info("Import Dependency Model V2 example finished.")