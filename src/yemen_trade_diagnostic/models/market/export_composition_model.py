"""
Export Composition Model for Yemen Trade Diagnostic (V2)

This module provides V2 functions for analyzing export composition by quantity and value.
"""
# Standard library imports
import time
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Tuple, Union

# Third-party imports
import numpy as np
import pandas as pd

# Project imports
# V2 Unified data loader
from yemen_trade_diagnostic.data import DataSource, load_data
from yemen_trade_diagnostic.interfaces.cache_interface import StorageTier, memoize
from yemen_trade_diagnostic.interfaces.hardware_interface import AccelerationType, get_hardware_manager
from yemen_trade_diagnostic.interfaces.logging_interface import LogLevel, configure_logging, get_logger, log_execution_time
from yemen_trade_diagnostic.interfaces.validation_interface import SchemaValidationRule, ValidationIssueLevel, ValidationResult, get_validation_manager, validate_schema
from yemen_trade_diagnostic.errors import protect, OperationType

logger = get_logger(__name__)

# Schema definitions for validation
TRADE_DATA_SCHEMA = {
    'required_columns': ['year', 'product', 'value', 'quantity', 'direction', 'country'],
    'column_types': {
        'year': 'integer', 
        'product': 'string', 
        'value': 'numeric',
        'quantity': 'numeric',
        'direction': 'string',
        'country': 'string'
    }
}

PRODUCT_METADATA_SCHEMA = {
    'required_columns': ['product_code', 'sector'],
    'column_types': {
        'product_code': 'string',
        'sector': 'string'
    }
}

def _validate_data_for_export_composition(
    trade_data: pd.DataFrame, 
    product_metadata: Optional[pd.DataFrame] = None
) -> ValidationResult:
    """Validate data for export composition analysis."""
    validation_manager = get_validation_manager()
    
    result = ValidationResult()
    if not isinstance(trade_data, pd.DataFrame) or trade_data.empty:
        result.add_issue("Trade data is empty or not a DataFrame", ValidationIssueLevel.ERROR)
        return result
    
    # Check required columns in trade data
    trade_required_cols = TRADE_DATA_SCHEMA.get('required_columns', [])
    trade_missing_cols = [col for col in trade_required_cols if col not in trade_data.columns]
    if trade_missing_cols:
        result.add_issue(f"Missing required columns in trade data: {trade_missing_cols}", ValidationIssueLevel.ERROR)
        
    # Check column types in trade data
    trade_column_types = TRADE_DATA_SCHEMA.get('column_types', {})
    for col, expected_type in trade_column_types.items():
        if col in trade_data.columns:
            # Check data type
            if expected_type == 'numeric' and not pd.api.types.is_numeric_dtype(trade_data[col]):
                result.add_issue(f"Column '{col}' should be numeric but is {trade_data[col].dtype}", ValidationIssueLevel.WARNING)
            elif expected_type == 'string' and not (pd.api.types.is_object_dtype(trade_data[col]) or pd.api.types.is_string_dtype(trade_data[col])):
                result.add_issue(f"Column '{col}' should be string but is {trade_data[col].dtype}", ValidationIssueLevel.WARNING)
            elif expected_type == 'integer' and not pd.api.types.is_integer_dtype(trade_data[col]):
                result.add_issue(f"Column '{col}' should be integer but is {trade_data[col].dtype}", ValidationIssueLevel.WARNING)
    
    # Check for negative values in value and quantity columns
    if 'value' in trade_data.columns and (trade_data['value'] < 0).any():
        result.add_issue("Negative values found in 'value' column", ValidationIssueLevel.WARNING)
    if 'quantity' in trade_data.columns and (trade_data['quantity'] < 0).any():
        result.add_issue("Negative values found in 'quantity' column", ValidationIssueLevel.WARNING)
    
    # Validate product metadata if provided
    if product_metadata is not None:
        if not isinstance(product_metadata, pd.DataFrame) or product_metadata.empty:
            result.add_issue("Product metadata is empty or not a DataFrame", ValidationIssueLevel.WARNING)
        else:
            # Check required columns in product metadata
            product_required_cols = PRODUCT_METADATA_SCHEMA.get('required_columns', [])
            product_missing_cols = [col for col in product_required_cols if col not in product_metadata.columns]
            if product_missing_cols:
                result.add_issue(f"Missing required columns in product metadata: {product_missing_cols}", ValidationIssueLevel.WARNING)
            
            # Check if product_code column in product_metadata matches product codes in trade_data
            if 'product_code' in product_metadata.columns and 'product' in trade_data.columns:
                unique_products = set(trade_data['product'].unique())
                product_codes = set(product_metadata['product_code'].unique())
                missing_products = unique_products - product_codes
                if missing_products and len(missing_products) > len(unique_products) * 0.1:  # If more than 10% of products missing
                    result.add_issue(f"Many product codes in trade data don't match codes in metadata: {len(missing_products)} out of {len(unique_products)}", ValidationIssueLevel.WARNING)
    
    return result

@memoize(ttl=3600, level=StorageTier.MEMORY)
@log_execution_time(logger=logger)
def _optimize_dataframe_for_export_analysis(df: pd.DataFrame) -> pd.DataFrame:
    """Optimize a DataFrame for export analysis using hardware acceleration if available."""
    if df.empty:
        return df
        
    # Use hardware acceleration if available
    hw_manager = get_hardware_manager()
    if hw_manager.is_hardware_acceleration_available():
        try:
            logger.debug("Using hardware acceleration for DataFrame optimization")
            optimized_df = hw_manager.accelerate_dataframe(df, optimize_for="memory")
            if optimized_df is not None:
                logger.debug("Hardware acceleration successful for DataFrame optimization")
                return optimized_df
            else:
                logger.warning("Hardware acceleration returned None for DataFrame optimization")
        except Exception as e:
            logger.warning(f"Hardware acceleration failed for DataFrame optimization: {e}")
                
    # Fallback to CPU optimization
    logger.debug("Using CPU optimization for DataFrame")
    
    # Copy to avoid modifying the original
    optimized_df = df.copy()
    
    # Optimize numeric columns
    for col in optimized_df.select_dtypes(include=['number']).columns:
        if optimized_df[col].min() >= 0:
            # For unsigned integers
            max_val = optimized_df[col].max()
            if max_val <= 255:
                optimized_df[col] = optimized_df[col].astype(np.uint8)
            elif max_val <= 65535:
                optimized_df[col] = optimized_df[col].astype(np.uint16)
            elif max_val <= 4294967295:
                optimized_df[col] = optimized_df[col].astype(np.uint32)
        else:
            # For signed integers
            max_val = abs(optimized_df[col].max())
            min_val = abs(optimized_df[col].min())
            max_abs = max(max_val, min_val)
            if max_abs <= 127:
                optimized_df[col] = optimized_df[col].astype(np.int8)
            elif max_abs <= 32767:
                optimized_df[col] = optimized_df[col].astype(np.int16)
            elif max_abs <= 2147483647:
                optimized_df[col] = optimized_df[col].astype(np.int32)
    
    # Optimize float columns to float32 if precision allows
    for col in optimized_df.select_dtypes(include=['float64']).columns:
        optimized_df[col] = optimized_df[col].astype(np.float32)
    
    # Optimize string columns using category for high-cardinality columns
    for col in optimized_df.select_dtypes(include=['object']).columns:
        if optimized_df[col].nunique() / len(optimized_df) < 0.5:  # If fewer than 50% unique values
            optimized_df[col] = optimized_df[col].astype('category')
    
    return optimized_df

@protect("analyze_export_value_composition_v2", OperationType.COMPUTATION)
@memoize(ttl=3600*24, level=StorageTier.DISK)
@log_execution_time(logger=logger)
def analyze_export_value_composition_v2(
    trade_data: pd.DataFrame,
    product_metadata: Optional[pd.DataFrame] = None,
    year: int = 2023,
    country_code: str = "887",  # Yemen by default
    product_col: str = "product",
    year_col: str = "year",
    value_col: str = "value",
    direction_col: str = "direction",
    product_code_col: str = "product_code",
    sector_col: str = "sector",
    top_n_products: int = 10,
    min_product_pct: float = 0.5,  # Minimum percentage for a product to be shown individually
    include_other_category: bool = True,
    include_sector_grouping: bool = True
) -> Dict[str, pd.DataFrame]:
    """
    Analyze export composition by value for a specific country and year.
    
    Args:
        trade_data: DataFrame with trade data (must have year, product, value columns)
        product_metadata: Optional DataFrame with product metadata (product_code, sector)
        year: Year to analyze
        country_code: Country code to analyze (default: "887" for Yemen)
        product_col: Name of the product column in trade_data
        year_col: Name of the year column in trade_data
        value_col: Name of the value column in trade_data
        direction_col: Name of the direction column in trade_data
        product_code_col: Name of the product code column in product_metadata
        sector_col: Name of the sector column in product_metadata
        top_n_products: Number of top products to show individually
        min_product_pct: Minimum percentage for a product to be shown individually
        include_other_category: Whether to include an "Other" category for small products
        include_sector_grouping: Whether to include sector-level grouping
        
    Returns:
        Dictionary with DataFrames for product-level and sector-level analysis
    """
    logger.info(f"Analyzing export value composition for {country_code} in {year}")
    
    # Validate inputs
    validation_result = _validate_data_for_export_composition(trade_data, product_metadata)
    if not validation_result.is_valid:
        for issue in validation_result.issues:
            logger.warning(f"Data validation issue: {issue}")
        if any(issue.level == ValidationIssueLevel.ERROR for issue in validation_result.issues):
            return {"product_level": pd.DataFrame(), "sector_level": pd.DataFrame()}
    
    # Filter data for the specified year, country, and exports
    if direction_col in trade_data.columns:
        # If direction column exists, use it to filter exports
        filtered_data = trade_data[
            (trade_data[year_col] == year) & 
            (trade_data[direction_col] == "export") &
            (trade_data.get("country", trade_data.get("exporter", "")) == country_code)
        ]
    else:
        # Otherwise, assume exporter column
        exporter_col = "exporter" if "exporter" in trade_data.columns else "i"
        filtered_data = trade_data[
            (trade_data[year_col] == year) & 
            (trade_data[exporter_col] == country_code)
        ]
    
    if filtered_data.empty:
        logger.warning(f"No export data found for {country_code} in {year}")
        return {"product_level": pd.DataFrame(), "sector_level": pd.DataFrame()}
    
    # Optimize the filtered DataFrame
    filtered_data = _optimize_dataframe_for_export_analysis(filtered_data)
    
    # Group by product and aggregate values
    hw_manager = get_hardware_manager()
    if hw_manager.is_hardware_acceleration_available(AccelerationType.CPU):
        try:
            logger.debug("Using hardware acceleration for product aggregation")
            result = hw_manager.accelerate_groupby(
                filtered_data, 
                by=product_col, 
                agg={value_col: 'sum'}
            )
            if result is not None:
                product_totals = result.reset_index()
                logger.debug("Hardware acceleration successful for product aggregation")
            else:
                logger.warning("Hardware acceleration returned None for product aggregation")
                product_totals = filtered_data.groupby(product_col)[value_col].sum().reset_index()
        except Exception as e:
            logger.warning(f"Hardware acceleration failed for product aggregation: {e}")
            product_totals = filtered_data.groupby(product_col)[value_col].sum().reset_index()
    else:
        product_totals = filtered_data.groupby(product_col)[value_col].sum().reset_index()
    
    # Calculate total value and percentages
    total_export_value = product_totals[value_col].sum()
    product_totals['percent_of_total'] = (product_totals[value_col] / total_export_value * 100) if total_export_value > 0 else 0
    
    # Sort by value in descending order
    product_totals = product_totals.sort_values(by=value_col, ascending=False)
    
    # Add ranks
    product_totals['rank'] = range(1, len(product_totals) + 1)
    
    # Determine products to show individually vs. group in "Other"
    if include_other_category:
        individual_products = product_totals[
            (product_totals['rank'] <= top_n_products) | 
            (product_totals['percent_of_total'] >= min_product_pct)
        ]
        other_products = product_totals[
            (product_totals['rank'] > top_n_products) & 
            (product_totals['percent_of_total'] < min_product_pct)
        ]
        
        # Create "Other" category if needed
        if not other_products.empty:
            other_row = pd.DataFrame({
                product_col: ['Other'],
                value_col: [other_products[value_col].sum()],
                'percent_of_total': [other_products['percent_of_total'].sum()],
                'rank': [len(individual_products) + 1]
            })
            product_level_results = pd.concat([individual_products, other_row], ignore_index=True)
        else:
            product_level_results = individual_products.copy()
    else:
        product_level_results = product_totals.copy()
    
    # Merge with product metadata if provided and if sector grouping is requested
    sector_level_results = pd.DataFrame()
    if include_sector_grouping:
        if product_metadata is not None and not product_metadata.empty and sector_col in product_metadata.columns:
            # Merge product totals with product metadata to get sectors
            product_code_col_in_metadata = product_code_col if product_code_col in product_metadata.columns else "code"
            merged_product_data = pd.merge(
                product_totals,
                product_metadata[[product_code_col_in_metadata, sector_col]],
                left_on=product_col,
                right_on=product_code_col_in_metadata,
                how="left"
            )
            # Use 'Unknown' for products without a sector
            merged_product_data[sector_col] = merged_product_data[sector_col].fillna('Unknown')
            
            # Group by sector
            if hw_manager.is_hardware_acceleration_available(AccelerationType.CPU):
                try:
                    logger.debug("Using hardware acceleration for sector aggregation")
                    result = hw_manager.accelerate_groupby(
                        merged_product_data, 
                        by=sector_col, 
                        agg={value_col: 'sum', 'percent_of_total': 'sum'}
                    )
                    if result is not None:
                        sector_totals = result.reset_index()
                        logger.debug("Hardware acceleration successful for sector aggregation")
                    else:
                        logger.warning("Hardware acceleration returned None for sector aggregation")
                        sector_totals = merged_product_data.groupby(sector_col)[[value_col, 'percent_of_total']].sum().reset_index()
                except Exception as e:
                    logger.warning(f"Hardware acceleration failed for sector aggregation: {e}")
                    sector_totals = merged_product_data.groupby(sector_col)[[value_col, 'percent_of_total']].sum().reset_index()
            else:
                sector_totals = merged_product_data.groupby(sector_col)[[value_col, 'percent_of_total']].sum().reset_index()
            
            # Sort by value in descending order
            sector_totals = sector_totals.sort_values(by=value_col, ascending=False)
            
            # Add ranks
            sector_totals['rank'] = range(1, len(sector_totals) + 1)
            
            sector_level_results = sector_totals
        else:
            # If no product metadata available but sector grouping is requested,
            # try to extract sector from product codes using a simple approach
            logger.warning("No product metadata available for sector grouping, attempting to derive sectors from product codes")
            
            # Use first 2 digits of product code as a proxy for sector
            product_totals['extracted_sector'] = product_totals[product_col].astype(str).str[:2]
            
            # Group by extracted sector
            sector_totals = product_totals.groupby('extracted_sector')[[value_col, 'percent_of_total']].sum().reset_index()
            
            # Sort by value in descending order
            sector_totals = sector_totals.sort_values(by=value_col, ascending=False)
            
            # Add ranks
            sector_totals['rank'] = range(1, len(sector_totals) + 1)
            
            # Rename extracted_sector to sector for consistency
            sector_totals = sector_totals.rename(columns={'extracted_sector': sector_col})
            
            sector_level_results = sector_totals
    
    # Add year and country information to results
    product_level_results['year'] = year
    product_level_results['country'] = country_code
    
    if not sector_level_results.empty:
        sector_level_results['year'] = year
        sector_level_results['country'] = country_code
    
    return {
        "product_level": product_level_results,
        "sector_level": sector_level_results
    }

@protect("analyze_export_quantity_composition_v2", OperationType.COMPUTATION)
@memoize(ttl=3600*24, level=StorageTier.DISK)
@log_execution_time(logger=logger)
def analyze_export_quantity_composition_v2(
    trade_data: pd.DataFrame,
    product_metadata: Optional[pd.DataFrame] = None,
    year: int = 2023,
    country_code: str = "887",  # Yemen by default
    product_col: str = "product",
    year_col: str = "year",
    quantity_col: str = "quantity",
    direction_col: str = "direction",
    product_code_col: str = "product_code",
    sector_col: str = "sector",
    top_n_products: int = 10,
    min_product_pct: float = 0.5,  # Minimum percentage for a product to be shown individually
    include_other_category: bool = True,
    include_sector_grouping: bool = True
) -> Dict[str, pd.DataFrame]:
    """
    Analyze export composition by quantity for a specific country and year.
    
    Args:
        trade_data: DataFrame with trade data (must have year, product, quantity columns)
        product_metadata: Optional DataFrame with product metadata (product_code, sector)
        year: Year to analyze
        country_code: Country code to analyze (default: "887" for Yemen)
        product_col: Name of the product column in trade_data
        year_col: Name of the year column in trade_data
        quantity_col: Name of the quantity column in trade_data
        direction_col: Name of the direction column in trade_data
        product_code_col: Name of the product code column in product_metadata
        sector_col: Name of the sector column in product_metadata
        top_n_products: Number of top products to show individually
        min_product_pct: Minimum percentage for a product to be shown individually
        include_other_category: Whether to include an "Other" category for small products
        include_sector_grouping: Whether to include sector-level grouping
        
    Returns:
        Dictionary with DataFrames for product-level and sector-level analysis
    """
    logger.info(f"Analyzing export quantity composition for {country_code} in {year}")
    
    # Check if quantity column exists
    if quantity_col not in trade_data.columns:
        logger.error(f"Quantity column '{quantity_col}' not found in trade data")
        raise ValueError(f"Required quantity column '{quantity_col}' not found.")
    
    # Validate inputs with modified schema to require quantity column
    modified_schema = TRADE_DATA_SCHEMA.copy()
    modified_schema['required_columns'] = [year_col, product_col, quantity_col]
    
    validation_result = _validate_data_for_export_composition(trade_data, product_metadata)
    if not validation_result.is_valid:
        for issue in validation_result.issues:
            logger.warning(f"Data validation issue: {issue}")
        if any(issue.level == ValidationIssueLevel.ERROR for issue in validation_result.issues):
            return {"product_level": pd.DataFrame(), "sector_level": pd.DataFrame()}
    
    # Filter data for the specified year, country, and exports
    if direction_col in trade_data.columns:
        # If direction column exists, use it to filter exports
        filtered_data = trade_data[
            (trade_data[year_col] == year) & 
            (trade_data[direction_col] == "export") &
            (trade_data.get("country", trade_data.get("exporter", "")) == country_code)
        ]
    else:
        # Otherwise, assume exporter column
        exporter_col = "exporter" if "exporter" in trade_data.columns else "i"
        filtered_data = trade_data[
            (trade_data[year_col] == year) & 
            (trade_data[exporter_col] == country_code)
        ]
    
    if filtered_data.empty:
        logger.warning(f"No export data found for {country_code} in {year}")
        return {"product_level": pd.DataFrame(), "sector_level": pd.DataFrame()}
    
    # Filter out records with missing or zero quantity
    filtered_data = filtered_data[filtered_data[quantity_col].notna() & (filtered_data[quantity_col] > 0)]
    
    if filtered_data.empty:
        logger.warning(f"No export data with valid quantity found for {country_code} in {year}")
        return {"product_level": pd.DataFrame(), "sector_level": pd.DataFrame()}
    
    # Optimize the filtered DataFrame
    filtered_data = _optimize_dataframe_for_export_analysis(filtered_data)
    
    # Group by product and aggregate quantities
    hw_manager = get_hardware_manager()
    if hw_manager.is_hardware_acceleration_available(AccelerationType.CPU):
        try:
            logger.debug("Using hardware acceleration for product aggregation")
            result = hw_manager.accelerate_groupby(
                filtered_data, 
                by=product_col, 
                agg={quantity_col: 'sum'}
            )
            if result is not None:
                product_totals = result.reset_index()
                logger.debug("Hardware acceleration successful for product aggregation")
            else:
                logger.warning("Hardware acceleration returned None for product aggregation")
                product_totals = filtered_data.groupby(product_col)[quantity_col].sum().reset_index()
        except Exception as e:
            logger.warning(f"Hardware acceleration failed for product aggregation: {e}")
            product_totals = filtered_data.groupby(product_col)[quantity_col].sum().reset_index()
    else:
        product_totals = filtered_data.groupby(product_col)[quantity_col].sum().reset_index()
    
    # Calculate total quantity and percentages
    total_export_quantity = product_totals[quantity_col].sum()
    product_totals['percent_of_total'] = (product_totals[quantity_col] / total_export_quantity * 100) if total_export_quantity > 0 else 0
    
    # Sort by quantity in descending order
    product_totals = product_totals.sort_values(by=quantity_col, ascending=False)
    
    # Add ranks
    product_totals['rank'] = range(1, len(product_totals) + 1)
    
    # Determine products to show individually vs. group in "Other"
    if include_other_category:
        individual_products = product_totals[
            (product_totals['rank'] <= top_n_products) | 
            (product_totals['percent_of_total'] >= min_product_pct)
        ]
        other_products = product_totals[
            (product_totals['rank'] > top_n_products) & 
            (product_totals['percent_of_total'] < min_product_pct)
        ]
        
        # Create "Other" category if needed
        if not other_products.empty:
            other_row = pd.DataFrame({
                product_col: ['Other'],
                quantity_col: [other_products[quantity_col].sum()],
                'percent_of_total': [other_products['percent_of_total'].sum()],
                'rank': [len(individual_products) + 1]
            })
            product_level_results = pd.concat([individual_products, other_row], ignore_index=True)
        else:
            product_level_results = individual_products.copy()
    else:
        product_level_results = product_totals.copy()
    
    # Add value column from original data if available
    if 'value' in filtered_data.columns:
        # Group by product to get value sums
        value_by_product = filtered_data.groupby(product_col)['value'].sum().reset_index()
        
        # Merge with product level results
        product_level_results = pd.merge(
            product_level_results,
            value_by_product,
            on=product_col,
            how='left'
        )
    
    # Merge with product metadata if provided and if sector grouping is requested
    sector_level_results = pd.DataFrame()
    if include_sector_grouping:
        if product_metadata is not None and not product_metadata.empty and sector_col in product_metadata.columns:
            # Merge product totals with product metadata to get sectors
            product_code_col_in_metadata = product_code_col if product_code_col in product_metadata.columns else "code"
            merged_product_data = pd.merge(
                product_totals,
                product_metadata[[product_code_col_in_metadata, sector_col]],
                left_on=product_col,
                right_on=product_code_col_in_metadata,
                how="left"
            )
            # Use 'Unknown' for products without a sector
            merged_product_data[sector_col] = merged_product_data[sector_col].fillna('Unknown')
            
            # Group by sector
            if hw_manager.is_hardware_acceleration_available(AccelerationType.CPU):
                try:
                    logger.debug("Using hardware acceleration for sector aggregation")
                    result = hw_manager.accelerate_groupby(
                        merged_product_data, 
                        by=sector_col, 
                        agg={quantity_col: 'sum', 'percent_of_total': 'sum'}
                    )
                    if result is not None:
                        sector_totals = result.reset_index()
                        logger.debug("Hardware acceleration successful for sector aggregation")
                    else:
                        logger.warning("Hardware acceleration returned None for sector aggregation")
                        sector_totals = merged_product_data.groupby(sector_col)[[quantity_col, 'percent_of_total']].sum().reset_index()
                except Exception as e:
                    logger.warning(f"Hardware acceleration failed for sector aggregation: {e}")
                    sector_totals = merged_product_data.groupby(sector_col)[[quantity_col, 'percent_of_total']].sum().reset_index()
            else:
                sector_totals = merged_product_data.groupby(sector_col)[[quantity_col, 'percent_of_total']].sum().reset_index()
            
            # Add value column to sector totals if available
            if 'value' in filtered_data.columns:
                # Merge product data with value
                merged_product_data_with_value = pd.merge(
                    merged_product_data,
                    value_by_product,
                    on=product_col,
                    how='left'
                )
                # Group by sector to get value sums
                sector_values = merged_product_data_with_value.groupby(sector_col)['value'].sum().reset_index()
                
                # Merge with sector totals
                sector_totals = pd.merge(
                    sector_totals,
                    sector_values,
                    on=sector_col,
                    how='left'
                )
            
            # Sort by quantity in descending order
            sector_totals = sector_totals.sort_values(by=quantity_col, ascending=False)
            
            # Add ranks
            sector_totals['rank'] = range(1, len(sector_totals) + 1)
            
            sector_level_results = sector_totals
        else:
            # If no product metadata available but sector grouping is requested,
            # try to extract sector from product codes using a simple approach
            logger.warning("No product metadata available for sector grouping, attempting to derive sectors from product codes")
            
            # Use first 2 digits of product code as a proxy for sector
            product_totals['extracted_sector'] = product_totals[product_col].astype(str).str[:2]
            
            # Group by extracted sector
            sector_totals = product_totals.groupby('extracted_sector')[[quantity_col, 'percent_of_total']].sum().reset_index()
            
            # Add value column to sector totals if available
            if 'value' in filtered_data.columns:
                # Group product data with extracted sector
                product_data_with_sector = product_totals.copy()
                product_data_with_sector['extracted_sector'] = product_data_with_sector[product_col].astype(str).str[:2]
                
                # Merge with value by product
                product_data_with_sector_and_value = pd.merge(
                    product_data_with_sector,
                    value_by_product,
                    on=product_col,
                    how='left'
                )
                # Group by extracted sector to get value sums
                sector_values = product_data_with_sector_and_value.groupby('extracted_sector')['value'].sum().reset_index()
                
                # Merge with sector totals
                sector_totals = pd.merge(
                    sector_totals,
                    sector_values,
                    on='extracted_sector',
                    how='left'
                )
            
            # Sort by quantity in descending order
            sector_totals = sector_totals.sort_values(by=quantity_col, ascending=False)
            
            # Add ranks
            sector_totals['rank'] = range(1, len(sector_totals) + 1)
            
            # Rename extracted_sector to sector for consistency
            sector_totals = sector_totals.rename(columns={'extracted_sector': sector_col})
            
            sector_level_results = sector_totals
    
    # Add year and country information to results
    product_level_results['year'] = year
    product_level_results['country'] = country_code
    
    if not sector_level_results.empty:
        sector_level_results['year'] = year
        sector_level_results['country'] = country_code
    
    return {
        "product_level": product_level_results,
        "sector_level": sector_level_results
    }

@protect("analyze_export_composition_time_series_v2", OperationType.COMPUTATION)
@memoize(ttl=3600*24, level=StorageTier.DISK)
@log_execution_time(logger=logger)
def analyze_export_composition_time_series_v2(
    trade_data: pd.DataFrame,
    product_metadata: Optional[pd.DataFrame] = None,
    country_code: str = "887",  # Yemen by default
    start_year: Optional[int] = None,
    end_year: Optional[int] = None,
    product_col: str = "product",
    year_col: str = "year",
    value_col: str = "value",
    quantity_col: Optional[str] = "quantity",
    direction_col: str = "direction",
    product_code_col: str = "product_code",
    sector_col: str = "sector",
    top_n_products: int = 5,
    include_sector_grouping: bool = True,
    composition_measure: str = "value"  # "value" or "quantity"
) -> Dict[str, pd.DataFrame]:
    """
    Analyze export composition over time for a specific country.
    
    Args:
        trade_data: DataFrame with trade data (must have year, product, value/quantity columns)
        product_metadata: Optional DataFrame with product metadata (product_code, sector)
        country_code: Country code to analyze (default: "887" for Yemen)
        start_year: Optional start year for time series analysis (defaults to min year in data)
        end_year: Optional end year for time series analysis (defaults to max year in data)
        product_col: Name of the product column in trade_data
        year_col: Name of the year column in trade_data
        value_col: Name of the value column in trade_data
        quantity_col: Optional name of the quantity column in trade_data
        direction_col: Name of the direction column in trade_data
        product_code_col: Name of the product code column in product_metadata
        sector_col: Name of the sector column in product_metadata
        top_n_products: Number of top products to track individually over time
        include_sector_grouping: Whether to include sector-level grouping
        composition_measure: Measure to use for composition analysis ("value" or "quantity")
        
    Returns:
        Dictionary with DataFrames for product-level and sector-level time series analysis
    """
    logger.info(f"Analyzing export composition time series for {country_code} from {start_year or 'min'} to {end_year or 'max'}")
    
    # Check if requested measure column exists
    measure_col = value_col if composition_measure == "value" else quantity_col
    if measure_col not in trade_data.columns:
        logger.error(f"Requested measure column '{measure_col}' not found in trade data")
        raise ValueError(f"Requested measure column '{measure_col}' not found.")
    
    # Validate inputs
    validation_result = _validate_data_for_export_composition(trade_data, product_metadata)
    if not validation_result.is_valid:
        for issue in validation_result.issues:
            logger.warning(f"Data validation issue: {issue}")
        if any(issue.level == ValidationIssueLevel.ERROR for issue in validation_result.issues):
            return {"product_level": pd.DataFrame(), "sector_level": pd.DataFrame()}
    
    # Filter data for the specified country and exports
    if direction_col in trade_data.columns:
        # If direction column exists, use it to filter exports
        filtered_data = trade_data[
            (trade_data[direction_col] == "export") &
            (trade_data.get("country", trade_data.get("exporter", "")) == country_code)
        ]
    else:
        # Otherwise, assume exporter column
        exporter_col = "exporter" if "exporter" in trade_data.columns else "i"
        filtered_data = trade_data[trade_data[exporter_col] == country_code]
    
    if filtered_data.empty:
        logger.warning(f"No export data found for {country_code}")
        return {"product_level": pd.DataFrame(), "sector_level": pd.DataFrame()}
    
    # Filter by year range if specified
    if start_year is not None:
        filtered_data = filtered_data[filtered_data[year_col] >= start_year]
    if end_year is not None:
        filtered_data = filtered_data[filtered_data[year_col] <= end_year]
    
    # Ensure all data has valid values for the measure column
    filtered_data = filtered_data[filtered_data[measure_col].notna() & (filtered_data[measure_col] >= 0)]
    
    if filtered_data.empty:
        logger.warning(f"No export data found for {country_code} in the specified year range with valid {measure_col}")
        return {"product_level": pd.DataFrame(), "sector_level": pd.DataFrame()}
    
    # Optimize the filtered DataFrame
    filtered_data = _optimize_dataframe_for_export_analysis(filtered_data)
    
    # Get unique years in the filtered data
    years = sorted(filtered_data[year_col].unique())
    
    # Identify top products across all years combined
    hw_manager = get_hardware_manager()
    if hw_manager.is_hardware_acceleration_available(AccelerationType.CPU):
        try:
            logger.debug("Using hardware acceleration for overall product aggregation")
            result = hw_manager.accelerate_groupby(
                filtered_data, 
                by=product_col, 
                agg={measure_col: 'sum'}
            )
            if result is not None:
                overall_product_totals = result.reset_index()
                logger.debug("Hardware acceleration successful for overall product aggregation")
            else:
                logger.warning("Hardware acceleration returned None for overall product aggregation")
                overall_product_totals = filtered_data.groupby(product_col)[measure_col].sum().reset_index()
        except Exception as e:
            logger.warning(f"Hardware acceleration failed for overall product aggregation: {e}")
            overall_product_totals = filtered_data.groupby(product_col)[measure_col].sum().reset_index()
    else:
        overall_product_totals = filtered_data.groupby(product_col)[measure_col].sum().reset_index()
    
    # Sort by measure in descending order and get top N products
    overall_product_totals = overall_product_totals.sort_values(by=measure_col, ascending=False)
    top_products = overall_product_totals.head(top_n_products)[product_col].tolist()
    
    # Create a list to hold results for each year
    product_level_results_by_year = []
    sector_level_results_by_year = []
    
    # Process each year
    for year in years:
        year_data = filtered_data[filtered_data[year_col] == year]
        
        # Get total for the year
        year_total = year_data[measure_col].sum()
        
        # Group by product for this year
        if hw_manager.is_hardware_acceleration_available(AccelerationType.CPU):
            try:
                logger.debug(f"Using hardware acceleration for product aggregation in {year}")
                result = hw_manager.accelerate_groupby(
                    year_data, 
                    by=product_col, 
                    agg={measure_col: 'sum'}
                )
                if result is not None:
                    year_product_totals = result.reset_index()
                    logger.debug(f"Hardware acceleration successful for product aggregation in {year}")
                else:
                    logger.warning(f"Hardware acceleration returned None for product aggregation in {year}")
                    year_product_totals = year_data.groupby(product_col)[measure_col].sum().reset_index()
            except Exception as e:
                logger.warning(f"Hardware acceleration failed for product aggregation in {year}: {e}")
                year_product_totals = year_data.groupby(product_col)[measure_col].sum().reset_index()
        else:
            year_product_totals = year_data.groupby(product_col)[measure_col].sum().reset_index()
        
        # Calculate percentages
        year_product_totals['percent_of_total'] = (year_product_totals[measure_col] / year_total * 100) if year_total > 0 else 0
        
        # Filter for top products across all years and add "Other" for the rest
        top_product_data = year_product_totals[year_product_totals[product_col].isin(top_products)]
        other_product_data = year_product_totals[~year_product_totals[product_col].isin(top_products)]
        
        if not other_product_data.empty:
            other_row = pd.DataFrame({
                product_col: ['Other'],
                measure_col: [other_product_data[measure_col].sum()],
                'percent_of_total': [other_product_data['percent_of_total'].sum()]
            })
            year_product_results = pd.concat([top_product_data, other_row], ignore_index=True)
        else:
            year_product_results = top_product_data.copy()
        
        # Add year and country information
        year_product_results['year'] = year
        year_product_results['country'] = country_code
        
        # Add to list of results
        product_level_results_by_year.append(year_product_results)
        
        # Process sector-level data if requested
        if include_sector_grouping and product_metadata is not None and not product_metadata.empty and sector_col in product_metadata.columns:
            # Merge product totals with product metadata to get sectors
            product_code_col_in_metadata = product_code_col if product_code_col in product_metadata.columns else "code"
            merged_product_data = pd.merge(
                year_product_totals,
                product_metadata[[product_code_col_in_metadata, sector_col]],
                left_on=product_col,
                right_on=product_code_col_in_metadata,
                how="left"
            )
            # Use 'Unknown' for products without a sector
            merged_product_data[sector_col] = merged_product_data[sector_col].fillna('Unknown')
            
            # Group by sector
            if hw_manager.is_hardware_acceleration_available(AccelerationType.CPU):
                try:
                    logger.debug(f"Using hardware acceleration for sector aggregation in {year}")
                    result = hw_manager.accelerate_groupby(
                        merged_product_data, 
                        by=sector_col, 
                        agg={measure_col: 'sum', 'percent_of_total': 'sum'}
                    )
                    if result is not None:
                        year_sector_totals = result.reset_index()
                        logger.debug(f"Hardware acceleration successful for sector aggregation in {year}")
                    else:
                        logger.warning(f"Hardware acceleration returned None for sector aggregation in {year}")
                        year_sector_totals = merged_product_data.groupby(sector_col)[[measure_col, 'percent_of_total']].sum().reset_index()
                except Exception as e:
                    logger.warning(f"Hardware acceleration failed for sector aggregation in {year}: {e}")
                    year_sector_totals = merged_product_data.groupby(sector_col)[[measure_col, 'percent_of_total']].sum().reset_index()
            else:
                year_sector_totals = merged_product_data.groupby(sector_col)[[measure_col, 'percent_of_total']].sum().reset_index()
            
            # Add year and country information
            year_sector_totals['year'] = year
            year_sector_totals['country'] = country_code
            
            # Add to list of results
            sector_level_results_by_year.append(year_sector_totals)
    
    # Combine results from all years
    product_level_time_series = pd.concat(product_level_results_by_year, ignore_index=True) if product_level_results_by_year else pd.DataFrame()
    sector_level_time_series = pd.concat(sector_level_results_by_year, ignore_index=True) if sector_level_results_by_year else pd.DataFrame()
    
    # Sort the time series results
    if not product_level_time_series.empty:
        product_level_time_series = product_level_time_series.sort_values(by=[year_col, 'percent_of_total'], ascending=[True, False])
    
    if not sector_level_time_series.empty:
        sector_level_time_series = sector_level_time_series.sort_values(by=[year_col, 'percent_of_total'], ascending=[True, False])
    
    return {
        "product_level": product_level_time_series,
        "sector_level": sector_level_time_series
    }

# Class-based API for the model
class ExportCompositionModelV2:
    """Model for export composition analysis (V2)."""
    
    def __init__(self, data_dir: Optional[Path] = None):
        """
        Initialize the model.
        
        Args:
            data_dir: Optional base directory for data files
        """
        self.logger = get_logger(f"{__name__}.ExportCompositionModelV2")
        self.data_dir = data_dir
        self.hw_manager = get_hardware_manager()
        self.logger.info("ExportCompositionModelV2 initialized")
    
    @protect("analyze_export_value_composition", OperationType.COMPUTATION)
    def analyze_export_value_composition(self, trade_data: pd.DataFrame, **kwargs) -> Dict[str, pd.DataFrame]:
        """Analyze export composition by value using the function-based API."""
        return analyze_export_value_composition_v2(trade_data, **kwargs)
    
    @protect("analyze_export_quantity_composition", OperationType.COMPUTATION)
    def analyze_export_quantity_composition(self, trade_data: pd.DataFrame, **kwargs) -> Dict[str, pd.DataFrame]:
        """Analyze export composition by quantity using the function-based API."""
        return analyze_export_quantity_composition_v2(trade_data, **kwargs)
    
    @protect("analyze_export_composition_time_series", OperationType.COMPUTATION)
    def analyze_export_composition_time_series(self, trade_data: pd.DataFrame, **kwargs) -> Dict[str, pd.DataFrame]:
        """Analyze export composition over time using the function-based API."""
        return analyze_export_composition_time_series_v2(trade_data, **kwargs)

# Example usage
if __name__ == "__main__":
    configure_logging(log_level=LogLevel.INFO, log_to_console=True)
    logger.info("--- Running Export Composition Model V2 Example ---")
    
    # Create sample data
    sample_trade_data = pd.DataFrame({
        'year': [2020, 2020, 2020, 2020, 2021, 2021, 2021, 2021],
        'product': ['010101', '010102', '020101', '030101', '010101', '010102', '020101', '030101'],
        'value': [500, 300, 200, 100, 600, 350, 180, 120],
        'quantity': [5000, 3000, 2000, 1000, 6000, 3500, 1800, 1200],
        'direction': ['export', 'export', 'export', 'export', 'export', 'export', 'export', 'export'],
        'country': ['887', '887', '887', '887', '887', '887', '887', '887']
    })
    
    sample_product_metadata = pd.DataFrame({
        'product_code': ['010101', '010102', '020101', '030101'],
        'product_name': ['Live horses', 'Live donkeys', 'Beef', 'Fish'],
        'sector': ['Animals', 'Animals', 'Meat', 'Seafood']
    })
    
    # Create model instance
    model = ExportCompositionModelV2()
    
    # Test export value composition for a specific year
    value_composition = model.analyze_export_value_composition(
        sample_trade_data,
        product_metadata=sample_product_metadata,
        year=2020,
        country_code='887'
    )
    logger.info(f"Export value composition for 2020 (product level):\n{value_composition['product_level']}")
    if 'sector_level' in value_composition and not value_composition['sector_level'].empty:
        logger.info(f"Export value composition for 2020 (sector level):\n{value_composition['sector_level']}")
    
    # Test export quantity composition for a specific year
    quantity_composition = model.analyze_export_quantity_composition(
        sample_trade_data,
        product_metadata=sample_product_metadata,
        year=2020,
        country_code='887'
    )
    logger.info(f"Export quantity composition for 2020 (product level):\n{quantity_composition['product_level']}")
    if 'sector_level' in quantity_composition and not quantity_composition['sector_level'].empty:
        logger.info(f"Export quantity composition for 2020 (sector level):\n{quantity_composition['sector_level']}")
    
    # Test export composition time series
    time_series = model.analyze_export_composition_time_series(
        sample_trade_data,
        product_metadata=sample_product_metadata,
        country_code='887',
        start_year=2020,
        end_year=2021,
        composition_measure='value'
    )
    logger.info(f"Export composition time series (product level):\n{time_series['product_level']}")
    if 'sector_level' in time_series and not time_series['sector_level'].empty:
        logger.info(f"Export composition time series (sector level):\n{time_series['sector_level']}")
    
    logger.info("--- Export Composition Model V2 Example Finished ---")