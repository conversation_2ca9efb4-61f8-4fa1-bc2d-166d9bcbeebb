"""
Optimized Gravity Model for Yemen Trade Diagnostic

This module provides a high-performance implementation of the gravity model for
international trade that leverages hardware acceleration and optimized matrix operations
to improve performance, especially for large datasets.
"""

# Standard library imports
import time
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union, cast

# Third-party imports
import numpy as np
import pandas as pd
import statsmodels.api as sm

# Project imports
from yemen_trade_diagnostic.data import DataSource, load_data
from yemen_trade_diagnostic.interfaces.cache_interface import StorageTier, DataLifetime, memoize
from yemen_trade_diagnostic.interfaces.hardware_interface import AccelerationType, WorkloadProfile, WorkloadType, get_hardware_manager
from yemen_trade_diagnostic.interfaces.logging_interface import LogLevel, get_logger, log_execution_time
from yemen_trade_diagnostic.interfaces.validation_interface import ValidationIssueLevel, ValidationResult, get_validation_manager
from yemen_trade_diagnostic.errors import protect, OperationType

# Configure logger
logger = get_logger(__name__)

# Constants
LOG_TRADE_OFFSET = 1
GRAVITY_MODEL_WORKLOAD = WorkloadProfile(
    name="gravity_model",
    workload_type=WorkloadType.COMPUTE_INTENSIVE,
    memory_usage="high",
    compute_intensity="high",
    io_intensity="low"
)

# Type aliases for clarity
GravityModelResult = Dict[str, Any]

def get_optimal_hardware_type() -> AccelerationType:
    """
    Determine the optimal hardware type for gravity model operations based on available hardware.
    
    Returns:
        AccelerationType: The optimal hardware type for matrix operations
    """
    hw_manager = get_hardware_manager()
    
    # Prefer GPU for large matrix operations, then CPU
    if hw_manager.is_hardware_acceleration_available(AccelerationType.GPU):
        return AccelerationType.GPU
    elif hw_manager.is_hardware_acceleration_available(AccelerationType.CPU):
        return AccelerationType.CPU
    else:
        return AccelerationType.NONE

class OptimizedGravityModel:
    """
    Optimized implementation of the gravity model for international trade analysis
    that leverages hardware acceleration and efficient matrix operations.
    """
    
    def __init__(
        self,
        raw_data_dir: Optional[Path] = None,
        processed_data_dir: Optional[Path] = None,
        use_acceleration: bool = True
    ):
        """
        Initialize the Optimized Gravity Model.
        
        Args:
            raw_data_dir: Directory for raw data files
            processed_data_dir: Directory for processed data and caching
            use_acceleration: Whether to use hardware acceleration
        """
        self.logger = get_logger(f"{__name__}.OptimizedGravityModel")
        self.hw_manager = get_hardware_manager()
        self.validation_manager = get_validation_manager()
        
        # Set default paths if not provided
        self.raw_data_dir = raw_data_dir if raw_data_dir else Path("./data/raw")
        self.processed_data_dir = processed_data_dir if processed_data_dir else Path("./data/processed")
        
        # Configure hardware acceleration
        self.use_acceleration = use_acceleration
        if use_acceleration:
            self.acceleration_type = get_optimal_hardware_type()
            acc_status = "ENABLED" if self.hw_manager.is_hardware_acceleration_available() else "NOT AVAILABLE"
            self.logger.info(f"Hardware acceleration {acc_status} using {self.acceleration_type.name}")
        else:
            self.acceleration_type = AccelerationType.NONE
            self.logger.info("Hardware acceleration disabled")
        
        self.logger.info("OptimizedGravityModel initialized")
    
    def _validate_input_data(
        self, 
        df: pd.DataFrame, 
        required_cols: List[str],
        col_types: Dict[str, str]
    ) -> bool:
        """
        Validate input DataFrame.
        
        Args:
            df: DataFrame to validate
            required_cols: List of required column names
            col_types: Dictionary mapping column names to expected types
            
        Returns:
            True if validation passed, False otherwise
        """
        # Check if DataFrame is empty
        if df.empty:
            self.logger.error("Input DataFrame is empty")
            return False
        
        # Check required columns
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            self.logger.error(f"Missing required columns: {missing_cols}")
            return False
        
        # Check column types
        for col, expected_type in col_types.items():
            if col not in df.columns:
                continue
                
            actual_dtype = df[col].dtype
            type_ok = False
            
            if expected_type == 'string':
                type_ok = pd.api.types.is_string_dtype(actual_dtype) or pd.api.types.is_object_dtype(actual_dtype)
                
                # Try to convert numeric to string if needed
                if not type_ok and pd.api.types.is_numeric_dtype(actual_dtype):
                    try:
                        df[col] = df[col].astype(str)
                        type_ok = True
                        self.logger.info(f"Converted column '{col}' from {actual_dtype} to string")
                    except Exception as e:
                        self.logger.warning(f"Could not convert column '{col}' to string: {e}")
                        
            elif expected_type == 'integer':
                type_ok = pd.api.types.is_integer_dtype(actual_dtype)
                
                # Try to convert to integer if numeric but not integer
                if not type_ok and pd.api.types.is_numeric_dtype(actual_dtype) and not df[col].isna().any():
                    try:
                        if (df[col] % 1 == 0).all():  # Check if all values are whole numbers
                            df[col] = df[col].astype(int)
                            type_ok = True
                            self.logger.info(f"Converted column '{col}' to integer")
                    except Exception as e:
                        self.logger.warning(f"Could not convert column '{col}' to integer: {e}")
                        
            elif expected_type == 'numeric':
                type_ok = pd.api.types.is_numeric_dtype(actual_dtype)
                
                # Try to convert to numeric if string/object
                if not type_ok and (pd.api.types.is_string_dtype(actual_dtype) or pd.api.types.is_object_dtype(actual_dtype)):
                    try:
                        df[col] = pd.to_numeric(df[col], errors='coerce')
                        type_ok = True
                        self.logger.info(f"Converted column '{col}' to numeric")
                    except Exception as e:
                        self.logger.warning(f"Could not convert column '{col}' to numeric: {e}")
            
            if not type_ok:
                self.logger.error(f"Column '{col}' has unexpected type {actual_dtype}, expected {expected_type}")
                return False
        
        return True
    
    def _filter_trade_by_year(
        self,
        trade_data: pd.DataFrame,
        year_col: str,
        year: Optional[int] = None,
        min_year: Optional[int] = None,
        max_year: Optional[int] = None
    ) -> pd.DataFrame:
        """
        Filter trade data by year with optimized performance.
        
        Args:
            trade_data: Trade data DataFrame
            year_col: Column name for year
            year: Specific year to filter for
            min_year: Minimum year (inclusive)
            max_year: Maximum year (inclusive)
            
        Returns:
            Filtered DataFrame
        """
        self.logger.debug(f"Filtering trade data by year. Period: {year or (min_year, max_year)}")
        
        # Use NumPy for faster boolean indexing
        years = trade_data[year_col].values
        
        if year is not None:
            mask = years == year
        elif min_year is not None and max_year is not None:
            mask = (years >= min_year) & (years <= max_year)
        elif min_year is not None:
            mask = years >= min_year
        elif max_year is not None:
            mask = years <= max_year
        else:
            # No filtering needed
            return trade_data.copy()
        
        # Apply mask using iloc for better performance with large DataFrames
        indices = np.where(mask)[0]
        filtered_trade = trade_data.iloc[indices].copy()
        
        return filtered_trade
    
    @log_execution_time(logger=logger)
    def _prepare_merged_dataset(
        self,
        trade_data: pd.DataFrame, 
        gdp_data: pd.DataFrame, 
        distance_data: pd.DataFrame,
        exporter_col: str, 
        importer_col: str, 
        year_col: str, 
        value_col: str,
        exporter_gdp_col: str, 
        importer_gdp_col: str, 
        distance_col: str, 
        gdp_col: str = "gdp"
    ) -> pd.DataFrame:
        """
        Optimized preparation of merged dataset for gravity model.
        
        Args:
            trade_data: Trade flow data
            gdp_data: GDP data
            distance_data: Distance data
            exporter_col: Column name for exporter country codes
            importer_col: Column name for importer country codes
            year_col: Column name for year
            value_col: Column name for trade values
            exporter_gdp_col: Output column name for exporter GDP
            importer_gdp_col: Output column name for importer GDP
            distance_col: Column name for distance
            gdp_col: Column name for GDP in gdp_data
            
        Returns:
            Merged dataset ready for gravity model estimation
        """
        self.logger.debug("Preparing merged dataset for gravity model with optimized operations")
        
        # Use groupby with optimized aggregation
        trade_flows = trade_data.groupby([exporter_col, importer_col, year_col], observed=True)[value_col].sum().reset_index()
        
        # Prepare GDP data with optimized renaming
        gdp_data_exp = gdp_data.rename(columns={gdp_col: exporter_gdp_col, 'country_code': exporter_col})
        gdp_data_imp = gdp_data.rename(columns={gdp_col: importer_gdp_col, 'country_code': importer_col})
        
        # Use optimized merge with only necessary columns
        gdp_exp_columns = [exporter_col, year_col, exporter_gdp_col]
        gdp_imp_columns = [importer_col, year_col, importer_gdp_col]
        dist_columns = [exporter_col, importer_col, distance_col]
        
        # Merge with exporter GDP
        merged_data = pd.merge(
            trade_flows, 
            gdp_data_exp[gdp_exp_columns], 
            on=[exporter_col, year_col], 
            how="inner"
        )
        
        # Merge with importer GDP
        merged_data = pd.merge(
            merged_data, 
            gdp_data_imp[gdp_imp_columns], 
            on=[importer_col, year_col], 
            how="inner"
        )
        
        # Merge with distance data
        merged_data = pd.merge(
            merged_data, 
            distance_data[dist_columns], 
            on=[exporter_col, importer_col], 
            how="inner"
        )
        
        if merged_data.empty:
            self.logger.warning("Merged dataset is empty after joins")
            raise ValueError("No data after merging required datasets for gravity model preparation")
        
        # Efficiently log-transform variables using hardware acceleration
        if self.use_acceleration and self.hw_manager.is_hardware_acceleration_available():
            self._log_transform_variables_accelerated(merged_data, value_col, exporter_gdp_col, importer_gdp_col, distance_col)
        else:
            self._log_transform_variables_standard(merged_data, value_col, exporter_gdp_col, importer_gdp_col, distance_col)
        
        # Drop rows with NaN in log-transformed cols, as they can't be used in OLS
        log_cols = [f"ln_{value_col}", f"ln_{exporter_gdp_col}", f"ln_{importer_gdp_col}", f"ln_{distance_col}"]
        merged_data.dropna(subset=log_cols, inplace=True)
        
        self.logger.debug(f"Merged dataset prepared with {len(merged_data)} rows")
        return merged_data
    
    def _log_transform_variables_accelerated(
        self,
        data: pd.DataFrame,
        value_col: str,
        exporter_gdp_col: str,
        importer_gdp_col: str,
        distance_col: str
    ) -> None:
        """
        Apply log transformations using hardware acceleration.
        
        Args:
            data: DataFrame to transform
            value_col: Column name for trade values
            exporter_gdp_col: Column name for exporter GDP
            importer_gdp_col: Column name for importer GDP
            distance_col: Column name for distance
        """
        hw_manager = self.hw_manager
        
        # Convert DataFrame columns to NumPy arrays for hardware acceleration
        trade_values = data[value_col].values.astype(np.float32) + LOG_TRADE_OFFSET
        exp_gdp = data[exporter_gdp_col].replace(0, np.nan).values.astype(np.float32) + LOG_TRADE_OFFSET
        imp_gdp = data[importer_gdp_col].replace(0, np.nan).values.astype(np.float32) + LOG_TRADE_OFFSET
        distances = data[distance_col].replace(0, np.nan).values.astype(np.float32)
        
        # Apply log transformations using hardware acceleration
        ln_trade = hw_manager.accelerate_array(trade_values, "log")
        ln_exp_gdp = hw_manager.accelerate_array(exp_gdp, "log")
        ln_imp_gdp = hw_manager.accelerate_array(imp_gdp, "log")
        ln_dist = hw_manager.accelerate_array(distances, "log")
        
        # Fall back to NumPy if hardware acceleration fails
        if ln_trade is None:
            ln_trade = np.log(trade_values)
        if ln_exp_gdp is None:
            ln_exp_gdp = np.log(exp_gdp)
        if ln_imp_gdp is None:
            ln_imp_gdp = np.log(imp_gdp)
        if ln_dist is None:
            ln_dist = np.log(distances)
        
        # Add transformed columns back to DataFrame
        data[f"ln_{value_col}"] = ln_trade
        data[f"ln_{exporter_gdp_col}"] = ln_exp_gdp
        data[f"ln_{importer_gdp_col}"] = ln_imp_gdp
        data[f"ln_{distance_col}"] = ln_dist
    
    def _log_transform_variables_standard(
        self,
        data: pd.DataFrame,
        value_col: str,
        exporter_gdp_col: str,
        importer_gdp_col: str,
        distance_col: str
    ) -> None:
        """
        Apply log transformations using standard NumPy operations.
        
        Args:
            data: DataFrame to transform
            value_col: Column name for trade values
            exporter_gdp_col: Column name for exporter GDP
            importer_gdp_col: Column name for importer GDP
            distance_col: Column name for distance
        """
        # Log-transform variables using NumPy
        data[f"ln_{value_col}"] = np.log(data[value_col] + LOG_TRADE_OFFSET)
        data[f"ln_{exporter_gdp_col}"] = np.log(data[exporter_gdp_col].replace(0, np.nan) + LOG_TRADE_OFFSET)
        data[f"ln_{importer_gdp_col}"] = np.log(data[importer_gdp_col].replace(0, np.nan) + LOG_TRADE_OFFSET)
        data[f"ln_{distance_col}"] = np.log(data[distance_col].replace(0, np.nan))
    
    def _run_gravity_regression(
        self,
        data: pd.DataFrame,
        value_col: str,
        exporter_gdp_col: str,
        importer_gdp_col: str,
        distance_col: str
    ) -> Optional[GravityModelResult]:
        """
        Run gravity model regression with optimized data preparation.
        
        Args:
            data: Prepared data for regression
            value_col: Column name for trade values
            exporter_gdp_col: Column name for exporter GDP
            importer_gdp_col: Column name for importer GDP
            distance_col: Column name for distance
            
        Returns:
            Gravity model estimation results
        """
        self.logger.debug("Running optimized OLS regression for gravity model")
        
        ln_trade_col = f"ln_{value_col}"
        ln_exp_gdp_col = f"ln_{exporter_gdp_col}"
        ln_imp_gdp_col = f"ln_{importer_gdp_col}"
        ln_dist_col = f"ln_{distance_col}"
        
        # Validate columns
        if not all(c in data.columns for c in [ln_trade_col, ln_exp_gdp_col, ln_imp_gdp_col, ln_dist_col]):
            self.logger.error("Missing required log-transformed columns for regression")
            raise ValueError("Missing required log-transformed columns for OLS regression")
        
        # Extract model variables efficiently
        X_data = data[[ln_exp_gdp_col, ln_imp_gdp_col, ln_dist_col]].copy()
        y_data = data[ln_trade_col].copy()
        
        if X_data.empty or y_data.empty or len(X_data) != len(y_data):
            self.logger.error("Independent (X) or dependent (y) variables are empty or mismatched for OLS")
            raise ValueError("Independent (X) or dependent (y) variables are empty or mismatched for OLS regression")
        
        # Add constant term
        X = sm.add_constant(X_data)
        
        # Run OLS regression
        try:
            # Use parallel processing in statsmodels if available (set in future statsmodels versions)
            # Run regression
            model = sm.OLS(y_data, X)
            results = model.fit()
            
            self.logger.info(f"Gravity model OLS regression completed. R-squared: {results.rsquared:.4f}")
            
            return {
                "parameters": results.params.to_dict(),
                "statistics": {
                    "r_squared": results.rsquared,
                    "adj_r_squared": results.rsquared_adj,
                    "num_observations": results.nobs,
                    "f_statistic": results.fvalue,
                    "p_value_f_statistic": results.f_pvalue,
                    "standard_errors": results.bse.to_dict()
                },
                "data": data,  # Return the data used for regression
                "r_squared": results.rsquared,
                "adj_r_squared": results.rsquared_adj,
                "covariance_matrix": results.cov_params().to_dict()
            }
        except Exception as e:
            self.logger.error(f"Error during OLS regression: {e}")
            raise RuntimeError(f"Error during OLS regression: {e}")
    
    @memoize(ttl=DataLifetime.DAILY.value if hasattr(DataLifetime, 'DAILY') else 3600*24, level=StorageTier.MEMORY)
    @log_execution_time(logger=logger)
    def estimate_gravity_model(
        self,
        trade_data: pd.DataFrame,
        gdp_data: pd.DataFrame,
        distance_data: pd.DataFrame,
        exporter_col: str = "i",
        importer_col: str = "j",
        year_col: str = "t",
        value_col: str = "v",
        gdp_country_col: str = "country_code",
        exporter_gdp_col: str = "gdp_exporter",
        importer_gdp_col: str = "gdp_importer",
        gdp_value_col: str = "gdp_value",
        distance_col: str = "dist",
        year: Optional[int] = None,
        min_year: Optional[int] = None,
        max_year: Optional[int] = None
    ) -> Optional[GravityModelResult]:
        """
        Estimate gravity model parameters using optimized, hardware-accelerated operations.
        
        Args:
            trade_data: Trade flow data
            gdp_data: GDP data
            distance_data: Distance data
            exporter_col: Column name for exporter country codes
            importer_col: Column name for importer country codes
            year_col: Column name for year
            value_col: Column name for trade values
            gdp_country_col: Column name for country codes in GDP data
            exporter_gdp_col: Output column name for exporter GDP
            importer_gdp_col: Output column name for importer GDP
            gdp_value_col: Column name for GDP values in GDP data
            distance_col: Column name for distance
            year: Specific year to use
            min_year: Minimum year (inclusive)
            max_year: Maximum year (inclusive)
            
        Returns:
            Gravity model estimation results
        """
        self.logger.info(f"Estimating gravity model for period: {year or (min_year, max_year)}")
        
        # Validate input data
        if not self._validate_input_data(
            trade_data,
            [exporter_col, importer_col, year_col, value_col],
            {exporter_col: 'string', importer_col: 'string', year_col: 'integer', value_col: 'numeric'}
        ) or not self._validate_input_data(
            gdp_data,
            [gdp_country_col, year_col, gdp_value_col],
            {gdp_country_col: 'string', year_col: 'integer', gdp_value_col: 'numeric'}
        ) or not self._validate_input_data(
            distance_data,
            [exporter_col, importer_col, distance_col],
            {exporter_col: 'string', importer_col: 'string', distance_col: 'numeric'}
        ):
            self.logger.error("Input data validation failed for estimate_gravity_model")
            raise ValueError("Input data validation failed for gravity model estimation")
        
        # Filter trade data by year
        filtered_trade = self._filter_trade_by_year(trade_data, year_col, year, min_year, max_year)
        if filtered_trade.empty:
            self.logger.warning("No trade data after year filtering for gravity model")
            raise ValueError("No trade data available for the specified period to estimate gravity model")
        
        # Check if we should accelerate the operations based on data size
        data_size = len(filtered_trade)
        self.use_acceleration = self.use_acceleration and data_size >= 1000
        
        # Rename GDP columns for clarity before merge
        gdp_data_renamed = gdp_data.rename(columns={gdp_country_col: 'country_code', gdp_value_col: 'gdp'})
        
        # Prepare merged dataset
        merged_data = self._prepare_merged_dataset(
            filtered_trade,
            gdp_data_renamed,
            distance_data,
            exporter_col,
            importer_col,
            year_col,
            value_col,
            exporter_gdp_col,
            importer_gdp_col,
            distance_col
        )
        
        if merged_data.empty:
            self.logger.warning("Merged dataset is empty, cannot estimate gravity model")
            raise RuntimeError("Data preparation resulted in an empty dataset before regression")
        
        # Run gravity regression
        return self._run_gravity_regression(merged_data, value_col, exporter_gdp_col, importer_gdp_col, distance_col)
    
    @memoize(ttl=3600, level=StorageTier.MEMORY)
    @log_execution_time(logger=logger)
    def predict_trade_flows(
        self,
        model_estimation_result: Optional[GravityModelResult],
        country_code_to_analyze: int = 887,  # Typically Yemen
        value_col: str = "v",
        exporter_col: str = "i",
        importer_col: str = "j",
        exporter_gdp_col: str = "gdp_exporter",
        importer_gdp_col: str = "gdp_importer",
        distance_col: str = "dist"
    ) -> pd.DataFrame:
        """
        Predict trade flows using estimated gravity model with optimized operations.
        
        Args:
            model_estimation_result: Gravity model estimation results
            country_code_to_analyze: Country code to analyze
            value_col: Column name for trade values
            exporter_col: Column name for exporter country codes
            importer_col: Column name for importer country codes
            exporter_gdp_col: Column name for exporter GDP
            importer_gdp_col: Column name for importer GDP
            distance_col: Column name for distance
            
        Returns:
            DataFrame with predicted trade flows
        """
        self.logger.info(f"Predicting trade flows for country {country_code_to_analyze}")
        
        # Validate input
        if not model_estimation_result or not model_estimation_result.get("parameters") or not isinstance(model_estimation_result.get("data"), pd.DataFrame):
            self.logger.error("Invalid or incomplete model estimation result provided for prediction")
            raise ValueError("Invalid or incomplete model estimation result provided for prediction")
        
        params = model_estimation_result["parameters"]
        data_used_for_estimation = model_estimation_result["data"]
        
        # Filter for the specific country efficiently using NumPy
        exporter_mask = data_used_for_estimation[exporter_col].values == country_code_to_analyze
        importer_mask = data_used_for_estimation[importer_col].values == country_code_to_analyze
        combined_mask = exporter_mask | importer_mask
        
        # Apply mask using boolean indexing
        country_specific_data = data_used_for_estimation.loc[combined_mask].copy()
        
        if country_specific_data.empty:
            self.logger.warning(f"No data for country {country_code_to_analyze} in the estimation dataset")
            return pd.DataFrame()
        
        # Log-transformed column names used in regression
        ln_exp_gdp_col = f"ln_{exporter_gdp_col}"
        ln_imp_gdp_col = f"ln_{importer_gdp_col}"
        ln_dist_col = f"ln_{distance_col}"
        
        # Get parameters for prediction
        const_term = params.get("const", 0)
        exp_gdp_coef = params.get(ln_exp_gdp_col, 0)
        imp_gdp_coef = params.get(ln_imp_gdp_col, 0)
        dist_coef = params.get(ln_dist_col, 0)
        
        # Calculate predicted trade using hardware acceleration if available
        if self.use_acceleration and self.hw_manager.is_hardware_acceleration_available():
            self._calculate_predicted_trade_accelerated(
                country_specific_data,
                ln_exp_gdp_col,
                ln_imp_gdp_col,
                ln_dist_col,
                const_term,
                exp_gdp_coef,
                imp_gdp_coef,
                dist_coef
            )
        else:
            # Standard calculation
            country_specific_data["ln_predicted_trade"] = (
                const_term +
                exp_gdp_coef * country_specific_data[ln_exp_gdp_col] +
                imp_gdp_coef * country_specific_data[ln_imp_gdp_col] +
                dist_coef * country_specific_data[ln_dist_col]
            )
            country_specific_data["predicted_trade"] = np.exp(country_specific_data["ln_predicted_trade"]) - LOG_TRADE_OFFSET
            country_specific_data["predicted_trade"] = country_specific_data["predicted_trade"].clip(lower=0)
        
        # Calculate trade intensity
        country_specific_data["trade_intensity"] = (country_specific_data[value_col] + LOG_TRADE_OFFSET) / (country_specific_data["predicted_trade"] + LOG_TRADE_OFFSET)
        country_specific_data["trade_intensity"] = country_specific_data["trade_intensity"].fillna(0)
        
        # Add direction column
        country_specific_data["direction"] = np.where(country_specific_data[exporter_col] == country_code_to_analyze, "export", "import")
        
        # Select columns for output
        cols_to_return = [exporter_col, importer_col, "direction", value_col, "predicted_trade", "trade_intensity"]
        if "partner_name" in country_specific_data.columns:
            cols_to_return.insert(2, "partner_name")
            
        return country_specific_data[cols_to_return].reset_index(drop=True)
    
    def _calculate_predicted_trade_accelerated(
        self,
        data: pd.DataFrame,
        ln_exp_gdp_col: str,
        ln_imp_gdp_col: str,
        ln_dist_col: str,
        const_term: float,
        exp_gdp_coef: float,
        imp_gdp_coef: float,
        dist_coef: float
    ) -> None:
        """
        Calculate predicted trade using hardware acceleration.
        
        Args:
            data: DataFrame containing the input data
            ln_exp_gdp_col: Column name for log exporter GDP
            ln_imp_gdp_col: Column name for log importer GDP
            ln_dist_col: Column name for log distance
            const_term: Constant term from regression
            exp_gdp_coef: Coefficient for exporter GDP
            imp_gdp_coef: Coefficient for importer GDP
            dist_coef: Coefficient for distance
        """
        hw_manager = self.hw_manager
        
        # Convert input columns to NumPy arrays
        ln_exp_gdp = data[ln_exp_gdp_col].values.astype(np.float32)
        ln_imp_gdp = data[ln_imp_gdp_col].values.astype(np.float32)
        ln_dist = data[ln_dist_col].values.astype(np.float32)
        
        # Compute term contributions
        exp_gdp_term = hw_manager.accelerate_array(ln_exp_gdp, "multiply", exp_gdp_coef)
        imp_gdp_term = hw_manager.accelerate_array(ln_imp_gdp, "multiply", imp_gdp_coef)
        dist_term = hw_manager.accelerate_array(ln_dist, "multiply", dist_coef)
        
        # Fall back to NumPy if acceleration fails
        if exp_gdp_term is None:
            exp_gdp_term = ln_exp_gdp * exp_gdp_coef
        if imp_gdp_term is None:
            imp_gdp_term = ln_imp_gdp * imp_gdp_coef
        if dist_term is None:
            dist_term = ln_dist * dist_coef
        
        # Sum all terms
        ln_predicted = const_term + exp_gdp_term + imp_gdp_term + dist_term
        
        # Transform to trade values
        predicted_trade = np.exp(ln_predicted) - LOG_TRADE_OFFSET
        predicted_trade = np.maximum(predicted_trade, 0)  # Ensure non-negative values
        
        # Add results to DataFrame
        data["ln_predicted_trade"] = ln_predicted
        data["predicted_trade"] = predicted_trade
    
    @protect("analyze_trade_relationships", OperationType.COMPUTATION)
    @memoize(ttl=7200, level=StorageTier.DISK)
    @log_execution_time(logger=logger)
    def analyze_trade_relationships(
        self,
        trade_data: pd.DataFrame,
        country_pairs: Optional[List[Tuple[str, str]]] = None,
        reference_years: Optional[List[int]] = None,
        distance_data: Optional[pd.DataFrame] = None,
        gdp_data: Optional[pd.DataFrame] = None,
        exporter_col: str = "exporter_iso",
        importer_col: str = "importer_iso",
        year_col: str = "year",
        value_col: str = "trade_value_usd"
    ) -> Dict[str, Any]:
        """
        Analyze bilateral trade relationships using gravity model principles
        with optimized and hardware-accelerated operations.
        
        Args:
            trade_data: DataFrame with trade flow data
            country_pairs: List of (exporter, importer) tuples to analyze
            reference_years: List of years to analyze
            distance_data: DataFrame with distance between countries
            gdp_data: DataFrame with GDP data for countries
            exporter_col: Column name for exporter country codes
            importer_col: Column name for importer country codes
            year_col: Column name for years
            value_col: Column name for trade values
            
        Returns:
            Dictionary with analysis results for each country pair
        """
        self.logger.info(f"Analyzing trade relationships for {len(country_pairs) if country_pairs else 'top'} country pairs")
        
        # Validate inputs
        if not self._validate_input_data(
            trade_data, 
            [exporter_col, importer_col, year_col, value_col],
            {exporter_col: 'string', importer_col: 'string', year_col: 'integer', value_col: 'numeric'}
        ):
            raise ValueError(f"Invalid trade data for relationship analysis")
        
        # Select country pairs if not provided
        if not country_pairs:
            country_pairs = self._identify_top_country_pairs(
                trade_data, exporter_col, importer_col, value_col, top_n=5
            )
            self.logger.info(f"Selected top {len(country_pairs)} country pairs for analysis")
        
        # Select reference years if not provided
        if not reference_years:
            available_years = sorted(trade_data[year_col].unique())
            if not available_years:
                self.logger.error("No years available in trade data")
                raise ValueError("No years available in trade data for relationship analysis")
            # Use last 5 years or all if fewer
            reference_years = available_years[-min(5, len(available_years)):]
            self.logger.info(f"Selected years for analysis: {reference_years}")
        
        # Load additional data if needed
        if distance_data is None:
            try:
                distance_data = load_data(source_name="country_distances")
                self.logger.info("Loaded distance data from data source")
            except Exception as e:
                self.logger.warning(f"Failed to load distance data: {e}. Analysis will proceed without distance information")
        
        if gdp_data is None:
            try:
                gdp_data = load_data(source_name="world_bank", indicator="NY.GDP.MKTP.CD")
                self.logger.info("Loaded GDP data from data source")
            except Exception as e:
                self.logger.warning(f"Failed to load GDP data: {e}. Analysis will proceed without GDP information")
        
        # Prepare results container
        results = {}
        
        # Process country pairs using hardware-accelerated parallelization if available
        country_results = []
        if self.use_acceleration and self.hw_manager.is_hardware_acceleration_available():
            # Prepare arguments for parallel processing
            process_args = []
            for exporter, importer in country_pairs:
                process_args.append({
                    'exporter': exporter,
                    'importer': importer,
                    'trade_data': trade_data,
                    'gdp_data': gdp_data,
                    'distance_data': distance_data,
                    'reference_years': reference_years,
                    'exporter_col': exporter_col,
                    'importer_col': importer_col,
                    'year_col': year_col,
                    'value_col': value_col
                })
            
            # Use parallel processing for country pair analysis if available
            try:
                # Define the processing function
                @protect("process_country_pair", OperationType.COMPUTATION)
                def process_country_pair(args):
                    return self._analyze_single_country_pair(**args)
                
                # Process in parallel
                if len(process_args) > 1:
                    country_results = self.hw_manager.parallelize(process_country_pair, process_args)
                else:
                    # Process sequentially if only one pair
                    country_results = [process_country_pair(args) for args in process_args]
            except Exception as e:
                self.logger.warning(f"Parallel processing failed: {e}. Falling back to sequential processing")
                # Fall back to sequential processing
                country_results = []
        
        # If parallel processing wasn't used or failed, process sequentially
        if not country_results:
            country_results = []
            for exporter, importer in country_pairs:
                result = self._analyze_single_country_pair(
                    exporter=exporter,
                    importer=importer,
                    trade_data=trade_data,
                    gdp_data=gdp_data,
                    distance_data=distance_data,
                    reference_years=reference_years,
                    exporter_col=exporter_col,
                    importer_col=importer_col,
                    year_col=year_col,
                    value_col=value_col
                )
                country_results.append(result)
        
        # Convert results to dictionary
        for result in country_results:
            if result:
                pair_key = f"{result['exporter']}_{result['importer']}"
                results[pair_key] = result
        
        self.logger.info(f"Analyzed {len(results)} trade relationships")
        return results
    
    def _analyze_single_country_pair(
        self,
        exporter: str,
        importer: str,
        trade_data: pd.DataFrame,
        gdp_data: Optional[pd.DataFrame],
        distance_data: Optional[pd.DataFrame],
        reference_years: List[int],
        exporter_col: str,
        importer_col: str,
        year_col: str,
        value_col: str
    ) -> Dict[str, Any]:
        """
        Analyze a single country pair's trade relationship.
        
        Args:
            exporter: Exporter country code
            importer: Importer country code
            trade_data: DataFrame with trade data
            gdp_data: DataFrame with GDP data
            distance_data: DataFrame with distance data
            reference_years: List of reference years
            exporter_col: Column name for exporter
            importer_col: Column name for importer
            year_col: Column name for year
            value_col: Column name for trade value
            
        Returns:
            Dictionary with analysis results
        """
        self.logger.debug(f"Analyzing trade relationship: {exporter} -> {importer}")
        
        # Filter trade data for this pair
        pair_mask = (
            (trade_data[exporter_col] == exporter) &
            (trade_data[importer_col] == importer) &
            trade_data[year_col].isin(reference_years)
        )
        pair_data = trade_data.loc[pair_mask].copy()
        
        if pair_data.empty:
            self.logger.warning(f"No trade data found for {exporter} -> {importer} in years {reference_years}")
            return {
                "exporter": exporter,
                "importer": importer,
                "status": "No data available"
            }
        
        # Calculate basic trade statistics
        total_trade = pair_data[value_col].sum()
        
        # Use efficient groupby operation
        annual_trade = pair_data.groupby(year_col)[value_col].sum()
        avg_annual_trade = annual_trade.mean()
        
        # Calculate year-on-year growth with NumPy for better performance
        annual_values = annual_trade.values
        if len(annual_values) > 1:
            pct_changes = np.diff(annual_values) / annual_values[:-1] * 100
            year_on_year_growth = np.mean(pct_changes) if len(pct_changes) > 0 else 0
        else:
            year_on_year_growth = 0
        
        # Get distance if available
        distance = None
        if distance_data is not None:
            try:
                distance_mask = (
                    ((distance_data["origin"] == exporter) & (distance_data["destination"] == importer)) |
                    ((distance_data["origin"] == importer) & (distance_data["destination"] == exporter))
                )
                distance_row = distance_data.loc[distance_mask]
                if not distance_row.empty and "distance" in distance_row.columns:
                    distance = distance_row["distance"].iloc[0]
            except Exception as e:
                self.logger.warning(f"Error retrieving distance for {exporter}-{importer}: {e}")
        
        # Get GDP data if available
        exporter_gdp = importer_gdp = None
        if gdp_data is not None:
            try:
                for country, gdp_var in [(exporter, "exporter_gdp"), (importer, "importer_gdp")]:
                    country_mask = (
                        (gdp_data["country_code"] == country) &
                        gdp_data[year_col].isin(reference_years)
                    )
                    country_gdp = gdp_data.loc[country_mask]
                    if not country_gdp.empty and "value" in country_gdp.columns:
                        locals()[gdp_var] = country_gdp["value"].mean()
            except Exception as e:
                self.logger.warning(f"Error retrieving GDP data: {e}")
        
        # Calculate trade potential if all data is available
        trade_potential = None
        if all(v is not None for v in [exporter_gdp, importer_gdp, distance]):
            trade_potential = self._calculate_trade_potential(
                exporter_gdp, importer_gdp, distance, total_trade
            )
        
        # Store results
        return {
            "exporter": exporter,
            "importer": importer,
            "years": sorted(pair_data[year_col].unique()),
            "total_trade": total_trade,
            "avg_annual_trade": avg_annual_trade,
            "year_on_year_growth": year_on_year_growth,
            "distance": distance,
            "exporter_gdp": exporter_gdp,
            "importer_gdp": importer_gdp,
            "trade_potential": trade_potential
        }
    
    def _identify_top_country_pairs(
        self,
        trade_data: pd.DataFrame,
        exporter_col: str,
        importer_col: str,
        value_col: str,
        top_n: int = 5
    ) -> List[Tuple[str, str]]:
        """
        Identify top trading country pairs by volume using optimized operations.
        
        Args:
            trade_data: DataFrame with trade data
            exporter_col: Column name for exporter
            importer_col: Column name for importer
            value_col: Column name for trade value
            top_n: Number of top pairs to return
            
        Returns:
            List of (exporter, importer) tuples
        """
        # Use optimized groupby and sorting
        pair_totals = trade_data.groupby([exporter_col, importer_col], observed=True)[value_col].sum()
        
        # Sort and select top pairs efficiently
        if self.use_acceleration and self.hw_manager.is_hardware_acceleration_available():
            # Convert to numpy array for faster sorting
            pairs = np.array(pair_totals.index.tolist())
            values = pair_totals.values
            
            # Sort indices by values in descending order
            sorted_indices = np.argsort(-values)
            
            # Select top N pairs
            top_indices = sorted_indices[:min(top_n, len(sorted_indices))]
            top_pairs = [(pairs[i][0], pairs[i][1]) for i in top_indices]
        else:
            # Standard pandas approach
            top_pairs_df = pair_totals.sort_values(ascending=False).head(top_n)
            top_pairs = [(row[0], row[1]) for row in top_pairs_df.index]
        
        return top_pairs
    
    def _calculate_trade_potential(
        self,
        exporter_gdp: float,
        importer_gdp: float,
        distance: float,
        actual_trade: float
    ) -> Dict[str, float]:
        """
        Calculate trade potential based on gravity model.
        
        Args:
            exporter_gdp: GDP of exporter country
            importer_gdp: GDP of importer country
            distance: Distance between countries
            actual_trade: Actual trade value
            
        Returns:
            Dictionary with expected trade and over/under-trading percentage
        """
        if any(v is None for v in [exporter_gdp, importer_gdp, distance]) or distance == 0:
            return None
        
        # Use hardware acceleration for power and division operations if available
        if self.use_acceleration and self.hw_manager.is_hardware_acceleration_available():
            hw_manager = self.hw_manager
            
            # Typical elasticity coefficients
            gdp_elasticity = 0.9
            distance_elasticity = -0.7
            
            # Calculate terms
            exp_gdp_term = hw_manager.accelerate_array(np.array([exporter_gdp], dtype=np.float32), "power", gdp_elasticity)
            imp_gdp_term = hw_manager.accelerate_array(np.array([importer_gdp], dtype=np.float32), "power", gdp_elasticity)
            dist_term = hw_manager.accelerate_array(np.array([distance], dtype=np.float32), "power", abs(distance_elasticity))
            
            # Fall back to NumPy if acceleration fails
            if exp_gdp_term is None:
                exp_gdp_term = np.array([exporter_gdp ** gdp_elasticity])
            if imp_gdp_term is None:
                imp_gdp_term = np.array([importer_gdp ** gdp_elasticity])
            if dist_term is None:
                dist_term = np.array([distance ** abs(distance_elasticity)])
            
            # Calculate expected trade
            numerator = exp_gdp_term[0] * imp_gdp_term[0]
            denominator = dist_term[0]
            
            expected_trade = (numerator / denominator) * 1e-6  # Scale factor
        else:
            # Standard calculation
            gdp_elasticity = 0.9
            distance_elasticity = -0.7
            
            expected_trade = (
                (exporter_gdp ** gdp_elasticity) * 
                (importer_gdp ** gdp_elasticity) / 
                (distance ** abs(distance_elasticity))
            ) * 1e-6  # Scale factor
        
        # Calculate trade ratio and over/under percentage
        if expected_trade > 0:
            trade_ratio = actual_trade / expected_trade
            over_under_percent = (trade_ratio - 1) * 100
        else:
            trade_ratio = 0
            over_under_percent = 0
        
        # Determine assessment
        if over_under_percent > 20:
            assessment = "Over-trading"
        elif over_under_percent < -20:
            assessment = "Under-trading"
        else:
            assessment = "Trading at expected level"
        
        return {
            "expected_trade": expected_trade,
            "trade_ratio": trade_ratio,
            "over_under_percent": over_under_percent,
            "assessment": assessment
        }
    
    @memoize(ttl=3600, level=StorageTier.MEMORY)
    def prepare_gravity_model_visualization_data(
        self,
        model_result: GravityModelResult,
        actual_trade_data: Optional[pd.DataFrame] = None,
        country_focus: Optional[str] = None,
        exporter_col: str = "exporter_iso",
        importer_col: str = "importer_iso",
        year_col: str = "year",
        value_col: str = "trade_value_usd",
        prediction_col: str = "predicted_trade"
    ) -> pd.DataFrame:
        """
        Prepare gravity model results for visualization with optimized operations.
        
        Args:
            model_result: Results from estimate_gravity_model
            actual_trade_data: Optional raw trade data for comparison
            country_focus: Optional country code to focus analysis on
            exporter_col: Column name for exporter country codes
            importer_col: Column name for importer country codes
            year_col: Column name for years
            value_col: Column name for actual trade values
            prediction_col: Column name for predicted trade values
            
        Returns:
            DataFrame with actual vs. predicted trade for visualization
        """
        self.logger.info("Preparing gravity model data for visualization")
        
        # Check if model_result is valid
        if not isinstance(model_result, dict) or 'data' not in model_result:
            self.logger.error("Invalid model_result for visualization")
            return pd.DataFrame()
        
        # Get the model data
        model_data = model_result['data']
        if not isinstance(model_data, pd.DataFrame) or model_data.empty:
            self.logger.error("Empty or invalid model data for visualization")
            return pd.DataFrame()
        
        # Initialize visualization DataFrame
        viz_data = model_data.copy()
        
        # Filter by country_focus if provided using NumPy for faster filtering
        if country_focus:
            exporter_mask = viz_data[exporter_col].values == country_focus
            importer_mask = viz_data[importer_col].values == country_focus
            combined_mask = exporter_mask | importer_mask
            
            viz_data = viz_data.loc[combined_mask]
            
            if viz_data.empty:
                self.logger.warning(f"No data found for country_focus: {country_focus}")
                return pd.DataFrame()
        
        # Check if predicted values exist in the model data
        if prediction_col not in viz_data.columns and 'predicted_log_trade' in viz_data.columns:
            # Convert from log scale if needed
            if self.use_acceleration and self.hw_manager.is_hardware_acceleration_available():
                # Use hardware acceleration for exp operation
                log_trade = viz_data['predicted_log_trade'].values.astype(np.float32)
                predicted_trade = self.hw_manager.accelerate_array(log_trade, "exp")
                
                if predicted_trade is not None:
                    viz_data[prediction_col] = predicted_trade - LOG_TRADE_OFFSET
                else:
                    viz_data[prediction_col] = np.exp(log_trade) - LOG_TRADE_OFFSET
            else:
                viz_data[prediction_col] = np.exp(viz_data['predicted_log_trade']) - LOG_TRADE_OFFSET
                
            self.logger.debug("Converted predicted_log_trade to predicted_trade for visualization")
        elif prediction_col not in viz_data.columns:
            self.logger.error(f"No prediction column found in model data: {viz_data.columns}")
            return pd.DataFrame()
        
        # Add residuals (actual - predicted)
        if value_col in viz_data.columns:
            viz_data['residual'] = viz_data[value_col] - viz_data[prediction_col]
            viz_data['residual_pct'] = (viz_data['residual'] / viz_data[prediction_col]) * 100
            
            # Categorize over/under trading using efficient NumPy operations
            residual_pct = viz_data['residual_pct'].values
            bins = np.array([-float('inf'), -30, -10, 10, 30, float('inf')])
            labels = [
                'Severe under-trading',
                'Moderate under-trading',
                'Expected level',
                'Moderate over-trading',
                'Severe over-trading'
            ]
            
            # Create categorical variable
            indices = np.digitize(residual_pct, bins) - 1
            viz_data['trading_status'] = pd.Categorical(
                np.array(labels)[indices],
                categories=labels,
                ordered=True
            )
        
        # Add country pair column for better visualization
        viz_data['country_pair'] = viz_data[exporter_col] + ' → ' + viz_data[importer_col]
        
        # Sort by the absolute residual to highlight largest deviations
        if 'residual' in viz_data.columns:
            viz_data['abs_residual'] = viz_data['residual'].abs()
            viz_data = viz_data.sort_values('abs_residual', ascending=False).reset_index(drop=True)
            viz_data = viz_data.drop(columns=['abs_residual'])
        
        self.logger.info(f"Prepared visualization data with {len(viz_data)} rows")
        return viz_data

def get_optimized_gravity_model(
    raw_data_dir: Optional[Path] = None,
    processed_data_dir: Optional[Path] = None,
    use_acceleration: bool = True
) -> OptimizedGravityModel:
    """
    Factory function to create a new OptimizedGravityModel instance.
    
    Args:
        raw_data_dir: Directory for raw data files
        processed_data_dir: Directory for processed data and caching
        use_acceleration: Whether to use hardware acceleration
        
    Returns:
        Configured OptimizedGravityModel instance
    """
    return OptimizedGravityModel(
        raw_data_dir=raw_data_dir,
        processed_data_dir=processed_data_dir,
        use_acceleration=use_acceleration
    )

@memoize(ttl=DataLifetime.DAILY.value if hasattr(DataLifetime, 'DAILY') else 3600*24, level=StorageTier.MEMORY)
@log_execution_time(logger=logger)
def estimate_optimized_gravity_model(
    trade_data: pd.DataFrame,
    gdp_data: pd.DataFrame,
    distance_data: pd.DataFrame,
    exporter_col: str = "i",
    importer_col: str = "j",
    year_col: str = "t",
    value_col: str = "v",
    gdp_country_col: str = "country_code",
    exporter_gdp_col: str = "gdp_exporter",
    importer_gdp_col: str = "gdp_importer",
    gdp_value_col: str = "gdp_value",
    distance_col: str = "dist",
    year: Optional[int] = None,
    min_year: Optional[int] = None,
    max_year: Optional[int] = None,
    use_acceleration: bool = True
) -> Optional[GravityModelResult]:
    """
    Standalone function to estimate gravity model with optimized operations.
    
    This function provides a simple interface for estimating gravity models without
    needing to create a model instance.
    
    Args:
        trade_data: Trade flow data
        gdp_data: GDP data
        distance_data: Distance data
        exporter_col: Column name for exporter country codes
        importer_col: Column name for importer country codes
        year_col: Column name for year
        value_col: Column name for trade values
        gdp_country_col: Column name for country codes in GDP data
        exporter_gdp_col: Output column name for exporter GDP
        importer_gdp_col: Output column name for importer GDP
        gdp_value_col: Column name for GDP values in GDP data
        distance_col: Column name for distance
        year: Specific year to use
        min_year: Minimum year (inclusive)
        max_year: Maximum year (inclusive)
        use_acceleration: Whether to use hardware acceleration
        
    Returns:
        Gravity model estimation results
    """
    model = get_optimized_gravity_model(use_acceleration=use_acceleration)
    return model.estimate_gravity_model(
        trade_data=trade_data,
        gdp_data=gdp_data,
        distance_data=distance_data,
        exporter_col=exporter_col,
        importer_col=importer_col,
        year_col=year_col,
        value_col=value_col,
        gdp_country_col=gdp_country_col,
        exporter_gdp_col=exporter_gdp_col,
        importer_gdp_col=importer_gdp_col,
        gdp_value_col=gdp_value_col,
        distance_col=distance_col,
        year=year,
        min_year=min_year,
        max_year=max_year
    )

def main():
    """Quick test and demonstration of optimized gravity model."""
    # Project imports
    from yemen_trade_diagnostic.interfaces.logging_interface import configure_logging
    from yemen_trade_diagnostic.errors import protect, OperationType

    # Configure logging
    configure_logging(log_level=LogLevel.INFO, log_to_console=True)
    logger.info("--- Testing Optimized Gravity Model ---")
    
    # Create sample data
    sample_trade = pd.DataFrame({
        't': [2020, 2020, 2021, 2021],      # year
        'i': [887, 156, 887, 4],    # exporter_iso (Yemen=887, China=156, Afghanistan=4)
        'j': [156, 887, 4, 887],    # importer_iso 
        'k': ['TOTAL', 'TOTAL', 'TOTAL', 'TOTAL'], # product_code (using TOTAL for aggregate example)
        'v': [100, 200, 150, 50]         # value
    })
    sample_gdp = pd.DataFrame({
        'country_code': [887, 156, 784, 4], # Assuming a column with country codes that match i and j
        't': [2020, 2020, 2020, 2020], # Assuming GDP is for a single year or needs merging by year
        'gdp_value': [10e9, 14e12, 2e12, 20e9] # GDP values
    })
    # For a multi-year model, GDP data should also be multi-year.
    # Simplified: Using 2020 GDP for all trade years in this example.
    sample_gdp_2020 = sample_gdp[sample_gdp['t'] == 2020][['country_code', 'gdp_value']]
    
    sample_distance = pd.DataFrame({
        'i': [887, 887, 156, 156, 4, 4],
        'j': [156, 4, 887, 4, 887, 156],
        'dist': [5000, 3000, 5000, 6000, 3000, 6000] # Distance in km
    })
    
    logger.debug(f"Sample trade data:\n{sample_trade}")
    logger.debug(f"Sample GDP data (2020):\n{sample_gdp_2020}")
    logger.debug(f"Sample distance data:\n{sample_distance}")
    
    # Create model instance
    model = OptimizedGravityModel(use_acceleration=True)
    
    # Test with acceleration enabled
    logger.info("Testing with hardware acceleration enabled")
    model_res_acc = model.estimate_gravity_model(
        sample_trade, sample_gdp, sample_distance, 
        year=2020, 
        exporter_gdp_col='gdp_exp', importer_gdp_col='gdp_imp',
        gdp_country_col='country_code', gdp_value_col='gdp_value'
    )
    if model_res_acc and model_res_acc.get("parameters"):
        logger.info(f"Gravity Model Estimated Parameters (acceleration): {model_res_acc['parameters']}")
        logger.info(f"Gravity Model Statistics (acceleration): {model_res_acc['statistics']}")
        
        # Predict trade flows for Yemen (887)
        predicted_flows_yem = model.predict_trade_flows(
            model_res_acc, country_code_to_analyze=887,
            exporter_gdp_col='gdp_exp', importer_gdp_col='gdp_imp'
        )
        logger.info(f"Predicted Trade Flows for Yemen (887):\n{predicted_flows_yem}")
    else:
        logger.error("Gravity model estimation failed with acceleration")
    
    # Test standalone function
    logger.info("Testing standalone function")
    model_res_func = estimate_optimized_gravity_model(
        sample_trade, sample_gdp, sample_distance, 
        year=2020, 
        exporter_gdp_col='gdp_exp', importer_gdp_col='gdp_imp',
        gdp_country_col='country_code', gdp_value_col='gdp_value'
    )
    if model_res_func and model_res_func.get("parameters"):
        logger.info(f"Gravity Model Estimated Parameters (standalone): {model_res_func['parameters']}")
    else:
        logger.error("Gravity model estimation failed with standalone function")
    
    logger.info("--- Optimized Gravity Model Test Completed ---")

if __name__ == "__main__":
    main()