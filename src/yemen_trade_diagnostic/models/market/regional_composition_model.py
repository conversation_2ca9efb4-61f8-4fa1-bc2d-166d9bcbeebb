"""
Regional Composition Model for Yemen Trade Diagnostic (V2)

This module provides V2 functions for analyzing trade by regional composition.
"""
# Standard library imports
import time
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Tuple, Union

# Third-party imports
import numpy as np
import pandas as pd

# Project imports
from yemen_trade_diagnostic.data import DataSource, load_data
from yemen_trade_diagnostic.interfaces.cache_interface import StorageTier, memoize
from yemen_trade_diagnostic.interfaces.hardware_interface import AccelerationType, get_hardware_manager
from yemen_trade_diagnostic.interfaces.logging_interface import LogLevel, configure_logging, get_logger, log_execution_time
from yemen_trade_diagnostic.interfaces.validation_interface import SchemaValidationRule, ValidationIssueLevel, ValidationResult, get_validation_manager, validate_schema
from yemen_trade_diagnostic.errors import protect, OperationType

# Schema definitions for validation
TRADE_DATA_SCHEMA = {
    'required_columns': ['year', 'partner', 'value'],
    'column_types': {
        'year': 'integer', 
        'partner': 'string', 
        'value': 'numeric'
    }
}

COUNTRY_METADATA_SCHEMA = {
    'required_columns': ['country_code', 'region'],
    'column_types': {
        'country_code': 'string',
        'region': 'string'
    }
}

# Default regions if not provided
DEFAULT_REGIONS = {
    'North America': ['USA', 'CAN', 'MEX'],
    'Europe': ['DEU', 'FRA', 'GBR', 'ITA', 'ESP'],
    'Asia': ['CHN', 'JPN', 'IND', 'KOR', 'SGP'],
    'Middle East': ['ARE', 'SAU', 'OMN', 'QAT', 'KWT', 'BHR', 'IRQ', 'JOR', 'LBN', 'SYR', 'YEM'],
    'Africa': ['EGY', 'ZAF', 'NGA', 'KEN', 'ETH'],
    'Latin America': ['BRA', 'ARG', 'CHL', 'COL', 'PER'],
    'Oceania': ['AUS', 'NZL'],
    'Other': []
}

def _validate_data_for_regional_analysis(
    trade_data: pd.DataFrame, 
    country_metadata: Optional[pd.DataFrame] = None
) -> ValidationResult:
    """Validate data for regional composition analysis."""
    validation_manager = get_validation_manager()
    
    result = ValidationResult()
    if not isinstance(trade_data, pd.DataFrame) or trade_data.empty:
        result.add_issue("Trade data is empty or not a DataFrame", ValidationIssueLevel.ERROR)
        return result
    
    # Check required columns in trade data
    trade_required_cols = TRADE_DATA_SCHEMA.get('required_columns', [])
    trade_missing_cols = [col for col in trade_required_cols if col not in trade_data.columns]
    if trade_missing_cols:
        result.add_issue(f"Missing required columns in trade data: {trade_missing_cols}", ValidationIssueLevel.ERROR)
        
    # Check column types in trade data
    trade_column_types = TRADE_DATA_SCHEMA.get('column_types', {})
    for col, expected_type in trade_column_types.items():
        if col in trade_data.columns:
            # Check data type
            if expected_type == 'numeric' and not pd.api.types.is_numeric_dtype(trade_data[col]):
                result.add_issue(f"Column '{col}' should be numeric but is {trade_data[col].dtype}", ValidationIssueLevel.WARNING)
            elif expected_type == 'string' and not (pd.api.types.is_object_dtype(trade_data[col]) or pd.api.types.is_string_dtype(trade_data[col])):
                result.add_issue(f"Column '{col}' should be string but is {trade_data[col].dtype}", ValidationIssueLevel.WARNING)
            elif expected_type == 'integer' and not pd.api.types.is_integer_dtype(trade_data[col]):
                result.add_issue(f"Column '{col}' should be integer but is {trade_data[col].dtype}", ValidationIssueLevel.WARNING)
    
    # Check for negative values in value column
    if 'value' in trade_data.columns and (trade_data['value'] < 0).any():
        result.add_issue("Negative values found in 'value' column", ValidationIssueLevel.WARNING)
    
    # Validate country metadata if provided
    if country_metadata is not None:
        if not isinstance(country_metadata, pd.DataFrame) or country_metadata.empty:
            result.add_issue("Country metadata is empty or not a DataFrame", ValidationIssueLevel.WARNING)
        else:
            # Check required columns in country metadata
            country_required_cols = COUNTRY_METADATA_SCHEMA.get('required_columns', [])
            country_missing_cols = [col for col in country_required_cols if col not in country_metadata.columns]
            if country_missing_cols:
                result.add_issue(f"Missing required columns in country metadata: {country_missing_cols}", ValidationIssueLevel.WARNING)
            
            # Check if country_code column in country_metadata matches partner codes in trade_data
            if 'country_code' in country_metadata.columns and 'partner' in trade_data.columns:
                unique_partners = set(trade_data['partner'].unique())
                country_codes = set(country_metadata['country_code'].unique())
                missing_partners = unique_partners - country_codes
                if missing_partners and len(missing_partners) > len(unique_partners) * 0.1:  # If more than 10% of partners missing
                    result.add_issue(f"Many partner codes in trade data don't match country codes in metadata: {len(missing_partners)} out of {len(unique_partners)}", ValidationIssueLevel.WARNING)
    
    return result

@memoize(ttl=3600, level=StorageTier.MEMORY)
@log_execution_time(logger=logger)
def _optimize_dataframe_for_regional_analysis(df: pd.DataFrame) -> pd.DataFrame:
    """Optimize a DataFrame for regional analysis using hardware acceleration if available."""
    if df.empty:
        return df
        
    # Use hardware acceleration if available
    hw_manager = get_hardware_manager()
    if hw_manager.is_hardware_acceleration_available():
        try:
            logger.debug("Using hardware acceleration for DataFrame optimization")
            optimized_df = hw_manager.accelerate_dataframe(df, optimize_for="memory")
            if optimized_df is not None:
                logger.debug("Hardware acceleration successful for DataFrame optimization")
                return optimized_df
            else:
                logger.warning("Hardware acceleration returned None for DataFrame optimization")
        except Exception as e:
            logger.warning(f"Hardware acceleration failed for DataFrame optimization: {e}")
                
    # Fallback to CPU optimization
    logger.debug("Using CPU optimization for DataFrame")
    
    # Copy to avoid modifying the original
    optimized_df = df.copy()
    
    # Optimize numeric columns
    for col in optimized_df.select_dtypes(include=['number']).columns:
        if optimized_df[col].min() >= 0:
            # For unsigned integers
            max_val = optimized_df[col].max()
            if max_val <= 255:
                optimized_df[col] = optimized_df[col].astype(np.uint8)
            elif max_val <= 65535:
                optimized_df[col] = optimized_df[col].astype(np.uint16)
            elif max_val <= 4294967295:
                optimized_df[col] = optimized_df[col].astype(np.uint32)
        else:
            # For signed integers
            max_val = abs(optimized_df[col].max())
            min_val = abs(optimized_df[col].min())
            max_abs = max(max_val, min_val)
            if max_abs <= 127:
                optimized_df[col] = optimized_df[col].astype(np.int8)
            elif max_abs <= 32767:
                optimized_df[col] = optimized_df[col].astype(np.int16)
            elif max_abs <= 2147483647:
                optimized_df[col] = optimized_df[col].astype(np.int32)
    
    # Optimize float columns to float32 if precision allows
    for col in optimized_df.select_dtypes(include=['float64']).columns:
        optimized_df[col] = optimized_df[col].astype(np.float32)
    
    # Optimize string columns using category for high-cardinality columns
    for col in optimized_df.select_dtypes(include=['object']).columns:
        if optimized_df[col].nunique() / len(optimized_df) < 0.5:  # If fewer than 50% unique values
            optimized_df[col] = optimized_df[col].astype('category')
    
    return optimized_df

@protect("analyze_regional_composition_v2", OperationType.MODEL_CALCULATION)
@memoize(ttl=3600*24, level=StorageTier.DISK)
@log_execution_time(logger=logger)
def analyze_regional_composition_v2(
    trade_data: pd.DataFrame,
    country_metadata: Optional[pd.DataFrame] = None,
    year: int = 2023,
    country_code: str = "887",  # Yemen by default
    direction: str = "export",  # "export" or "import"
    year_col: str = "year",
    partner_col: str = "partner",
    value_col: str = "value",
    direction_col: str = "direction",
    country_code_col: str = "country_code",
    region_col: str = "region",
    custom_regions: Optional[Dict[str, List[str]]] = None,
    include_other_region: bool = True,
    include_country_detail: bool = False
) -> pd.DataFrame:
    """
    Analyze trade by regional composition for a specific country and year.
    
    Args:
        trade_data: DataFrame with trade data (must have year, partner, value columns)
        country_metadata: Optional DataFrame with country metadata (country_code, region)
        year: Year to analyze
        country_code: Country code to analyze (default: "887" for Yemen)
        direction: Trade direction, "export" for exports or "import" for imports
        year_col: Name of the year column in trade_data
        partner_col: Name of the partner column in trade_data
        value_col: Name of the value column in trade_data
        direction_col: Name of the direction column in trade_data
        country_code_col: Name of the country code column in country_metadata
        region_col: Name of the region column in country_metadata
        custom_regions: Optional dictionary mapping region names to lists of country codes
        include_other_region: Whether to include an "Other" region for countries not in any specified region
        include_country_detail: Whether to include country-level detail within regions
        
    Returns:
        DataFrame with regional composition analysis
    """
    logger.info(f"Analyzing {direction} regional composition for {country_code} in {year}")
    
    # Validate direction
    if direction not in ["export", "import"]:
        logger.error(f"Invalid direction: {direction}. Must be 'export' or 'import'.")
        raise ValueError(f"Invalid trade direction specified: {direction}. Must be 'export' or 'import'.")
    
    # Validate inputs
    validation_result = _validate_data_for_regional_analysis(trade_data, country_metadata)
    if not validation_result.is_valid:
        for issue in validation_result.issues:
            logger.warning(f"Data validation issue: {issue}")
        if any(issue.level == ValidationIssueLevel.ERROR for issue in validation_result.issues):
            return pd.DataFrame()
    
    # Filter data for the specified year, country, and direction
    if direction_col in trade_data.columns:
        # If direction column exists, use it to filter
        filtered_data = trade_data[
            (trade_data[year_col] == year) & 
            (trade_data[direction_col] == direction) &
            (trade_data.get("country", trade_data.get("exporter" if direction == "export" else "importer", "")) == country_code)
        ]
    else:
        # Otherwise, assume exporter/importer columns
        if direction == "export":
            exporter_col = "exporter" if "exporter" in trade_data.columns else "i"
            filtered_data = trade_data[
                (trade_data[year_col] == year) & 
                (trade_data[exporter_col] == country_code)
            ]
        else:  # import
            importer_col = "importer" if "importer" in trade_data.columns else "j"
            filtered_data = trade_data[
                (trade_data[year_col] == year) & 
                (trade_data[importer_col] == country_code)
            ]
    
    if filtered_data.empty:
        logger.warning(f"No {direction} data found for {country_code} in {year}")
        return pd.DataFrame()
    
    # Optimize the filtered DataFrame
    filtered_data = _optimize_dataframe_for_regional_analysis(filtered_data)
    
    # Prepare country-to-region mapping
    country_to_region_map = {}
    regions = {}
    
    if country_metadata is not None and not country_metadata.empty and country_code_col in country_metadata.columns and region_col in country_metadata.columns:
        # Create country-to-region mapping from metadata
        for _, row in country_metadata.iterrows():
            country_to_region_map[row[country_code_col]] = row[region_col]
            
        # Create regions dict from metadata
        for region in country_metadata[region_col].unique():
            countries = country_metadata[country_metadata[region_col] == region][country_code_col].tolist()
            regions[region] = countries
    elif custom_regions is not None:
        # Use custom regions if provided
        regions = custom_regions
        for region, countries in custom_regions.items():
            for country in countries:
                country_to_region_map[country] = region
    else:
        # Use default regions if neither metadata nor custom regions provided
        regions = DEFAULT_REGIONS
        for region, countries in DEFAULT_REGIONS.items():
            for country in countries:
                country_to_region_map[country] = region
    
    # Add region column to filtered data
    filtered_data['region'] = filtered_data[partner_col].map(country_to_region_map)
    
    # Assign "Other" to countries without a region
    if include_other_region:
        filtered_data['region'] = filtered_data['region'].fillna('Other')
    
    # Group data by region and aggregate values
    hw_manager = get_hardware_manager()
    if hw_manager.is_hardware_acceleration_available(AccelerationType.CPU):
        try:
            logger.debug("Using hardware acceleration for regional aggregation")
            result = hw_manager.accelerate_groupby(
                filtered_data, 
                by='region', 
                agg={value_col: 'sum'}
            ) # Added closing parenthesis
            if result is not None:
                regional_totals = result.reset_index()
                logger.debug("Hardware acceleration successful for regional aggregation")
            else:
                logger.warning("Hardware acceleration returned None for regional aggregation")
                # Fallback to CPU
                regional_totals = filtered_data.groupby('region')[value_col].sum().reset_index()
        except Exception as e:
            logger.warning(f"Hardware acceleration failed for regional aggregation: {e}")
            regional_totals = filtered_data.groupby('region')[value_col].sum().reset_index()
    else:
        regional_totals = filtered_data.groupby('region')[value_col].sum().reset_index()
    # Sort by value in descending order
    regional_totals = regional_totals.sort_values(by=value_col, ascending=False)
    
    # Calculate percentage of total
    total_value = regional_totals[value_col].sum()
    regional_totals['percent_of_total'] = (regional_totals[value_col] / total_value * 100) if total_value > 0 else 0
    
    # Add country detail if requested
    if include_country_detail:
        # Group by region and partner
        country_detail = filtered_data.groupby(['region', partner_col])[value_col].sum().reset_index()
        
        # Calculate percentage of regional total for each country
        country_detail = pd.merge(
            country_detail,
            regional_totals[['region', value_col]].rename(columns={value_col: 'region_total'}),
            on='region',
            how='left'
        ) # Added closing parenthesis
        country_detail['percent_of_region'] = (country_detail[value_col] / country_detail['region_total'] * 100)
        # Calculate percentage of total for each country
        country_detail['percent_of_total'] = (country_detail[value_col] / total_value * 100) if total_value > 0 else 0
        
        # Sort within each region
        country_detail = country_detail.sort_values(by=['region', value_col], ascending=[True, False])
        
        # Add country names if available
        if country_metadata is not None and 'country_name' in country_metadata.columns:
            country_detail = pd.merge(
                country_detail,
                country_metadata[[country_code_col, 'country_name']],
                left_on=partner_col,
                right_on=country_code_col,
                how='left'
            ) # Added closing parenthesis
        # Return both regional totals and country detail
        return {
            'regional_totals': regional_totals,
            'country_detail': country_detail
        }
    
    # Add direction column
    regional_totals['direction'] = direction
    
    return regional_totals

@protect("analyze_regional_composition_time_series_v2", OperationType.MODEL_CALCULATION)
@memoize(ttl=3600*24, level=StorageTier.DISK)
@log_execution_time(logger=logger)
def analyze_regional_composition_time_series_v2(
    trade_data: pd.DataFrame,
    country_metadata: Optional[pd.DataFrame] = None,
    country_code: str = "887",  # Yemen by default
    direction: str = "export",  # "export" or "import"
    start_year: Optional[int] = None,
    end_year: Optional[int] = None,
    year_col: str = "year",
    partner_col: str = "partner",
    value_col: str = "value",
    direction_col: str = "direction",
    country_code_col: str = "country_code",
    region_col: str = "region",
    custom_regions: Optional[Dict[str, List[str]]] = None,
    include_other_region: bool = True
) -> pd.DataFrame:
    """
    Analyze regional composition over time for a specific country.
    
    Args:
        trade_data: DataFrame with trade data (must have year, partner, value columns)
        country_metadata: Optional DataFrame with country metadata (country_code, region)
        country_code: Country code to analyze (default: "887" for Yemen)
        direction: Trade direction, "export" for exports or "import" for imports
        start_year: Optional start year for time series analysis (defaults to min year in data)
        end_year: Optional end year for time series analysis (defaults to max year in data)
        year_col: Name of the year column in trade_data
        partner_col: Name of the partner column in trade_data
        value_col: Name of the value column in trade_data
        direction_col: Name of the direction column in trade_data
        country_code_col: Name of the country code column in country_metadata
        region_col: Name of the region column in country_metadata
        custom_regions: Optional dictionary mapping region names to lists of country codes
        include_other_region: Whether to include an "Other" region for countries not in any specified region
        
    Returns:
        DataFrame with regional composition time series analysis
    """
    logger.info(f"Analyzing {direction} regional composition time series for {country_code} from {start_year or 'min'} to {end_year or 'max'}")
    
    # Validate direction
    if direction not in ["export", "import"]:
        logger.error(f"Invalid direction: {direction}. Must be 'export' or 'import'.")
        raise ValueError(f"Invalid trade direction specified: {direction}. Must be 'export' or 'import'.")
    
    # Validate inputs
    validation_result = _validate_data_for_regional_analysis(trade_data, country_metadata)
    if not validation_result.is_valid:
        for issue in validation_result.issues:
            logger.warning(f"Data validation issue: {issue}")
        if any(issue.level == ValidationIssueLevel.ERROR for issue in validation_result.issues):
            return pd.DataFrame()
    
    # Filter data for the specified country and direction
    if direction_col in trade_data.columns:
        # If direction column exists, use it to filter
        filtered_data = trade_data[
            (trade_data[direction_col] == direction) &
            (trade_data.get("country", trade_data.get("exporter" if direction == "export" else "importer", "")) == country_code)
        ]
    else:
        # Otherwise, assume exporter/importer columns
        if direction == "export":
            exporter_col = "exporter" if "exporter" in trade_data.columns else "i"
            filtered_data = trade_data[trade_data[exporter_col] == country_code]
        else:  # import
            importer_col = "importer" if "importer" in trade_data.columns else "j"
            filtered_data = trade_data[trade_data[importer_col] == country_code]
    
    if filtered_data.empty:
        logger.warning(f"No {direction} data found for {country_code}")
        return pd.DataFrame()
    
    # Filter by year range if specified
    if start_year is not None:
        filtered_data = filtered_data[filtered_data[year_col] >= start_year]
    if end_year is not None:
        filtered_data = filtered_data[filtered_data[year_col] <= end_year]
    
    if filtered_data.empty:
        logger.warning(f"No {direction} data found for {country_code} in the specified year range")
        return pd.DataFrame()
    
    # Optimize the filtered DataFrame
    filtered_data = _optimize_dataframe_for_regional_analysis(filtered_data)
    
    # Prepare country-to-region mapping
    country_to_region_map = {}
    regions = {}
    
    if country_metadata is not None and not country_metadata.empty and country_code_col in country_metadata.columns and region_col in country_metadata.columns:
        # Create country-to-region mapping from metadata
        for _, row in country_metadata.iterrows():
            country_to_region_map[row[country_code_col]] = row[region_col]
            
        # Create regions dict from metadata
        for region in country_metadata[region_col].unique():
            countries = country_metadata[country_metadata[region_col] == region][country_code_col].tolist()
            regions[region] = countries
    elif custom_regions is not None:
        # Use custom regions if provided
        regions = custom_regions
        for region, countries in custom_regions.items():
            for country in countries:
                country_to_region_map[country] = region
    else:
        # Use default regions if neither metadata nor custom regions provided
        regions = DEFAULT_REGIONS
        for region, countries in DEFAULT_REGIONS.items():
            for country in countries:
                country_to_region_map[country] = region
    
    # Add region column to filtered data
    filtered_data['region'] = filtered_data[partner_col].map(country_to_region_map)
    
    # Assign "Other" to countries without a region
    if include_other_region:
        filtered_data['region'] = filtered_data['region'].fillna('Other')
    
    # Group data by year and region, and aggregate values
    hw_manager = get_hardware_manager()
    if hw_manager.is_hardware_acceleration_available(AccelerationType.CPU):
        try:
            logger.debug("Using hardware acceleration for time series aggregation")
            result = hw_manager.accelerate_groupby(
                filtered_data, 
                by=[year_col, 'region'], 
                agg={value_col: 'sum'}
            ) # Added closing parenthesis
            if result is not None:
                regional_time_series = result.reset_index()
                logger.debug("Hardware acceleration successful for time series aggregation")
            else:
                logger.warning("Hardware acceleration returned None for time series aggregation")
                # Fallback to CPU
                regional_time_series = filtered_data.groupby([year_col, 'region'])[value_col].sum().reset_index()
        except Exception as e:
            logger.warning(f"Hardware acceleration failed for time series aggregation: {e}")
            regional_time_series = filtered_data.groupby([year_col, 'region'])[value_col].sum().reset_index()
    else:
        regional_time_series = filtered_data.groupby([year_col, 'region'])[value_col].sum().reset_index()
    # Calculate total value per year
    yearly_totals = regional_time_series.groupby(year_col)[value_col].sum().reset_index()
    yearly_totals = yearly_totals.rename(columns={value_col: 'total_value'})
    
    # Merge total value back to regional time series
    regional_time_series = pd.merge(
        regional_time_series,
        yearly_totals,
        on=year_col,
        how='left'
    ) # Added closing parenthesis
    # Calculate percentage of total for each region in each year
    regional_time_series['percent_of_total'] = (regional_time_series[value_col] / regional_time_series['total_value'] * 100)
    
    # Add direction column
    regional_time_series['direction'] = direction
    
    # Sort by year and value
    regional_time_series = regional_time_series.sort_values(by=[year_col, value_col], ascending=[True, False])
    
    return regional_time_series

# Class-based API for the model
class RegionalCompositionModelV2:
    """Model for regional composition analysis (V2)."""
    
    def __init__(self, data_dir: Optional[Path] = None):
        """
        Initialize the model.
        
        Args:
            data_dir: Optional base directory for data files
        """
        self.logger = get_logger(f"{__name__}.RegionalCompositionModelV2")
        self.data_dir = data_dir
        self.hw_manager = get_hardware_manager()
        self.logger.info("RegionalCompositionModelV2 initialized")
    
    @protect("analyze_regional_composition", OperationType.MODEL_CALCULATION)
    def analyze_regional_composition(self, trade_data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """Analyze regional composition using the function-based API."""
        return analyze_regional_composition_v2(trade_data, **kwargs)

    @protect("analyze_regional_composition_time_series", OperationType.MODEL_CALCULATION)
    def analyze_regional_composition_time_series(self, trade_data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """Analyze regional composition time series using the function-based API."""
        return analyze_regional_composition_time_series_v2(trade_data, **kwargs)
    
    def get_major_regions(self) -> Dict[str, List[str]]:
        """Get a dictionary of major regions and their country codes."""
        return DEFAULT_REGIONS.copy()

# Example usage
if __name__ == "__main__":
    configure_logging(log_level=LogLevel.INFO, log_to_console=True)
    logger.info("--- Running Regional Composition Model V2 Example ---")
    
    # Create sample data
    sample_trade_data = pd.DataFrame({
        'year': [2020, 2020, 2020, 2020, 2021, 2021, 2021, 2021],
        'partner': ['CHN', 'USA', 'SAU', 'GBR', 'CHN', 'USA', 'SAU', 'GBR'],
        'value': [500, 300, 200, 100, 600, 350, 180, 120],
        'direction': ['export', 'export', 'export', 'export', 'export', 'export', 'export', 'export'],
        'country': ['887', '887', '887', '887', '887', '887', '887', '887']
    })
    
    sample_country_metadata = pd.DataFrame({
        'country_code': ['CHN', 'USA', 'SAU', 'GBR', 'YEM'],
        'country_name': ['China', 'United States', 'Saudi Arabia', 'United Kingdom', 'Yemen'],
        'region': ['Asia', 'North America', 'Middle East', 'Europe', 'Middle East']
    })
    
    # Create model instance
    model = RegionalCompositionModelV2()
    
    # Test regional composition for a specific year
    regional_composition = model.analyze_regional_composition(
        sample_trade_data,
        country_metadata=sample_country_metadata,
        year=2020,
        country_code='887',
        direction='export'
    ) # Added closing parenthesis
    logger.info(f"Regional composition for 2020:\n{regional_composition}") # Corrected indentation
    # Test regional composition time series
    regional_time_series = model.analyze_regional_composition_time_series(
        sample_trade_data,
        country_metadata=sample_country_metadata,
        country_code='887',
        direction='export',
        start_year=2020,
        end_year=2021
    )
    logger.info(f"Regional composition time series:\n{regional_time_series}")
    logger.info("--- Regional Composition Model V2 Example Finished ---")