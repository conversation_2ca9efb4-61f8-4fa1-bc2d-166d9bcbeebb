"""
Trade Openness Model for Yemen Trade Diagnostic (V2)

This module calculates trade openness metrics for trade data, measuring how
integrated Yemen is with the global economy, using V2 interfaces.
"""

# Standard library imports
import json
from typing import Any, Dict, List, Optional, Tuple, Union

# Third-party imports
import numpy as np
import pandas as pd

# Project imports
from yemen_trade_diagnostic.interfaces.cache_interface import StorageTier, memoize
from yemen_trade_diagnostic.interfaces.hardware_interface import get_hardware_manager
from yemen_trade_diagnostic.errors import protect, OperationType
# V2 Interface Imports
from yemen_trade_diagnostic.interfaces.logging_interface import LogLevel, get_logger, log_execution_time
from yemen_trade_diagnostic.interfaces.validation_interface import ValidationIssueLevel, ValidationResult, get_validation_manager

logger = get_logger(__name__)

# --- Validation Schemas (can be moved to a shared schema location) ---
EXPORTS_IMPORTS_SCHEMA = {
    'required_columns': ['year', 'value'],
    'column_types': {'year': 'integer', 'value': 'numeric'},
    'no_missing': ['year', 'value']
}

GDP_SCHEMA = {
    'required_columns': ['year', 'gdp'],
    'column_types': {'year': 'integer', 'gdp': 'numeric'},
    'no_missing': ['year', 'gdp']
}

OPENNESS_DF_SCHEMA = {
    'required_columns': ['year', 'openness'],
    'column_types': {'year': 'integer', 'openness': 'numeric'},
    'no_missing': ['year', 'openness']
}

# --- Helper for V2 Validation ---
def _validate_df(df: pd.DataFrame, schema: Dict, df_name: str, validation_manager: Any) -> bool:
    """Validates a DataFrame using the V2 validation manager."""
    # This is a simplified validation call. In a real scenario, you might use
    # validation_manager.validate_schema(df, schema_rules_object) or a similar method.
    # For now, mimicking basic checks and reporting.
    
    # Using a placeholder for actual validation logic if get_validation_manager().validate_schema is not directly usable
    # Here we'll manually check for required columns and types as an example
    validation_result = ValidationResult()

    if not isinstance(df, pd.DataFrame):
        validation_result.add_issue(f"{df_name} is not a pandas DataFrame.", ValidationIssueLevel.ERROR)
    else:
        for col in schema.get('required_columns', []):
            if col not in df.columns:
                validation_result.add_issue(f"Required column '{col}' missing in {df_name}.", ValidationIssueLevel.ERROR, location=col)
        
        if validation_result.is_valid: # Only check types if columns are present
            for col, expected_type in schema.get('column_types', {}).items():
                if col in df.columns:
                    actual_dtype = df[col].dtype
                    is_type_ok = False
                    if expected_type == 'integer': is_type_ok = pd.api.types.is_integer_dtype(actual_dtype)
                    elif expected_type == 'numeric': is_type_ok = pd.api.types.is_numeric_dtype(actual_dtype)
                    # Add other type checks as needed
                    
                    if not is_type_ok:
                        validation_result.add_issue(f"Column '{col}' in {df_name} has incorrect type. Expected {expected_type}, got {actual_dtype}.", ValidationIssueLevel.ERROR, location=col)
    
    if not validation_result.is_valid:
        for issue in validation_result.issues:
            logger.error(f"Validation error: {issue}")
        return False
    return True

@protect("calculate_trade_openness_v2", OperationType.COMPUTATION)
@memoize(ttl=3600, level=StorageTier.MEMORY) # Cache for 1 hour
@log_execution_time(logger=logger, level=LogLevel.DEBUG)
def calculate_trade_openness_v2(
    exports_df: pd.DataFrame,
    imports_df: pd.DataFrame,
    gdp_df: pd.DataFrame,
    year_col: str = "year",
    value_col: str = "value",
    gdp_col: str = "gdp"
) -> pd.DataFrame:
    """
    Calculate trade openness ((exports + imports) / GDP) using V2 interfaces.
    """
    logger.info("Calculating trade openness (V2)")
    validation_manager = get_validation_manager() # For potential future detailed schema validation

    # Basic V2-style validation
    if not _validate_df(exports_df, EXPORTS_IMPORTS_SCHEMA, "exports_df", validation_manager): return pd.DataFrame()
    if not _validate_df(imports_df, EXPORTS_IMPORTS_SCHEMA, "imports_df", validation_manager): return pd.DataFrame()
    if not _validate_df(gdp_df, GDP_SCHEMA, "gdp_df", validation_manager): return pd.DataFrame()
    
    hw_manager = get_hardware_manager()

    # Group by year and sum values
    yearly_exports = exports_df.groupby(year_col)[value_col].sum().reset_index()
    yearly_imports = imports_df.groupby(year_col)[value_col].sum().reset_index()
    
    trade_data = pd.merge(
        yearly_exports.rename(columns={value_col: "exports"}),
        yearly_imports.rename(columns={value_col: "imports"}),
        on=year_col,
        how="outer"
    )
    trade_data["exports"] = trade_data["exports"].fillna(0)
    trade_data["imports"] = trade_data["imports"].fillna(0)
    # Calculate total trade
    exports_values = trade_data["exports"].values
    imports_values = trade_data["imports"].values
    
    if hw_manager.is_hardware_acceleration_available():
        try:
            # Example: using accelerate_vector if the operation is simple element-wise
            # For simple addition, direct pandas/numpy might be faster or equivalent
            # This is a placeholder for a more complex acceleration scenario.
            # total_trade_values = hw_manager.accelerate_vector(exports_values, operation='add', other_vector=imports_values)
            # Simpler approach for now, direct sum
            trade_data["total_trade"] = hw_manager.accelerate_dataframe(trade_data, operations=['add_columns'], columns_to_add={'total_trade': ('exports', 'imports')})['total_trade']
            logger.debug("Calculated total trade with hardware acceleration hint.")
        except Exception as e:
            logger.warning(f"Hardware acceleration for total trade failed: {e}. Falling back to CPU.")
            trade_data["total_trade"] = trade_data["exports"] + trade_data["imports"]
    else:
        trade_data["total_trade"] = trade_data["exports"] + trade_data["imports"]
    
    trade_openness_df = pd.merge(
        trade_data,
        gdp_df[[year_col, gdp_col]],
        on=year_col,
        how="left"
    )
    # Calculate trade openness
    total_trade_values = trade_openness_df["total_trade"].values
    gdp_values = trade_openness_df[gdp_col].values.astype(float) # Ensure float for division
    
    # Replace 0s in GDP with NaN to avoid division by zero, then fill resulting NaNs with 0 or keep as NaN
    gdp_values_safe = np.where(gdp_values == 0, np.nan, gdp_values)

    if hw_manager.is_hardware_acceleration_available():
        try:
            # Placeholder for a more complex acceleration.
            # For division and multiplication, direct numpy/pandas is often efficient.
            # openness_values = hw_manager.accelerate_vector(total_trade_values, operation='divide_multiply', divisor=gdp_values_safe, multiplier=100)
            # Simpler approach:
            openness_values = (total_trade_values / gdp_values_safe) * 100
            trade_openness_df["openness"] = openness_values
            logger.debug("Calculated trade openness with hardware acceleration hint.")
        except Exception as e:
            logger.warning(f"Hardware acceleration for openness calculation failed: {e}. Falling back to CPU.")
            trade_openness_df["openness"] = (total_trade_values / gdp_values_safe) * 100
    else:
        trade_openness_df["openness"] = (total_trade_values / gdp_values_safe) * 100
        
    trade_openness_df["openness"] = trade_openness_df["openness"].fillna(0) # Or handle NaNs as per specific requirements
    trade_openness_df = trade_openness_df.sort_values(year_col)
    logger.info("Trade openness calculation completed (V2).")
    return trade_openness_df

@memoize(level=StorageTier.MEMORY)
def get_openness_category_v2(openness: Optional[float]) -> str:
    """
    Categorize trade openness values (V2).
    """
    if openness is None or pd.isna(openness):
        return "Unknown"
    
    if openness < 30: return "Very Low"
    if openness < 60: return "Low"
    if openness < 90: return "Medium"
    if openness < 120: return "High"
    return "Very High"

@protect("analyze_openness_trend_v2", OperationType.MODEL_CALCULATION)
@memoize(ttl=3600, level=StorageTier.MEMORY)
@log_execution_time(logger=logger, level=LogLevel.DEBUG)
def analyze_openness_trend_v2(
    openness_df: pd.DataFrame, 
    year_col: str = "year", 
    openness_col: str = "openness"
) -> Dict[str, Any]:
    """
    Analyze the trend in trade openness over time (V2).
    """
    logger.info("Analyzing trade openness trend (V2)")
    validation_manager = get_validation_manager()

    if not _validate_df(openness_df, {'required_columns': [year_col, openness_col], 'column_types': {year_col: 'integer', openness_col: 'numeric'}}, "openness_df", validation_manager):
        return {} # Return empty dict on validation failure, error already reported

    df = openness_df.sort_values(year_col).copy()
    if df[openness_col].isnull().all(): # If all openness values are null
        logger.warning(f"All values in openness column '{openness_col}' are null. Cannot analyze trend.")
        return {"trend_direction": "unknown_all_null", "avg_openness": np.nan}

    avg_openness = df[openness_col].mean()
    min_openness = df[openness_col].min()
    max_openness = df[openness_col].max()
    volatility = df[openness_col].std()
    
    trend_direction = "insufficient data"
    if len(df) > 1:
        # Filter out NaNs for regression
        valid_data = df[[year_col, openness_col]].dropna()
        if len(valid_data) > 1:
            years = valid_data[year_col].values
            openness_values = valid_data[openness_col].values
            
            # Simple linear regression slope: (N * Sxy - Sx * Sy) / (N * Sxx - Sx^2)
            n = len(years)
            sum_x = np.sum(years)
            sum_y = np.sum(openness_values)
            sum_xy = np.sum(years * openness_values)
            sum_xx = np.sum(years**2)
            
            denominator = (n * sum_xx) - (sum_x**2)
            if denominator != 0:
                slope = (n * sum_xy - sum_x * sum_y) / denominator
                if slope > 0.5: trend_direction = "increasing"
                elif slope < -0.5: trend_direction = "decreasing"
                else: trend_direction = "stable"
            else:
                trend_direction = "stable (no variance in years)"
        else:
            trend_direction = "insufficient valid data points for trend"

    year_to_year_changes: Dict[str, float] = {}
    if len(df) > 1:
        for i in range(1, len(df)):
            prev_row = df.iloc[i-1]
            curr_row = df.iloc[i]
            prev_year = prev_row[year_col]
            curr_year = curr_row[year_col]
            prev_value = prev_row[openness_col]
            curr_value = curr_row[openness_col]
            
            if pd.notna(prev_value) and pd.notna(curr_value) and prev_value != 0:
                change_pct = ((curr_value - prev_value) / prev_value) * 100
                year_to_year_changes[f"{int(prev_year)}-{int(curr_year)}"] = round(change_pct, 2)
    
    latest_year = None
    latest_openness = None
    current_category = "unknown"
    if not df.empty:
        latest_row = df.iloc[-1]
        latest_year = int(latest_row[year_col])
        latest_openness = latest_row[openness_col]
        current_category = get_openness_category_v2(latest_openness if pd.notna(latest_openness) else None)
    
    trend_analysis = {
        "avg_openness": round(avg_openness,2) if pd.notna(avg_openness) else None,
        "min_openness": round(min_openness,2) if pd.notna(min_openness) else None,
        "max_openness": round(max_openness,2) if pd.notna(max_openness) else None,
        "trend_direction": trend_direction,
        "volatility": round(volatility,2) if pd.notna(volatility) else None,
        "year_to_year_changes": year_to_year_changes,
        "latest_year": latest_year,
        "latest_openness": round(latest_openness,2) if pd.notna(latest_openness) else None,
        "current_category": current_category,
        "countries_comparison": "data not available"
    }
    logger.info("Trade openness trend analysis completed (V2).")
    return trend_analysis

if __name__ == '__main__':
    # Project imports
    from yemen_trade_diagnostic.interfaces.logging_interface import configure_logging

@protect("create_validation_result", OperationType.MODEL_CALCULATION)
def create_validation_result(is_valid=True, messages=None):
    """Helper to create ValidationResult with the correct interface."""
    result = ValidationResult()
    if messages is None:
        messages = []
    
    # Add messages as issues
    for msg in messages:
        if not is_valid or "error" in msg.lower():
            result.add_issue(msg, ValidationIssueLevel.ERROR)
        else:
            result.add_issue(msg, ValidationIssueLevel.WARNING)
    
    return result

# REMOVED DUPLICATE validate_dataframe function

    # Project imports
    from yemen_trade_diagnostic.interfaces.logging_interface import configure_logging
    configure_logging(log_level=LogLevel.DEBUG, log_to_console=True, log_to_file=False)

    logger.info("--- Running Trade Openness Model V2 Example ---")

    # Sample DataFrames
    exports_sample = pd.DataFrame({
        'year': [2020, 2021, 2022, 2020],
        'value': [100, 120, 110, 50] # year 2020 has two entries
    })
    imports_sample = pd.DataFrame({
        'year': [2020, 2021, 2022],
        'value': [200, 210, 190]
    })
    gdp_sample = pd.DataFrame({
        'year': [2020, 2021, 2022, 2023],
        'gdp': [1000, 1100, 1050, 1200]
    })
    
    empty_df = pd.DataFrame()

    # Test calculate_trade_openness_v2
    logger.info("--- Testing calculate_trade_openness_v2 ---")
    try:
        openness_results = calculate_trade_openness_v2(exports_sample, imports_sample, gdp_sample)
        logger.info(f"Trade Openness Results: {openness_results}")

        # Test with one empty DF (should trigger validation and fallback)
        logger.info("--- Testing with empty exports_df ---")
        openness_results_empty_export = calculate_trade_openness_v2(empty_df, imports_sample, gdp_sample)
        logger.info(f"Trade Openness with empty exports (should be empty or error): {openness_results_empty_export}")

    except Exception as e:
        logger.error(f"Error in calculate_trade_openness_v2 test: {e}", exc_info=True)

    # Test get_openness_category_v2
    logger.info("--- Testing get_openness_category_v2 ---")
    test_values = [25, 50, 75, 100, 130, None, np.nan]
    for val in test_values:
        cat = get_openness_category_v2(val)
        logger.info(f"Openness: {val}, Category: {cat}")

    # Test analyze_openness_trend_v2
    logger.info("--- Testing analyze_openness_trend_v2 ---")
    if not openness_results.empty:
        try:
            trend_analysis = analyze_openness_trend_v2(openness_results)
            logger.info(f"Trend Analysis: {json.dumps(trend_analysis, indent=2)}")

            # Test with openness data that includes NaNs
            openness_with_nan = openness_results.copy()
            if len(openness_with_nan) > 1:
                 openness_with_nan.loc[0, 'openness'] = np.nan # Set first openness to NaN
            logger.info("--- Testing trend analysis with NaN in openness ---")
            trend_nan = analyze_openness_trend_v2(openness_with_nan)
            logger.info(f"Trend Analysis with NaN: {json.dumps(trend_nan, indent=2)}")

        except Exception as e:
                        logger.error(f"Error in analyze_openness_trend_v2 test: {e}", exc_info=True)
    else:
        logger.warning("Skipping analyze_openness_trend_v2 test as initial calculation failed.")

    logger.info("--- Trade Openness Model V2 Example Finished ---") 