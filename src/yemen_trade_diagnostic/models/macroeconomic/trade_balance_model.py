"""
Trade Balance Model for Yemen Trade Diagnostic (V2)

This module calculates trade balance metrics for trade data using V2 interfaces.
"""

# Standard library imports
from typing import Any, Callable, Dict, List, Optional, Tuple, Union

# Third-party imports
import numpy as np
import pandas as pd

# Project imports
# from yemen_trade_diagnostic.data.loaders import load_yemen_exports, load_yemen_imports # Placeholder
from yemen_trade_diagnostic.data import DataSource, load_data  # Use unified API
from yemen_trade_diagnostic.interfaces.cache_interface import StorageTier, DataLifetime, memoize
from yemen_trade_diagnostic.interfaces.hardware_interface import AccelerationType, get_hardware_manager
from yemen_trade_diagnostic.interfaces.logging_interface import LogLevel, get_logger, log_execution_time
from yemen_trade_diagnostic.interfaces.validation_interface import SchemaValidationRule, ValidationIssueLevel, ValidationResult, get_validation_manager, validate_schema
from yemen_trade_diagnostic.models.growth.growth_model_utils import get_grouped_yearly_exports_v2
from yemen_trade_diagnostic.errors import protect, OperationType

logger = get_logger(__name__)

def validate_dataframe(df, schema, df_type="DataFrame"):
    """Fixed validation function that works with actual ValidationResult interface."""
    validation_result = ValidationResult()  # No parameters
    
    # Handle None or empty DataFrame
    if df is None or df.empty:
        validation_result.add_issue(
            f"{df_type} is None or empty",
            ValidationIssueLevel.ERROR
        )
        return validation_result
    # Check required columns
    required_cols = schema.get('required_columns', [])
    missing_cols = [col for col in required_cols if col not in df.columns]
    
    if missing_cols:
        validation_result.add_issue(
            f"Missing required columns in {df_type}: {missing_cols}",
            ValidationIssueLevel.ERROR
        )
        return validation_result
    # Check column types
    for col, expected_type in schema.get('column_types', {}).items():
        if col not in df.columns:
            continue
            
        # Add type checking logic
        if expected_type == 'integer':
            if not pd.api.types.is_integer_dtype(df[col]):
                validation_result.add_issue(
                    f"Column '{col}' in {df_type} should be integer",
                    ValidationIssueLevel.WARNING,
                    location=col
                )
        elif expected_type == 'numeric':
            if not pd.api.types.is_numeric_dtype(df[col]):
                validation_result.add_issue(
                    f"Column '{col}' in {df_type} should be numeric",
                    ValidationIssueLevel.WARNING,
                    location=col
                )
        elif expected_type == 'datetime':
            if not pd.api.types.is_datetime64_any_dtype(df[col]):
                validation_result.add_issue(
                    f"Column '{col}' in {df_type} should be datetime",
                    ValidationIssueLevel.WARNING,
                    location=col
                )
    return validation_result

# --- V2 Aligned Validation Helpers ---
# These would ideally use schema files loaded via ValidationManager or direct rule construction.

def _get_trade_data_schema_v2(year_col: str = 't', value_col: str = 'v') -> Dict[str, Any]:
    return {
        'required_columns': [year_col, value_col],
        'column_types': {year_col: 'integer', value_col: 'numeric'},
        'value_ranges': {value_col: {'min': 0}, year_col: {'min': 1900, 'max': 2100}}
    }

def _get_gdp_data_schema_v2(year_col: str = 't', gdp_col: str = 'gdp') -> Dict[str, Any]:
    return {
        'required_columns': [year_col, gdp_col],
        'column_types': {year_col: 'integer', gdp_col: 'numeric'},
        'value_ranges': {gdp_col: {'min': 0}, year_col: {'min': 1900, 'max': 2100}}
    }

# This function demonstrates how V2 validation could be used.
# In a full V2 setup, these might be registered rules in ValidationManager.
# REMOVED DUPLICATE - using the one defined at line 26

@protect("calculate_trade_balance_v2", OperationType.COMPUTATION)
@memoize(ttl=3600*24, level=StorageTier.MEMORY)
@log_execution_time(logger=logger)
def calculate_trade_balance_v2(
    exports: pd.DataFrame,
    imports: pd.DataFrame,
    year_col: str = 't',
    value_col: str = 'v'
) -> pd.DataFrame:
    """Calculate trade balance (exports - imports) (V2)."""
    logger.info(f"Calculating trade balance. Year: {year_col}, Value: {value_col}")
    empty_res_cols = [year_col, 'exports', 'imports', 'balance', 'surplus_deficit']

    val_schema = _get_trade_data_schema_v2(year_col, value_col)
    exports_val_res = validate_dataframe(exports, val_schema, "Exports")
    imports_val_res = validate_dataframe(imports, val_schema, "Imports")

    if not exports_val_res.is_valid or not imports_val_res.is_valid:
        all_issues = exports_val_res.messages + imports_val_res.messages
        logger.error(f"Trade balance input data validation failed: {all_issues}")
        return pd.DataFrame(columns=empty_res_cols)
    if exports.empty or imports.empty: # Check after _validate_df_v2 which might return empty if input was None
        logger.warning("Exports or imports data is empty after validation checks.")
        return pd.DataFrame(columns=empty_res_cols)

    # Use mapped column names if available from validation
    # Fixed: Use val_schema instead of undefined schema_config
    exports_schema_cols = {col: col for col in val_schema.get('required_columns', [])}
    imports_schema_cols = {col: col for col in val_schema.get('required_columns', [])}

    # Get effective column names for aggregation
    effective_year_col = exports_schema_cols.get(year_col, year_col)
    effective_value_col = exports_schema_cols.get(value_col, value_col)

    yearly_exports = get_grouped_yearly_exports_v2(exports, effective_year_col, [effective_value_col])
    yearly_imports = get_grouped_yearly_exports_v2(imports, effective_year_col, [effective_value_col])

    trade_balance_df = pd.merge(
        yearly_exports.rename(columns={effective_value_col: 'exports'}),
        yearly_imports.rename(columns={effective_value_col: 'imports'}),
        on=effective_year_col, how='outer'
    ).fillna(0)

    hw_manager = get_hardware_manager()
    if hw_manager.is_hardware_acceleration_available() and hasattr(hw_manager, 'accelerate_vector'):
        try:
            balance_vals = hw_manager.accelerate_vector(trade_balance_df['exports'].values, 'subtract', trade_balance_df['imports'].values)
            trade_balance_df['balance'] = balance_vals
            logger.debug("Trade balance calculated with hardware acceleration.")
        except Exception as e:
            logger.warning(f"Hardware accelerated balance calculation failed: {e}. Falling back.")
            trade_balance_df['balance'] = trade_balance_df['exports'] - trade_balance_df['imports']
    else:
        trade_balance_df['balance'] = trade_balance_df['exports'] - trade_balance_df['imports']

    trade_balance_df['surplus_deficit'] = trade_balance_df['balance'].apply(lambda x: 'surplus' if x > 0 else ('deficit' if x < 0 else 'balanced'))
    trade_balance_df = trade_balance_df.sort_values(year_col).reset_index(drop=True)
    logger.info("Trade balance calculated successfully.")
    return trade_balance_df

@protect("calculate_trade_balance_ratio_v2", OperationType.COMPUTATION)
@memoize(ttl=3600*24, level=StorageTier.MEMORY)
@log_execution_time(logger=logger)
def calculate_trade_balance_ratio_v2(
    exports: pd.DataFrame,
    imports: pd.DataFrame,
    year_col: str = 't',
    value_col: str = 'v'
) -> pd.DataFrame:
    """Calculate trade balance ratio (exports / imports) (V2)."""
    logger.info(f"Calculating trade balance ratio. Year: {year_col}, Value: {value_col}")
    trade_balance_df = calculate_trade_balance_v2(exports, imports, year_col, value_col)
    empty_res_cols = [year_col, 'exports', 'imports', 'ratio']

    if trade_balance_df.empty or not all(c in trade_balance_df.columns for c in ['exports', 'imports']):
        logger.error("Failed to get valid trade balance for ratio calculation.")
        return pd.DataFrame(columns=empty_res_cols)

    hw_manager = get_hardware_manager()
    if hw_manager.is_hardware_acceleration_available() and hasattr(hw_manager, 'accelerate_vector'):
        try:
            exports_vals = trade_balance_df['exports'].values.astype(float)
            imports_vals = trade_balance_df['imports'].values.astype(float)
            # Replace 0 in imports with NaN for safe division
            imports_safe_vals = np.where(imports_vals != 0, imports_vals, np.nan)
            ratio_vals = hw_manager.accelerate_vector(exports_vals, 'divide', imports_safe_vals)
            trade_balance_df['ratio'] = ratio_vals
            logger.debug("Trade balance ratio calculated with hardware acceleration.")
        except Exception as e:
            logger.warning(f"Hardware accelerated ratio calculation failed: {e}. Falling back.")
            trade_balance_df['ratio'] = trade_balance_df['exports'] / trade_balance_df['imports'].replace(0, np.nan)
    else:
        trade_balance_df['ratio'] = trade_balance_df['exports'] / trade_balance_df['imports'].replace(0, np.nan)
    
    logger.info("Trade balance ratio calculated successfully.")
    return trade_balance_df[[year_col, 'exports', 'imports', 'ratio']]

@protect("calculate_trade_balance_as_percent_of_gdp_v2", OperationType.MODEL_CALCULATION)
@memoize(ttl=3600*24, level=StorageTier.MEMORY)
@log_execution_time(logger=logger)
def calculate_trade_balance_as_percent_of_gdp_v2(
    exports: pd.DataFrame,
    imports: pd.DataFrame,
    gdp_data: pd.DataFrame,
    year_col: str = 't',
    value_col: str = 'v',
    gdp_col: str = 'NY.GDP.MKTP.CD' # Common World Bank indicator for GDP
) -> pd.DataFrame:
    """Calculate trade balance as a percentage of GDP (V2)."""
    logger.info("Calculating trade balance as % of GDP.")
    empty_res_cols = [year_col, 'exports', 'imports', 'balance', 'balance_pct_gdp']
    trade_balance_df = calculate_trade_balance_v2(exports, imports, year_col, value_col)

    gdp_schema = _get_gdp_data_schema_v2(year_col, gdp_col)
    gdp_val_res = validate_dataframe(gdp_data, gdp_schema, "GDP")

    if not gdp_val_res.is_valid:
        logger.error(f"GDP data validation failed: {gdp_val_res.messages}")
        return pd.DataFrame(columns=empty_res_cols)
    if gdp_data.empty: # Check after _validate_df_v2
        logger.warning("GDP data is empty.")
        return pd.DataFrame(columns=empty_res_cols)
    if trade_balance_df.empty:
        logger.warning("Trade balance data is empty, cannot calculate % of GDP.")
        return pd.DataFrame(columns=empty_res_cols)

    # Ensure GDP data has unique years for merging
    gdp_yearly = gdp_data.groupby(year_col, observed=True)[gdp_col].first().reset_index()

    merged_df = pd.merge(trade_balance_df, gdp_yearly, on=year_col, how='left')
    
    hw_manager = get_hardware_manager()
    if hw_manager.is_hardware_acceleration_available() and hasattr(hw_manager, 'accelerate_vector'):
        try:
            balance_vals = merged_df['balance'].values.astype(float)
            gdp_vals = merged_df[gdp_col].values.astype(float)
            gdp_safe_vals = np.where(gdp_vals != 0, gdp_vals, np.nan)
            
            pct_gdp_intermediate = hw_manager.accelerate_vector(balance_vals, 'divide', gdp_safe_vals)
            pct_gdp_vals = hw_manager.accelerate_vector(pct_gdp_intermediate, 'multiply', 100.0)
            merged_df['balance_pct_gdp'] = pct_gdp_vals
            logger.debug("Trade balance as % of GDP calculated with hardware acceleration.")
        except Exception as e:
            logger.warning(f"Hardware accelerated % GDP calculation failed: {e}. Falling back.")
            merged_df['balance_pct_gdp'] = (merged_df['balance'] / merged_df[gdp_col].replace(0, np.nan)) * 100
    else:
        merged_df['balance_pct_gdp'] = (merged_df['balance'] / merged_df[gdp_col].replace(0, np.nan)) * 100
    
    logger.info("Trade balance as % of GDP calculated.")
    return merged_df[[year_col, 'exports', 'imports', 'balance', gdp_col, 'balance_pct_gdp']]

@protect("calculate_trade_openness_v2", OperationType.COMPUTATION)
@memoize(ttl=3600*24, level=StorageTier.MEMORY)
@log_execution_time(logger=logger)
def calculate_trade_openness_v2(
    exports: pd.DataFrame,
    imports: pd.DataFrame,
    gdp_data: pd.DataFrame,
    year_col: str = 't',
    value_col: str = 'v',
    gdp_col: str = 'NY.GDP.MKTP.CD'
) -> pd.DataFrame:
    """Calculate trade openness ((exports + imports) / GDP) (V2)."""
    logger.info("Calculating trade openness.")
    empty_res_cols = [year_col, 'exports', 'imports', 'total_trade', gdp_col, 'openness']
    
    # Trade balance provides summed exports and imports by year
    trade_balance_df = calculate_trade_balance_v2(exports, imports, year_col, value_col)
    
    gdp_schema = _get_gdp_data_schema_v2(year_col, gdp_col)
    gdp_val_res = validate_dataframe(gdp_data, gdp_schema, "GDP")

    if not gdp_val_res.is_valid:
        logger.error(f"GDP data validation failed: {gdp_val_res.messages}")
        return pd.DataFrame(columns=empty_res_cols)
    if gdp_data.empty: 
        logger.warning("GDP data is empty.")
        return pd.DataFrame(columns=empty_res_cols)
    if trade_balance_df.empty or not all(c in trade_balance_df.columns for c in ['exports', 'imports']):
        logger.warning("Trade balance data for openness calc is invalid or empty.")
        return pd.DataFrame(columns=empty_res_cols)

    gdp_yearly = gdp_data.groupby(year_col, observed=True)[gdp_col].first().reset_index()
    merged_df = pd.merge(trade_balance_df, gdp_yearly, on=year_col, how='left')

    hw_manager = get_hardware_manager()
    if hw_manager.is_hardware_acceleration_available() and hasattr(hw_manager, 'accelerate_vector'):
        try:
            exports_vals = merged_df['exports'].values.astype(float)
            imports_vals = merged_df['imports'].values.astype(float)
            gdp_vals = merged_df[gdp_col].values.astype(float)
            gdp_safe_vals = np.where(gdp_vals != 0, gdp_vals, np.nan)

            total_trade_vals = hw_manager.accelerate_vector(exports_vals, 'add', imports_vals)
            merged_df['total_trade'] = total_trade_vals
            
            openness_intermediate = hw_manager.accelerate_vector(total_trade_vals, 'divide', gdp_safe_vals)
            openness_vals = hw_manager.accelerate_vector(openness_intermediate, 'multiply', 100.0)
            merged_df['openness'] = openness_vals
            logger.debug("Trade openness calculated with hardware acceleration.")
        except Exception as e:
            logger.warning(f"Hardware accelerated openness calculation failed: {e}. Falling back.")
            merged_df['total_trade'] = merged_df['exports'] + merged_df['imports']
            merged_df['openness'] = (merged_df['total_trade'] / merged_df[gdp_col].replace(0, np.nan)) * 100
    else:
        merged_df['total_trade'] = merged_df['exports'] + merged_df['imports']
        merged_df['openness'] = (merged_df['total_trade'] / merged_df[gdp_col].replace(0, np.nan)) * 100
    
    logger.info("Trade openness calculated.")
    return merged_df[[year_col, 'exports', 'imports', 'total_trade', gdp_col, 'openness']]

def get_openness_category_v2(openness: Optional[float]) -> str:
    """Categorize trade openness values (V2)."""
    if openness is None or pd.isna(openness):
        return "Unknown"
    
    if openness < 30: return "Very Low"
    if openness < 60: return "Low"
    if openness < 90: return "Medium"
    if openness < 120: return "High"
    return "Very High"

@protect("analyze_openness_trend_v2", OperationType.MODEL_CALCULATION)
@memoize(ttl=3600*24, level=StorageTier.MEMORY)
@log_execution_time(logger=logger)
def analyze_openness_trend_v2(
    openness_df: pd.DataFrame, 
    year_col: str = "t", 
    openness_col: str = "openness" # This is the output col from calculate_trade_openness_v2
) -> Dict[str, Any]:
    """Analyze the trend in trade openness over time (V2)."""
    logger.info("Analyzing trade openness trend.")
    default_res = {"avg_openness": np.nan, "min_openness": np.nan, "max_openness": np.nan, 
                   "trend_direction": "unknown", "volatility": np.nan, 
                   "year_to_year_changes": {}, "latest_year": None, 
                   "latest_openness": np.nan, "current_category": "unknown"}

    if not isinstance(openness_df, pd.DataFrame) or openness_df.empty:
        logger.warning("Input openness_df is empty or not a DataFrame for trend analysis.")
        return default_res
    
    required_cols = [year_col, openness_col]
    if not all(col in openness_df.columns for col in required_cols):
        missing = [col for col in required_cols if col not in openness_df.columns]
        logger.error(f"Openness DataFrame missing required columns: {missing}")
        return default_res

    df = openness_df.sort_values(year_col).copy().dropna(subset=[openness_col]).reset_index(drop=True)
    if len(df) < 2:
        logger.warning("Insufficient data points (after NaN drop) for openness trend analysis.")
        if len(df) == 1:
             single_val = df[openness_col].iloc[0]
             single_year = df[year_col].iloc[0]
             return {"avg_openness": single_val, "min_openness": single_val, "max_openness": single_val, 
                     "trend_direction": "insufficient data", "volatility": 0.0, 
                     "year_to_year_changes": {}, "latest_year": single_year, 
                     "latest_openness": single_val, "current_category": get_openness_category_v2(single_val)}
        return default_res

    avg_openness = df[openness_col].mean()
    min_openness = df[openness_col].min()
    max_openness = df[openness_col].max()
    volatility = df[openness_col].std()

    # Trend direction using simple linear regression slope
    years_numeric = pd.to_numeric(df[year_col], errors='coerce')
    openness_values_numeric = pd.to_numeric(df[openness_col], errors='coerce')
    valid_mask = years_numeric.notna() & openness_values_numeric.notna()
    
    trend_direction = "insufficient data"
    if valid_mask.sum() > 1:
        x = years_numeric[valid_mask].values
        y = openness_values_numeric[valid_mask].values
        n = len(x)
        # Using np.polyfit for robustness and to handle potential perfect vertical lines better than manual formula
        try:
            slope, _ = np.polyfit(x, y, 1)
            if slope > 0.5: trend_direction = "increasing"
            elif slope < -0.5: trend_direction = "decreasing"
            else: trend_direction = "stable"
        except (np.linalg.LinAlgError, TypeError, ValueError) as lin_err:
            logger.warning(f"Could not calculate trend slope: {lin_err}. Trend direction set to 'stable' as fallback.")
            trend_direction = "stable" # Fallback if regression fails
    
    year_to_year_changes: Dict[str, float] = {}
    df['pct_change'] = df[openness_col].pct_change() * 100
    for i in range(1, len(df)):
        prev_year = df.iloc[i-1][year_col]
        curr_year = df.iloc[i][year_col]
        change_val = df.iloc[i]['pct_change']
        if pd.notna(change_val):
            year_to_year_changes[f"{int(prev_year)}-{int(curr_year)}"] = round(change_val, 2)

    latest_year = df[year_col].iloc[-1]
    latest_openness = df[openness_col].iloc[-1]
    current_category = get_openness_category_v2(latest_openness)

    analysis_results = {
        "avg_openness": avg_openness,
        "min_openness": min_openness,
        "max_openness": max_openness,
        "trend_direction": trend_direction,
        "volatility": volatility,
        "year_to_year_changes": year_to_year_changes,
        "latest_year": latest_year,
        "latest_openness": latest_openness,
        "current_category": current_category,
    }
    logger.info("Trade openness trend analysis completed.")
    return analysis_results

if __name__ == '__main__':
    # Project imports
    from yemen_trade_diagnostic.interfaces.logging_interface import configure_logging

@protect("create_validation_result", OperationType.MODEL_CALCULATION)
def create_validation_result(is_valid=True, messages=None):
    """Helper to create ValidationResult with the correct interface."""
    result = ValidationResult()
    if messages is None:
        messages = []
    
    # Add messages as issues
    for msg in messages:
        if not is_valid or "error" in msg.lower():
            result.add_issue(msg, ValidationIssueLevel.ERROR)
        else:
            result.add_issue(msg, ValidationIssueLevel.WARNING)
    
    return result

# REMOVED THIRD DUPLICATE validate_dataframe function

    # Project imports
    from yemen_trade_diagnostic.interfaces.logging_interface import configure_logging
    configure_logging(log_level=LogLevel.DEBUG, log_to_console=True, log_to_file=False)
    logger.info("Starting Trade Balance & Openness Model V2 example.")

    # Sample data
    exports_sample = pd.DataFrame({
        't': [2020, 2021, 2022, 2020],
        'v': [100, 110, 120, 50] 
    })
    imports_sample = pd.DataFrame({
        't': [2020, 2021, 2022, 2022],
        'v': [80, 90, 100, 30] 
    })
    gdp_sample = pd.DataFrame({
        't': [2020, 2021, 2022],
        'NY.GDP.MKTP.CD': [1000, 1100, 1200]
    })

    # Test trade balance functions
    tb_df = calculate_trade_balance_v2(exports_sample, imports_sample, year_col='t', value_col='v')
    logger.info(f"Trade Balance:\n{tb_df}")
    tbr_df = calculate_trade_balance_ratio_v2(exports_sample, imports_sample, year_col='t', value_col='v')
    logger.info(f"Trade Balance Ratio:\n{tbr_df}")
    tb_gdp_df = calculate_trade_balance_as_percent_of_gdp_v2(exports_sample, imports_sample, gdp_sample, year_col='t', value_col='v', gdp_col='NY.GDP.MKTP.CD')
    logger.info(f"Trade Balance as % of GDP:\n{tb_gdp_df}")
    
    # Test trade openness functions
    to_df = calculate_trade_openness_v2(exports_sample, imports_sample, gdp_sample, year_col='t', value_col='v', gdp_col='NY.GDP.MKTP.CD')
    logger.info(f"Trade Openness:\n{to_df}")

    if not to_df.empty and 'openness' in to_df.columns:
        # Test get_openness_category_v2
        # Ensure there's data for max year before trying to access .iloc[0]
        max_year_data = to_df[to_df['t'] == to_df['t'].max()]
        if not max_year_data.empty:
            latest_openness_val = max_year_data['openness'].iloc[0]
            category = get_openness_category_v2(latest_openness_val)
            logger.info(f"Latest Openness Category ({to_df['t'].max()}): {category} (Value: {latest_openness_val:.2f})")
        else:
            logger.warning(f"No data for max year {to_df['t'].max()} in openness dataframe.")

        # Test analyze_openness_trend_v2
        openness_trend_analysis = analyze_openness_trend_v2(to_df.copy(), year_col='t', openness_col='openness')
        logger.info(f"Trade Openness Trend Analysis: {openness_trend_analysis}")
    else:
        logger.warning("Trade openness DataFrame is empty or lacks 'openness' column, skipping trend analysis.")

    logger.info("Trade Balance & Openness Model V2 example finished.") 