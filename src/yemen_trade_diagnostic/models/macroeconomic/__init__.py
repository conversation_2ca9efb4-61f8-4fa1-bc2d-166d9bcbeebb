"""
Macroeconomic Models (V2)

This package contains V2 models related to macroeconomic analysis of trade data.
"""

# Project imports
from yemen_trade_diagnostic.models.macroeconomic.terms_of_trade_model import (
    calculate_price_indices_v2,
    calculate_terms_of_trade_v2,
    get_tot_trend_v2
)
from yemen_trade_diagnostic.models.macroeconomic.trade_balance_model import (
    calculate_trade_balance_as_percent_of_gdp_v2,
    calculate_trade_balance_ratio_v2,
    calculate_trade_balance_v2
)
# Added for the new dedicated trade_openness_model
from yemen_trade_diagnostic.models.macroeconomic.trade_openness_model import (
    analyze_openness_trend_v2,
    calculate_trade_openness_v2,
    get_openness_category_v2
)

__all__ = [
    # from trade_balance_model
    "calculate_trade_balance_v2",
    "calculate_trade_balance_ratio_v2",
    "calculate_trade_balance_as_percent_of_gdp_v2",
    # from terms_of_trade_model
    "calculate_price_indices_v2",
    "calculate_terms_of_trade_v2",
    "get_tot_trend_v2",
    # from trade_openness_model
    "calculate_trade_openness_v2",
    "get_openness_category_v2",
    "analyze_openness_trend_v2",
]