"""
Terms of Trade Model for Yemen Trade Diagnostic (V2)

This module calculates terms of trade metrics for Yemen's trade data using V2 interfaces.
"""

# Standard library imports
from typing import Any, Callable, Dict, List, Optional, Tuple, Union

# Third-party imports
import numpy as np
import pandas as pd

# Project imports
from yemen_trade_diagnostic.data import DataSource, load_data
from yemen_trade_diagnostic.interfaces.cache_interface import StorageTier, DataLifetime, memoize
from yemen_trade_diagnostic.interfaces.hardware_interface import AccelerationType, get_hardware_manager
from yemen_trade_diagnostic.interfaces.logging_interface import LogLevel, get_logger, log_execution_time
from yemen_trade_diagnostic.interfaces.validation_interface import SchemaValidationRule, ValidationIssueLevel, ValidationResult, get_validation_manager, validate_schema
from yemen_trade_diagnostic.errors import protect, OperationType

logger = get_logger(__name__)

# Ad-hoc validation helper adapted for V2 (similar to one in trade_balance_model_v2.py)
def _validate_price_data_v2(df: pd.DataFrame, year_col: str, value_col: str, quantity_col: str, df_name: str) -> ValidationResult:
    validation_result = ValidationResult()
    if not isinstance(df, pd.DataFrame) or df.empty:
        validation_result.add_issue(f"{df_name} data is not a valid or non-empty DataFrame.", ValidationIssueLevel.ERROR)
        return validation_result
    
    required_cols = [year_col, value_col, quantity_col]
    missing_cols = [col for col in required_cols if col not in df.columns]
    if missing_cols:
        validation_result.add_issue(f"Missing required columns in {df_name}: {missing_cols}", ValidationIssueLevel.ERROR)
    
    # Simplified type checks for brevity
    if not pd.api.types.is_numeric_dtype(df[value_col]) or not pd.api.types.is_numeric_dtype(df[quantity_col]):
        validation_result.add_issue(f"Value or quantity column in {df_name} is not numeric.", ValidationIssueLevel.ERROR)
    return validation_result

@protect("calculate_growth_rate_v2", OperationType.COMPUTATION)
def calculate_growth_rate_v2(current_values: np.ndarray, previous_values: np.ndarray) -> np.ndarray:
    """Calculate percentage growth rate between consecutive values."""
    if not (isinstance(current_values, np.ndarray) and isinstance(previous_values, np.ndarray)):
        logger.error("Inputs must be NumPy arrays.")
        return np.array([])
    if current_values.shape != previous_values.shape:
        logger.error("Inputs must have the same shape.")
        return np.array([])
    
    # Avoid division by zero
    safe_prev_values = np.where(previous_values == 0, np.nan, previous_values)
    growth_rates = ((current_values / safe_prev_values) - 1) * 100
    return growth_rates

@protect("_calculate_unit_values_v2", OperationType.COMPUTATION)
def _calculate_unit_values_v2(values: np.ndarray, quantities: np.ndarray) -> np.ndarray:
    """Calculate unit values (value / quantity) ensuring inputs are NumPy arrays (V2)."""
    if not (isinstance(values, np.ndarray) and isinstance(quantities, np.ndarray)):
        logger.error("Inputs 'values' and 'quantities' must be NumPy arrays.")
        return np.array([])
    if values.shape != quantities.shape:
        logger.error("Inputs 'values' and 'quantities' must have the same shape.")
        return np.array([])
    if values.size == 0:
        return np.array([]) # Return empty if input is empty

    # Ensure float for division and handle zero quantities to avoid division by zero -> inf/nan
    safe_quantities = np.where(quantities.astype(float) == 0, np.nan, quantities.astype(float))
    
    hw_manager = get_hardware_manager()
    result_array: Optional[np.ndarray] = None

    if hw_manager.is_hardware_acceleration_available():
        logger.debug("Attempting HW acceleration for unit value calculation.")
        try:
            # Ensure values is also float for the operation
            values_float = values.astype(float)
            result_array = hw_manager.accelerate_array(values_float, "divide", safe_quantities)
            if result_array is None: # Fallback if acceleration returns None
                logger.warning("Unit value HW acceleration returned None, falling back.")
                result_array = values_float / safe_quantities
            else:
                logger.debug("Unit value HW acceleration successful.")
        except Exception as e:
            logger.warning(f"Unit value HW acceleration attempt failed: {e}. Falling back to NumPy.")
            result_array = values.astype(float) / safe_quantities
    else:
        logger.debug("HW acceleration not available for unit values. Using NumPy.")
        result_array = values.astype(float) / safe_quantities
    
    return np.nan_to_num(result_array, nan=0.0, posinf=0.0, neginf=0.0) # Replace nan/inf with 0 after calculation

@protect("calculate_price_indices_v2", OperationType.COMPUTATION)
@memoize(ttl=DataLifetime.DAILY.value if hasattr(DataLifetime, 'DAILY') else 3600*24, level=StorageTier.MEMORY)
@log_execution_time(logger=logger)
def calculate_price_indices_v2(
    df: pd.DataFrame,
    base_year: int = 2010,
    year_col: str = "t",
    value_col: str = "v",
    quantity_col: str = "q"
) -> pd.DataFrame:
    """Calculate price indices using the base year method (V2)."""
    logger.info(f"Calculating price indices with base year: {base_year}.")
    empty_res_cols = [year_col, 'price_index']
    
    validation_res = _validate_price_data_v2(df, year_col, value_col, quantity_col, "InputDF")
    if not validation_res.is_valid:
        logger.error(f"Price index input data validation failed: {validation_res.issues}")
        return pd.DataFrame(columns=empty_res_cols)

    current_base_year = base_year
    if base_year not in df[year_col].unique():
        original_base_year_missing_msg = f"Base year {base_year} not found in data."
        available_years = sorted(df[year_col].unique())
        if not available_years:
            logger.error(f"{original_base_year_missing_msg} No years available in data.")
            return pd.DataFrame(columns=empty_res_cols)
        current_base_year = available_years[0]
        logger.warning(f"{original_base_year_missing_msg} Using earliest available year {current_base_year} as base.")

    # Using a simplified groupby, assuming a shared V2 util like get_grouped_yearly_exports_v2 would be ideal
    yearly_data = df.groupby(year_col, observed=True).agg(
        total_value=(value_col, 'sum'),
        total_quantity=(quantity_col, 'sum')
    ).reset_index()

    yearly_data['unit_value'] = _calculate_unit_values_v2(yearly_data['total_value'].values, yearly_data['total_quantity'].values)
    
    base_year_unit_value_series = yearly_data.loc[yearly_data[year_col] == current_base_year, 'unit_value']
    if base_year_unit_value_series.empty or pd.isna(base_year_unit_value_series.iloc[0]) or base_year_unit_value_series.iloc[0] == 0:
        logger.error(f"Base year {current_base_year} has invalid or zero unit value. Cannot calculate price index.")
        yearly_data['price_index'] = np.nan
        return yearly_data[[year_col, 'price_index']]
    base_unit_val = base_year_unit_value_series.iloc[0]

    yearly_data['price_index'] = (yearly_data['unit_value'] / base_unit_val) * 100
    logger.info("Price indices calculated.")
    return yearly_data[[year_col, 'price_index']].sort_values(year_col).reset_index(drop=True)

@protect("calculate_terms_of_trade_v2", OperationType.COMPUTATION)
@memoize(ttl=DataLifetime.DAILY.value if hasattr(DataLifetime, 'DAILY') else 3600*24, level=StorageTier.MEMORY)
@log_execution_time(logger=logger)
def calculate_terms_of_trade_v2(
    exports_df: pd.DataFrame,
    imports_df: pd.DataFrame,
    base_year: int = 2010,
    year_col: str = "t",
    value_col: str = "v",
    quantity_col: str = "q"
) -> pd.DataFrame:
    """Calculate terms of trade (export price index / import price index) (V2)."""
    logger.info(f"Calculating terms of trade, base year: {base_year}.")
    empty_res_cols = [year_col, 'export_price_index', 'import_price_index', 'terms_of_trade', 'indexed_tot']

    export_price_idx_df = calculate_price_indices_v2(exports_df, base_year, year_col, value_col, quantity_col)
    import_price_idx_df = calculate_price_indices_v2(imports_df, base_year, year_col, value_col, quantity_col)

    if export_price_idx_df.empty or import_price_idx_df.empty:
        logger.error("Failed to calculate export or import price indices for ToT.")
        return pd.DataFrame(columns=empty_res_cols)

    tot_df = pd.merge(
        export_price_idx_df.rename(columns={'price_index': 'export_price_index'}),
        import_price_idx_df.rename(columns={'price_index': 'import_price_index'}),
        on=year_col, how='outer'
    )
    
    # Calculate ToT = (ExportPI / ImportPI) * 100
    # Hardware acceleration can be applied here if hw_manager.accelerate_vector is robust
    hw_manager = get_hardware_manager()
    if hw_manager.is_hardware_acceleration_available() and hasattr(hw_manager, 'accelerate_vector'):
        try:
            epi_vals = tot_df['export_price_index'].values.astype(float)
            ipi_vals = tot_df['import_price_index'].values.astype(float)
            ipi_safe_vals = np.where(ipi_vals != 0, ipi_vals, np.nan)
            
            tot_intermediate = hw_manager.accelerate_vector(epi_vals, 'divide', ipi_safe_vals)
            tot_final_vals = hw_manager.accelerate_vector(tot_intermediate, 'multiply', 100.0)
            tot_df['terms_of_trade'] = tot_final_vals
            logger.debug("Terms of trade calculated with hardware acceleration.")
        except Exception as e_hw:
            logger.warning(f"Hardware accelerated ToT calculation failed: {e_hw}. Falling back.")
            tot_df['terms_of_trade'] = (tot_df['export_price_index'] / tot_df['import_price_index'].replace(0, np.nan)) * 100
    else:
        tot_df['terms_of_trade'] = (tot_df['export_price_index'] / tot_df['import_price_index'].replace(0, np.nan)) * 100

    # Index ToT to the base_year = 100
    base_tot_series = tot_df.loc[tot_df[year_col] == base_year, 'terms_of_trade']
    if not base_tot_series.empty and pd.notna(base_tot_series.iloc[0]) and base_tot_series.iloc[0] != 0:
        base_tot_val = base_tot_series.iloc[0]
        tot_df['indexed_tot'] = (tot_df['terms_of_trade'] / base_tot_val) * 100
    else:
        logger.warning(f"Base year {base_year} ToT value is invalid or not found. Indexed ToT will be NaN.")
        tot_df['indexed_tot'] = np.nan
    
    logger.info("Terms of trade calculation complete.")
    return tot_df.sort_values(year_col).reset_index(drop=True)

@protect("calculate_terms_of_trade_from_indices", OperationType.COMPUTATION)
@memoize(ttl=3600*24, level=StorageTier.MEMORY)  # Use numeric value for ttl to avoid str/float issues
@log_execution_time(logger=logger)
def calculate_terms_of_trade_from_indices(
    export_price_index_df: pd.DataFrame,
    import_price_index_df: pd.DataFrame,
    year_col: str = "year",
    price_index_col: str = "price_index"
) -> pd.DataFrame:
    """Calculate terms of trade using pre-calculated export and import price indices."""
    logger.info(f"Calculating terms of trade from pre-calculated price indices.")
    empty_res_cols = [year_col, 'export_price_index', 'import_price_index', 'terms_of_trade', 'indexed_tot']

    if export_price_index_df.empty or import_price_index_df.empty:
        logger.error("Export or import price index DataFrame is empty.")
        return pd.DataFrame(columns=empty_res_cols)

    try:
        # Create entirely new dataframes with only the required data
        # Extract years from both dataframes
        export_years = export_price_index_df[year_col].iloc[:,0] if isinstance(export_price_index_df[year_col], pd.DataFrame) else export_price_index_df[year_col]
        import_years = import_price_index_df[year_col].iloc[:,0] if isinstance(import_price_index_df[year_col], pd.DataFrame) else import_price_index_df[year_col]
        
        # Extract price indices
        export_prices = export_price_index_df[price_index_col].iloc[:,0] if isinstance(export_price_index_df[price_index_col], pd.DataFrame) else export_price_index_df[price_index_col]
        import_prices = import_price_index_df[price_index_col].iloc[:,0] if isinstance(import_price_index_df[price_index_col], pd.DataFrame) else import_price_index_df[price_index_col]
        
        # Create clean dataframes
        export_df = pd.DataFrame({year_col: export_years, 'export_price_index': export_prices})
        import_df = pd.DataFrame({year_col: import_years, 'import_price_index': import_prices})
        
        # Get all unique years
        all_years = pd.Series(pd.concat([export_df[year_col], import_df[year_col]]).unique())
        
        # Create result dataframe
        tot_df = pd.DataFrame({year_col: all_years})
        
        # Merge with export and import data
        tot_df = tot_df.merge(export_df, on=year_col, how='left')
        tot_df = tot_df.merge(import_df, on=year_col, how='left')
    
    except Exception as e:
        logger.error(f"Error in creating terms of trade dataframe: {str(e)}")
        # Standard library imports
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return pd.DataFrame(columns=empty_res_cols)

    # Calculate ToT = (ExportPI / ImportPI) * 100
    hw_manager = get_hardware_manager()
    if hw_manager.is_hardware_acceleration_available() and hasattr(hw_manager, 'accelerate_vector'):
        try:
            epi_vals = tot_df['export_price_index'].values.astype(float)
            ipi_vals = tot_df['import_price_index'].values.astype(float)
            ipi_safe_vals = np.where(ipi_vals != 0, ipi_vals, np.nan)
            
            tot_intermediate = hw_manager.accelerate_vector(epi_vals, 'divide', ipi_safe_vals)
            tot_final_vals = hw_manager.accelerate_vector(tot_intermediate, 'multiply', 100.0)
            tot_df['terms_of_trade'] = tot_final_vals
            logger.debug("Terms of trade calculated with hardware acceleration.")
        except Exception as e_hw:
            logger.warning(f"Hardware accelerated ToT calculation failed: {e_hw}. Falling back.")
            tot_df['terms_of_trade'] = (tot_df['export_price_index'] / tot_df['import_price_index'].replace(0, np.nan)) * 100
    else:
        tot_df['terms_of_trade'] = (tot_df['export_price_index'] / tot_df['import_price_index'].replace(0, np.nan)) * 100

    # Use first year as base year for indexed ToT if there's data
    base_year = tot_df[year_col].min() if not tot_df.empty else 2010
    base_tot_series = tot_df.loc[tot_df[year_col] == base_year, 'terms_of_trade']
    
    if not base_tot_series.empty and pd.notna(base_tot_series.iloc[0]) and base_tot_series.iloc[0] != 0:
        base_tot_val = base_tot_series.iloc[0]
        tot_df['indexed_tot'] = (tot_df['terms_of_trade'] / base_tot_val) * 100
    else:
        logger.warning(f"Base year {base_year} ToT value is invalid or not found. Indexed ToT will be NaN.")
        tot_df['indexed_tot'] = np.nan
    
    logger.info("Terms of trade calculation from indices complete.")
    return tot_df.sort_values(year_col).reset_index(drop=True)

@protect("get_tot_trend_v2", OperationType.COMPUTATION)
@memoize(ttl=3600, level=StorageTier.MEMORY)
def get_tot_trend_v2(
    tot_df: pd.DataFrame, 
    year_col: str = "t", 
    tot_col: str = "terms_of_trade"
) -> Dict[str, Any]:
    """
    Analyze terms of trade trend (V2).
    
    This is the primary function for analyzing terms of trade trends.
    It replaces duplicate functions to provide a single interface.
    
    Args:
        tot_df: DataFrame containing terms of trade data
        year_col: Column name for years
        tot_col: Column name for terms of trade values
        
    Returns:
        Dictionary with trend analysis, including trend direction, average change, and volatility
    """
    logger.info("Analyzing terms of trade trend.")
    default_res = {"trend": "unknown", "avg_annual_change": 0.0, "volatility": 0.0, "description": "Insufficient data"}
    if not isinstance(tot_df, pd.DataFrame) or tot_df.empty or not all(c in tot_df.columns for c in [year_col, tot_col]):
        logger.warning("Invalid or empty DataFrame for ToT trend analysis.")
        return default_res

    df_sorted = tot_df.sort_values(year_col).dropna(subset=[tot_col]).reset_index(drop=True)
    if len(df_sorted) < 2:
        logger.warning("Insufficient data points for ToT trend analysis after NaN drop.")
        return default_res

    # Use V2 calculate_growth_rate_v2 for pct_change
    values = df_sorted[tot_col].values
    prev_values = np.roll(values, 1).astype(float)
    if len(prev_values)>0: prev_values[0] = np.nan
    df_sorted['pct_change'] = calculate_growth_rate_v2(values, prev_values) # Already in %
    
    valid_growth_rates = df_sorted['pct_change'].dropna().values
    avg_annual_change = float(np.mean(valid_growth_rates)) if len(valid_growth_rates) > 0 else 0.0
    volatility = float(np.std(valid_growth_rates)) if len(valid_growth_rates) > 0 else 0.0

    first_value = df_sorted.iloc[0][tot_col]
    last_value = df_sorted.iloc[-1][tot_col]
    total_change_pct = ((last_value / first_value) - 1) * 100 if first_value != 0 and not pd.isna(first_value) else 0.0
    
    trend = "stable"
    if total_change_pct > 5: trend = "improving"
    elif total_change_pct < -5: trend = "deteriorating"
    
    description = f"Terms of trade trend: {trend} (Avg Annual Change: {avg_annual_change:.2f}%, Volatility: {volatility:.2f})."
    logger.info(f"ToT trend analysis: {description}")
    return {"trend": trend, "avg_annual_change": avg_annual_change, "volatility": volatility, "description": description,
            "total_change_pct": total_change_pct, "start_year": df_sorted.iloc[0][year_col], "end_year": df_sorted.iloc[-1][year_col]}

if __name__ == '__main__':
    # Project imports
    from yemen_trade_diagnostic.interfaces.logging_interface import configure_logging
    
    configure_logging(log_level=LogLevel.DEBUG, log_to_console=True, log_to_file=False)
    logger.info("Starting Terms of Trade Model V2 example.")

    # Sample data
    exports_sample_df = pd.DataFrame({
        't': [2010, 2011, 2012, 2013],
        'v': [100, 110, 120, 130], # Export Value
        'q': [10, 11, 12, 12.5]    # Export Quantity
    })
    imports_sample_df = pd.DataFrame({
        't': [2010, 2011, 2012, 2013],
        'v': [80, 85, 95, 100],  # Import Value
        'q': [9, 9.5, 10, 10.2]   # Import Quantity
    })
    logger.debug(f"Sample exports:\n{exports_sample_df}")
    logger.debug(f"Sample imports:\n{imports_sample_df}")

    # Test calculate_price_indices_v2
    export_pi = calculate_price_indices_v2(exports_sample_df.copy(), base_year=2010)
    logger.info(f"Export Price Index (Base 2010):\n{export_pi}")
    import_pi = calculate_price_indices_v2(imports_sample_df.copy(), base_year=2010)
    logger.info(f"Import Price Index (Base 2010):\n{import_pi}")

    # Test calculate_terms_of_trade_v2
    tot_df = calculate_terms_of_trade_v2(exports_sample_df.copy(), imports_sample_df.copy(), base_year=2010)
    logger.info(f"Terms of Trade (Base 2010):\n{tot_df}")

    # Test get_tot_trend_v2
    if not tot_df.empty:
        trend_info = get_tot_trend_v2(tot_df.copy())
        logger.info(f"Terms of Trade Trend Analysis: {trend_info}")
    else:
        logger.warning("Skipping ToT trend analysis as ToT DataFrame is empty.")

    logger.info("Terms of Trade Model V2 example finished.") 