"""
Import Composition Model (V2)

This module contains V2 functions for calculating import composition.
"""

# Standard library imports
from typing import Any, Dict, List, Optional, Tuple, Union

# Third-party imports
import numpy as np
import pandas as pd

# Project imports
from yemen_trade_diagnostic.data import DataSource, load_data
from yemen_trade_diagnostic.interfaces.cache_interface import StorageTier, memoize
from yemen_trade_diagnostic.interfaces.hardware_interface import (
    AccelerationType,
    get_hardware_manager,
)
# V2 Interface Imports
from yemen_trade_diagnostic.interfaces.logging_interface import LogLevel, get_logger
from yemen_trade_diagnostic.interfaces.validation_interface import (
    ValidationIssueLevel,
    ValidationResult,
    get_validation_manager,
    validate_schema,
)
from yemen_trade_diagnostic.errors import protect, OperationType

logger = get_logger(__name__)

class DataLoadError(Exception):
    """Custom exception for data loading failures."""
    pass

@memoize(ttl=3600 * 24, level=StorageTier.MEMORY) # Cache for 1 day
def _get_product_name_mapping_v2() -> Dict[str, str]:
    """
    Get mapping from HS2 product code to product name (V2).
    Raises DataLoadError or ValueError on failure.
    """
    logger.debug("Loading product codes for mapping.")
    try:
        product_codes_df = load_data(source_name="product_codes")
        if product_codes_df is None or product_codes_df.empty:
            err_msg = "Failed to load product codes or product codes are empty."
            logger.error(err_msg)
            raise DataLoadError(err_msg)

        required_cols = ['code', 'description']
        if not all(col in product_codes_df.columns for col in required_cols):
            missing = [col for col in required_cols if col not in product_codes_df.columns]
            err_msg = f"Product codes DataFrame missing required columns: {missing}"
            logger.error(err_msg)
            raise ValueError(err_msg)

        product_codes_df['hs2'] = product_codes_df['code'].astype(str).str.slice(0, 2)
        product_name_mapping = dict(zip(product_codes_df['hs2'], product_codes_df['description']))
        logger.debug("Product name mapping created successfully.")
        return product_name_mapping

    except Exception as e:
        logger.error(f"Error in _get_product_name_mapping_v2: {e}")
        if not isinstance(e, (DataLoadError, ValueError)):
            raise DataLoadError(f"Error loading product codes: {e}") from e
        else:
            raise

@protect("calculate_import_composition_v2", OperationType.COMPUTATION)
@memoize(ttl=3600, level=StorageTier.MEMORY) # Cache for 1 hour
def calculate_import_composition_v2(year: int) -> pd.DataFrame:
    """
    Calculate import composition by product category (V2).
    Raises DataLoadError or ValueError on failure.
    """
    logger.info(f"Calculating import composition for year {year}.")
    hardware_manager = get_hardware_manager()

    try:
        logger.debug(f"Loading import and product code data for year {year}.")
        imports_data = load_data(source_name="yemen_imports", years=year)

        if imports_data is None or imports_data.empty:
            err_msg = f"No import data loaded or data is empty for year {year}."
            logger.error(err_msg)
            raise DataLoadError(err_msg)

    except Exception as e:
        logger.error(f"Error loading imports for year {year} in calculate_import_composition_v2: {e}")
        if not isinstance(e, DataLoadError):
             raise DataLoadError(f"Error loading imports for year {year}: {e}") from e
        else:
             raise

    product_name_mapping = _get_product_name_mapping_v2()

    imports_data = imports_data.copy()
    if 'product_code' not in imports_data.columns:
        err_msg = "Column 'product_code' (product code) not found in imports DataFrame."
        logger.error(err_msg)
        raise ValueError(err_msg)

    imports_data['hs2'] = imports_data['product_code'].astype(str).str.slice(0, 2)
    try:
        imports_data['product_name'] = imports_data['hs2'].map(product_name_mapping).fillna('Other Products')
    except KeyError as map_error:
        logger.error(f"KeyError during product name mapping: {map_error}. Check product code mapping data.")
        raise ValueError(f"Failed to map product code {map_error} to name.") from map_error

    logger.debug("Aggregating import data by product...")
    import_by_product = imports_data.groupby(['hs2', 'product_name'], observed=True)['value'].sum().reset_index()

    if import_by_product.empty:
        logger.warning(f"No data after grouping imports for year {year}.")
        return pd.DataFrame(columns=['hs2', 'product_name', 'value', 'year', 'percent_of_total'])

    import_by_product['year'] = year

    logger.debug("Calculating percentages for import composition...")
    import_total = import_by_product['value'].sum()
    if import_total == 0:
        logger.warning(f"Total import value is 0 for year {year}. Percentages will be 0 or NaN.")
        import_by_product['percent_of_total'] = 0.0
    else:
        import_by_product['percent_of_total'] = (import_by_product['value'] / import_total) * 100

    import_by_product = import_by_product.rename(columns={'value': 'value'}) # Renaming 'value' to 'value' is redundant
    import_by_product = import_by_product.sort_values(by='value', ascending=False).reset_index(drop=True)

    logger.info(f"Successfully calculated import composition for year {year}.")
    return import_by_product

def get_top_imports_v2(import_composition_df: Optional[pd.DataFrame], top_n: int = 10) -> pd.DataFrame:
    """
    Get the top N imports by value and group the rest as "Other" (V2).
    Raises ValueError on validation failure. Returns DataFrame otherwise.
    """
    if import_composition_df is None or import_composition_df.empty:
        logger.warning("Input import_composition_df is None or empty for get_top_imports_v2.")
        return pd.DataFrame(columns=["hs2", "product_name", "value", "year", "percent_of_total"])

    required_cols = ["hs2", "product_name", "value", "year", "percent_of_total"]
    if not all(col in import_composition_df.columns for col in required_cols):
        missing = [col for col in required_cols if col not in import_composition_df.columns]
        err_msg = f"Missing required columns in import_composition_df: {missing}"
        logger.error(err_msg)
        raise ValueError(err_msg)

    logger.debug(f"Getting top {top_n} imports.")
    result_df = import_composition_df.copy()

    if len(result_df) > top_n:
        top_imports = result_df.nlargest(top_n, 'value')
        other_imports = result_df.iloc[top_n:]

        if not other_imports.empty:
            other_row = pd.DataFrame({
                'hs2': ['9999'],
                'product_name': ['Other Imports'],
                'value': [other_imports['value'].sum()],
                'year': [result_df['year'].iloc[0]],
                'percent_of_total': [other_imports['percent_of_total'].sum()]
            })
            final_df = pd.concat([top_imports, other_row], ignore_index=True)
        else:
            final_df = top_imports
    else:
        final_df = result_df

    final_df = final_df.sort_values(by='value', ascending=False).reset_index(drop=True)
    logger.debug(f"Finished getting top {top_n} imports.")
    return final_df

@protect("calculate_year_over_year_import_change_v2", OperationType.COMPUTATION)
@memoize(ttl=3600, level=StorageTier.MEMORY) # Cache for 1 hour
def calculate_year_over_year_import_change_v2(current_year: int) -> pd.DataFrame:
    """
    Calculate year-over-year change in import composition (V2).
    Raises exceptions if data calculation fails.
    """
    logger.info(f"Calculating YoY import change for {current_year}.")

    current_year_data = calculate_import_composition_v2(current_year)

    previous_year = current_year - 1
    logger.debug(f"Loading previous year ({previous_year}) data for YoY comparison.")

    previous_year_data = None
    try:
        previous_year_data = calculate_import_composition_v2(previous_year)
    except (DataLoadError, ValueError) as prev_err:
         logger.warning(f"Could not load or process import data for previous year {previous_year}: {prev_err}. YoY change will be NaN or 100%.")

    current_year_data = current_year_data.copy()

    if previous_year_data is None or previous_year_data.empty:
        logger.warning(f"No import data for previous year {previous_year}. YoY change will be NaN or 100% for new products.")
        current_year_data['previous_value'] = np.nan
        current_year_data['yoy_change'] = 100.0 # Assume all are new if previous year missing
    else:
        prev_year_values_map = dict(zip(previous_year_data['hs2'], previous_year_data['value']))
        current_year_data['previous_value'] = current_year_data['hs2'].map(prev_year_values_map)

        current_year_data['yoy_change'] = np.where(
            current_year_data['previous_value'].notna() & (current_year_data['previous_value'] != 0),
            ((current_year_data['value'] - current_year_data['previous_value']) / current_year_data['previous_value']) * 100,
            np.nan
        )
        current_year_data.loc[current_year_data['previous_value'].isna(), 'yoy_change'] = 100.0
    logger.info(f"Successfully calculated YoY import change for {current_year}.")
    return current_year_data

@protect("calculate_import_sectoral_composition_v2", OperationType.COMPUTATION)
@memoize(ttl=3600, level=StorageTier.MEMORY) # Cache for 1 hour
def calculate_import_sectoral_composition_v2(df: pd.DataFrame, year: int, product_col: str = 'k', value_col: str = 'v', year_col: str = 't') -> pd.DataFrame:
    """
    Calculate import sectoral composition (V2).
    Raises ValueError on validation issues, RuntimeError on calculation issues.
    """
    logger.info(f"Calculating import sectoral composition for year {year}")
    hardware_manager = get_hardware_manager()

    try:
        from yemen_trade_diagnostic.utils.column_mapping import map_dataframe_columns
        # Removed: from yemen_trade_diagnostic.errors import protect, OperationType, MODEL_CONFIG (already imported)

        df = map_dataframe_columns(
            df,
            [product_col, value_col, year_col],
            inplace=False,
            create_missing=True
        ) # Added closing parenthesis
        if df is None or df.empty:
            logger.warning(f"Input DataFrame is None or empty for year {year}")
            return pd.DataFrame(columns=['sector', 'value', 'percent_of_total', 'year'])

        required_cols_provided = [product_col, value_col, year_col] # Renamed to avoid conflict

        # Check required columns exist
        if not all(col in df.columns for col in required_cols_provided):
            missing = [col for col in required_cols_provided if col not in df.columns]
            logger.error(f"Initial check: Missing input columns in import data: {missing}")

            if value_col not in df.columns:
                if 'trade_value' in df.columns:
                    logger.info(f"'{value_col}' column not found, using 'trade_value' instead")
                    df[value_col] = df['trade_value']
                elif 'trade_value_usd' in df.columns:
                    logger.info(f"'{value_col}' column not found, using 'trade_value_usd' instead")
                    df[value_col] = df['trade_value_usd']
                elif 'value' in df.columns: # Check for generic 'value' as last resort
                    logger.info(f"'{value_col}' column not found, using 'value' instead")
                    df[value_col] = df['value']

            if product_col not in df.columns:
                if 'product_code' in df.columns:
                    logger.info(f"'{product_col}' column not found, using 'product_code' instead")
                    df[product_col] = df['product_code']
                elif 'product' in df.columns: # Check for generic 'product'
                    logger.info(f"'{product_col}' column not found, using 'product' instead")
                    df[product_col] = df['product']

            if year_col not in df.columns:
                if 'year' in df.columns: # Check for generic 'year'
                    logger.info(f"'{year_col}' column not found, using 'year' instead")
                    df[year_col] = df['year']

            # Check again if required columns exist after attempting to map
            if not all(col in df.columns for col in required_cols_provided):
                missing_after_map = [col for col in required_cols_provided if col not in df.columns]
                error_msg = f"Missing required columns in import data after mapping attempts: {missing_after_map}"
                logger.error(error_msg)
                raise ValueError(error_msg)

        if df[year_col].nunique() > 1:
            filtered_df = df[df[year_col] == year].copy()
            if filtered_df.empty:
                logger.warning(f"No data found for year {year} in the provided DataFrame")
                return pd.DataFrame(columns=['sector', 'value', 'percent_of_total', 'year'])
        else:
            filtered_df = df.copy()

        filtered_df['hs2'] = filtered_df[product_col].astype(str).str.slice(0, 2)

        SECTOR_MAPPING = {
            ('01', '05'): 'Animal Products', ('06', '15'): 'Vegetable Products',
            ('16', '24'): 'Foodstuffs', ('25', '27'): 'Mineral Products',
            ('28', '38'): 'Chemical Products', ('39', '40'): 'Plastics and Rubbers',
            ('41', '43'): 'Raw Hides, Skins, Leather', ('44', '49'): 'Wood and Wood Products',
            ('50', '63'): 'Textiles', ('64', '67'): 'Footwear and Headwear',
            ('68', '71'): 'Stone and Glass', ('72', '83'): 'Metals',
            ('84', '85'): 'Machinery and Electrical', ('86', '89'): 'Transportation',
            ('90', '97'): 'Miscellaneous', ('98', '99'): 'Service'
        }

        def map_to_sector(hs2_code):
            if not hs2_code or not hs2_code.isdigit():
                return 'Other'
            hs2_int = int(hs2_code)
            for (start, end), sector in SECTOR_MAPPING.items():
                if int(start) <= hs2_int <= int(end):
                    return sector
            return 'Other'

        filtered_df['sector'] = filtered_df['hs2'].apply(map_to_sector)

        logger.debug("Aggregating import data by sector")
        sector_composition = filtered_df.groupby('sector', observed=True)[value_col].sum().reset_index()

        total_value = sector_composition[value_col].sum()
        if total_value > 0:
            sector_composition['percent_of_total'] = (sector_composition[value_col] / total_value) * 100
        else:
            sector_composition['percent_of_total'] = 0.0

        sector_composition['year'] = year
        sector_composition = sector_composition.rename(columns={value_col: 'value'})
        sector_composition = sector_composition.sort_values(by='value', ascending=False).reset_index(drop=True)

        logger.info(f"Successfully calculated import sectoral composition for year {year}")
        return sector_composition

    except Exception as e:
        error_msg = f"Error calculating import sectoral composition for year {year}: {str(e)}"
        logger.error(error_msg, exc_info=True)
        raise RuntimeError(error_msg) from e

def get_top_import_sectors_v2(import_sector_composition_df: pd.DataFrame, top_n: int = 10, value_col: str = 'value') -> pd.DataFrame:
    """
    Get the top N import sectors by value and group the rest as "Other" (V2).
    Raises ValueError on validation failure.
    """
    logger.debug(f"Getting top {top_n} import sectors")

    if import_sector_composition_df is None or import_sector_composition_df.empty:
        logger.warning("Input import_sector_composition_df is None or empty")
        return pd.DataFrame(columns=['sector', value_col, 'percent_of_total', 'year'])

    required_cols = ['sector', value_col, 'percent_of_total', 'year']
    if not all(col in import_sector_composition_df.columns for col in required_cols):
        missing = [col for col in required_cols if col not in import_sector_composition_df.columns]
        error_msg = f"Missing required columns in import sector data: {missing}"
        logger.error(error_msg)
        raise ValueError(error_msg)

    result_df = import_sector_composition_df.copy()

    if len(result_df) > top_n:
        top_sectors = result_df.nlargest(top_n, value_col)
        # Correctly select rows NOT in top_sectors for 'other_sectors'
        other_sectors_indices = result_df.index.difference(top_sectors.index)
        other_sectors = result_df.loc[other_sectors_indices]


        if not other_sectors.empty:
            other_row = pd.DataFrame({
                'sector': ['Other Sectors'],
                value_col: [other_sectors[value_col].sum()],
                'percent_of_total': [other_sectors['percent_of_total'].sum()],
                'year': [result_df['year'].iloc[0]]
            })
            final_df = pd.concat([top_sectors, other_row], ignore_index=True)
        else:
            final_df = top_sectors
    else:
        final_df = result_df

    final_df = final_df.sort_values(by=value_col, ascending=False).reset_index(drop=True)
    logger.debug(f"Finished getting top {top_n} import sectors")
    return final_df

@protect("analyze_import_composition_trend_v2", OperationType.COMPUTATION)
@memoize(ttl=3600, level=StorageTier.MEMORY)
def analyze_import_composition_trend_v2(df: pd.DataFrame, product_col: str = 'k', value_col: str = 'v', year_col: str = 't') -> pd.DataFrame:
    """
    Analyze import composition trend over time (V2).
    Raises ValueError on validation issues, RuntimeError on calculation issues.
    """
    logger.info("Analyzing import composition trend")
    hardware_manager = get_hardware_manager()

    try:
        if df is None or df.empty:
            logger.warning("Input DataFrame is None or empty")
            return pd.DataFrame(columns=['year', 'sector', 'value', 'percent_of_total', 'yoy_change'])

        required_cols_trend = [product_col, value_col, year_col] # Renamed
        if not all(col in df.columns for col in required_cols_trend):
            missing = [col for col in required_cols_trend if col not in df.columns]
            error_msg = f"Missing required columns in import data: {missing}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        years = sorted(df[year_col].unique())
        logger.debug(f"Found {len(years)} distinct years in the data: {years}")

        if len(years) < 2:
            logger.warning("At least 2 years of data are required for trend analysis")
            if len(years) == 1:
                single_year = years[0]
                # Corrected arguments for calculate_import_sectoral_composition_v2
                return calculate_import_sectoral_composition_v2(df, year=single_year, product_col=product_col,
                                                              value_col=value_col, year_col=year_col)
            else:
                return pd.DataFrame(columns=['year', 'sector', 'value', 'percent_of_total', 'yoy_change'])

        yearly_compositions = []
        for year_val in years: # Renamed iteration variable
            year_df = df[df[year_col] == year_val]
            if not year_df.empty:
                # Corrected arguments for calculate_import_sectoral_composition_v2
                composition = calculate_import_sectoral_composition_v2(year_df, year=year_val, product_col=product_col,
                                                                    value_col=value_col, year_col=year_col)
                yearly_compositions.append(composition)

        if not yearly_compositions:
            logger.warning("No valid yearly compositions calculated")
            return pd.DataFrame(columns=['year', 'sector', 'value', 'percent_of_total', 'yoy_change'])

        trend_df = pd.concat(yearly_compositions, ignore_index=True)

        trend_df = trend_df.sort_values(by=['sector', 'year'])
        trend_df['previous_value'] = trend_df.groupby('sector')['value'].shift(1)
        # 'previous_year' column was created but not used, can be removed if not needed later
        # trend_df['previous_year'] = trend_df.groupby('sector')['year'].shift(1)

        trend_df['yoy_change'] = np.where(
            (trend_df['previous_value'].notna()) & (trend_df['previous_value'] != 0),
            ((trend_df['value'] - trend_df['previous_value']) / trend_df['previous_value']) * 100,
            np.nan
        )
        trend_df.loc[trend_df['previous_value'].isna(), 'yoy_change'] = 100.0

        result_df = trend_df[['year', 'sector', 'value', 'percent_of_total', 'yoy_change']]

        logger.info("Successfully analyzed import composition trend")
        return result_df.sort_values(by=['year', 'value'], ascending=[True, False]).reset_index(drop=True)

    except Exception as e:
        error_msg = f"Error analyzing import composition trend: {str(e)}"
        logger.error(error_msg, exc_info=True)
        raise RuntimeError(error_msg) from e


if __name__ == '__main__':
    from yemen_trade_diagnostic.interfaces.logging_interface import configure_logging
    # Removed: MODEL_CONFIG from errors import, as it's not used here
    # from yemen_trade_diagnostic.errors import protect, OperationType

    configure_logging(log_level=LogLevel.DEBUG, log_to_console=True, log_to_file=False)

    logger.info("Starting import composition model V2 example.")
    try:
        composition_2022 = calculate_import_composition_v2(year=2022)
        # Removed redundant 'is not None' check as function now raises or returns DataFrame
        logger.info(f"Calculated import composition for 2022:\n{composition_2022.head()}")
        top_imports_2022 = get_top_imports_v2(composition_2022, top_n=5)
        logger.info(f"Top 5 imports for 2022:\n{top_imports_2022}")

        yoy_change_2022 = calculate_year_over_year_import_change_v2(current_year=2022)
        logger.info(f"Calculated YoY import change for 2022:\n{yoy_change_2022.head()}")

        try:
            imports_data_main = load_data(source_name="yemen_imports") # Renamed to avoid conflict
            if imports_data_main is not None and not imports_data_main.empty:
                # Corrected arguments for calculate_import_sectoral_composition_v2
                import_sectors = calculate_import_sectoral_composition_v2(imports_data_main, year=2022) # Assuming year 2022 for consistency
                logger.info(f"Import sectoral composition:\n{import_sectors.head()}")

                top_sectors = get_top_import_sectors_v2(import_sectors, top_n=5)
                logger.info(f"Top 5 import sectors:\n{top_sectors}")

                trend = analyze_import_composition_trend_v2(imports_data_main)
                logger.info(f"Import composition trend:\n{trend.head()}")
            else:
                logger.warning("No import data available for testing new functions")
        except Exception as ie_inner: # Renamed inner exception
            logger.error(f"Could not test new functions due to data loading issues: {ie_inner}")

    except ImportError as ie_outer: # Renamed outer exception
        logger.error(f"Could not run example due to missing V2 components (e.g., data loaders): {ie_outer}")
    except DataLoadError as dle:
        logger.error(f"A DataLoadError occurred during the example: {dle}")
    except ValueError as ve:
        logger.error(f"A ValueError occurred during the example: {ve}")
    except RuntimeError as rte:
        logger.error(f"A RuntimeError occurred during the example: {rte}")
    except Exception as e_main: # Renamed main exception
        logger.error(f"An unexpected error occurred during the example: {e_main}", exc_info=True)

    logger.info("Import composition model V2 example finished.")