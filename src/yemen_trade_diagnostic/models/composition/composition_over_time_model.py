"""
Composition Over Time Model (V2)

This module contains V2 functions for calculating trade composition over time.
"""

# Standard library imports
from typing import Any, Dict, List, Optional, Tuple, Union

# Third-party imports
import numpy as np
import pandas as pd

# Project imports
from yemen_trade_diagnostic.data import DataSource, load_data
from yemen_trade_diagnostic.interfaces.cache_interface import StorageTier, memoize
from yemen_trade_diagnostic.errors import protect, OperationType # Removed MODEL_CONFIG as it's not used
from yemen_trade_diagnostic.interfaces.hardware_interface import (
    AccelerationType,
    get_hardware_manager,
)
from yemen_trade_diagnostic.interfaces.logging_interface import LogLevel, get_logger
from yemen_trade_diagnostic.interfaces.validation_interface import (
    ValidationIssueLevel,
    ValidationResult,
    get_validation_manager,
    validate_schema,
)
from yemen_trade_diagnostic.models.composition.sector_composition_model import (
    _calculate_percentages_v2,
    _extract_hs2_code_v2,
    _map_hs2_to_sector_v2,
    # calculate_sector_composition_v2, # Not directly used in calculate_composition_over_time_v2
)

logger = get_logger(__name__)

class DataLoadError(Exception):
    """Custom exception for data loading failures."""
    pass

@protect("_load_multi_year_data_v2", OperationType.DATA_LOADING)
def _load_multi_year_data_v2(start_year: int, end_year: int) -> Tuple[Optional[pd.DataFrame], Optional[pd.DataFrame]]:
    """
    Load Yemen's exports and imports for multiple years using V2 loaders and error handling.
    Raises DataLoadError if no data can be loaded for the entire range.
    """
    all_exports_list: List[pd.DataFrame] = []
    all_imports_list: List[pd.DataFrame] = []
    hardware_manager = get_hardware_manager()

    logger.info(f"Loading multi-year data from {start_year} to {end_year}.")

    for year in range(start_year, end_year + 1):
        try:
            logger.debug(f"Loading data for year {year}.")
            exports_df = load_data(source_name="yemen_exports", years=year)
            if exports_df is not None and not exports_df.empty:
                exports_df['year'] = year
                all_exports_list.append(exports_df)
            else:
                logger.warning(f"No export data loaded or empty for year {year}.")

            imports_df = load_data(source_name="yemen_imports", years=year)
            if imports_df is not None and not imports_df.empty:
                imports_df['year'] = year
                all_imports_list.append(imports_df)
            else:
                logger.warning(f"No import data loaded or empty for year {year}.")
        except Exception as e:
            logger.error(f"Failed to load data for year {year} in _load_multi_year_data_v2: {e}. Continuing to next year.")
            # Continue to next year

    final_exports_df = pd.concat(all_exports_list, ignore_index=True) if all_exports_list else None
    final_imports_df = pd.concat(all_imports_list, ignore_index=True) if all_imports_list else None

    if final_exports_df is None and final_imports_df is None:
        err_msg = f"No data loaded for any year in the range {start_year}-{end_year}."
        logger.error(err_msg)
        raise DataLoadError(err_msg) # Raise DataLoadError

    logger.info("Finished loading multi-year data.")
    return final_exports_df, final_imports_df

@protect("_get_top_sectors_across_years_v2", OperationType.COMPUTATION)
def _get_top_sectors_across_years_v2(df: pd.DataFrame, value_col: str = 'value', top_n: int = 5) -> List[str]:
    """
    Get the top N sectors by average value across all years (V2).
    """
    if not isinstance(df, pd.DataFrame) or df.empty:
        logger.warning("Input DataFrame for top sectors is empty or not a DataFrame.")
        return []
    if value_col not in df.columns or 'sector_name' not in df.columns:
        err_msg = f"Required columns '{value_col}' or 'sector_name' not in DataFrame for _get_top_sectors_across_years_v2."
        logger.error(err_msg)
        raise ValueError(err_msg) # Raise ValueError

    logger.debug(f"Getting top {top_n} sectors across years based on column '{value_col}'.")
    avg_by_sector = df.groupby('sector_name', observed=True)[value_col].mean().reset_index()
    top_sectors = avg_by_sector.nlargest(top_n, value_col)['sector_name'].tolist()
    logger.debug(f"Top sectors identified: {top_sectors}")
    return top_sectors

@protect("calculate_composition_over_time_v2", OperationType.COMPUTATION)
@memoize(ttl=3600 * 2, level=StorageTier.MEMORY) # Cache for 2 hours
def calculate_composition_over_time_v2(
    start_year: int = 2002,
    end_year: int = 2023
) -> pd.DataFrame:
    """
    Calculate trade composition over time (V2).
    Raises exceptions on data loading or processing errors.
    """
    logger.info(f"Calculating composition over time from {start_year} to {end_year}.")
    hardware_manager = get_hardware_manager()

    all_exports, all_imports = _load_multi_year_data_v2(start_year, end_year)

    processed_data_list = []
    # Default value column to 'v' if not present, assuming 'v' from earlier processing steps or load_data.
    # This needs to align with how load_data structures the DataFrame.
    # For now, we'll assume 'v' is the primary value column after initial loading/mapping.
    # The helpers _extract_hs2_code_v2 and _map_hs2_to_sector_v2 might also depend on specific column names.
    # If load_data returns e.g. 'trade_value_usd', it needs to be mapped to 'v' or helpers adapted.

    # Assuming product_code is 'k' and value is 'v' as per typical UN Comtrade shorthand
    # or that load_data standardizes to these. If not, mapping is needed.
    # For this refactor, we assume the helpers are robust or data is pre-mapped.

    if all_exports is not None and not all_exports.empty:
        logger.debug("Processing multi-year export data...")
        # Ensure 'k' (product_code) and 'v' (value) columns exist or map them
        # This is a placeholder for actual mapping logic if needed:
        # if 'k' not in all_exports.columns and 'product_code' in all_exports.columns: all_exports.rename(columns={'product_code':'k'}, inplace=True)
        # if 'v' not in all_exports.columns and 'trade_value_usd' in all_exports.columns: all_exports.rename(columns={'trade_value_usd':'v'}, inplace=True)
        try:
            exports_processed = _extract_hs2_code_v2(all_exports, product_code_col='k' if 'k' in all_exports.columns else 'product_code')
            exports_processed = _map_hs2_to_sector_v2(exports_processed)
            exports_processed['trade_type'] = 'Export'
            # Ensure 'v' exists for aggregation, map if necessary
            if 'v' not in exports_processed.columns and 'trade_value_usd' in exports_processed.columns:
                 exports_processed.rename(columns={'trade_value_usd':'v'}, inplace=True)
            elif 'v' not in exports_processed.columns and 'value' in exports_processed.columns:
                 exports_processed.rename(columns={'value':'v'}, inplace=True)

            if 'v' in exports_processed.columns: # Only append if value column is present
                processed_data_list.append(exports_processed)
            else:
                logger.warning("Value column 'v' not found in exports_processed, skipping.")
        except ValueError as e:
            logger.error(f"Error processing exports: {e}. Skipping export processing.")


    if all_imports is not None and not all_imports.empty:
        logger.debug("Processing multi-year import data...")
        try:
            imports_processed = _extract_hs2_code_v2(all_imports, product_code_col='k' if 'k' in all_imports.columns else 'product_code')
            imports_processed = _map_hs2_to_sector_v2(imports_processed)
            imports_processed['trade_type'] = 'Import'
            if 'v' not in imports_processed.columns and 'trade_value_usd' in imports_processed.columns:
                 imports_processed.rename(columns={'trade_value_usd':'v'}, inplace=True)
            elif 'v' not in imports_processed.columns and 'value' in imports_processed.columns:
                 imports_processed.rename(columns={'value':'v'}, inplace=True)

            if 'v' in imports_processed.columns: # Only append if value column is present
                processed_data_list.append(imports_processed)
            else:
                logger.warning("Value column 'v' not found in imports_processed, skipping.")
        except ValueError as e:
            logger.error(f"Error processing imports: {e}. Skipping import processing.")


    if not processed_data_list:
        err_msg = "No data available for processing after attempting to load and process exports and imports."
        logger.error(err_msg)
        raise ValueError(err_msg)

    all_years_data = pd.concat(processed_data_list, ignore_index=True)

    if all_years_data.empty or 'v' not in all_years_data.columns:
        err_msg = "Combined data is empty or missing value column 'v' before aggregation. Cannot calculate composition."
        logger.error(err_msg)
        raise ValueError(err_msg)

    logger.debug("Aggregating data by year, trade_type, and sector_name...")
    aggregated_data = all_years_data.groupby(
        ['year', 'trade_type', 'sector_name'], observed=True
    )['v'].sum().reset_index()

    top_export_sectors = []
    top_import_sectors = []

    export_agg_data = aggregated_data[aggregated_data['trade_type'] == 'Export']
    if not export_agg_data.empty:
        try:
            top_export_sectors = _get_top_sectors_across_years_v2(export_agg_data, value_col='v')
        except ValueError as e:
             logger.warning(f"Could not get top export sectors: {e}")


    import_agg_data = aggregated_data[aggregated_data['trade_type'] == 'Import']
    if not import_agg_data.empty:
        try:
            top_import_sectors = _get_top_sectors_across_years_v2(import_agg_data, value_col='v')
        except ValueError as e:
            logger.warning(f"Could not get top import sectors: {e}")


    logger.debug("Mapping sectors to top N or 'Other' group...")
    def map_to_top_or_other(row):
        sector_name = row['sector_name']
        trade_type = row['trade_type']
        if trade_type == 'Export':
            return sector_name if sector_name in top_export_sectors else 'Other'
        elif trade_type == 'Import':
            return sector_name if sector_name in top_import_sectors else 'Other'
        return 'Other'

    aggregated_data['sector_group'] = aggregated_data.apply(map_to_top_or_other, axis=1)

    logger.debug("Aggregating data by sector_group...")
    composition_over_time = aggregated_data.groupby(
        ['year', 'trade_type', 'sector_group'], observed=True
    )['v'].sum().reset_index()

    logger.debug("Calculating percentages...")
    composition_over_time = _calculate_percentages_v2(composition_over_time, 'v', ['year', 'trade_type'])

    composition_over_time = composition_over_time.rename(columns={
        'v': 'value',
        'sector_group': 'sector_name'
    })

    final_columns = ['year', 'trade_type', 'sector_name', 'value', 'percent_of_total']
    composition_over_time = composition_over_time.reindex(columns=final_columns)

    logger.info(f"Successfully calculated composition over time from {start_year} to {end_year}.")
    return composition_over_time

@protect("calculate_yoy_change_v2", OperationType.COMPUTATION)
def calculate_yoy_change_v2(df: pd.DataFrame,
                          time_col: str = 'year',
                          value_col: str = 'value',
                          group_cols: Optional[List[str]] = None) -> pd.DataFrame:
    """
    Calculate year-over-year change in percentages for a time series.
    """
    logger.info(f"Calculating year-over-year change for {len(df)} rows")

    if df is None or df.empty:
        logger.warning("Input DataFrame is empty or None for YoY calculation.")
        # Return an empty DataFrame with expected columns if input is empty
        expected_cols = [time_col, value_col, f"{value_col}_yoy_change"]
        if group_cols:
            expected_cols = group_cols + expected_cols
        return pd.DataFrame(columns=list(set(expected_cols))) # Use set to avoid duplicates if col names overlap

    if time_col not in df.columns or value_col not in df.columns:
        error_msg = f"Required columns not found in DataFrame for YoY. Needs: {time_col}, {value_col}"
        logger.error(error_msg)
        raise ValueError(error_msg)

    result_df = df.copy()

    sort_cols = [time_col]
    if group_cols:
        if not all(col in df.columns for col in group_cols):
            missing = [col for col in group_cols if col not in df.columns]
            error_msg = f"Group columns not found in DataFrame for YoY: {missing}"
            logger.error(error_msg)
            raise ValueError(error_msg)
        sort_cols = group_cols + [time_col]

    result_df = result_df.sort_values(by=sort_cols)

    if group_cols:
        result_df[f"{value_col}_yoy_change"] = result_df.groupby(group_cols, observed=True)[value_col].pct_change() * 100
    else:
        result_df[f"{value_col}_yoy_change"] = result_df[value_col].pct_change() * 100

    result_df.replace([np.inf, -np.inf], np.nan, inplace=True)

    logger.info("Year-over-year change calculation complete")
    return result_df


if __name__ == '__main__':
    from yemen_trade_diagnostic.interfaces.logging_interface import configure_logging
    # Removed: from yemen_trade_diagnostic.errors import protect, OperationType, MODEL_CONFIG
    configure_logging(log_level=LogLevel.DEBUG, log_to_console=True, log_to_file=False)

    logger.info("Starting composition over time model V2 example.")
    try:
        composition_ot = calculate_composition_over_time_v2(start_year=2020, end_year=2022)
        # Check if DataFrame is not None before checking if it's empty
        if composition_ot is not None:
            if not composition_ot.empty:
                logger.info(f"Calculated composition over time (2020-2022):\n{composition_ot.head()}")
                if 'Export' in composition_ot['trade_type'].unique():
                    exports_summary = composition_ot[composition_ot['trade_type'] == 'Export'].groupby('year')['value'].sum()
                    logger.info(f"Total Export Value by Year (from result):\n{exports_summary}")
            else:
                 logger.warning("Composition over time calculation resulted in an empty DataFrame.")
        # No else needed as calculate_composition_over_time_v2 now raises on critical failure

    except ImportError as ie:
        logger.error(f"Could not run example due to missing V2 components (e.g., data loaders): {ie}")
    except DataLoadError as dle:
        logger.error(f"A DataLoadError occurred during the example: {dle}")
    except ValueError as ve:
        logger.error(f"A ValueError occurred during the example: {ve}")
    except Exception as e:
        logger.error(f"An unexpected error occurred during the example: {e}", exc_info=True)

    logger.info("Composition over time model V2 example finished.")