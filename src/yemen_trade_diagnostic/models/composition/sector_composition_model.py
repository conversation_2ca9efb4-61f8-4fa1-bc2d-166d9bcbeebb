"""
Sector Composition Model (V2)

This module contains functions for calculating sector composition of trade data,
adapted for the V2 architecture using specified interfaces.
"""
# Standard library imports
from typing import Any, Dict, List, Optional, Tuple, Union

# Third-party imports
import numpy as np
import pandas as pd

# Project imports
from yemen_trade_diagnostic.data import DataSource, load_data
from yemen_trade_diagnostic.errors import protect, OperationType # Removed error_context as it's not used
from yemen_trade_diagnostic.interfaces.cache_interface import StorageTier, memoize
from yemen_trade_diagnostic.interfaces.hardware_interface import (
    AccelerationType,
    get_hardware_manager,
)
from yemen_trade_diagnostic.interfaces.logging_interface import LogLevel, get_logger
from yemen_trade_diagnostic.interfaces.validation_interface import (
    ValidationIssueLevel,
    ValidationResult,
    get_validation_manager,
    validate_schema,
)

logger = get_logger(__name__)

class DataLoadError(Exception):
    """Custom exception for data loading failures."""
    pass

# --- Helper Functions ---

def _extract_hs2_code_v2(df: pd.DataFrame, product_code_col: str = 'product_code') -> pd.DataFrame:
    """
    Extract HS2 code from the product code (V2).
    Uses hardware manager for potential DataFrame acceleration.
    """
    if product_code_col not in df.columns:
        err_msg = f"Column '{product_code_col}' (product code) not found in DataFrame for HS2 extraction."
        logger.error(err_msg)
        raise ValueError(err_msg) # Raise ValueError

    df_copy = df.copy() # Work on a copy
    df_copy['hs2'] = df_copy[product_code_col].astype(str).str.slice(0, 2)
    return df_copy

SECTOR_MAPPING = {
    ('01', '05'): 'Animal Products', ('06', '15'): 'Vegetable Products',
    ('16', '24'): 'Foodstuffs', ('25', '27'): 'Mineral Products',
    ('28', '38'): 'Chemical Products', ('39', '40'): 'Plastics and Rubbers',
    ('41', '43'): 'Raw Hides, Skins, Leather', ('44', '49'): 'Wood and Wood Products',
    ('50', '63'): 'Textiles', ('64', '67'): 'Footwear and Headwear',
    ('68', '71'): 'Stone and Glass', ('72', '83'): 'Metals',
    ('84', '85'): 'Machinery and Electrical', ('86', '89'): 'Transportation',
    ('90', '97'): 'Miscellaneous', ('98', '99'): 'Service'
}

def _map_hs2_to_sector_v2(df: pd.DataFrame) -> pd.DataFrame:
    """
    Map HS2 codes to sector names (V2).
    Leverages hardware manager for parallel application if beneficial.
    """
    if 'hs2' not in df.columns:
        err_msg = "Column 'hs2' not found in DataFrame for sector mapping."
        logger.error(err_msg)
        raise ValueError(err_msg)

    df_copy = df.copy() # Work on a copy

    def get_sector(hs2_code_str: str) -> str:
        if not hs2_code_str or not isinstance(hs2_code_str, str) or not hs2_code_str.isdigit() or len(hs2_code_str) != 2:
            return 'Other'
        hs2_int = int(hs2_code_str)
        for (start_str, end_str), sector in SECTOR_MAPPING.items():
            if int(start_str) <= hs2_int <= int(end_str):
                return sector
        return 'Other'

    hardware_manager = get_hardware_manager()

    # Hardware acceleration logic remains, ensure df_copy is used
    if hardware_manager.is_hardware_acceleration_available() and len(df_copy) > 10000:
        logger.debug("Using hardware acceleration for sector mapping.")
        try:
            df_copy['sector_name'] = hardware_manager.apply_function(get_sector, df_copy['hs2'])
        except (AttributeError, NotImplementedError):
            logger.debug("Hardware acceleration not available for this operation, falling back to standard apply.")
            df_copy['sector_name'] = df_copy['hs2'].apply(get_sector)
        except Exception as accel_error:
            logger.warning(f"Hardware acceleration failed for sector mapping: {accel_error}, falling back to standard apply.")
            df_copy['sector_name'] = df_copy['hs2'].apply(get_sector)
    else:
        logger.debug("Using standard apply for sector mapping.")
        df_copy['sector_name'] = df_copy['hs2'].apply(get_sector)

    return df_copy

def _calculate_percentages_v2(df: pd.DataFrame, value_col: str, group_cols: List[str]) -> pd.DataFrame:
    """
    Calculate percentages within groups (V2).
    """
    if value_col not in df.columns or not all(col in df.columns for col in group_cols):
        missing_val = value_col if value_col not in df.columns else ""
        missing_group = [col for col in group_cols if col not in df.columns]
        err_msg = f"Missing columns for percentage calculation. Value col '{missing_val}' or Group cols '{missing_group}' not found."
        logger.error(err_msg)
        # It's safer to raise an error than to return a potentially incorrect df
        raise ValueError(err_msg)

    df_copy = df.copy() # Work on a copy
    # hardware_manager = get_hardware_manager() # hardware_manager not used in current pandas implementation

    group_totals = df_copy.groupby(group_cols, observed=True)[value_col].transform('sum')
    
    # Handle division by zero explicitly
    df_copy['percent_of_total'] = np.where(group_totals != 0, (df_copy[value_col] / group_totals) * 100, 0.0)
    df_copy['percent_of_total'] = df_copy['percent_of_total'].fillna(0.0)

    return df_copy

@protect("calculate_sector_composition_v2", OperationType.COMPUTATION)
@memoize(ttl=3600, level=StorageTier.MEMORY)
def calculate_sector_composition_v2(year: int) -> pd.DataFrame: # Return type changed to non-optional
    """
    Calculate sector composition of Yemen's exports and imports (V2).
    Raises DataLoadError or ValueError on failure.
    """
    logger.info(f"Calculating sector composition for year {year} using V2 model.")
    # hardware_manager = get_hardware_manager() # Not directly used here

    try:
        from yemen_trade_diagnostic.utils.column_mapping import map_dataframe_columns

        exports_data = load_data(source_name="yemen_exports", years=year)
        if exports_data is None or exports_data.empty: # Check for empty DataFrame too
            raise DataLoadError(f"Failed to load or received empty exports data for year {year}")

        # Standard column names expected by helpers
        # If map_dataframe_columns renames to 'k', 'v', 't', then helpers should use those.
        # Assuming product_code='product_code', value='trade_value_usd' before mapping for clarity.
        # If map_dataframe_columns creates e.g. 'k', 'v', 't', then these become the new defaults.
        # For this refactor, will assume `map_dataframe_columns` maps to 'product_code', 'trade_value_usd', 'year'.
        # If it maps to 'k', 'v', 't', then _extract_hs2_code_v2 product_code_col needs to be 'k'
        # and subsequent value access would be 'v'.

        exports_data = map_dataframe_columns(
            exports_data,
            expected_cols=['product_code', 'trade_value_usd', 'year'], # Semantic names
            actual_cols_map=None, # Or provide a map if columns are different and map_dataframe_columns supports it
            inplace=False,
            create_missing=False # Safer to error if critical columns are missing after load
        )
        # Value column to be used after mapping
        value_column_semantic = 'trade_value_usd' # This should be the column name after mapping
        product_column_semantic = 'product_code' # This should be the column name after mapping

        if product_column_semantic not in exports_data.columns:
             raise ValueError(f"'{product_column_semantic}' not found in exports after mapping.")
        if value_column_semantic not in exports_data.columns:
             raise ValueError(f"'{value_column_semantic}' not found in exports after mapping.")


        logger.debug(f"Processing export data for year {year}...")
        exports = _extract_hs2_code_v2(exports_data, product_code_col=product_column_semantic)
        exports = _map_hs2_to_sector_v2(exports)

        if 'sector_name' not in exports.columns:
            logger.error(f"'sector_name' column not found in exports DataFrame. Assigning 'Unknown'.")
            exports['sector_name'] = 'Unknown' # Fallback

        export_sector_composition = exports.groupby('sector_name', observed=True)[value_column_semantic].sum().reset_index()

        total_exports = export_sector_composition[value_column_semantic].sum()
        export_sector_composition['share'] = (export_sector_composition[value_column_semantic] / total_exports) * 100 if total_exports > 0 else 0.0
        export_sector_composition["trade_type"] = "Export"
        export_sector_composition["year"] = year
        # The _calculate_percentages_v2 expects 'value_col' to be 'share', and group_cols ['year', 'trade_type']
        # It calculates 'percent_of_total' based on 'share' within these groups.
        # This seems like a nested percentage, ensure this is the intended logic.
        # If 'share' is already the final percentage, this step might be redundant or misapplied.
        # Assuming 'share' is the value to be used for 'percent_of_total' within year/trade_type group.
        export_sector_composition = _calculate_percentages_v2(export_sector_composition, value_col='share', group_cols=['year', 'trade_type'])


        imports_data = load_data(source_name="yemen_imports", years=year)
        if imports_data is None or imports_data.empty: # Check for empty
            raise DataLoadError(f"Failed to load or received empty imports data for year {year}")

        imports_data = map_dataframe_columns(
            imports_data,
            expected_cols=['product_code', 'trade_value_usd', 'year'],
            inplace=False, create_missing=False
        )
        if product_column_semantic not in imports_data.columns:
             raise ValueError(f"'{product_column_semantic}' not found in imports after mapping.")
        if value_column_semantic not in imports_data.columns:
             raise ValueError(f"'{value_column_semantic}' not found in imports after mapping.")

        logger.debug(f"Processing import data for year {year}...")
        imports = _extract_hs2_code_v2(imports_data, product_code_col=product_column_semantic)
        imports = _map_hs2_to_sector_v2(imports)

        if 'sector_name' not in imports.columns:
            logger.error(f"'sector_name' column not found in imports DataFrame. Assigning 'Unknown'.")
            imports['sector_name'] = 'Unknown'

        import_sector_composition = imports.groupby('sector_name', observed=True)[value_column_semantic].sum().reset_index()

        total_imports = import_sector_composition[value_column_semantic].sum()
        import_sector_composition['share'] = (import_sector_composition[value_column_semantic] / total_imports) * 100 if total_imports > 0 else 0.0
        import_sector_composition["trade_type"] = "Import"
        import_sector_composition["year"] = year
        import_sector_composition = _calculate_percentages_v2(import_sector_composition, value_col='share', group_cols=['year', 'trade_type'])

        logger.debug(f"Combining export and import sector data for year {year}...")
        # Rename 'trade_value_usd' to 'value' consistently for combined DataFrame
        export_sector_composition.rename(columns={value_column_semantic: 'value_original_sum', 'share': 'value'}, inplace=True)
        import_sector_composition.rename(columns={value_column_semantic: 'value_original_sum', 'share': 'value'}, inplace=True)


        sector_composition = pd.concat([export_sector_composition, import_sector_composition], ignore_index=True)
        # 'value' now represents the 'share', and 'percent_of_total' is calculated based on this 'share'.
        # If 'value' should be the original sum (trade_value_usd), the renaming and calculation needs adjustment.
        # For now, assuming 'value' should be the sectoral share as calculated.

        sector_composition = sector_composition.sort_values(
            by=["trade_type", "value"], # 'value' here is the 'share'
            ascending=[True, False]
        ).reset_index(drop=True)

        logger.info(f"Successfully calculated sector composition for year {year}.")
        return sector_composition

    except DataLoadError as e: # Specific catch for DataLoadError
        logger.error(f"Data loading failed in calculate_sector_composition_v2 for year {year}: {e}")
        raise # Re-raise to be handled by @protect or caller
    except ValueError as e: # Specific catch for ValueError (e.g. missing columns)
        logger.error(f"Data validation or processing error in calculate_sector_composition_v2 for year {year}: {e}")
        raise
    except Exception as e: # General catch for other unexpected errors
        logger.error(f"Unexpected error in calculate_sector_composition_v2 for year {year}: {e}", exc_info=True)
        # Convert to a runtime error or re-raise a more specific custom error if appropriate
        raise RuntimeError(f"Failed to calculate sector composition for year {year} due to: {e}") from e


@protect("get_top_sectors_v2", OperationType.COMPUTATION)
def get_top_sectors_v2(sector_composition_df: pd.DataFrame, top_n: int = 10) -> pd.DataFrame: # Return non-optional
    """
    Get the top N sectors by value and group the rest as "Other" (V2).
    """
    if not isinstance(sector_composition_df, pd.DataFrame):
        err_msg = "Input sector_composition_df must be a pandas DataFrame."
        logger.error(err_msg)
        raise TypeError(err_msg)
    if sector_composition_df.empty:
        logger.warning("Input sector_composition_df is empty for get_top_sectors_v2.")
        # Return empty DF with expected columns
        return pd.DataFrame(columns=["trade_type", "value", "sector_name", "year", "percent_of_total"])


    # 'value' column here refers to the 'share' from calculate_sector_composition_v2
    required_cols = ["trade_type", "value", "sector_name", "year", "percent_of_total"]
    if not all(col in sector_composition_df.columns for col in required_cols):
        missing = [col for col in required_cols if col not in sector_composition_df.columns]
        err_msg = f"Missing required columns in sector_composition_df for get_top_sectors: {missing}"
        logger.error(err_msg)
        raise ValueError(err_msg)

    logger.debug(f"Getting top {top_n} sectors.")

    processed_dfs = []
    for trade_type in sector_composition_df["trade_type"].unique():
        type_df = sector_composition_df[sector_composition_df["trade_type"] == trade_type].copy()
        type_df = type_df.sort_values(by="value", ascending=False) # 'value' is 'share'

        if len(type_df) > top_n:
            top_df = type_df.head(top_n)
            other_df_indices = type_df.index.difference(top_df.index) # Correct way to get other rows
            other_df = type_df.loc[other_df_indices]


            if not other_df.empty:
                other_row = pd.DataFrame({
                    "sector_name": ["Other"],
                    "value": [other_df["value"].sum()], # Sum of 'shares'
                    "trade_type": [trade_type],
                    "year": [type_df["year"].iloc[0]],
                    "percent_of_total": [other_df["percent_of_total"].sum()] # Sum of 'percent_of_total'
                })
                processed_dfs.append(pd.concat([top_df, other_row], ignore_index=True))
            else:
                processed_dfs.append(top_df)
        else:
            processed_dfs.append(type_df)

    if not processed_dfs:
        logger.warning("No data to process for top sectors, returning empty DataFrame.")
        return pd.DataFrame(columns=sector_composition_df.columns)

    final_df = pd.concat(processed_dfs, ignore_index=True)
    final_df = final_df.sort_values(by=["trade_type", "value"], ascending=[True, False]).reset_index(drop=True)

    logger.debug(f"Finished getting top {top_n} sectors.")
    return final_df

# Alias for pipeline compatibility
calculate_sectoral_composition_v2 = calculate_sector_composition_v2

@protect("get_sector_evolution_v2", OperationType.COMPUTATION)
def get_sector_evolution_v2(df: pd.DataFrame, group_col: str = 'hs2',
                          time_col: str = 'year', value_col: str = 'trade_value_usd') -> pd.DataFrame:
    """
    Get sector evolution over time.
    """
    logger.info(f"Calculating sector evolution for {len(df)} rows, grouped by {group_col}")

    if df is None or df.empty:
        logger.warning("Input DataFrame is empty for get_sector_evolution_v2.")
        return pd.DataFrame(columns=[group_col, time_col, value_col, 'previous_value', 'percentage_change', 'yearly_total', 'share_of_total'])


    required_cols_evol = [group_col, time_col, value_col] # Renamed
    if not all(col in df.columns for col in required_cols_evol):
        missing = [col for col in required_cols_evol if col not in df.columns]
        error_msg = f"Missing required columns for sector evolution: {missing}"
        logger.error(error_msg)
        raise ValueError(error_msg)

    result = df.groupby([group_col, time_col], observed=True)[value_col].sum().reset_index()

    result = result.sort_values([group_col, time_col])
    result['previous_value'] = result.groupby(group_col, observed=True)[value_col].shift(1)

    # Calculate percentage_change, handle division by zero and NaN previous_value
    result['percentage_change'] = np.where(
        result['previous_value'].notna() & (result['previous_value'] != 0),
        ((result[value_col] - result['previous_value']) / result['previous_value']) * 100,
        np.nan # Or 0 or 100 for new entries, depending on desired behavior
    )
    # For new entries (previous_value is NaN), if 100% change is desired:
    result.loc[result['previous_value'].isna(), 'percentage_change'] = 100.0


    yearly_totals = df.groupby(time_col, observed=True)[value_col].sum().reset_index()
    yearly_totals.rename(columns={value_col: 'yearly_total'}, inplace=True)
    result = pd.merge(result, yearly_totals, on=time_col)

    result['share_of_total'] = np.where(
        result['yearly_total'] != 0,
        (result[value_col] / result['yearly_total']) * 100,
        0.0
    )


    logger.info(f"Sector evolution calculation complete. Result has {len(result)} rows.")
    return result


if __name__ == '__main__':
    from yemen_trade_diagnostic.interfaces.logging_interface import configure_logging
    configure_logging(log_level=LogLevel.DEBUG, log_to_console=True, log_to_file=False)

    logger.info("Starting sector composition model V2 example.")
    try:
        composition_2023 = calculate_sector_composition_v2(year=2023)
        # No None check as func raises or returns DF
        logger.info(f"Calculated composition for 2023:\n{composition_2023.head()}")

        top_sectors_2023 = get_top_sectors_v2(composition_2023, top_n=5)
        logger.info(f"Top 5 sectors for 2023:\n{top_sectors_2023}")

    except ImportError as ie:
        logger.error(f"Could not run example due to missing V2 components (e.g., data loaders): {ie}")
    except DataLoadError as dle:
        logger.error(f"A DataLoadError occurred during the sector composition example: {dle}")
    except ValueError as ve:
        logger.error(f"A ValueError occurred during the sector composition example: {ve}")
    except RuntimeError as rte:
        logger.error(f"A RuntimeError occurred during the sector composition example: {rte}")
    except Exception as e:
        logger.error(f"An unexpected error occurred during the sector composition example: {e}", exc_info=True)

    logger.info("Sector composition model V2 example finished.")