"""
Streamlined validation system for data loading
"""

from dataclasses import dataclass
from typing import Any, Dict, List, Optional, Callable

import pandas as pd

from yemen_trade_diagnostic.data.loader import DataSource, LoadRequest
from yemen_trade_diagnostic.errors import protect, error_context, OperationType
from yemen_trade_diagnostic.interfaces.logging_interface import get_logger
from yemen_trade_diagnostic.monitoring.decorators import monitor_performance


@dataclass
class ValidationRule:
    """Single validation rule"""
    name: str
    check: Callable[[pd.DataFrame, LoadRequest], bool]
    error_message: str
    severity: str = "error"  # error, warning, info


class DataValidator:
    """Streamlined data validator"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.rules = self._initialize_rules()
    
    @monitor_performance()
    @protect("validate_data", OperationType.DATA_LOADING)
    def validate(self, df: pd.DataFrame, source: DataSource, request: LoadRequest) -> None:
        """Validate dataframe against source-specific rules"""
        source_rules = self.rules.get(source, [])
        
        for rule in source_rules:
            if not rule.check(df, request):
                if rule.severity == "error":
                    raise ValueError(f"Validation failed: {rule.error_message}")
                else:
                    self.logger.warning(f"Validation warning: {rule.error_message}")
    
    def _initialize_rules(self) -> Dict[DataSource, List[ValidationRule]]:
        """Initialize validation rules for each source"""
        return {
            DataSource.BACI: [
                ValidationRule(
                    name="required_columns",
                    check=lambda df, req: all(col in df.columns for col in ["i", "j", "k", "v", "q"]),
                    error_message="Missing required BACI columns"
                ),
                ValidationRule(
                    name="positive_values",
                    check=lambda df, req: (df["v"] > 0).all() and (df["q"] >= 0).all(),
                    error_message="Negative trade values or quantities found"
                ),
                ValidationRule(
                    name="valid_country_codes",
                    check=lambda df, req: df["i"].str.len().eq(3).all() and df["j"].str.len().eq(3).all(),
                    error_message="Invalid country codes found"
                ),
                ValidationRule(
                    name="valid_product_codes",
                    check=lambda df, req: df["k"].str.len().eq(6).all(),
                    error_message="Invalid product codes (should be 6 digits)"
                )
            ],
            DataSource.WORLDBANK: [
                ValidationRule(
                    name="required_columns",
                    check=lambda df, req: all(col in df.columns for col in ["Country Code", "Indicator Code"]),
                    error_message="Missing required World Bank columns"
                ),
                ValidationRule(
                    name="valid_years",
                    check=lambda df, req: df["Year"].between(1960, 2030).all() if "Year" in df.columns else True,
                    error_message="Invalid year values found",
                    severity="warning"
                ),
                ValidationRule(
                    name="indicator_filter",
                    check=lambda df, req: len(df) > 0 if req.indicators else True,
                    error_message="No data found for requested indicators"
                )
            ],
            DataSource.YEMEN_EXPORTS: [
                ValidationRule(
                    name="yemen_specific",
                    check=lambda df, req: len(df) > 0,
                    error_message="No Yemen export data found"
                ),
                ValidationRule(
                    name="product_codes",
                    check=lambda df, req: df["product"].notna().all() if "product" in df.columns else True,
                    error_message="Missing product codes"
                ),
                ValidationRule(
                    name="value_quantity",
                    check=lambda df, req: (df["value"] >= 0).all() and (df["quantity"] >= 0).all()
                    if all(col in df.columns for col in ["value", "quantity"]) else True,
                    error_message="Negative values or quantities found"
                )
            ],
            DataSource.YEMEN_IMPORTS: [
                ValidationRule(
                    name="yemen_specific",
                    check=lambda df, req: len(df) > 0,
                    error_message="No Yemen import data found"
                ),
                ValidationRule(
                    name="product_codes",
                    check=lambda df, req: df["product"].notna().all() if "product" in df.columns else True,
                    error_message="Missing product codes"
                ),
                ValidationRule(
                    name="value_quantity",
                    check=lambda df, req: (df["value"] >= 0).all() and (df["quantity"] >= 0).all()
                    if all(col in df.columns for col in ["value", "quantity"]) else True,
                    error_message="Negative values or quantities found"
                )
            ],
            DataSource.GDP: [
                ValidationRule(
                    name="required_columns",
                    check=lambda df, req: all(col in df.columns for col in ["country", "year", "gdp"]),
                    error_message="Missing required GDP columns"
                ),
                ValidationRule(
                    name="valid_gdp",
                    check=lambda df, req: (df["gdp"] >= 0).all(),
                    error_message="Negative GDP values found"
                ),
                ValidationRule(
                    name="country_filter",
                    check=lambda df, req: len(df) > 0 if req.countries else True,
                    error_message="No data found for requested countries"
                )
            ],
            DataSource.COUNTRY_CODES: [
                ValidationRule(
                    name="required_columns",
                    check=lambda df, req: all(col in df.columns for col in ["code", "name"]),
                    error_message="Missing required country code columns"
                ),
                ValidationRule(
                    name="unique_codes",
                    check=lambda df, req: df["code"].is_unique,
                    error_message="Duplicate country codes found"
                )
            ],
            DataSource.PRODUCT_CODES: [
                ValidationRule(
                    name="required_columns",
                    check=lambda df, req: all(col in df.columns for col in ["code", "description"]),
                    error_message="Missing required product code columns"
                ),
                ValidationRule(
                    name="unique_codes",
                    check=lambda df, req: df["code"].is_unique,
                    error_message="Duplicate product codes found"
                )
            ]
        }


# Public interface
_validator = None

def get_validator() -> DataValidator:
    """Get the singleton validator instance"""
    global _validator
    if _validator is None:
        _validator = DataValidator()
    return _validator


@protect("validate_dataframe", OperationType.DATA_LOADING)
def validate_dataframe(df: pd.DataFrame, source: DataSource, request: LoadRequest) -> None:
    """Validate a dataframe"""
    validator = get_validator()
    validator.validate(df, source, request)