"""
Unified Data Loader for Yemen Trade Diagnostic
Hardware-accelerated, monitored, error-handled, cached
"""

from dataclasses import dataclass
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

import pandas as pd

from yemen_trade_diagnostic.errors import protect, error_context, OperationType
from yemen_trade_diagnostic.interfaces.logging_interface import get_logger
from yemen_trade_diagnostic.monitoring.decorators import monitor_performance
from yemen_trade_diagnostic.interfaces.hardware_interface import get_hardware_manager


class DataSource(Enum):
    """Supported data sources"""
    BACI = "baci"
    WORLDBANK = "worldbank"
    YEMEN_EXPORTS = "yemen_exports"
    YEMEN_IMPORTS = "yemen_imports"
    GDP = "gdp"
    COUNTRY_CODES = "country_codes"
    PRODUCT_CODES = "product_codes"


@dataclass
class LoadRequest:
    """Unified load request specification"""
    source: DataSource
    years: Optional[List[int]] = None
    countries: Optional[List[str]] = None
    products: Optional[List[str]] = None
    indicators: Optional[List[str]] = None
    columns: Optional[List[str]] = None
    filters: Optional[Dict[str, Any]] = None
    
    # Performance options
    hardware_accelerate: bool = True
    cache_enabled: bool = True
    validate: bool = True
    compress: bool = True
    
    # Advanced options
    chunk_size: Optional[int] = None
    parallel_chunks: bool = True
    memory_limit_mb: Optional[int] = None


class DataLoader:
    """
    Unified data loader with full system integration.
    Replaces all previous loader implementations.
    """
    
    def __init__(self):
        """Initialize the unified data loader"""
        self.logger = get_logger(__name__)
        self.hw_manager = get_hardware_manager()
        # Use the new unified cache interface
        from yemen_trade_diagnostic.hardware.cache import get_cache
        self.cache = get_cache()
        
        # Source configurations
        self._source_configs = self._initialize_sources()
        
    @monitor_performance(track_args=True, log_threshold=1.0)
    @protect("load_data", OperationType.DATA_LOADING)
    def load(self, request: LoadRequest) -> pd.DataFrame:
        """
        Load data according to the request specification.
        
        This is the single entry point for all data loading operations.
        """
        # Generate cache key
        cache_key = self._generate_cache_key(request)
        
        # Try cache first
        if request.cache_enabled:
            from yemen_trade_diagnostic.hardware.cache import cache_get, StorageTier
            cached = cache_get(cache_key, level=StorageTier.MEDIUM)
            if cached is not None:
                self.logger.info(f"Cache hit for {request.source.value}")
                return cached
        
        # Load with hardware acceleration
        if request.hardware_accelerate and self.hw_manager.can_accelerate("data_loading"):
            data = self._hardware_accelerated_load(request)
        else:
            data = self._standard_load(request)
        
        # Validate if requested
        if request.validate:
            self._validate_data(data, request)
        
        # Cache the result
        if request.cache_enabled:
            from yemen_trade_diagnostic.hardware.cache import cache_set, StorageTier
            cache_set(cache_key, data, level=StorageTier.MEDIUM)
        
        return data
    
    @protect("hardware_accelerated_load", OperationType.DATA_LOADING)
    def _hardware_accelerated_load(self, request: LoadRequest) -> pd.DataFrame:
        """Load data with hardware acceleration"""
        from yemen_trade_diagnostic.data.hardware.io import HardwareIO
        
        hw_io = HardwareIO()
        config = self._source_configs[request.source]
        
        # Determine file paths
        file_paths = self._get_file_paths(request, config)
        
        # Load with hardware acceleration
        if len(file_paths) == 1:
            return hw_io.read_optimized(
                file_paths[0],
                columns=request.columns,
                filters=request.filters,
                chunk_size=request.chunk_size,
                memory_limit_mb=request.memory_limit_mb
            )
        else:
            # Parallel multi-file loading
            return hw_io.read_multiple(
                file_paths,
                columns=request.columns,
                filters=request.filters,
                parallel=request.parallel_chunks,
                memory_limit_mb=request.memory_limit_mb
            )
    
    @protect("standard_data_load", OperationType.DATA_LOADING)
    def _standard_load(self, request: LoadRequest) -> pd.DataFrame:
        """Standard pandas-based loading"""
        config = self._source_configs[request.source]
        file_paths = self._get_file_paths(request, config)
        
        dfs = []
        for path in file_paths:
            if path.suffix == '.csv':
                df = pd.read_csv(path, usecols=request.columns)
            elif path.suffix == '.parquet':
                df = pd.read_parquet(path, columns=request.columns)
            else:
                raise ValueError(f"Unsupported file format: {path.suffix}")
            
            # Apply filters
            if request.filters:
                for col, values in request.filters.items():
                    if col in df.columns:
                        df = df[df[col].isin(values)]
            
            dfs.append(df)
        
        return pd.concat(dfs, ignore_index=True) if dfs else pd.DataFrame()
    
    @protect("validate_loaded_data", OperationType.DATA_LOADING)
    def _validate_data(self, data: pd.DataFrame, request: LoadRequest) -> None:
        """Streamlined validation"""
        from yemen_trade_diagnostic.data.validation import validate_dataframe
        
        validate_dataframe(data, request.source, request)
    
    def _generate_cache_key(self, request: LoadRequest) -> str:
        """Generate semantic cache key"""
        import hashlib
        import json
        
        # Create a stable representation of the request
        key_data = {
            "operation": "data_load",
            "source": request.source.value,
            "years": sorted(request.years) if request.years else None,
            "countries": sorted(request.countries) if request.countries else None,
            "products": sorted(request.products) if request.products else None,
            "indicators": sorted(request.indicators) if request.indicators else None,
            "columns": sorted(request.columns) if request.columns else None,
            "filters": request.filters
        }
        
        # Generate hash
        key_str = json.dumps(key_data, sort_keys=True)
        key_hash = hashlib.md5(key_str.encode()).hexdigest()
        
        return f"data_load_{request.source.value}_{key_hash}"
    
    def _get_file_paths(self, request: LoadRequest, config: Dict[str, Any]) -> List[Path]:
        """Determine file paths based on request and source configuration"""
        base_path = Path(config["base_path"])
        paths = []
        
        if request.years and config.get("year_based"):
            for year in request.years:
                pattern = config["file_pattern"].format(year=year)
                paths.extend(base_path.glob(pattern))
        else:
            pattern = config.get("file_pattern", "*")
            paths.extend(base_path.glob(pattern))
        
        return sorted(paths)
    
    def _initialize_sources(self) -> Dict[DataSource, Dict[str, Any]]:
        """Initialize source configurations"""
        from pathlib import Path
        import os
        
        # Get data directory from environment or use default
        data_dir = Path(os.environ.get("YTD_DATA_DIR", "data"))
        
        return {
            DataSource.BACI: {
                "base_path": data_dir / "baci",
                "file_pattern": "BACI_HS92_Y{year}_V202001.csv",
                "year_based": True,
                "required_columns": ["i", "j", "k", "v", "q"],
                "index_columns": ["i", "j", "k"]
            },
            DataSource.WORLDBANK: {
                "base_path": data_dir / "worldbank",
                "file_pattern": "WDI_*.csv",
                "year_based": False,
                "required_columns": ["Country Code", "Indicator Code", "Year"],
                "index_columns": ["Country Code", "Indicator Code", "Year"]
            },
            DataSource.YEMEN_EXPORTS: {
                "base_path": data_dir / "yemen",
                "file_pattern": "yemen_exports_{year}.csv",
                "year_based": True,
                "required_columns": ["product", "value", "quantity"],
                "index_columns": ["product"]
            },
            DataSource.YEMEN_IMPORTS: {
                "base_path": data_dir / "yemen",
                "file_pattern": "yemen_imports_{year}.csv",
                "year_based": True,
                "required_columns": ["product", "value", "quantity"],
                "index_columns": ["product"]
            },
            DataSource.GDP: {
                "base_path": data_dir / "gdp",
                "file_pattern": "gdp_data.csv",
                "year_based": False,
                "required_columns": ["country", "year", "gdp"],
                "index_columns": ["country", "year"]
            },
            DataSource.COUNTRY_CODES: {
                "base_path": data_dir / "lookup",
                "file_pattern": "country_codes.json",
                "year_based": False,
                "required_columns": ["code", "name"],
                "index_columns": ["code"]
            },
            DataSource.PRODUCT_CODES: {
                "base_path": data_dir / "lookup",
                "file_pattern": "product_codes.json",
                "year_based": False,
                "required_columns": ["code", "description"],
                "index_columns": ["code"]
            }
        }


# Single public interface
_loader = None

def get_loader() -> DataLoader:
    """Get the singleton data loader instance"""
    global _loader
    if _loader is None:
        _loader = DataLoader()
    return _loader


@monitor_performance()
@protect("public_load_data", OperationType.DATA_LOADING)
def load_data(
    source: Union[str, DataSource],
    years: Optional[Union[int, List[int]]] = None,
    countries: Optional[Union[str, List[str]]] = None,
    products: Optional[Union[str, List[str]]] = None,
    **kwargs
) -> pd.DataFrame:
    """
    Public API for loading data.
    
    This is the only function that should be used for data loading.
    """
    # Convert string to enum
    if isinstance(source, str):
        source = DataSource(source.lower())
    
    # Normalize parameters
    if isinstance(years, int):
        years = [years]
    if isinstance(countries, str):
        countries = [countries]
    if isinstance(products, str):
        products = [products]
    
    # Create request
    request = LoadRequest(
        source=source,
        years=years,
        countries=countries,
        products=products,
        **kwargs
    )
    
    # Load data
    loader = get_loader()
    return loader.load(request)