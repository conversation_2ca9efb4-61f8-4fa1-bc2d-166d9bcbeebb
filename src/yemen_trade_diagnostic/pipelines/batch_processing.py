"""
Batch Processing Utilities for Pipelines

Provides utilities for processing multiple years in a single batch
to improve performance and hardware utilization.
"""
# Standard library imports
import time
from typing import Any, Callable, Dict, List, Optional, Union

# Third-party imports
import numpy as np
import pandas as pd

# Project imports
from yemen_trade_diagnostic.interfaces.hardware_interface import get_hardware_manager
from yemen_trade_diagnostic.interfaces.logging_interface import get_logger
from yemen_trade_diagnostic.pipelines.pipeline import Pipeline
from yemen_trade_diagnostic.errors import protect, error_context, OperationType, PIPELINE_CONFIG
from yemen_trade_diagnostic.data import load_data, DataSource

logger = get_logger(__name__)

class YearBatchProcessor:
    """
    Processes multiple years in batches for improved performance.
    
    This class helps pipelines process multiple years at once,
    leveraging vectorized operations when possible and reducing
    data loading overhead.
    """
    
    def __init__(self, max_batch_size: int = 3):
        """
        Initialize the batch processor.
        
        Args:
            max_batch_size: Maximum number of years to process in a single batch
        """
        self.max_batch_size = max_batch_size
        self.logger = get_logger(__name__)
        self.hw_manager = get_hardware_manager()
    
    def create_year_batches(self, years: List[int]) -> List[List[int]]:
        """
        Create batches of years for processing.
        
        Args:
            years: List of years to process
            
        Returns:
            List of year batches (each batch is a list of years)
        """
        # Sort years to ensure chronological processing
        sorted_years = sorted(years)
        
        # Create batches with max_batch_size years each
        batches = []
        for i in range(0, len(sorted_years), self.max_batch_size):
            batch = sorted_years[i:i + self.max_batch_size]
            batches.append(batch)
        
        return batches
    
    def process_batch(self, 
                      pipeline: Pipeline, 
                      years: List[int], 
                      load_data_func: Callable[[List[int]], pd.DataFrame],
                      process_func: Callable[[pd.DataFrame, List[int]], Dict[int, Any]],
                      save: bool = True,
                      **kwargs) -> Dict[int, Any]:
        """
        Process a batch of years.
        
        Args:
            pipeline: The pipeline to use for processing
            years: List of years to process in this batch
            load_data_func: Function to load data for all years
            process_func: Function to process the data for all years
            save: Whether to save results
            **kwargs: Additional arguments for processing
            
        Returns:
            Dictionary mapping each year to its results
        """
        self.logger.info(f"Processing batch of {len(years)} years: {years}")
        start_time = time.time()
        
        # Load data for all years in batch
        batch_data = load_data_func(years)
        
        # Process all years
        results = process_func(batch_data, years)
        
        # Save results if requested
        if save:
            for year, year_results in results.items():
                pipeline.save_results(year_results, year, **kwargs)
        
        elapsed_time = time.time() - start_time
        self.logger.info(f"Batch processing completed in {elapsed_time:.2f} seconds")
        
        return results
    
    def optimize_batch_size(self, available_memory: Optional[int] = None) -> int:
        """
        Optimize batch size based on available system resources.
        
        Args:
            available_memory: Available memory in bytes (auto-detect if None)
            
        Returns:
            Optimized batch size
        """
        if available_memory is None:
            # Get system memory information
            system_info = self.hw_manager.get_available_hardware()
            total_mem_gb = system_info.get("memory", {}).get("total_gb", 8)
            available_memory = int(total_mem_gb * 0.6 * 1024 * 1024 * 1024)  # Use 60% of total memory
        
        # Simple heuristic: 1GB per year as a starting point
        memory_per_year = 1 * 1024 * 1024 * 1024
        
        # Calculate optimal batch size
        optimal_batch_size = max(1, min(self.max_batch_size, available_memory // memory_per_year))
        
        self.logger.info(f"Optimized batch size: {optimal_batch_size} years")
        return optimal_batch_size

def create_unified_data_loader(
    source: Union[str, DataSource],
    countries: Optional[List[str]] = None,
    products: Optional[List[str]] = None,
    **load_kwargs
) -> Callable[[List[int]], pd.DataFrame]:
    """
    Create a data loader function that uses the unified data loader API.
    
    Args:
        source: Data source to load from
        countries: Optional list of countries to filter
        products: Optional list of products to filter
        **load_kwargs: Additional arguments for load_data function
        
    Returns:
        A function that loads data for a list of years
    """
    @protect("batch_data_loader", OperationType.DATA_LOADING)
    def loader(years: List[int]) -> pd.DataFrame:
        """Load data for multiple years using unified data loader."""
        return load_data(
            source=source,
            years=years,
            countries=countries,
            products=products,
            **load_kwargs
        )
    
    return loader


def batch_process_years(pipeline: Pipeline, 
                        years: List[int], 
                        load_func: Optional[Callable] = None, 
                        process_func: Callable = None,
                        max_batch_size: int = 3,
                        data_source: Optional[Union[str, DataSource]] = None,
                        **kwargs) -> Dict[int, Any]:
    """
    Process multiple years in batches.
    
    Args:
        pipeline: The pipeline to use
        years: List of years to process
        load_func: Function to load data for a batch of years (optional if data_source provided)
        process_func: Function to process data for a batch of years
        max_batch_size: Maximum batch size
        data_source: Data source to use with unified loader (optional if load_func provided)
        **kwargs: Additional arguments
        
    Returns:
        Dictionary mapping each year to its results
    """
    # Create loader function if not provided
    if load_func is None:
        if data_source is None:
            raise ValueError("Either load_func or data_source must be provided")
        
        # Extract data loading specific kwargs
        load_kwargs = {k: v for k, v in kwargs.items() 
                      if k in ['countries', 'products', 'indicators', 'hardware_accelerate', 'cache_enabled']}
        
        # Create unified data loader
        load_func = create_unified_data_loader(
            source=data_source,
            **load_kwargs
        )
    
    processor = YearBatchProcessor(max_batch_size=max_batch_size)
    optimal_batch_size = processor.optimize_batch_size()
    processor.max_batch_size = optimal_batch_size
    
    year_batches = processor.create_year_batches(years)
    
    all_results = {}
    for batch in year_batches:
        batch_results = processor.process_batch(
            pipeline=pipeline,
            years=batch,
            load_data_func=load_func,
            process_func=process_func,
            **kwargs
        )
        all_results.update(batch_results)
    
    return all_results