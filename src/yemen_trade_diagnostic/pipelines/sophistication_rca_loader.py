"""
RCA Data Loader for Sophistication Pipeline

This module provides functions to load RCA data from different sources (standard RCA pipeline
or optimized RCA implementation) for use in the sophistication pipeline.
"""

# Standard library imports
import json
from pathlib import Path
from typing import Any, Dict, Optional, Tuple, Union

# Third-party imports
import pandas as pd

# Project imports
from yemen_trade_diagnostic.interfaces.logging_interface import get_logger
from yemen_trade_diagnostic.utils.standardization import standardize_columns
from yemen_trade_diagnostic.errors import protect, error_context, OperationType, PIPELINE_CONFIG
from yemen_trade_diagnostic.data import load_data, DataSource
from yemen_trade_diagnostic.interfaces.hardware_interface import get_hardware_manager

# Configure logger
logger = get_logger(__name__)

@protect("load_rca_output_file", OperationType.DATA_LOADING)
def _load_rca_output_file(file_path: Path) -> Optional[pd.DataFrame]:
    """
    Load RCA output file using unified error handling and monitoring.
    
    Args:
        file_path: Path to the RCA output file
        
    Returns:
        DataFrame if successful, None otherwise
    """
    if not file_path.exists():
        return None
        
    try:
        with error_context(f"loading_file_{file_path.name}", OperationType.DATA_LOADING):
            if file_path.suffix == '.parquet':
                # Use hardware-accelerated loading for parquet files
                hw_manager = get_hardware_manager()
                if hw_manager and hw_manager.can_accelerate("parquet_read"):
                    return hw_manager.accelerate("parquet_read", file_path)
                else:
                    return pd.read_parquet(file_path)
            elif file_path.suffix == '.csv':
                return pd.read_csv(file_path)
            elif file_path.suffix == '.json':
                with open(file_path, 'r') as f:
                    data = json.load(f)
                if isinstance(data, list) and data:
                    return pd.DataFrame(data)
                return None
            else:
                logger.warning(f"Unsupported file format: {file_path.suffix}")
                return None
    except Exception as e:
        logger.warning(f"Error loading file {file_path}: {e}")
        return None


def load_rca_data_for_sophistication(
    year: int,
    dependency_results: Optional[Dict[str, Any]] = None,
    year_col: str = "year",
    product_col: str = "product_code",
    exporter_col: str = "exporter_iso",
    rca_col: str = "rca",
    value_col: str = "trade_value_usd",
    yemen_country_code: int = 887
) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
    """
    Load RCA data from various sources for use in sophistication pipeline.
    
    This function attempts to load RCA data with the following priority:
    1. From dependency results (if provided)
    2. From optimized RCA output files
    3. From standard RCA output files
    
    Args:
        year: Year for which to load RCA data
        dependency_results: Optional results from dependency pipelines
        year_col: Column name for year
        product_col: Column name for product code
        exporter_col: Column name for exporter country
        rca_col: Column name for RCA value
        value_col: Column name for trade value
        yemen_country_code: Country code for Yemen
        
    Returns:
        Tuple of (yemen_rca_df, global_rca_for_proximity, proximity_matrix)
    """
    logger.info(f"Loading RCA data for sophistication pipeline for year {year}")
    
    # Initialize empty DataFrames
    yemen_rca_df = pd.DataFrame()
    global_rca_for_proximity = pd.DataFrame()
    proximity_matrix = pd.DataFrame()
    
    # Try to load from dependency results first
    if dependency_results and 'rca' in dependency_results:
        logger.info("Attempting to load RCA data from dependency results")
        rca_results = dependency_results['rca']
        
        # Check for Yemen RCA data
        if isinstance(rca_results.get("yemen_rca"), pd.DataFrame) and not rca_results["yemen_rca"].empty:
            yemen_rca_df = rca_results["yemen_rca"].copy()
            logger.info(f"Loaded Yemen RCA from dependencies. Shape: {yemen_rca_df.shape}")
            
        # Check for global RCA data
        if isinstance(rca_results.get("global_rca_data"), pd.DataFrame) and not rca_results["global_rca_data"].empty:
            global_rca_for_proximity = rca_results["global_rca_data"].copy()
            logger.info(f"Loaded global RCA from dependencies. Shape: {global_rca_for_proximity.shape}")
            
        # Check for proximity matrix
        if isinstance(rca_results.get("product_proximity_matrix"), pd.DataFrame) and not rca_results["product_proximity_matrix"].empty:
            proximity_matrix = rca_results["product_proximity_matrix"].copy()
            logger.info(f"Loaded proximity matrix from dependencies. Shape: {proximity_matrix.shape}")
    
    # If Yemen RCA data wasn't found in dependencies, try optimized RCA output
    if yemen_rca_df.empty:
        logger.info("Attempting to load Yemen RCA from optimized RCA output")
        
        # Try to load from optimized RCA output
        optimized_paths = [
            Path(f"output/rca/optimized_rca_{year}.parquet"),
            Path(f"output/rca/optimized_rca_{year}.csv")
        ]
        
        for path in optimized_paths:
            df = _load_rca_output_file(path)
            if df is not None:
                yemen_rca_df = df
                logger.info(f"Loaded Yemen RCA from optimized output: {path}. Shape: {yemen_rca_df.shape}")
                
                # Ensure year column exists
                if year_col not in yemen_rca_df.columns:
                    yemen_rca_df[year_col] = year
                
                # Filter for Yemen if needed
                if exporter_col in yemen_rca_df.columns:
                    yemen_rca_df = yemen_rca_df[yemen_rca_df[exporter_col] == yemen_country_code]
                
                # Standardize columns
                standardize_columns(yemen_rca_df, source_type='rca', inplace=True)
                break
    
    # If Yemen RCA data still not found, try standard RCA output
    if yemen_rca_df.empty:
        logger.info("Attempting to load Yemen RCA from standard RCA output")
        
        # Try to load from standard RCA output
        standard_path = Path(f"output/rca/yemen_rca_{year}.json")
        
        df = _load_rca_output_file(standard_path)
        if df is not None:
            yemen_rca_df = df
            logger.info(f"Loaded Yemen RCA from standard output: {standard_path}. Shape: {yemen_rca_df.shape}")
            
            # Standardize columns
            standardize_columns(yemen_rca_df, source_type='rca', inplace=True)
    
    # Log results
    if yemen_rca_df.empty:
        logger.warning("Failed to load Yemen RCA data from any source")
    else:
        logger.info(f"Successfully loaded Yemen RCA data with {len(yemen_rca_df)} records")
        logger.info(f"Yemen RCA data columns: {yemen_rca_df.columns.tolist()}")
    
    # For global RCA data, we would need to generate it from BACI if not found in dependencies
    # This is handled in the sophistication pipeline itself
    
    return yemen_rca_df, global_rca_for_proximity, proximity_matrix