"""Market Analysis Pipeline for V2 Trade Diagnostic Analysis"""
# Standard library imports
import json
import os
import time
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

# Third-party imports
import numpy as np
import pandas as pd

# Project imports
# New unified data loader
from yemen_trade_diagnostic.data import DataSource, load_data
from yemen_trade_diagnostic.interfaces.cache_interface import CacheLevel, memoize
from yemen_trade_diagnostic.interfaces.hardware_interface import get_hardware_manager

# V2 Interface Imports
from yemen_trade_diagnostic.interfaces.logging_interface import (
    LogLevel,
    configure_logging,
    get_logger,
)
from yemen_trade_diagnostic.interfaces.visualization_interface import ChartType, OutputFormat
from yemen_trade_diagnostic.models.market.export_survival_model import (
    get_survival_chart_data_v2,
    process_export_survival_data_v2,
)
from yemen_trade_diagnostic.models.market.import_dependency_model import (
    TradeAnalyzerV2 as ImportDependencyAnalyzerV2,
)

# Import specific V2 market models
from yemen_trade_diagnostic.models.market.trade_flow_model import analyze_trade_flows_v2
from yemen_trade_diagnostic.models.market.trade_flow_model import (
    get_top_partners_v2 as get_top_trade_partners_v2,
    get_top_products_v2 as get_top_trade_products_v2,
)

# Base Pipeline Class
from yemen_trade_diagnostic.pipelines.pipeline import Pipeline, ensure_dir_exists
from yemen_trade_diagnostic.utils.feature_flags import (
    get_feature_flag_manager,
    is_refactored_visualization_enabled,
)
from yemen_trade_diagnostic.utils.memory_optimizer import optimize_dataframe_dtypes
from yemen_trade_diagnostic.utils.standardization import standardize_columns

# V2 Visualization imports
from yemen_trade_diagnostic.visualization.core.component_managers import DefaultChartFactory
from yemen_trade_diagnostic.errors import protect, OperationType

class MarketPipeline(Pipeline):
    """
    Pipeline for market analysis, including trade flows, import dependencies,
    gravity model estimations, and export survival.
    Adheres to V2 standards, using real data loaders and robust data handling.
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__(name="market", config=config)
        self.hw_manager = get_hardware_manager()

        # Semantic column names used throughout the pipeline
        self.year_col = self.config.get('year_col', "year")
        self.product_col = self.config.get('product_col', "product_code")
        self.value_col = self.config.get('value_col', "trade_value_usd")
        self.exporter_col = self.config.get('exporter_col', "exporter_iso")
        self.importer_col = self.config.get('importer_col', "importer_iso")
        self.quantity_col = self.config.get('quantity_col', "quantity")
        self.product_name_col = self.config.get('product_name_col', "product_name") # For display
        self.source_col_in_imports = self.config.get('source_col_in_imports', 'source') # Special for yemen_imports data

        self.country_code_yem_numeric = self.config.get('country_code_yemen', 887) # Numeric ISO for Yemen
        self.output_dir = Path(self.config.get('output_dir', f'output/{self.name}'))

        # Configure gravity model output directory
        self.gravity_model_dir = Path(self.config.get('gravity_model_dir', 'output/gravity_model'))

        # Initialize the MarketDataProvider for gravity model data
        market_data_provider_config = {
            'exporter_col': self.exporter_col,
            'importer_col': self.importer_col,
            'year_col': self.year_col,
            'yemen_iso_numeric': self.country_code_yem_numeric
        }
        # Note: MarketDataProvider import removed to fix undefined class
        # self.market_data_provider = MarketDataProvider(config=market_data_provider_config)

        # Load essentiality scores from configuration file
        self.essentiality_scores = self.config.get('essentiality_scores', {})
        if not self.essentiality_scores:
            essentiality_path = Path('config/essentiality_scores.json')
            if essentiality_path.exists():
                try:
                    with open(essentiality_path, 'r') as f:
                        self.essentiality_scores = json.load(f)
                    self.logger.info(f"Loaded {len(self.essentiality_scores)} essentiality scores from configuration file")
                except Exception as e:
                    self.logger.error(f"Failed to load essentiality scores from file: {e}")
            else:
                self.logger.warning("No essentiality scores configuration file found at config/essentiality_scores.json")

        # Chart factory for V2 visualization (lazy initialization)
        self._chart_factory = None

    @property
    def chart_factory(self):
        """Lazy initialization of chart factory"""
        # Check if refactored visualization is enabled
        if self._chart_factory is None and is_refactored_visualization_enabled():
            self._chart_factory = DefaultChartFactory()
        return self._chart_factory

    @protect("pipeline_execution", OperationType.PIPELINE_EXECUTION)
    def _load_standardized_optimized_data(self, source_name: str, year: Optional[int] = None,
                                        start_year: Optional[int] = None, end_year: Optional[int] = None,
                                        required_cols: Optional[List[str]] = None,
                                        allow_empty: bool = False) -> Union[pd.DataFrame, Dict[int, pd.DataFrame]]:
        """Helper to load, standardize, and optimize data using new unified loader."""
        data = None
        specific_cols_to_load = list(set(required_cols)) if required_cols else None # Ensure unique columns
        
        # Convert source name to DataSource enum
        source_map = {
            'yemen_exports': DataSource.YEMEN_EXPORTS,
            'yemen_imports': DataSource.YEMEN_IMPORTS,
            'baci': DataSource.BACI,
            'gdp_per_capita': DataSource.GDP_PER_CAPITA,
            'worldbank': DataSource.WORLD_BANK,
            'price_index': DataSource.PRICE_INDEX,
            'country_codes': DataSource.COUNTRY_CODES,
            'product_codes': DataSource.PRODUCT_CODES
        }
        
        source = source_map.get(source_name.lower(), source_name)
        if year is not None:
            data = load_data(source, years=year)
            if data is not None and not data.empty:
                standardize_columns(data, source_type=source_name, inplace=True)
                data = optimize_dataframe_dtypes(data)
            elif not allow_empty:
                self.logger.error(f"Data for '{source_name}' (year {year}) is None or empty and not allowed.")
                # Raise an error or return a specifically marked empty DF if pipeline should halt
                return pd.DataFrame()
            else:
                 self.logger.warning(f"Data for '{source_name}' (year {year}) is None or empty (allowed).")
                 data = pd.DataFrame()
        elif start_year is not None and end_year is not None:
            # Load data for multiple years
            processed_data_dict = {}
            for yr in range(start_year, end_year + 1):
                df_yr = load_data(source, years=yr)
                if df_yr is not None and not df_yr.empty:
                    standardize_columns(df_yr, source_type=source_name, inplace=True)
                    df_yr_processed = optimize_dataframe_dtypes(df_yr)
                    processed_data_dict[yr] = df_yr_processed
                else:
                    self.logger.warning(f"Data for '{source_name}' (year {yr} in range) is None or empty.")
                    processed_data_dict[yr] = pd.DataFrame()
            return processed_data_dict
        else:
            self.logger.error("Invalid arguments for _load_standardized_optimized_data: specify year or start_year/end_year.")
            return pd.DataFrame() if year is not None else {}
        return data

    @protect("pipeline_execution", OperationType.PIPELINE_EXECUTION)
    def _generate_visualizations(self, results: Dict[str, Any], year: int, save: bool = True) -> Dict[str, Any]:
        """
        Generate visualizations for the pipeline results using V2 visualization system.
        Falls back gracefully if V2 is not available or disabled.
        """
        visualization_results = {}
        
        # Check if V2 visualization is enabled
        if self.chart_factory is None:
            self.logger.info("V2 visualization is disabled, skipping chart generation")
            return visualization_results
            
        try:
            self.logger.info("Generating visualizations using V2 system")
            
            # Trade Flow Visualization
            if "trade_flow_analysis" in results and results["trade_flow_analysis"]:
                chart = self.chart_factory.create_chart(
                    chart_id=f"trade_flow_{year}",
                    chart_type="bar",
                    data=results["trade_flow_analysis"],
                    config={
                        "title": f"Yemen Trade Flow Analysis - {year}",
                        "x_axis": "trade_type",
                        "y_axis": "value",
                        "color": "#17a2b8",
                        "world_bank_styling": True
                    }
                )
                visualization_results["trade_flow_chart"] = chart
            
            # Import Dependency Visualization
            if "import_dependency_analysis" in results and isinstance(results["import_dependency_analysis"], pd.DataFrame):
                if not results["import_dependency_analysis"].empty:
                    # Top 10 dependencies
                    dep_data = results["import_dependency_analysis"].nlargest(10, 'dependency_score')
                    chart = self.chart_factory.create_chart(
                        chart_id=f"import_dependency_{year}",
                        chart_type="bar",
                        data=dep_data,
                        config={
                            "title": f"Top Import Dependencies - {year}",
                            "x_axis": self.product_name_col if self.product_name_col in dep_data.columns else self.product_col,
                            "y_axis": "dependency_score",
                            "colors": ["#dc3545"],
                            "world_bank_styling": True,
                            "orientation": "horizontal"
                        }
                    )
                    visualization_results["import_dependency_chart"] = chart
            
            # Gravity Model Visualization
            if "gravity_model_estimation" in results and results["gravity_model_estimation"]:
                # Create a simple visualization of the model coefficients
                coef_data = pd.DataFrame.from_dict(
                    results["gravity_model_estimation"]["coefficients"], 
                    orient='index', 
                    columns=['value']
                ).reset_index()
                coef_data.columns = ['coefficient', 'value']
                
                chart = self.chart_factory.create_chart(
                    chart_id=f"gravity_model_coefficients_{year}",
                    chart_type="bar",
                    data=coef_data,
                    config={
                        "title": f"Gravity Model Coefficients - {year}",
                        "x_axis": "coefficient",
                        "y_axis": "value",
                        "color": "#28a745",
                        "world_bank_styling": True
                    }
                )
                visualization_results["gravity_model_chart"] = chart
            
            # Export Survival Visualization
            if "export_survival_chart_data" in results and results["export_survival_chart_data"]:
                chart = self.chart_factory.create_chart(
                    chart_id=f"export_survival_{year}",
                    chart_type="line",
                    data=results["export_survival_chart_data"],
                    config={
                        "title": f"Export Product Survival - {year}",
                        "x_axis": "years_since_conflict",
                        "y_axis": "survival_rate",
                        "color": "#6c757d",
                        "world_bank_styling": True,
                        "y_axis_range": [0, 100]
                    }
                )
                visualization_results["export_survival_chart"] = chart
            
            # Market Concentration Visualization
            if "top_export_partners_yemen_from_baci" in results and isinstance(results["top_export_partners_yemen_from_baci"], pd.DataFrame):
                if not results["top_export_partners_yemen_from_baci"].empty:
                    chart = self.chart_factory.create_chart(
                        chart_id=f"market_concentration_{year}",
                        chart_type="pie",
                        data=results["top_export_partners_yemen_from_baci"].head(5),  # Top 5 partners
                        config={
                            "title": f"Export Market Concentration - {year}",
                            "values": "share" if "share" in results["top_export_partners_yemen_from_baci"].columns else self.value_col,
                            "names": "partner_name" if "partner_name" in results["top_export_partners_yemen_from_baci"].columns else self.importer_col,
                            "color_scale": ["#007bff", "#17a2b8", "#28a745", "#ffc107", "#6c757d"],
                            "world_bank_styling": True
                        }
                    )
                    visualization_results["market_concentration_chart"] = chart
            
            # Save visualizations if requested
            if save and visualization_results:
                output_dir = self.output_dir / "charts"
                ensure_dir_exists(output_dir)
                
                for chart_name, chart in visualization_results.items():
                    try:
                        # Export in multiple formats
                        for fmt in ["png", "html"]:
                            output_path = output_dir / f"{chart_name}.{fmt}"
                            if hasattr(chart, 'save'):
                                chart.save(str(output_path), format=fmt)
                            elif hasattr(chart, 'export'):
                                chart.export(str(output_path), format=fmt)
                        self.logger.info(f"Exported {chart_name} to {output_dir}")
                    except Exception as e:
                        self.logger.warning(f"Failed to export {chart_name}: {e}")
            
        except Exception as e:
            self.logger.error(f"Error generating visualizations: {e}", exc_info=True)
        return visualization_results

if __name__ == '__main__':
    configure_logging(log_level=LogLevel.DEBUG, log_to_console=True, log_to_file=True, log_file_name="market_pipeline_main.log")
    logger = get_logger(__name__)
    logger.info("--- Running Market Pipeline Example (Directly) ---")

    pipeline_config = {
        'country_code_yemen': 887,
        'output_dir': 'output/market',
        'raw_data_root': 'data/raw',
        'processed_data_root': 'data/processed',
        'year_col': 'year',
        'value_col': 'trade_value_usd',
        'product_col': 'product_code',
        'exporter_col': 'exporter_iso',
        'importer_col': 'importer_iso',
        'quantity_col': 'quantity',
        'product_name_col': 'product_name',
        'source_col_in_imports': 'source',
        # Essentiality scores will be loaded from config/essentiality_scores.json
        'sector_mapping': {'01': 'Live Animals', '02': 'Meat Products'}, # Sample sector mapping
        'iso_to_name_map': {'887': 'Yemen', '123': 'OtherCountry'}, # For survival
        'hs2_to_description_map': {'01': 'Live Animals', '02': 'Meat Products'} # For survival
    }
    market_pipeline = MarketPipeline(config=pipeline_config)
    test_year = 2022

    try:
        run_results = market_pipeline.run(year=test_year, save=True)
        logger.info(f"Market Pipeline run completed for year {test_year}.")
        for key, data_result in run_results.items():
            if isinstance(data_result, pd.DataFrame):
                logger.info(f"Result '{key}' shape: {data_result.shape}")
                if not data_result.empty:
                    logger.debug(f"First 2 rows of '{key}':\n{data_result.head(2)}")
            elif isinstance(data_result, (dict, list)):
                 logger.info(f"Result '{key}' (structure): {type(data_result)}, Len: {len(data_result) if hasattr(data_result, '__len__') else 'N/A'}")
            else:
                 logger.info(f"Result '{key}': {data_result}")

    except Exception as e:
        logger.critical(f"Market Pipeline example failed: {e}", exc_info=True)

    logger.info("--- Market Pipeline Example Finished ---")