"""
Concentration Pipeline for V2 Trade Diagnostic Analysis
"""
# Standard library imports
import time
from typing import Any, Dict, Optional

# Third-party imports
import pandas as pd

# Project imports
from yemen_trade_diagnostic.data import DataSource, load_data
from yemen_trade_diagnostic.interfaces.cache_interface import CacheLevel, memoize
from yemen_trade_diagnostic.interfaces.hardware_interface import get_hardware_manager
from yemen_trade_diagnostic.errors import protect, OperationType
from yemen_trade_diagnostic.interfaces.logging_interface import LogLevel, get_logger

# Model imports
from yemen_trade_diagnostic.models.concentration.hhi_model import (
    calculate_hhi_v2_standalone,
)
from yemen_trade_diagnostic.models.concentration.market_concentration_model import (
    calculate_market_concentration_v2 as calculate_market_concentration_standalone_v2,
)

# Base Pipeline Class
from yemen_trade_diagnostic.pipelines.pipeline import Pipeline, ensure_dir_exists, get_output_dir

class ConcentrationPipeline(Pipeline):
    """
    Pipeline for trade concentration analysis including HHI and market concentration.
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__(name="concentration", config=config)

        # Extract shared services from context if available
        self.context = config.get("context", {}) if config else {}
        self.hw_manager = self.context.get("hardware_manager", get_hardware_manager())

        # Get shared data manager if available, otherwise it will be None
        # and the pipeline will use regular load_data
        self.shared_data_manager = self.context.get("shared_data_manager")

    @memoize(ttl=3600*24, level=CacheLevel.DISK_HEAVY)
    @protect("pipeline_execution", OperationType.PIPELINE_EXECUTION)
    def run(self, year: int, save: bool = True, **kwargs) -> Dict[str, Any]:
        """
        Run the concentration pipeline for the specified year.
        
        Args:
            year: The year to analyze
            save: Whether to save results to files (default True)
            **kwargs: Additional parameters (e.g., output_format for saving)
            
        Returns:
            Dict with keys:
            - "product_hhi": DataFrame with HHI results by product.
            - "market_concentration": DataFrame with market concentration results for the country.
        """
        self.logger.info(f"Running Concentration Pipeline for year {year}. Hardware Acceleration: {self.hw_manager.is_hardware_acceleration_available()}")
        start_time = time.time()
        results = {}

        try:
            # Load required data using new unified loader
            self.logger.info("Loading BACI data for HHI calculation.")
            baci_data = load_data(DataSource.BACI, years=year)

            if baci_data is None or baci_data.empty:
                self.logger.error("BACI data could not be loaded or is empty. Aborting HHI calculation.")
                results["product_hhi"] = pd.DataFrame()
            else:
                self.logger.info("Calculating Product HHI.")
                from yemen_trade_diagnostic.models.concentration.hhi_model import (
                    calculate_hhi_by_year_v2_standalone,
                )

                # Use semantic column names after BaciLoader transformation
                product_hhi_results = calculate_hhi_by_year_v2_standalone(
                    baci_data,
                    year_col='year',
                    value_col='trade_value_usd',
                    group_col='product_code',
                    entity_type='product'
                )

                results["product_hhi"] = product_hhi_results

            self.logger.info("Loading Yemen exports data for Market Concentration.")
            yemen_exports = load_data(DataSource.YEMEN_EXPORTS, years=year)

            if yemen_exports is None or yemen_exports.empty:
                self.logger.error("Yemen exports data could not be loaded or is empty. Aborting market concentration.")
                results["market_concentration"] = pd.DataFrame()
            else:
                self.logger.info("Calculating Market Concentration.")
                
                # Clone the dataframe to avoid modifying the original
                mapped_yemen_exports = yemen_exports.copy()

                # First check if the loader already standardized using semantic names
                if 'reporter_code' in mapped_yemen_exports.columns and 'partner_code' in mapped_yemen_exports.columns:
                    # Rename partner_code to destination
                    mapped_yemen_exports.rename(columns={
                        'partner_code': 'destination',
                        'trade_value': 'trade_value_usd'
                    }, inplace=True)
                else:
                    # Fall back to original column names if standardization wasn't applied
                    column_mapping = {
                        't': 'year',
                        'j': 'destination',
                        'v': 'trade_value_usd'
                    }

                    # Apply the mapping for each column
                    for old_col, new_col in column_mapping.items():
                        if old_col in mapped_yemen_exports.columns:
                            mapped_yemen_exports.rename(columns={old_col: new_col}, inplace=True)

                self.logger.info(f"Column mapping applied. Columns after mapping: {mapped_yemen_exports.columns.tolist()}")

                # calculate_market_concentration_standalone_v2 expects (df, year, year_col, market_col, value_col)
                market_concentration_results = calculate_market_concentration_standalone_v2(
                    df=mapped_yemen_exports,
                    year=year,
                    year_col='year',
                    market_col='destination',
                    value_col='trade_value_usd'
                )

                results["market_concentration"] = pd.DataFrame([market_concentration_results])

        except Exception as e:
            # Catch any other exception during the run process
            err_msg = f"Error during Concentration Pipeline run for year {year}: {e}"
            self.logger.error(err_msg, exc_info=True)
            # Ensure results dict has keys even on failure, with empty DataFrames
            if "product_hhi" not in results: 
                results["product_hhi"] = pd.DataFrame()
            if "market_concentration" not in results: 
                results["market_concentration"] = pd.DataFrame()

        # Save results if requested
        if save:
            self.save_results(results, year, **kwargs)
        
        elapsed_time = time.time() - start_time
        self.logger.info(f"Concentration Pipeline completed in {elapsed_time:.2f} seconds for year {year}")
        
        return results

    def save_results(self, results: Dict[str, Any], year: int, **kwargs) -> None:
        """
        Save concentration pipeline results with specific filenames.
        """
        output_format = kwargs.get('output_format', 'json')
        pipeline_output_dir = get_output_dir() / self.name 
        ensure_dir_exists(pipeline_output_dir)
        self.logger.info(f"Saving Concentration Pipeline results for year {year} to {pipeline_output_dir} as {output_format}")

        # Save Product HHI results
        if "product_hhi" in results and isinstance(results["product_hhi"], pd.DataFrame) and not results["product_hhi"].empty:
            file_path = pipeline_output_dir / f"product_hhi_by_year_{year}.{output_format}"
            try:
                if output_format == 'json': 
                    results["product_hhi"].to_json(file_path, orient='records', indent=4)
                elif output_format == 'csv': 
                    results["product_hhi"].to_csv(file_path, index=False)
                elif output_format == 'parquet': 
                    results["product_hhi"].to_parquet(file_path, index=False)
                self.logger.info(f"Saved Product HHI results to {file_path}")
            except Exception as e:
                self.logger.error(f"Failed to save Product HHI results to {file_path}: {e}")
        else:
            self.logger.warning(f"No Product HHI data to save for year {year}.")

        # Save market concentration results
        if "market_concentration" in results and isinstance(results["market_concentration"], pd.DataFrame) and not results["market_concentration"].empty:
            file_path = pipeline_output_dir / f"yemen_market_concentration_{year}.{output_format}"
            try:
                if output_format == 'json': 
                    results["market_concentration"].to_json(file_path, orient='records', indent=4)
                elif output_format == 'csv': 
                    results["market_concentration"].to_csv(file_path, index=False)
                elif output_format == 'parquet': 
                    results["market_concentration"].to_parquet(file_path, index=False)
                self.logger.info(f"Saved Market Concentration results to {file_path}")
            except Exception as e:
                self.logger.error(f"Failed to save Market Concentration results to {file_path}: {e}")
        else:
            self.logger.warning(f"No Market Concentration data to save for year {year}.")

if __name__ == '__main__':
    from yemen_trade_diagnostic.interfaces.logging_interface import configure_logging
    configure_logging(log_level=LogLevel.INFO, log_to_console=True, log_to_file=True)

    logger = get_logger(__name__)
    logger.info("--- Running Concentration Pipeline Example ---")
    
    pipeline_config = {
        # Add any specific configurations for this pipeline instance if needed
    }
    concentration_pipeline = ConcentrationPipeline(config=pipeline_config)
    
    test_year = 2020
    try:
        results = concentration_pipeline.run(year=test_year, save=True, output_format='json')
        logger.info(f"Concentration Pipeline completed for year {test_year}")
        for key, value in results.items():
            if isinstance(value, pd.DataFrame):
                logger.info(f"Result '{key}' shape: {value.shape}")
            else:
                logger.info(f"Result '{key}': {type(value)}")
    except Exception as e:
        logger.error(f"Concentration Pipeline example failed: {e}", exc_info=True)