#!/usr/bin/env python3
"""
Consolidation script for V2 pipeline implementations.

This script consolidates duplicate pipeline implementations, ensuring that the best version
of each pipeline is used throughout the project. It performs the following actions:
1. Backs up existing files
2. Copies the best version of each pipeline to the standard name
3. Updates import references in other files

Run this script after validating that the consolidated pipelines work as expected.
"""

# Standard library imports
import argparse
import logging
import os
import re
import shutil
from datetime import datetime
from pathlib import Path

# Project imports
from yemen_trade_diagnostic.errors import protect, OperationType

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    handlers=[
        logging.FileHandler("pipeline_consolidation.log"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("pipeline_consolidation")

# Directory containing pipeline implementations
PIPELINE_DIR = Path(__file__).parent

def backup_file(file_path: Path) -> Path:
    """Create a backup of a file with timestamp."""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_path = file_path.with_suffix(f".bak_{timestamp}")
    
    shutil.copy2(file_path, backup_path)
    logger.info(f"Created backup: {backup_path}")
    
    return backup_path

def consolidate_pipeline(source_file: str, target_file: str) -> None:
    """Consolidate a pipeline by using the source file as the target file."""
    source_path = PIPELINE_DIR / source_file
    target_path = PIPELINE_DIR / target_file
    
    if not source_path.exists():
        logger.error(f"Source file does not exist: {source_path}")
        return
    
    # Backup the target file if it exists
    if target_path.exists():
        backup_file(target_path)
    
    # Copy the source file to the target
    shutil.copy2(source_path, target_path)
    logger.info(f"Consolidated {source_file} → {target_file}")

def update_imports(consolidated_pipelines: dict) -> None:
    """Update import references in other files."""
    for file_path in PIPELINE_DIR.glob("*.py"):
        if file_path.name == "consolidate_pipelines.py":
            continue
        
        with open(file_path, "r") as f:
            content = f.read()
        
        modified = False
        
        for source, target in consolidated_pipelines.items():
            # Extract the module name without extension
            source_module = os.path.splitext(source)[0]
            target_module = os.path.splitext(target)[0]
            
            # Pattern for direct imports: from .source_module import ...
            pattern = re.compile(rf"from \.{source_module} import")
            if pattern.search(content):
                content = pattern.sub(f"from .{target_module} import", content)
                modified = True
                logger.info(f"Updated import in {file_path}: {source_module} → {target_module}")
        
        if modified:
            # Backup the file before changing it
            backup_file(file_path)
            
            # Write the updated content
            with open(file_path, "w") as f:
                f.write(content)
            
            logger.info(f"Updated imports in {file_path}")

def main():
    parser = argparse.ArgumentParser(description="Consolidate duplicate pipeline implementations.")
    parser.add_argument("--dry-run", action="store_true", help="Show what would be done without making changes.")
    args = parser.parse_args()
    
    # Mapping of source files to target files for consolidation
    consolidated_pipelines = {
        "rca_fixed.py": "rca.py",
        "sophistication_v2_updated.py": "sophistication.py",
        "market_v2_updated.py": "market.py"
    }
    
    logger.info("Starting pipeline consolidation...")
    
    if args.dry_run:
        logger.info("DRY RUN - No files will be modified")
        for source, target in consolidated_pipelines.items():
            logger.info(f"Would consolidate {source} → {target}")
        return
    
    # First, create a backup directory
    backup_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_dir = PIPELINE_DIR / f"backup_{backup_timestamp}"
    backup_dir.mkdir(exist_ok=True)
    logger.info(f"Created backup directory: {backup_dir}")
    
    # Copy all pipeline files to the backup directory
    for file_path in PIPELINE_DIR.glob("*.py"):
        if file_path.name != "consolidate_pipelines.py":
            backup_path = backup_dir / file_path.name
            shutil.copy2(file_path, backup_path)
    
    # Consolidate each pipeline
    for source, target in consolidated_pipelines.items():
        consolidate_pipeline(source, target)
    
    # Update import references
    update_imports(consolidated_pipelines)
    
    logger.info("Pipeline consolidation completed successfully.")

if __name__ == "__main__":
    main()