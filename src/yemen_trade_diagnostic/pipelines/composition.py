"""
Composition Pipeline for V2 Trade Diagnostic Analysis
"""
# Standard library imports
import json
import time
from typing import Any, Dict, Optional

# Third-party imports
import pandas as pd

# Project imports
# New unified data loader
from yemen_trade_diagnostic.data import DataSource, load_data
from yemen_trade_diagnostic.interfaces.cache_interface import CacheLevel, memoize
from yemen_trade_diagnostic.interfaces.hardware_interface import get_hardware_manager
from yemen_trade_diagnostic.errors import protect, error_context, OperationType, PIPELINE_CONFIG

# V2 Interface Imports
from yemen_trade_diagnostic.interfaces.logging_interface import configure_logging, get_logger
from yemen_trade_diagnostic.models.composition.composition_over_time_model import (
    calculate_composition_over_time_v2,
    calculate_yoy_change_v2 as calculate_composition_yoy_change_v2,  # Alias if generic name
)
from yemen_trade_diagnostic.models.composition.import_composition_model import (
    analyze_import_composition_trend_v2,
    calculate_import_sectoral_composition_v2,
    get_top_import_sectors_v2,
)

# Import specific V2 composition models
from yemen_trade_diagnostic.models.composition.sector_composition_model import (
    calculate_sectoral_composition_v2,
    get_sector_evolution_v2,
    get_top_sectors_v2,
)

# Base Pipeline Class
from yemen_trade_diagnostic.pipelines.pipeline import (
    Pipeline,  # Assuming pipeline.py is in the same directory
    ensure_dir_exists,
    get_output_dir,
)

class CompositionPipeline(Pipeline):
    """
    Pipeline for trade composition analysis, including sectoral breakdowns for exports and imports.
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__(name="composition", config=config)
        self.hw_manager = get_hardware_manager()

    @memoize(ttl=3600*24, level=CacheLevel.DISK_HEAVY)
    @protect("pipeline_execution", OperationType.PIPELINE_EXECUTION)
    def run(self, year: int, save: bool = True, **kwargs) -> Dict[str, Any]:
        """
        Run the composition pipeline for the specified year.

        Args:
            year: The year to analyze.
            save: Whether to save results to files (default True).
            **kwargs: Additional parameters (e.g., output_format for saving).

        Returns:
            Dict with keys for various composition analysis DataFrames/results.
        """
        self.logger.info(f"Running Composition Pipeline for year {year}. Hardware Accel: {self.hw_manager.is_hardware_acceleration_available()}")
        start_time = time.time()
        results: Dict[str, Any] = {}

        try:
            # Load required data - Yemen exports and imports are typically needed for composition
            self.logger.info("Loading Yemen exports data.")
            # Using new unified data loader API
            yemen_exports = load_data(DataSource.YEMEN_EXPORTS, years=year)

            self.logger.info("Loading Yemen imports data.")
            yemen_imports = load_data(DataSource.YEMEN_IMPORTS, years=year)

            if yemen_exports is None or yemen_exports.empty:
                self.logger.warning("Yemen exports data not available or empty for composition analysis.")
                results["export_sectoral_composition"] = pd.DataFrame()
                results["top_export_sectors"] = pd.DataFrame()
                results["export_sector_evolution"] = pd.DataFrame()
                results["export_composition_over_time"] = pd.DataFrame()
                results["export_composition_yoy_change"] = pd.DataFrame()
            else:
                self.logger.info("Calculating Export Sectoral Composition.")
                results["export_sectoral_composition"] = calculate_sectoral_composition_v2(year=year)

                self.logger.info("Calculating Top Export Sectors.")
                if not results["export_sectoral_composition"].empty:
                    results["top_export_sectors"] = get_top_sectors_v2(results["export_sectoral_composition"], top_n=10) # Using default value column
                else:
                    results["top_export_sectors"] = pd.DataFrame()

                # For evolution and over_time, models might need data beyond a single `year`.
                # The `load_data` API might need to support year ranges or loading all available data.
                # For this example, let's assume these models can run on single-year data or have internal logic for time series if needed.
                # Or, the pipeline could load multi-year data if specified by `kwargs` or config.
                self.logger.info("Calculating Export Sector Evolution (conceptual on single year data).")
                # Skip sector evolution for now as it requires additional parameters
                results["export_sector_evolution"] = pd.DataFrame()

                self.logger.info("Calculating Export Composition Over Time (conceptual on single year data).")
                # Skip composition over time for now as it requires additional parameters
                results["export_composition_over_time"] = pd.DataFrame()

                if not results["export_composition_over_time"].empty:
                    self.logger.info("Calculating Export Composition YoY Change.")
                    results["export_composition_yoy_change"] = calculate_composition_yoy_change_v2(results["export_composition_over_time"], time_col='year', value_col_pattern='share_sector_') # Adjust pattern
                else:
                    results["export_composition_yoy_change"] = pd.DataFrame()

            if yemen_imports is None or yemen_imports.empty:
                self.logger.warning("Yemen imports data not available or empty for composition analysis.")
                results["import_sectoral_composition"] = pd.DataFrame()
                results["top_import_sectors"] = pd.DataFrame()
                results["import_composition_trend"] = pd.DataFrame()
            else:
                self.logger.info("Calculating Import Sectoral Composition.")
                results["import_sectoral_composition"] = calculate_import_sectoral_composition_v2(yemen_imports, year=year, product_col='product_code', value_col='trade_value_usd', year_col='year')

                self.logger.info("Calculating Top Import Sectors.")
                if not results["import_sectoral_composition"].empty:
                    results["top_import_sectors"] = get_top_import_sectors_v2(results["import_sectoral_composition"], top_n=10, value_col='value')
                else:
                    results["top_import_sectors"] = pd.DataFrame()

                self.logger.info("Analyzing Import Composition Trend (conceptual on single year data).")

                # Apply column mapping before sending to analysis
                # Project imports
                from yemen_trade_diagnostic.utils.column_mapping import map_dataframe_columns

                # Map columns for import composition analysis
                mapped_yemen_imports = map_dataframe_columns(
                    yemen_imports,
                    target_columns=['trade_value_usd', 'product_code', 'year']
                )

                results["import_composition_trend"] = analyze_import_composition_trend_v2(
                    mapped_yemen_imports,
                    product_col='product_code',
                    value_col='trade_value_usd',
                    year_col='year'
                )

        except Exception as e:
            err_msg = f"Error during Composition Pipeline run for year {year}: {e}"
            self.logger.error(err_msg, exc_info=True)
            # Ensure all expected result keys exist with empty DataFrames upon failure
            expected_keys = [
                "export_sectoral_composition", "top_export_sectors", "export_sector_evolution",
                "export_composition_over_time", "export_composition_yoy_change",
                "import_sectoral_composition", "top_import_sectors", "import_composition_trend"
            ]
            for k in expected_keys: results.setdefault(k, pd.DataFrame())

        if save:
            self.save_results(results, year, **kwargs)

        elapsed_time = time.time() - start_time
        self.logger.info(f"Composition Pipeline completed in {elapsed_time:.2f} seconds for year {year}")
        return results

    # Overriding save_results to use specific filenames based on the result keys.
    def save_results(self, results: Dict[str, Any], year: int, **kwargs) -> None:
        output_format = kwargs.get('output_format', 'json')
        pipeline_output_dir = get_output_dir() / self.name
        ensure_dir_exists(pipeline_output_dir)
        self.logger.info(f"Saving Composition Pipeline results for year {year} to {pipeline_output_dir} as {output_format}")

        for result_name, data in results.items():
            if isinstance(data, pd.DataFrame) and not data.empty:
                file_path = pipeline_output_dir / f"{result_name}_{year}.{output_format}"
                try:
                    if output_format == 'json': data.to_json(file_path, orient='records', indent=4)
                    elif output_format == 'csv': data.to_csv(file_path, index=False)
                    elif output_format == 'parquet': data.to_parquet(file_path, index=False)
                    self.logger.info(f"Saved {result_name} to {file_path}")
                except Exception as e:
                    self.logger.error(f"Failed to save {result_name} to {file_path}: {e}")
            elif isinstance(data, dict) and data: # For dict results if any
                file_path = pipeline_output_dir / f"{result_name}_{year}.json" # Always json for dicts
                try:
                    with open(file_path, 'w') as f:
                        json.dump(data, f, indent=4)
                    self.logger.info(f"Saved {result_name} to {file_path}")
                except Exception as e:
                    self.logger.error(f"Failed to save {result_name} to {file_path}: {e}")
            else:
                self.logger.info(f"No data or empty DataFrame for '{result_name}' for year {year}. Skipping save.")

if __name__ == '__main__':
    # Project imports
    from yemen_trade_diagnostic.interfaces.logging_interface import LogLevel
from yemen_trade_diagnostic.errors import protect, error_context, OperationType, PIPELINE_CONFIG
    configure_logging(log_level=LogLevel.INFO, log_to_console=True, log_to_file=True)

    logger = get_logger(__name__)
    logger.info("--- Running Composition Pipeline Example ---")
    # This example requires functional data loaders and sample data.

    composition_pipeline = CompositionPipeline()
    test_year = 2020 # Example year

    try:
        run_results = composition_pipeline.run(year=test_year, save=True, output_format='json')
        logger.info(f"Composition Pipeline run completed for year {test_year}.")
        for key, res_df in run_results.items():
            if isinstance(res_df, pd.DataFrame):
                logger.info(f"Result '{key}' shape: {res_df.shape}")
                if not res_df.empty:
                    logger.info(f"First 2 rows of '{key}':\n{res_df.head(2)}")
            elif isinstance(res_df, dict):
                 logger.info(f"Result '{key}' (dict): {str(res_df)[:100]}...") # Print snippet

    except Exception as e:
        logger.critical(f"Composition Pipeline example failed: {e}", exc_info=True)

    logger.info("--- Composition Pipeline Example Finished ---")