"""
Concentration Pipeline using new streamlined data loader
Example of how to migrate pipelines to the new system
"""

from typing import Any, Dict, Optional

import pandas as pd

from yemen_trade_diagnostic.data import DataSource, load_data
from yemen_trade_diagnostic.interfaces.logging_interface import get_logger
from yemen_trade_diagnostic.models.concentration.hhi_model import calculate_hhi_v2_standalone
from yemen_trade_diagnostic.models.concentration.market_concentration_model import (
    calculate_market_concentration_v2 as calculate_market_concentration_standalone_v2
)
from yemen_trade_diagnostic.monitoring.decorators import monitor_performance
from yemen_trade_diagnostic.pipelines.pipeline import Pipeline
from yemen_trade_diagnostic.errors import protect, OperationType

class ConcentrationPipeline(Pipeline):
    """
    Pipeline for concentration analysis using the new data loader.
    
    This demonstrates how pipelines should use the new streamlined API.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__(name="concentration", config=config)
        self.logger = get_logger(__name__)
    
    @monitor_performance(track_args=True)
    @protect("concentration_pipeline_run", OperationType.PIPELINE_EXECUTION)
    def _run_impl(self, year: int, **kwargs) -> Dict[str, pd.DataFrame]:
        """
        Execute concentration analysis pipeline.
        
        Notice how simple data loading has become:
        - No loader classes
        - No registry lookups
        - No complex configurations
        - Just one function call
        """
        self.logger.info(f"Running concentration pipeline for year {year}")
        
        # Load BACI data with the new API - so simple!
        trade_data = load_data(
            source=DataSource.BACI,
            years=[year],
            columns=["i", "j", "k", "v", "q"],  # Only load needed columns
            hardware_accelerate=True,  # Use hardware acceleration
            cache_enabled=True,  # Enable caching
            validate=True  # Validate data
        )

        # Load country codes if needed
        country_codes = load_data(
            source=DataSource.COUNTRY_CODES,
            cache_enabled=True  # Country codes rarely change, great for caching
        )

        # Calculate HHI
        hhi_results = calculate_hhi_v2_standalone(
            data=trade_data,
            year=year,
            country_codes=country_codes
        )

        # Calculate market concentration
        market_concentration = calculate_market_concentration_standalone_v2(
            data=trade_data,
            year=year,
            country_codes=country_codes
        )

        # Return results
        return {
            "hhi": hhi_results,
            "market_concentration": market_concentration
        }
    
    @protect("concentration_multi_year", OperationType.PIPELINE_EXECUTION)
    def run_multi_year(self, start_year: int, end_year: int) -> Dict[str, pd.DataFrame]:
        """
        Run concentration analysis for multiple years.
        
        Demonstrates loading multiple years at once.
        """
        years = list(range(start_year, end_year + 1))
        
        # Load all years at once - the new loader handles this efficiently
        trade_data = load_data(
            source=DataSource.BACI,
            years=years,
            columns=["i", "j", "k", "v", "q"],
            hardware_accelerate=True,
            parallel_chunks=True  # Enable parallel loading for multiple files
        )

        # Process by year
        results = {}
        for year in years:
            year_data = trade_data[trade_data['t'] == year]
            
            hhi = calculate_hhi_v2_standalone(
                data=year_data,
                year=year
            )

            market_conc = calculate_market_concentration_standalone_v2(
                data=year_data,
                year=year
            )

            results[year] = {
                "hhi": hhi,
                "market_concentration": market_conc
            }
        
        return results

# Example usage showing the simplicity
if __name__ == "__main__":
    # Initialize pipeline
    pipeline = ConcentrationPipeline()
    
    # Run for single year
    results = pipeline.run(year=2019)
    
    # Run for multiple years
    multi_year_results = pipeline.run_multi_year(2015, 2020)
    
    # Load data for custom analysis - one line!
    yemen_exports = load_data(DataSource.YEMEN_EXPORTS, years=[2019, 2020])
    
    # Load with filters - still one line!
    gcc_trade = load_data(
        DataSource.BACI,
        years=[2019],
        countries=["SAU", "UAE", "KWT", "QAT", "BHR", "OMN"],
        hardware_accelerate=True
    )
