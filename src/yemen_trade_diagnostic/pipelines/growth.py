"""
Growth Pipeline for V2 Trade Diagnostic Analysis
"""
# Standard library imports
import json
import time
from typing import Any, Dict, Optional

# Third-party imports
import pandas as pd

# Project imports
from yemen_trade_diagnostic.data import DataSource, load_data
from yemen_trade_diagnostic.interfaces.cache_interface import CacheLevel, memoize
from yemen_trade_diagnostic.interfaces.hardware_interface import get_hardware_manager
from yemen_trade_diagnostic.interfaces.logging_interface import (
    LogLevel,
    configure_logging,
    get_logger,
)
from yemen_trade_diagnostic.models.growth.export_growth_rates_model import (
    analyze_growth_performance_v2,
    calculate_export_growth_rates_v2,
)
from yemen_trade_diagnostic.models.growth.export_value_trends_model import (
    analyze_export_value_trends_v2,
    calculate_moving_average_v2 as calculate_export_value_moving_average_v2,
)
from yemen_trade_diagnostic.models.growth.export_volume_index_model import (
    analyze_volume_index_trend_v2,
    calculate_export_volume_index_v2,
)

# Base Pipeline Class
from yemen_trade_diagnostic.pipelines.pipeline import (
    Pipeline,
    ensure_dir_exists,
    get_output_dir,
)
from yemen_trade_diagnostic.errors import protect, OperationType

class GrowthPipeline(Pipeline):
    """
    Pipeline for trade growth analysis, including export growth rates, value trends, and volume indices.
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__(name="growth", config=config)
        self.hw_manager = get_hardware_manager()

    @memoize(ttl=3600*24, level=CacheLevel.DISK_HEAVY)
    @protect("pipeline_execution", OperationType.PIPELINE_EXECUTION)
    def run(self, year: int, save: bool = True, **kwargs) -> Dict[str, Any]:
        """
        Run the growth pipeline.
        Note: Many growth models require time-series data. This pipeline might need to load data for a range of years.
        The 'year' parameter might signify the end year of the analysis period.

        Args:
            year: The primary year for analysis (e.g., end year of a period).
            save: Whether to save results to files (default True).
            **kwargs: Additional parameters (e.g., start_year, output_format).

        Returns:
            Dict with keys for various growth analysis DataFrames/results.
        """
        self.logger.info(f"Running Growth Pipeline, focusing on year {year}. Hardware Accel: {self.hw_manager.is_hardware_acceleration_available()}")
        start_time = time.time()
        results: Dict[str, Any] = {}

        # Determine year range for time-series models
        start_year = kwargs.get('start_year', year - 5)  # Default to 5 years prior if not specified
        end_year = year  # The provided 'year' is the end_year
        self.logger.info(f"Analysis period for growth models: {start_year}-{end_year}")

        try:
            # Load required data - typically Yemen exports over a range of years
            self.logger.info(f"Loading Yemen exports data from {start_year} to {end_year}.")
            # Load data for multiple years using the new API
            yemen_exports_list = []
            for year_i in range(start_year, end_year + 1):
                df = load_data(DataSource.YEMEN_EXPORTS, years=year_i)
                if df is not None and not df.empty:
                    yemen_exports_list.append(df)
            
            # Combine all years into a single DataFrame
            if yemen_exports_list:
                yemen_exports_timeseries = pd.concat(yemen_exports_list, ignore_index=True)
            else:
                yemen_exports_timeseries = pd.DataFrame()

            if yemen_exports_timeseries.empty:
                self.logger.warning("Yemen exports time-series data not available or empty.")
                # Initialize all expected results to empty collections that are JSON serializable
                results["export_growth_rates"] = []
                results["growth_performance_analysis"] = {}
                results["growth_drivers"] = []
                results["export_value_trends"] = {"trends": [], "patterns": {}, "forecast": []}
                results["export_value_moving_average"] = []
                results["export_volume_index"] = []
                results["volume_index_trend"] = {}
            else:
                # First map columns to ensure proper column names (t, v, k)
                from yemen_trade_diagnostic.models.growth.growth_model_utils import (
                    map_dataframe_columns,
                )

                mapped_exports = map_dataframe_columns(yemen_exports_timeseries)

                self.logger.info("Calculating Export Growth Rates.")
                # calculate_export_growth_rates_v2 expects: exports, year_col, value_col
                growth_rates_df = calculate_export_growth_rates_v2(
                    mapped_exports,
                    year_col='t',
                    value_col='v'
                )

                # Convert to dictionary for serialization
                if isinstance(growth_rates_df, pd.DataFrame) and not growth_rates_df.empty:
                    results["export_growth_rates"] = growth_rates_df.to_dict(orient='records')
                else:
                    results["export_growth_rates"] = []

                self.logger.info("Analyzing Growth Performance.")
                # This model might also require comparison data or benchmarks, passed via config or kwargs.
                if isinstance(growth_rates_df, pd.DataFrame) and not growth_rates_df.empty:
                    performance_result = analyze_growth_performance_v2(
                        growth_rates_df,
                        start_year=start_year,
                        end_year=end_year,
                        year_col='t',
                        value_col='growth_rate'
                    )

                    # Ensure performance_result is JSON serializable
                    results["growth_performance_analysis"] = performance_result if isinstance(performance_result, dict) else {}
                else:
                    results["growth_performance_analysis"] = {}

                self.logger.info("Analyzing Export Value Trends.")
                value_trends_result = analyze_export_value_trends_v2(
                    mapped_exports,
                    year_col='t',
                    value_col='v'
                )
                results["export_value_trends"] = value_trends_result if isinstance(value_trends_result, dict) else {"trends": [], "patterns": {}, "forecast": []}

                self.logger.info("Calculating Export Value Moving Average.")
                moving_avg_df = calculate_export_value_moving_average_v2(
                    mapped_exports,
                    year_col='t',
                    value_col='v',
                    window=3
                )
                if isinstance(moving_avg_df, pd.DataFrame) and not moving_avg_df.empty:
                    results["export_value_moving_average"] = moving_avg_df.to_dict(orient='records')
                else:
                    results["export_value_moving_average"] = []

                self.logger.info("Calculating Export Volume Index.")
                volume_index_df = calculate_export_volume_index_v2(
                    yemen_exports_timeseries,
                    year_col='t',
                    quantity_col='q',
                    base_year=start_year
                )
                if isinstance(volume_index_df, pd.DataFrame) and not volume_index_df.empty:
                    results["export_volume_index"] = volume_index_df.to_dict(orient='records')
                else:
                    results["export_volume_index"] = []

                self.logger.info("Analyzing Volume Index Trend.")
                if isinstance(volume_index_df, pd.DataFrame) and not volume_index_df.empty:
                    trend_result = analyze_volume_index_trend_v2(volume_index_df, index_col='volume_index')
                    results["volume_index_trend"] = trend_result if isinstance(trend_result, dict) else {}
                else:
                    results["volume_index_trend"] = {}

        except Exception as e:
            err_msg = f"Error during Growth Pipeline run for period ending {year}: {e}"
            self.logger.error(err_msg, exc_info=True)
            defaults = {
                "export_growth_rates": [],
                "growth_performance_analysis": {},
                "growth_drivers": [],
                "export_value_trends": {"trends": [], "patterns": {}, "forecast": []},
                "export_value_moving_average": [],
                "export_volume_index": [],
                "volume_index_trend": {}
            }
            for k, default_value in defaults.items():
                if k not in results or results[k] is None:
                    results[k] = default_value
                elif isinstance(results[k], pd.DataFrame):
                    if results[k].empty:
                        results[k] = []
                    else:
                        try:
                            results[k] = results[k].to_dict(orient='records')
                        except Exception as df_err:
                            self.logger.error(f"Error converting DataFrame to dict for {k}: {df_err}")
                            results[k] = []

        if save:
            self.save_results(results, end_year, **kwargs)
        elapsed_time = time.time() - start_time
        self.logger.info(f"Growth Pipeline completed in {elapsed_time:.2f} seconds for period ending {end_year}")
        return results

    def save_results(self, results: Dict[str, Any], year: int, **kwargs) -> None:
        output_format = kwargs.get('output_format', 'json')
        pipeline_output_dir = get_output_dir() / self.name
        ensure_dir_exists(pipeline_output_dir)
        self.logger.info(f"Saving Growth Pipeline results for period ending {year} to {pipeline_output_dir} as {output_format}")

        for result_name, data in results.items():
            file_path = pipeline_output_dir / f"{result_name}_ending_{year}.{output_format}"
            if isinstance(data, pd.DataFrame) and not data.empty:
                try:
                    if output_format == 'json':
                        data.to_json(file_path, orient='records', indent=4)
                    elif output_format == 'csv':
                        data.to_csv(file_path, index=False)
                    elif output_format == 'parquet':
                        data.to_parquet(file_path, index=False)
                    self.logger.info(f"Saved {result_name} to {file_path}")
                except Exception as e:
                    self.logger.error(f"Failed to save {result_name} to {file_path}: {e}")
            elif isinstance(data, dict) and data:
                file_path_dict = pipeline_output_dir / f"{result_name}_ending_{year}.json"
                try:
                    with open(file_path_dict, 'w') as f:
                        json.dump(data, f, indent=4)
                    self.logger.info(f"Saved {result_name} to {file_path_dict}")
                except Exception as e:
                    self.logger.error(f"Failed to save {result_name} to {file_path_dict}: {e}")
            else:
                self.logger.info(f"No data or empty result for '{result_name}' for period ending {year}. Skipping save.")

if __name__ == '__main__':
    configure_logging(log_level=LogLevel.INFO, log_to_console=True, log_to_file=True)
    logger = get_logger(__name__)
    logger.info("--- Running Growth Pipeline Example ---")

    growth_pipeline = GrowthPipeline()
    test_end_year = 2021
    test_start_year = 2018
    try:
        run_results = growth_pipeline.run(year=test_end_year, save=True, output_format='json', start_year=test_start_year)
        logger.info(f"Growth Pipeline run completed for period {test_start_year}-{test_end_year}.")
        for key, res_data in run_results.items():
            if isinstance(res_data, pd.DataFrame):
                logger.info(f"Result '{key}' shape: {res_data.shape}")
                if not res_data.empty:
                    logger.info(f"First 2 rows of '{key}':\n{res_data.head(2)}")
            elif isinstance(res_data, dict):
                logger.info(f"Result '{key}' (dict): {str(res_data)[:200]}...")

    except Exception as e:
        logger.critical(f"Growth Pipeline example failed: {e}", exc_info=True)

    logger.info("--- Growth Pipeline Example Finished ---")