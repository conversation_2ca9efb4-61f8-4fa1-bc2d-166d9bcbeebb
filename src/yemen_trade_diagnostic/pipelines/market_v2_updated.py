"""
Market Analysis Pipeline for V2 Trade Diagnostic Analysis
"""
# Standard library imports
import json
import time
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

# Third-party imports
import numpy as np
import pandas as pd

# Project imports
# V2 Data and Model Imports
from yemen_trade_diagnostic.data import DataSource, load_data
from yemen_trade_diagnostic.interfaces.cache_interface import CacheLevel, memoize
from yemen_trade_diagnostic.interfaces.hardware_interface import get_hardware_manager

# V2 Interface Imports
from yemen_trade_diagnostic.interfaces.logging_interface import (
    LogLevel,
    configure_logging,
    get_logger,
)
from yemen_trade_diagnostic.interfaces.visualization_interface import process_pipeline_visualization
from yemen_trade_diagnostic.models.market.export_survival_model import (
    get_survival_chart_data_v2,
    process_export_survival_data_v2,
)
from yemen_trade_diagnostic.models.market.import_dependency_model import (
    TradeAnalyzerV2 as ImportDependencyAnalyzerV2,
)

# Import specific V2 market models
from yemen_trade_diagnostic.models.market.trade_flow_model import analyze_trade_flows_v2
from yemen_trade_diagnostic.models.market.trade_flow_model import (
    get_top_partners_v2 as get_top_trade_partners_v2,
    get_top_products_v2 as get_top_trade_products_v2,
)

# Base Pipeline Class
from yemen_trade_diagnostic.pipelines.pipeline import Pipeline, ensure_dir_exists
from yemen_trade_diagnostic.utils.feature_flags import get_feature_flag_manager, is_feature_enabled
from yemen_trade_diagnostic.utils.memory_optimizer import optimize_dataframe_dtypes
from yemen_trade_diagnostic.utils.standardization import standardize_columns

# V2 Visualization imports
from yemen_trade_diagnostic.visualization.core.component_managers import DefaultChartFactory
from yemen_trade_diagnostic.errors import protect, OperationType

class MarketPipeline(Pipeline):
    """
    Pipeline for market analysis, including trade flows, import dependencies,
    gravity model estimations, and export survival.
    Adheres to V2 standards, using real data loaders and robust data handling.
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__(name="market", config=config)
        self.hw_manager = get_hardware_manager()

        # Semantic column names used throughout the pipeline
        self.year_col = self.config.get('year_col', "year")
        self.product_col = self.config.get('product_col', "product_code")
        self.value_col = self.config.get('value_col', "trade_value_usd")
        self.exporter_col = self.config.get('exporter_col', "exporter_iso")
        self.importer_col = self.config.get('importer_col', "importer_iso")
        self.quantity_col = self.config.get('quantity_col', "quantity")
        self.product_name_col = self.config.get('product_name_col', "product_name")
        self.source_col_in_imports = self.config.get('source_col_in_imports', 'source')

        self.country_code_yem_numeric = self.config.get('country_code_yemen', 887)
        self.output_dir = Path(self.config.get('output_dir', f'output/{self.name}'))

        # Configure gravity model output directory
        self.gravity_model_dir = Path(self.config.get('gravity_model_dir', 'output/gravity_model'))

        # Load essentiality scores from configuration file
        self.essentiality_scores = self.config.get('essentiality_scores', {})
        if not self.essentiality_scores:
            essentiality_path = Path('config/essentiality_scores.json')
            if essentiality_path.exists():
                try:
                    with open(essentiality_path, 'r') as f:
                        self.essentiality_scores = json.load(f)
                    self.logger.info(f"Loaded {len(self.essentiality_scores)} essentiality scores from configuration file")
                except Exception as e:
                    self.logger.error(f"Failed to load essentiality scores from file: {e}")
            else:
                self.logger.warning("No essentiality scores configuration file found at config/essentiality_scores.json")

        # Chart factory for V2 visualization (lazy initialization)
        self._chart_factory = None

    @property
    def chart_factory(self):
        """Lazy initialization of chart factory"""
        if self._chart_factory is None and is_feature_enabled("use_refactored_visualization"):
            self._chart_factory = DefaultChartFactory()
        return self._chart_factory

    @protect("pipeline_execution", OperationType.PIPELINE_EXECUTION)
    def _load_standardized_optimized_data(self, source_name: str, year: Optional[int] = None,
                                        start_year: Optional[int] = None, end_year: Optional[int] = None,
                                        required_cols: Optional[List[str]] = None,
                                        allow_empty: bool = False) -> Union[pd.DataFrame, Dict[int, pd.DataFrame]]:
        """Helper to load, standardize, and optimize data using unified_loader_api."""
        data = None
        specific_cols_to_load = list(set(required_cols)) if required_cols else None

        if year is not None:
            # Convert string source to DataSource enum
            source_enum = DataSource(source_name.lower()) if isinstance(source_name, str) else source_name
            data = load_data(source_enum, years=year, columns=specific_cols_to_load)
            if data is not None and not data.empty:
                standardize_columns(data, source_type=source_name, inplace=True)
                data = optimize_dataframe_dtypes(data)
            elif not allow_empty:
                self.logger.error(f"Data for '{source_name}' (year {year}) is None or empty and not allowed.")
                return pd.DataFrame()
            else:
                self.logger.warning(f"Data for '{source_name}' (year {year}) is None or empty (allowed).")
                data = pd.DataFrame()
        elif start_year is not None and end_year is not None:
            # Convert string source to DataSource enum
            source_enum = DataSource(source_name.lower()) if isinstance(source_name, str) else source_name
            # Load data for year range
            years_list = list(range(start_year, end_year + 1))
            data = load_data(source_enum, years=years_list, columns=specific_cols_to_load)
            
            processed_data_dict = {}
            if data is not None and not data.empty:
                # Group by year if year column exists
                if 'year' in data.columns:
                    for yr in years_list:
                        df_yr = data[data['year'] == yr].copy()
                        if not df_yr.empty:
                            standardize_columns(df_yr, source_type=source_name, inplace=True)
                            df_yr_processed = optimize_dataframe_dtypes(df_yr)
                            processed_data_dict[yr] = df_yr_processed
                        else:
                            self.logger.warning(f"Data for '{source_name}' (year {yr} in range) is empty.")
                            processed_data_dict[yr] = pd.DataFrame()
                else:
                    # If no year column, return the whole dataset for each year (edge case)
                    self.logger.warning(f"No 'year' column found in {source_name} data, returning same data for all years")
                    for yr in years_list:
                        standardize_columns(data, source_type=source_name, inplace=True)
                        processed_data_dict[yr] = optimize_dataframe_dtypes(data.copy())
            else:
                self.logger.warning(f"No data loaded for '{source_name}' in range {start_year}-{end_year}")
                for yr in years_list:
                    processed_data_dict[yr] = pd.DataFrame()
            return processed_data_dict
        else:
            self.logger.error("Invalid arguments for _load_standardized_optimized_data: specify year or start_year/end_year.")
            return pd.DataFrame() if year is not None else {}
        return data

    @protect("pipeline_execution", OperationType.PIPELINE_EXECUTION)
    def _generate_visualizations(self, results: Dict[str, Any], year: int, save: bool = True) -> Dict[str, Any]:
        """
        Generate visualizations for the pipeline results using V2 visualization system.
        Falls back gracefully if V2 is not available or disabled.
        """
        visualization_results = {}
        
        # Check if V2 visualization is enabled
        if self.chart_factory is None:
            self.logger.info("V2 visualization is disabled, skipping chart generation")
            return visualization_results
            
        try:
            self.logger.info("Generating visualizations using V2 system")
            
            # Trade Flow Visualization
            if "trade_flow_analysis" in results and results["trade_flow_analysis"]:
                chart = self.chart_factory.create_chart(
                    chart_id=f"trade_flow_{year}",
                    chart_type="bar",
                    data=results["trade_flow_analysis"],
                    config={
                        "title": f"Yemen Trade Flow Analysis - {year}",
                        "x_axis": "trade_type",
                        "y_axis": "value",
                        "color": "#17a2b8",
                        "world_bank_styling": True
                    }
                )
                visualization_results["trade_flow_chart"] = chart
            
            # Import Dependency Visualization
            if "import_dependency_analysis" in results and isinstance(results["import_dependency_analysis"], pd.DataFrame):
                if not results["import_dependency_analysis"].empty:
                    # Top 10 dependencies
                    dep_data = results["import_dependency_analysis"].nlargest(10, 'dependency_score')
                    chart = self.chart_factory.create_chart(
                        chart_id=f"import_dependency_{year}",
                        chart_type="bar",
                        data=dep_data,
                        config={
                            "title": f"Top Import Dependencies - {year}",
                            "x_axis": self.product_name_col if self.product_name_col in dep_data.columns else self.product_col,
                            "y_axis": "dependency_score",
                            "colors": ["#dc3545"],
                            "world_bank_styling": True,
                            "orientation": "horizontal"
                        }
                    )
                    visualization_results["import_dependency_chart"] = chart
            
            # ...existing code for other visualizations...
            
        except Exception as e:
            self.logger.error(f"Error generating visualizations: {e}", exc_info=True)
        
        return visualization_results

    @memoize(ttl=3600*24, level=CacheLevel.DISK_HEAVY)
    @protect("pipeline_execution", OperationType.PIPELINE_EXECUTION)
    def run(self, year: int, save: bool = True, **kwargs) -> Dict[str, Any]:
        self.logger.info(f"Running Market Pipeline for year {year}. Hardware Accel: {self.hw_manager.is_hardware_acceleration_available()}")
        start_time = time.time()
        results: Dict[str, Any] = {}

        # Define columns needed for various parts of the market analysis
        baci_cols = [self.year_col, self.exporter_col, self.importer_col, self.product_col, self.value_col, self.quantity_col]
        yemen_imports_cols = [self.year_col, self.product_col, self.source_col_in_imports, self.value_col, self.quantity_col, self.product_name_col]

        # --- Load Data --- (Strictly use loaders)
        baci_data_tf = self._load_standardized_optimized_data("baci", year=year, required_cols=baci_cols)
        yemen_imports_data = self._load_standardized_optimized_data("yemen_imports", year=year, required_cols=yemen_imports_cols)

        # --- Trade Flow Analysis (using BACI data) ---
        if baci_data_tf.empty:
            self.logger.warning("BACI data for trade flows empty. Skipping related analyses.")
            results["trade_flow_analysis"] = {}
            results["top_export_partners_yemen_from_baci"] = pd.DataFrame()
            results["top_exported_products_yemen_from_baci"] = pd.DataFrame()
        else:
            try:
                # Ensure exporter_col exists
                if self.exporter_col not in baci_data_tf.columns:
                    # Look for possible alternatives
                    alt_exporter_cols = ['reporter_code', 'exporter_iso', 'i', 'reporter', 'exporter', 'country']
                    for col in alt_exporter_cols:
                        if col in baci_data_tf.columns:
                            self.logger.info(f"Using '{col}' instead of missing exporter column '{self.exporter_col}'")
                            # Create the exporter_col from the alternative
                            baci_data_tf[self.exporter_col] = baci_data_tf[col]
                            break
                    else:
                        self.logger.error(f"Column '{self.exporter_col}' missing and no suitable alternative found.")
                        raise KeyError(f"Missing required exporter column: {self.exporter_col}")

                # Analyze overall trade flows for Yemen (as exporter)
                results["trade_flow_analysis"] = analyze_trade_flows_v2(
                    baci_data_tf,
                    country_code=self.country_code_yem_numeric,
                    trade_type='export',
                    time_period=year,
                    product_col=self.product_col,
                    value_col=self.value_col,
                    exporter_col=self.exporter_col,
                    importer_col=self.importer_col,
                    year_col=self.year_col
                )

                # Get top export partners and products for Yemen from BACI data
                yemen_exports_from_baci = baci_data_tf[baci_data_tf[self.exporter_col] == self.country_code_yem_numeric]
                if not yemen_exports_from_baci.empty:
                    results["top_export_partners_yemen_from_baci"] = get_top_trade_partners_v2(
                        yemen_exports_from_baci,
                        country_code=self.country_code_yem_numeric,
                        is_export=True,
                        exporter_col=self.exporter_col,
                        importer_col=self.importer_col,
                        value_col=self.value_col,
                        top_n=10
                    )

                    results["top_exported_products_yemen_from_baci"] = get_top_trade_products_v2(
                        yemen_exports_from_baci,
                        product_col=self.product_col,
                        value_col=self.value_col,
                        top_n=10
                    )
                else:
                    self.logger.warning("No Yemen export data found in BACI to get top partners/products.")
                    results["top_export_partners_yemen_from_baci"] = pd.DataFrame()
                    results["top_exported_products_yemen_from_baci"] = pd.DataFrame()

            except Exception as e:
                self.logger.error(f"Error in BACI-based trade flow analysis: {e}", exc_info=True)
                results["trade_flow_analysis"] = {"error": str(e)}
                results["top_export_partners_yemen_from_baci"] = pd.DataFrame()
                results["top_exported_products_yemen_from_baci"] = pd.DataFrame()

        # ...existing code for other analyses...

        # Generate visualizations if V2 is enabled
        if is_feature_enabled("use_refactored_visualization"):
            visualization_results = self._generate_visualizations(results, year, save)
            results["visualizations"] = visualization_results

        if save:
            self.save_results(results, year, **kwargs)

        elapsed_time = time.time() - start_time
        self.logger.info(f"Market Pipeline completed in {elapsed_time:.2f} seconds for year {year}")
        return results

    # ...existing code for save_results and other methods...

if __name__ == '__main__':
    configure_logging(log_level=LogLevel.DEBUG, log_to_console=True, log_to_file=True, log_file_name="market_pipeline_main.log")
    logger = get_logger(__name__)
    logger.info("--- Running Market Pipeline Example (Directly) ---")
    
    # ...existing code for example...