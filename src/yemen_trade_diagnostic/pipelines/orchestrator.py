"""
Pipeline orchestration system for Yemen Trade Diagnostic.

This module provides a robust orchestration system for managing pipeline dependencies,
execution order, and error handling. It supports registration, discovery, and execution
of pipelines in the correct order based on their dependencies.
"""

# Available pipelines
AVAILABLE_PIPELINES = [
    'concentration',
    'composition',
    'macroeconomic',
    'market',
    'growth',
    'sophistication',
    'rca'
]

# Standard library imports
import importlib
import inspect
import logging
import logging as logger
import pkgutil
import threading
import time
from collections import defaultdict
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Tuple, Type, Union, cast

# Third-party imports
import networkx as nx

# Project imports
from yemen_trade_diagnostic.errors import protect, error_context, OperationType
from yemen_trade_diagnostic.interfaces.hardware_interface import get_hardware_manager
from yemen_trade_diagnostic.interfaces.logging_interface import get_logger
from yemen_trade_diagnostic.interfaces.visualization_interface import process_pipeline_visualization
from yemen_trade_diagnostic.pipelines.pipeline import Pipeline
from yemen_trade_diagnostic.utils.feature_flags import (
    FeatureFlag,
    is_feature_enabled,
    is_hardware_acceleration_enabled,
    is_refactored_pipelines_enabled,
    is_refactored_visualization_enabled,
)

class PipelineState(Enum):
    """
    Possible states of a pipeline in the orchestration process.

    - PENDING: Pipeline is waiting to be executed
    - RUNNING: Pipeline is currently executing
    - COMPLETED: Pipeline has completed successfully
    - FAILED: Pipeline has failed
    - SKIPPED: Pipeline was skipped (due to circuit breaker or dependency failure)
    """
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"

class DependencyType(Enum):
    """
    Types of dependencies between pipelines.

    - HARD: Pipeline cannot execute if dependency has failed
    - SOFT: Pipeline can execute even if dependency has failed
    """
    HARD = "hard"
    SOFT = "soft"

class PipelineDependencyManager:
    """
    Manager for pipeline dependencies.

    Tracks dependencies between pipelines and determines execution order.
    """

    def __init__(self) -> None:
        """Initialize the dependency manager."""
        self.logger = get_logger("pipeline_dependency_manager")
        self._dependencies = defaultdict(set)  # Pipeline -> Set of dependencies
        self._dependents = defaultdict(set)  # Pipeline -> Set of dependents
        self._dependency_types = {}  # (Pipeline, Dependency) -> DependencyType

    def add_dependency(self,
                      pipeline: str,
                      dependency: str,
                      dep_type: DependencyType = DependencyType.HARD) -> None:
        """
        Add a dependency between pipelines.

        Args:
            pipeline: Name of the pipeline
            dependency: Name of the pipeline it depends on
            dep_type: Type of dependency (hard or soft)
        """
        if pipeline == dependency:
            self.logger.warning(f"Ignoring self-dependency for pipeline {pipeline}")
            return

        self._dependencies[pipeline].add(dependency)
        self._dependents[dependency].add(pipeline)
        self._dependency_types[(pipeline, dependency)] = dep_type

        self.logger.debug(f"Added {dep_type.value} dependency: {pipeline} -> {dependency}")

    def remove_dependency(self, pipeline: str, dependency: str) -> None:
        """
        Remove a dependency between pipelines.

        Args:
            pipeline: Name of the pipeline
            dependency: Name of the pipeline dependency to remove
        """
        if dependency in self._dependencies[pipeline]:
            self._dependencies[pipeline].remove(dependency)
            self._dependents[dependency].remove(pipeline)
            if (pipeline, dependency) in self._dependency_types:
                del self._dependency_types[(pipeline, dependency)]

            self.logger.debug(f"Removed dependency: {pipeline} -> {dependency}")

    def get_dependencies(self, pipeline: str) -> Set[str]:
        """
        Get all dependencies for a pipeline.

        Args:
            pipeline: Name of the pipeline

        Returns:
            Set of pipeline dependencies
        """
        return self._dependencies.get(pipeline, set()).copy()

    def get_dependents(self, pipeline: str) -> Set[str]:
        """
        Get all dependents (pipelines that depend on this one).

        Args:
            pipeline: Name of the pipeline

        Returns:
            Set of pipeline dependents
        """
        return self._dependents.get(pipeline, set()).copy()

    def get_dependency_type(self, pipeline: str, dependency: str) -> DependencyType:
        """
        Get the type of dependency between pipelines.

        Args:
            pipeline: Name of the pipeline
            dependency: Name of the dependency

        Returns:
            Type of dependency (hard or soft)
        """
        return self._dependency_types.get((pipeline, dependency), DependencyType.HARD)

    def get_execution_order(self, pipeline_names: List[str]) -> List[List[str]]:
        """
        Get optimal execution order for pipelines.

        Args:
            pipeline_names: List of pipeline names to order

        Returns:
            List of pipeline levels, each level is a list of pipelines
            that can be executed in parallel.

        Raises:
            ValueError: If a circular dependency is detected
        """
        # Create a directed graph from dependencies
        graph = nx.DiGraph()

        # Add nodes for all pipelines
        for name in pipeline_names:
            graph.add_node(name)

        # Add edges for dependencies
        for name in pipeline_names:
            dependencies = self._dependencies.get(name, set())
            for dep in dependencies:
                if dep in pipeline_names:  # Only consider dependencies in the list
                    graph.add_edge(dep, name)  # Edge from dependency to dependent

        # Check for circular dependencies
        try:
            nx.find_cycle(graph)
            raise ValueError("Circular dependency detected in pipelines")
        except nx.NetworkXNoCycle:
            pass  # No cycles, continue

        # Get topological generations (levels)
        # Each level contains pipelines that can be executed in parallel
        try:
            levels = list(nx.topological_generations(graph))
            return levels
        except nx.NetworkXUnfeasible:
            # This should not happen if we already checked for cycles
            raise ValueError("Could not determine execution order due to unfeasible dependency graph")

    def has_circular_dependency(self) -> bool:
        """
        Check if pipeline dependencies have a circular reference.

        Returns:
            Whether a circular dependency exists
        """
        # Create a directed graph from dependencies
        graph = nx.DiGraph()

        # Add nodes and edges
        for pipeline, dependencies in self._dependencies.items():
            graph.add_node(pipeline)
            for dep in dependencies:
                graph.add_node(dep)
                graph.add_edge(dep, pipeline)  # Edge from dependency to dependent

        # Check for circular dependencies
        try:
            nx.find_cycle(graph)
            return True
        except nx.NetworkXNoCycle:
            return False

class PipelineExecutionResult:
    """
    Result of a pipeline execution.

    Contains the output data, execution status, and metadata.
    """

    def __init__(self,
                 pipeline_name: str,
                 state: PipelineState,
                 data: Optional[Dict[str, Any]] = None,
                 error: Optional[Exception] = None,
                 execution_time: float = 0.0,
                 metadata: Optional[Dict[str, Any]] = None,
                 visualization_results: Optional[Dict[str, Any]] = None) -> None:
        """
        Initialize a pipeline execution result.

        Args:
            pipeline_name: Name of the pipeline
            state: Final execution state
            data: Output data from the pipeline
            error: Exception if the pipeline failed
            execution_time: Time taken to execute the pipeline (seconds)
            metadata: Additional metadata about the execution
            visualization_results: Results from visualization generation
        """
        self.pipeline_name = pipeline_name
        self.state = state
        self.data = data or {}
        self.error = error
        self.execution_time = execution_time
        self.metadata = metadata or {}
        self.visualization_results = visualization_results
        self.timestamp = time.time()

    def is_successful(self) -> bool:
        """
        Check if the pipeline execution was successful.

        Returns:
            Whether the pipeline completed successfully
        """
        return self.state == PipelineState.COMPLETED

    def is_failed(self) -> bool:
        """
        Check if the pipeline execution failed.

        Returns:
            Whether the pipeline failed
        """
        return self.state == PipelineState.FAILED

    def is_skipped(self) -> bool:
        """
        Check if the pipeline execution was skipped.

        Returns:
            Whether the pipeline was skipped
        """
        return self.state == PipelineState.SKIPPED

    def get_error_message(self) -> Optional[str]:
        """
        Get the error message if the pipeline failed.

        Returns:
            Error message or None if no error
        """
        return str(self.error) if self.error else None

    def to_dict(self) -> Dict[str, Any]:
        """
        Convert to dictionary representation.

        Returns:
            Dictionary with execution result details
        """
        result = {
            "pipeline_name": self.pipeline_name,
            "state": self.state.value,
            "execution_time": self.execution_time,
            "timestamp": self.timestamp,
            "metadata": self.metadata,
            "data_keys": list(self.data.keys()) if self.data else []
        }

        if self.error:
            result["error"] = {
                "type": type(self.error).__name__,
                "message": str(self.error)
            }

        if self.visualization_results:
            result["visualization_results"] = self.visualization_results

        return result

    def get_minimal_result(self) -> Dict[str, Any]:
        """
        Get a minimal version of the result data.

        Used for dependency resolution when a pipeline fails.

        Returns:
            Minimal result dictionary with required keys
        """
        # Create an empty result with the expected keys
        if not self.data:
            return {}

        minimal = {}
        for key, value in self.data.items():
            # For each key, provide a minimal placeholder
            if isinstance(value, dict):
                minimal[key] = {}
            elif isinstance(value, list):
                minimal[key] = []
            elif isinstance(value, (int, float)):
                minimal[key] = 0
            elif isinstance(value, str):
                minimal[key] = ""
            else:
                minimal[key] = None

        return minimal

class PipelineOrchestrator:
    """
    Orchestrator for pipeline execution.

    Manages pipeline registration, dependency resolution, and execution.
    Now with V2 visualization system integration and feature flag awareness.
    """

    def __init__(self,
                config: Optional[Dict[str, Any]] = None,
                auto_discover: bool = True) -> None:
        """
        Initialize the pipeline orchestrator.

        Args:
            config: Configuration for the orchestrator
            auto_discover: Whether to auto-discover pipelines
        """
        self.logger = get_logger("pipeline_orchestrator")
        self.config = config or {}

        # Pipeline registry
        self._pipeline_registry = {}  # Name -> Pipeline class or instance
        self._pipeline_instances = {}  # Name -> Pipeline instance

        # Dependency manager
        self._dependency_manager = PipelineDependencyManager()

        # Execution history
        self._execution_history = []  # List of orchestration runs

        # Hardware manager for resource coordination
        self._hardware_manager = get_hardware_manager()

        # Visualization configuration
        self.visualization_config = self.config.get("visualization", {})

        # Auto-discover pipelines if requested
        if auto_discover:
            self._discover_pipelines()

        # Register standard pipelines
        self._register_standard_pipelines()

    def _discover_pipelines(self) -> None:
        """Discover pipeline classes through module inspection."""
        try:
            # Get the pipelines package
            package_name = "yemen_trade_diagnostic.pipelines"
            package = importlib.import_module(package_name)

            # Get all modules in the pipelines package
            for _, name, is_pkg in pkgutil.iter_modules(package.__path__, package.__name__ + '.'):
                if is_pkg:
                    continue  # Skip subpackages

                try:
                    # Import the module
                    module = importlib.import_module(name)

                    # Find pipeline classes in the module
                    for attr_name in dir(module):
                        attr = getattr(module, attr_name)

                        # Check if it's a class and a subclass of Pipeline
                        if (inspect.isclass(attr) and
                            issubclass(attr, Pipeline) and
                            attr != Pipeline and
                            attr.__module__ == module.__name__):

                            # Register the pipeline class
                            try:
                                pipeline_name = attr.get_name() if hasattr(attr, 'get_name') else attr_name.lower()
                                if pipeline_name.endswith('pipeline'):
                                    pipeline_name = pipeline_name[:-8]  # Remove 'pipeline' suffix

                                self.register_pipeline_class(attr, pipeline_name)
                                self.logger.debug(f"Auto-discovered pipeline: {pipeline_name}")
                            except Exception as e:
                                self.logger.warning(f"Failed to register discovered pipeline {attr_name}: {str(e)}")

                except Exception as e:
                    self.logger.warning(f"Error discovering pipelines in module {name}: {str(e)}")

        except Exception as e:
            self.logger.warning(f"Error during pipeline discovery: {str(e)}")

    def _register_standard_pipelines(self) -> None:
        """Register standard pipeline classes."""
        standard_pipelines = [
            (CompositionPipeline, "composition"),
            (ConcentrationPipeline, "concentration"),
            (GrowthPipeline, "growth"),
            (MacroeconomicPipeline, "macroeconomic"),
            (MarketPipeline, "market"),
            (RcaPipeline, "rca"),
            (SophisticationPipeline, "sophistication")
        ]

        for pipeline_class, name in standard_pipelines:
            try:
                self.register_pipeline_class(pipeline_class, name)
            except (NameError, Exception) as e:
                # Pipeline class might not be imported, log and continue
                self.logger.debug(f"Could not register standard pipeline {name}: {str(e)}")

    def register_pipeline(self,
                         pipeline: Pipeline,
                         name: Optional[str] = None) -> None:
        """
        Register a pipeline instance.

        Args:
            pipeline: Pipeline instance to register
            name: Optional name for the pipeline (default: pipeline.name)
        """
        # Get pipeline name
        pipeline_name = name or pipeline.name

        # Register the pipeline
        self._pipeline_registry[pipeline_name] = pipeline
        self._pipeline_instances[pipeline_name] = pipeline

        # Register pipeline dependencies
        self._register_pipeline_dependencies(pipeline)

        self.logger.info(f"Registered pipeline: {pipeline_name}")

    def register_pipeline_class(self,
                              pipeline_class: Type[Pipeline],
                              name: Optional[str] = None) -> None:
        """
        Register a pipeline class.

        Args:
            pipeline_class: Pipeline class to register
            name: Optional name for the pipeline
        """
        # Check if the class is a subclass of Pipeline
        if not issubclass(pipeline_class, Pipeline):
            raise ValueError(f"Class {pipeline_class.__name__} is not a subclass of Pipeline")

        # Determine pipeline name
        if name is None:
            # Try to determine name from class
            if hasattr(pipeline_class, 'get_name'):
                name = pipeline_class.get_name()
            else:
                name = pipeline_class.__name__.lower()
                if name.endswith('pipeline'):
                    name = name[:-8]  # Remove 'pipeline' suffix

        # Register the pipeline class
        self._pipeline_registry[name] = pipeline_class

        self.logger.info(f"Registered pipeline class: {name} ({pipeline_class.__name__})")

    def _register_pipeline_dependencies(self, pipeline: Pipeline) -> None:
        """
        Register dependencies for a pipeline.

        Args:
            pipeline: Pipeline instance
        """
        if not hasattr(pipeline, 'dependencies'):
            return

        dependencies = pipeline.dependencies()
        for dependency in dependencies:
            # Check if it's a tuple with dependency type
            if isinstance(dependency, tuple) and len(dependency) == 2:
                dep_name, dep_type_str = dependency
                dep_type = DependencyType.SOFT if dep_type_str.lower() == 'soft' else DependencyType.HARD
            else:
                dep_name = dependency
                dep_type = DependencyType.HARD

            # Register the dependency
            self._dependency_manager.add_dependency(pipeline.name, dep_name, dep_type)

    def get_pipeline(self, name: str) -> Pipeline:
        """
        Get or create a pipeline instance.

        Args:
            name: Name of the pipeline

        Returns:
            Pipeline instance

        Raises:
            KeyError: If the pipeline is not registered
        """
        # Check if refactored pipelines are enabled
        if not is_refactored_pipelines_enabled():
            # Try to use V1 pipeline if V2 is disabled
            return self._create_pipeline_from_v1(name)

        # Check if we already have an instance
        if name in self._pipeline_instances:
            return self._pipeline_instances[name]

        # Check if we have the class or instance registered
        if name not in self._pipeline_registry:
            raise KeyError(f"Pipeline not registered: {name}")

        pipeline_class_or_instance = self._pipeline_registry[name]

        # If it's already an instance, return it
        if isinstance(pipeline_class_or_instance, Pipeline):
            self._pipeline_instances[name] = pipeline_class_or_instance
            return pipeline_class_or_instance

        # Otherwise, create a new instance
        try:
            # Create and store the instance
            instance = pipeline_class_or_instance()
            self._pipeline_instances[name] = instance

            # Register dependencies
            self._register_pipeline_dependencies(instance)

            return instance
        except Exception as e:
            self.logger.error(f"Error creating pipeline instance {name}: {str(e)}")
            raise

    def get_pipeline_names(self) -> List[str]:
        """
        Get names of all registered pipelines.

        Returns:
            List of pipeline names
        """
        return list(self._pipeline_registry.keys())

    def _create_pipeline_from_v1(self, name: str) -> Pipeline:
        """
        Create a V2 pipeline wrapper for a V1 pipeline.

        Args:
            name: Name of the V1 pipeline

        Returns:
            V2 Pipeline instance wrapping the V1 pipeline

        Raises:
            ImportError: If the V1 pipeline cannot be imported
        """
        # Import V1 pipeline
        try:
            # Legacy pipeline import from src_v2
            module_name = f"yemen_trade_diagnostic.pipelines.{name}"
            module = importlib.import_module(module_name)

            # Find the pipeline function
            pipeline_func = None
            for attr_name in dir(module):
                if attr_name.startswith("run_") and callable(getattr(module, attr_name)):
                    pipeline_func = getattr(module, attr_name)
                    break

            if pipeline_func is None:
                raise ImportError(f"Could not find V1 pipeline function in module {module_name}")

            # Create a V2 wrapper for the V1 pipeline
            # V1PipelineWrapper not available - raise error for now
            raise NotImplementedError(f"V1 pipeline wrapper not implemented for {name}")

        except ImportError as e:
            self.logger.error(f"Error importing V1 pipeline {name}: {str(e)}")
            raise

    @protect("pipeline_execution", OperationType.PIPELINE_EXECUTION)
    def run_pipeline(self,
                    name: str,
                    year: int,
                    inputs: Optional[Dict[str, Any]] = None,
                    save: bool = True,
                    generate_visualizations: bool = True,
                    output_dir: Optional[str] = None,
                    validate: bool = True,
                    validation_mode: Optional[Any] = None) -> Dict[str, Any]:
        """
        Run a single pipeline.

        Args:
            name: Name of the pipeline
            year: Year to run the pipeline for
            inputs: Optional input data for the pipeline
            save: Whether to save the pipeline results
            generate_visualizations: Whether to generate visualizations for pipeline output
            output_dir: Directory to save output (default: output/{pipeline_name})
            validate: Whether to validate pipeline results before saving
            validation_mode: Validation mode to use

        Returns:
            Pipeline results

        Raises:
            KeyError: If the pipeline is not registered
            Exception: If the pipeline execution fails
        """
        try:
            # Get the pipeline instance
            pipeline = self.get_pipeline(name)
            
            # Prepare run parameters
            run_params = {
                "year": year,
                "save": save
            }
            
            if inputs:
                run_params["inputs"] = inputs
            if output_dir:
                run_params["output_dir"] = output_dir
            if generate_visualizations:
                run_params["generate_visualizations"] = generate_visualizations
            
            # Run the pipeline
            self.logger.info(f"Running pipeline: {name} for year {year}")
            results = pipeline.run(**run_params)
            
            # Generate visualizations if requested and V2 is enabled
            if generate_visualizations and is_refactored_visualization_enabled():
                try:
                    vis_output_dir = output_dir or f"output/visualizations/{name}_{year}"
                    visualization_results = process_pipeline_visualization(
                        pipeline_output=results,
                        output_dir=vis_output_dir,
                        format="png"
                    )
                    results["visualizations"] = visualization_results
                    self.logger.info(f"Generated visualizations for pipeline {name}")
                except Exception as e:
                    self.logger.warning(f"Failed to generate visualizations for pipeline {name}: {str(e)}")
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error running pipeline {name}: {str(e)}")
            raise

    @protect("pipeline_execution", OperationType.PIPELINE_EXECUTION)
    def run_pipelines(self,
                     pipeline_names: Optional[List[str]] = None,
                     year: int = 2022,
                     save: bool = True,
                     parallel: bool = True,
                     retry_failed: bool = True,
                     retry_count: int = 1,
                     generate_visualizations: bool = True,
                     max_workers: Optional[int] = None,
                     output_dir: Optional[str] = None) -> Dict[str, PipelineExecutionResult]:
        """
        Run multiple pipelines with dependency resolution.

        Args:
            pipeline_names: List of pipeline names to run (default: all registered)
            year: Year to run the pipelines for
            save: Whether to save the pipeline results
            parallel: Whether to run pipelines in parallel when possible
            retry_failed: Whether to retry failed pipelines
            retry_count: Number of retry attempts for failed pipelines
            generate_visualizations: Whether to generate visualizations
            max_workers: Maximum number of worker threads for parallel execution
            output_dir: Directory to save output (default: output/)

        Returns:
            Dictionary of pipeline execution results
        """
        # Use all registered pipelines if none specified
        if pipeline_names is None:
            pipeline_names = self.get_pipeline_names()

        self.logger.info(f"Running {len(pipeline_names)} pipelines for year {year}")
        
        # Track orchestration metadata
        start_time = time.time()
        orchestration_metadata = {
            "year": year,
            "total_pipelines": len(pipeline_names),
            "parallel": parallel,
            "retry_enabled": retry_failed,
            "retry_count": retry_count
        }

        # Get execution order based on dependencies
        try:
            execution_levels = self._dependency_manager.get_execution_order(pipeline_names)
            self.logger.info(f"Pipeline execution order: {execution_levels}")
        except ValueError as e:
            self.logger.error(f"Error determining execution order: {str(e)}")
            raise

        # Initialize results and tracking
        results = {}
        failed_dependencies = defaultdict(list)

        # Execute pipelines level by level
        for level_idx, level in enumerate(execution_levels):
            self.logger.info(f"Executing level {level_idx + 1}/{len(execution_levels)}: {level}")
            
            if parallel and len(level) > 1:
                level_results = self._execute_pipelines_parallel(
                    level, year, results, failed_dependencies, save, retry_failed, retry_count,
                    generate_visualizations, max_workers, output_dir
                )
            else:
                level_results = self._execute_pipelines_sequential(
                    level, year, results, failed_dependencies, save, retry_failed, retry_count,
                    generate_visualizations, output_dir
                )
            
            results.update(level_results)
            
            # Update failed dependencies for next level
            for pipeline_name in level:
                if pipeline_name in results and results[pipeline_name].is_failed():
                    dependents = self._dependency_manager.get_dependents(pipeline_name)
                    for dependent in dependents:
                        failed_dependencies[dependent].append(pipeline_name)

        # Calculate orchestration time and update metadata
        orchestration_time = time.time() - start_time
        orchestration_metadata.update({
            "execution_time": orchestration_time,
            "completed_pipelines": sum(1 for r in results.values() if r.is_successful()),
            "failed_pipelines": sum(1 for r in results.values() if r.is_failed()),
            "skipped_pipelines": sum(1 for r in results.values() if r.is_skipped())
        })

        # Store execution history
        self._execution_history.append({
            "timestamp": time.time(),
            "year": year,
            "pipelines": pipeline_names,
            "results": {name: result.to_dict() for name, result in results.items()},
            "metadata": orchestration_metadata
        })

        self.logger.info(f"Pipeline orchestration completed in {orchestration_time:.2f} seconds")
        self.logger.info(f"Results: {orchestration_metadata['completed_pipelines']} completed, "
                      f"{orchestration_metadata['failed_pipelines']} failed, "
                      f"{orchestration_metadata['skipped_pipelines']} skipped")

        return results

# Default orchestrator instance
_default_orchestrator = None

def get_orchestrator() -> PipelineOrchestrator:
    """
    Get the default orchestrator instance.

    Returns:
        Default PipelineOrchestrator instance
    """
    global _default_orchestrator
    if _default_orchestrator is None:
        _default_orchestrator = PipelineOrchestrator()
    return _default_orchestrator

# Examples of usage:

def run_pipeline_sequence_example() -> None:
    """Example of running pipelines in sequence with dependency handling."""
    # Get the orchestrator
    orchestrator = get_orchestrator()

    # Check feature flag status
    flags = orchestrator.get_feature_flag_status()
    logger.info(f"Feature flags: {flags}")

    # Run specific pipelines for year 2022
    pipeline_names = ["macroeconomic", "growth", "market"]
    results = orchestrator.run_pipelines(
        pipeline_names=pipeline_names,
        year=2022,
        save=True,
        parallel=False,
        generate_visualizations=True,
        output_dir="output/example"
    )

    # Print results
    for name, result in results.items():
        logger.info(f"Pipeline {name}: {result.state.value}")
        if result.is_successful():
            logger.info(f"  Output keys: {list(result.data.keys())}")
            if result.visualization_results:
                logger.info(f"  Visualizations: {result.visualization_results.get('saved_paths', {})}")
        elif result.is_failed():
            logger.error(f"  Error: {result.get_error_message()}")

def run_all_pipelines_example() -> None:
    """Example of running all registered pipelines with parallel execution."""
    # Get the orchestrator
    orchestrator = get_orchestrator()
    logger = get_logger("pipeline_orchestrator")

    # Run all pipelines for year 2022
    results = orchestrator.run_pipelines(
        year=2022,
        save=True,
        parallel=True,
        retry_failed=True,
        retry_count=1,
        generate_visualizations=True,
        output_dir="output/all_pipelines"
    )

    # Print statistics
    completed = sum(1 for r in results.values() if r.is_successful())
    failed = sum(1 for r in results.values() if r.is_failed())
    skipped = sum(1 for r in results.values() if r.is_skipped())

    logger.info(f"Pipeline execution results: {completed} completed, {failed} failed, {skipped} skipped")

    # Print visualization results
    visualizations_generated = sum(1 for r in results.values() if r.visualization_results)
    logger.info(f"Visualizations generated for {visualizations_generated} pipelines")

    # Print failed pipelines
    if failed > 0:
        logger.error("Failed pipelines:")
        for name, result in results.items():
            if result.is_failed():
                logger.error(f"  {name}: {result.get_error_message()}")

# Helper functions for CLI integration

def get_available_pipelines() -> List[str]:
    """
    Get a list of available pipeline names.

    Returns:
        List of pipeline names
    """
    return AVAILABLE_PIPELINES

def get_pipeline_info(pipeline_name: str) -> Dict[str, Any]:
    """
    Get information about a specific pipeline.

    Args:
        pipeline_name: Name of the pipeline

    Returns:
        Dictionary with pipeline information
    """
    orchestrator = get_orchestrator()
    pipeline_module = importlib.import_module(f"yemen_trade_diagnostic.pipelines.{pipeline_name}")

    # Get pipeline class
    pipeline_class = None
    for name, obj in inspect.getmembers(pipeline_module):
        if inspect.isclass(obj) and name.endswith("Pipeline"):
            pipeline_class = obj
            break

    if pipeline_class is None:
        return {"name": pipeline_name, "status": "not found"}

    # Get pipeline dependencies
    dependencies = orchestrator._get_pipeline_dependencies(pipeline_name)

    return {
        "name": pipeline_name,
        "class": pipeline_class.__name__,
        "dependencies": list(dependencies),
        "description": pipeline_class.__doc__.strip() if pipeline_class.__doc__ else "No description"
    }

if __name__ == "__main__":
    # Example usage
    print(f"Available pipelines: {get_available_pipelines()}")
    for pipeline in get_available_pipelines():
        print(f"Pipeline info for {pipeline}: {get_pipeline_info(pipeline)}")
    run_pipeline_sequence_example()