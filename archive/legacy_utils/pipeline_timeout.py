"""Timeout utilities for preventing pipeline operations from hanging.

This module provides decorators and utilities to add timeout functionality to
long-running operations in the Yemen Trade Diagnostic pipelines.
"""
# Standard library imports
import functools
import signal
import threading
import time
from concurrent.futures import Future, ThreadPoolExecutor
from concurrent.futures import TimeoutError as FuturesTimeoutError
from typing import Any, Callable, Dict, Optional, TypeVar, Union, cast

# Project imports
from yemen_trade_diagnostic.interfaces.logging_interface import get_logger

logger = get_logger(__name__)

# Type variable for function return type
T = TypeVar('T')

class TimeoutError(Exception):
    """Exception raised when a function execution times out."""
    pass


def with_timeout(timeout_seconds: float, fallback_value: Any = None) -> Callable:
    """Decorator that adds a timeout to a function using threading.
    
    Args:
        timeout_seconds: Maximum execution time in seconds
        fallback_value: Value to return if timeout occurs
        
    Returns:
        Decorated function with timeout capability
    """
    def decorator(func: Callable[..., T]) -> Callable[..., T]:
        @functools.wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> T:
            result = [fallback_value]
            
            def target():
                result[0] = func(*args, **kwargs)
                
            thread = threading.Thread(target=target)
            thread.daemon = True
            thread.start()
            
            thread.join(timeout_seconds)
            if thread.is_alive():
                logger.warning(f"Function {func.__name__} timed out after {timeout_seconds} seconds")
                raise TimeoutError(f"Function {func.__name__} timed out after {timeout_seconds} seconds")
                
            return cast(T, result[0])
        return wrapper
    return decorator


def with_retry_and_timeout(max_retries: int = 3, 
                          timeout_seconds: float = 60.0, 
                          retry_delay: float = 1.0,
                          fallback_value: Any = None) -> Callable:
    """Decorator that adds timeout and retry capability to a function.
    
    Args:
        max_retries: Maximum number of retry attempts
        timeout_seconds: Maximum execution time in seconds per attempt
        retry_delay: Delay between retry attempts in seconds
        fallback_value: Value to return if all retries fail
        
    Returns:
        Decorated function with timeout and retry capability
    """
    def decorator(func: Callable[..., T]) -> Callable[..., T]:
        @functools.wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> T:
            last_error = None
            
            for attempt in range(max_retries):
                try:
                    with ThreadPoolExecutor(max_workers=1) as executor:
                        future = executor.submit(func, *args, **kwargs)
                        return cast(T, future.result(timeout=timeout_seconds))
                except (TimeoutError, FuturesTimeoutError) as e:
                    last_error = e
                    logger.warning(f"Attempt {attempt+1}/{max_retries} for {func.__name__} timed out after {timeout_seconds}s")
                except Exception as e:
                    last_error = e
                    logger.warning(f"Attempt {attempt+1}/{max_retries} for {func.__name__} failed with error: {str(e)}")
                    
                # Wait before retrying
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)
                    
            # If we get here, all retries failed
            logger.error(f"All {max_retries} attempts failed for {func.__name__}. Last error: {str(last_error)}")
            
            if fallback_value is not None:
                logger.info(f"Using fallback value for {func.__name__}")
                return cast(T, fallback_value)
            else:
                if last_error:
                    raise last_error
                else:
                    raise TimeoutError(f"Function {func.__name__} timed out after {max_retries} attempts")
                    
        return wrapper
    return decorator


def run_with_timeout(func: Callable[..., T],
                    args: tuple = (),
                    kwargs: Dict[str, Any] = None,
                    timeout_seconds: float = 300.0,  # Increased default timeout to 5 minutes
                    fallback_value: Any = None) -> T:
    """Run a function with a timeout.
    
    Args:
        func: Function to run
        args: Positional arguments for the function
        kwargs: Keyword arguments for the function
        timeout_seconds: Maximum execution time in seconds
        fallback_value: Value to return if timeout occurs
        
    Returns:
        Function result or fallback value if timeout occurs
    """
    if kwargs is None:
        kwargs = {}
        
    with ThreadPoolExecutor(max_workers=1) as executor:
        future = executor.submit(func, *args, **kwargs)
        try:
            return cast(T, future.result(timeout=timeout_seconds))
        except FuturesTimeoutError:
            logger.warning(f"Function {func.__name__} timed out after {timeout_seconds} seconds")
            return cast(T, fallback_value)


def process_in_chunks_with_timeout(df: 'pd.DataFrame',
                                  processing_fn: Callable[['pd.DataFrame'], 'pd.DataFrame'],
                                  chunk_size: int = 50000,
                                  timeout_per_chunk: float = 600.0,  # Increased default timeout to 10 minutes
                                  fallback_fn: Optional[Callable[['pd.DataFrame'], 'pd.DataFrame']] = None) -> 'pd.DataFrame':
    """Process a large DataFrame in chunks with timeout protection.
    
    Args:
        df: DataFrame to process
        processing_fn: Function to apply to each chunk
        chunk_size: Number of rows per chunk
        timeout_per_chunk: Maximum time per chunk in seconds
        fallback_fn: Function to use if processing times out
        
    Returns:
        Processed DataFrame
    """
    # Import here to avoid circular imports
    # Third-party imports
    import pandas as pd

    # Project imports
    from yemen_trade_diagnostic.utils.memory_optimizer import (
        chunk_generator,
        safely_concatenate_dfs,
    )
    
    if df is None or df.empty:
        return df
        
    results = []
    
    # Process each chunk with timeout
    for i, chunk in enumerate(chunk_generator(df, chunk_size)):
        try:
            start_time = time.time()
            logger.debug(f"Processing chunk {i+1} of size {len(chunk)}")
            
            # Process chunk with timeout
            processed_chunk = run_with_timeout(
                processing_fn,
                (chunk,),
                timeout_seconds=timeout_per_chunk,
                fallback_value=None
            )

            # If timeout occurred and fallback function provided, use it
            if processed_chunk is None and fallback_fn is not None:
                logger.warning(f"Using fallback processing for chunk {i+1}")
                try:
                    processed_chunk = fallback_fn(chunk)
                except Exception as fallback_err:
                    logger.error(f"Fallback processing failed for chunk {i+1}: {str(fallback_err)}")

            # If we have a processed chunk (either from main or fallback), add it
            if processed_chunk is not None:
                results.append(processed_chunk)
                logger.info(f"Chunk {i+1} processed in {time.time() - start_time:.2f}s")
            else:
                logger.warning(f"Skipping chunk {i+1} due to timeout or processing failure")
                
        except Exception as e:
            logger.error(f"Error processing chunk {i+1}: {str(e)}")
            
    # Safely combine results
    return safely_concatenate_dfs(results)