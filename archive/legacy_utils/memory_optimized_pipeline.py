"""
Memory-optimized pipeline utilities.

This module provides integration between memory pooling and pipeline operations
to optimize memory usage across the Yemen Trade Diagnostic system.
"""
# Standard library imports
import gc
from contextlib import contextmanager
from functools import wraps
from typing import Any, Callable, Dict, List, Optional

# Third-party imports
import numpy as np
import pandas as pd

# Project imports
from yemen_trade_diagnostic.interfaces.error_interface import (
    ErrorCategory,
    ErrorSeverity,
    with_error_handling,
)
from yemen_trade_diagnostic.interfaces.logging_interface import get_logger
from yemen_trade_diagnostic.pipelines.pipeline import Pipeline
from yemen_trade_diagnostic.utils.memory_monitor import get_memory_monitor, profile_memory
from yemen_trade_diagnostic.utils.memory_pool import (
    acquire_dataframe,
    get_pool_stats,
    release_dataframe,
    trigger_gc,
)

logger = get_logger(__name__)


class MemoryOptimizedPipeline(Pipeline):
    """
    Base pipeline class with memory optimization features.
    
    Extends the base Pipeline class with memory pooling and monitoring
    capabilities for better memory efficiency.
    """
    
    def __init__(self, name: str, config: Optional[Dict[str, Any]] = None):
        super().__init__(name, config)
        
        # Memory optimization settings
        self.use_memory_pool = self.config.get('use_memory_pool', True)
        self.gc_threshold_mb = self.config.get('gc_threshold_mb', 800)
        self.auto_gc = self.config.get('auto_gc', True)
        
        # Initialize memory monitor
        self.memory_monitor = get_memory_monitor()
        
        logger.info(f"Initialized memory-optimized pipeline '{name}' "
                   f"(pool={'enabled' if self.use_memory_pool else 'disabled'}, "
                   f"gc_threshold={self.gc_threshold_mb}MB)")
    
    @contextmanager
    def pooled_dataframe(self, 
                        rows: Optional[int] = None,
                        columns: Optional[List[str]] = None,
                        dtype: Optional[Dict[str, Any]] = None):
        """
        Context manager for pooled DataFrames.
        
        Args:
            rows: Number of rows
            columns: Column names
            dtype: Column data types
            
        Yields:
            DataFrame from pool
        """
        if not self.use_memory_pool:
            # Fall back to regular DataFrame
            if rows is None:
                df = pd.DataFrame(columns=columns)
            else:
                df = pd.DataFrame(index=range(rows), columns=columns)
            yield df
            return
        
        df = None
        try:
            df = acquire_dataframe(rows=rows, columns=columns, dtype=dtype)
            yield df
        finally:
            if df is not None:
                release_dataframe(df)
    
    def check_memory_usage(self) -> Dict[str, float]:
        """Check current memory usage and trigger GC if needed."""
        memory_stats = self.memory_monitor.get_memory_usage()
        
        if self.auto_gc and memory_stats['rss_mb'] > self.gc_threshold_mb:
            logger.info(f"Memory usage {memory_stats['rss_mb']:.1f}MB exceeds threshold, triggering GC")
            trigger_gc()
            
            # Re-check after GC
            memory_stats = self.memory_monitor.get_memory_usage()
        
        return memory_stats
    
    @profile_memory()
    def run(self, year: int, save: bool = True, **kwargs) -> Dict[str, Any]:
        """
        Run the pipeline with memory optimization.
        
        This method should be overridden by specific pipeline implementations.
        """
        logger.info(f"Running memory-optimized pipeline '{self.name}' for year {year}")
        
        # Check memory before starting
        pre_memory = self.check_memory_usage()
        logger.info(f"Pre-run memory: {pre_memory['rss_mb']:.1f}MB")
        
        try:
            # Run the actual pipeline (to be implemented by subclasses)
            results = self._run_pipeline_logic(year, save=save, **kwargs)
            
            # Check memory after completion
            post_memory = self.check_memory_usage()
            memory_used = post_memory['rss_mb'] - pre_memory['rss_mb']
            logger.info(f"Post-run memory: {post_memory['rss_mb']:.1f}MB "
                       f"(used: {memory_used:.1f}MB)")
            
            return results
            
        except Exception as e:
            logger.error(f"Error in memory-optimized pipeline: {e}")
            raise
        finally:
            # Always trigger GC after pipeline run
            if self.auto_gc:
                trigger_gc()
    
    def _run_pipeline_logic(self, year: int, save: bool = True, **kwargs) -> Dict[str, Any]:
        """
        The actual pipeline logic to be implemented by subclasses.
        """
        raise NotImplementedError("Subclasses must implement _run_pipeline_logic")


def optimize_dataframe_memory(df: pd.DataFrame, 
                            aggressive: bool = True,
                            categorical_threshold: float = 0.5) -> pd.DataFrame:
    """
    Optimize DataFrame memory usage by converting types.
    
    Args:
        df: Input DataFrame
        aggressive: Whether to use aggressive optimization
        categorical_threshold: Threshold for converting to categorical
        
    Returns:
        Memory-optimized DataFrame
    """
    logger.debug(f"Optimizing DataFrame memory (shape: {df.shape})")
    initial_memory = df.memory_usage(deep=True).sum() / (1024 ** 2)
    
    # Create a copy to avoid modifying original
    df_optimized = df.copy()
    
    # Optimize numeric columns
    for col in df_optimized.select_dtypes(include=['int64', 'float64']).columns:
        col_type = df_optimized[col].dtype
        
        if col_type == 'int64':
            # Try to downcast integers
            try:
                if df_optimized[col].min() >= 0:
                    # Unsigned integers
                    if df_optimized[col].max() < 256:
                        df_optimized[col] = df_optimized[col].astype(np.uint8)
                    elif df_optimized[col].max() < 65536:
                        df_optimized[col] = df_optimized[col].astype(np.uint16)
                    elif df_optimized[col].max() < 4294967296:
                        df_optimized[col] = df_optimized[col].astype(np.uint32)
                else:
                    # Signed integers
                    if -128 <= df_optimized[col].min() and df_optimized[col].max() < 128:
                        df_optimized[col] = df_optimized[col].astype(np.int8)
                    elif -32768 <= df_optimized[col].min() and df_optimized[col].max() < 32768:
                        df_optimized[col] = df_optimized[col].astype(np.int16)
                    elif -2147483648 <= df_optimized[col].min() and df_optimized[col].max() < 2147483648:
                        df_optimized[col] = df_optimized[col].astype(np.int32)
            except:
                pass
                
        elif col_type == 'float64' and aggressive:
            # Downcast floats if aggressive
            try:
                df_optimized[col] = pd.to_numeric(df_optimized[col], downcast='float')
            except:
                pass
    
    # Convert object columns to categorical if appropriate
    for col in df_optimized.select_dtypes(include=['object']).columns:
        num_unique = df_optimized[col].nunique()
        num_total = len(df_optimized[col])
        
        if num_unique / num_total < categorical_threshold:
            df_optimized[col] = df_optimized[col].astype('category')
    
    # Calculate memory savings
    final_memory = df_optimized.memory_usage(deep=True).sum() / (1024 ** 2)
    reduction_pct = (initial_memory - final_memory) / initial_memory * 100
    
    logger.debug(f"Memory optimization complete: {initial_memory:.2f}MB -> {final_memory:.2f}MB "
                f"({reduction_pct:.1f}% reduction)")
    
    return df_optimized


def batch_process_with_pool(data: pd.DataFrame,
                          process_func: Callable,
                          batch_size: int = 10000,
                          use_pool: bool = True) -> pd.DataFrame:
    """
    Process data in batches using memory pool.
    
    Args:
        data: Input DataFrame
        process_func: Function to apply to each batch
        batch_size: Size of each batch
        use_pool: Whether to use memory pool
        
    Returns:
        Processed DataFrame
    """
    results = []
    num_batches = (len(data) + batch_size - 1) // batch_size
    
    logger.info(f"Processing {len(data)} rows in {num_batches} batches")
    
    for i in range(0, len(data), batch_size):
        batch = data.iloc[i:i + batch_size]
        
        if use_pool:
            # Use pooled DataFrame for result
            with contextmanager(lambda: acquire_dataframe())() as result_df:
                processed = process_func(batch)
                results.append(processed.copy())
        else:
            processed = process_func(batch)
            results.append(processed)
        
        # Check memory after each batch
        if i % (batch_size * 10) == 0:
            memory_stats = get_memory_monitor().get_memory_usage()
            if memory_stats['rss_mb'] > 1000:  # 1GB threshold
                logger.info(f"High memory usage ({memory_stats['rss_mb']:.1f}MB), triggering GC")
                trigger_gc()
    
    # Combine results
    result = pd.concat(results, ignore_index=True)
    
    # Final memory optimization
    return optimize_dataframe_memory(result)


def memory_efficient_merge(left: pd.DataFrame,
                         right: pd.DataFrame,
                         on: Union[str, List[str]],
                         how: str = 'inner',
                         chunk_size: int = 100000) -> pd.DataFrame:
    """
    Memory-efficient merge operation for large DataFrames.
    
    Args:
        left: Left DataFrame
        right: Right DataFrame
        on: Column(s) to merge on
        how: Type of merge
        chunk_size: Size of chunks for processing
        
    Returns:
        Merged DataFrame
    """
    if len(left) * len(right) < 1e8:  # Small enough for regular merge
        return pd.merge(left, right, on=on, how=how)
    
    logger.info(f"Using chunked merge for large DataFrames "
               f"({len(left)}x{len(right)} potential combinations)")
    
    # Process in chunks
    results = []
    
    for i in range(0, len(left), chunk_size):
        left_chunk = left.iloc[i:i + chunk_size]
        
        # Merge with entire right DataFrame
        merged_chunk = pd.merge(left_chunk, right, on=on, how=how)
        
        if not merged_chunk.empty:
            results.append(merged_chunk)
        
        # Check memory
        if i % (chunk_size * 5) == 0:
            memory_stats = get_memory_monitor().get_memory_usage()
            if memory_stats['rss_mb'] > 1200:
                logger.info(f"Memory usage high during merge, triggering GC")
                trigger_gc()
    
    # Combine results
    if results:
        result = pd.concat(results, ignore_index=True)
        return optimize_dataframe_memory(result)
    else:
        return pd.DataFrame()


# Example implementation
class ExampleMemoryOptimizedPipeline(MemoryOptimizedPipeline):
    """Example implementation of a memory-optimized pipeline."""
    
    def _run_pipeline_logic(self, year: int, save: bool = True, **kwargs) -> Dict[str, Any]:
        """Example pipeline logic with memory optimization."""
        results = {}
        
        # Example: Load data with pooled DataFrame
        with self.pooled_dataframe(rows=10000, columns=['a', 'b', 'c']) as df:
            df['a'] = np.random.rand(10000)
            df['b'] = np.random.rand(10000)
            df['c'] = df['a'] + df['b']
            
            # Process data
            results['summary'] = df.describe()
        
        # Example: Batch processing
        large_data = pd.DataFrame({
            'x': np.random.rand(100000),
            'y': np.random.rand(100000)
        })
        
        def process_batch(batch):
            batch['z'] = batch['x'] * batch['y']
            return batch
        
        results['processed'] = batch_process_with_pool(
            large_data, 
            process_batch,
            batch_size=10000,
            use_pool=True
        )
        
        return results


if __name__ == "__main__":
    # Project imports
    from yemen_trade_diagnostic.interfaces.logging_interface import LogLevel, configure_logging
    configure_logging(log_level=LogLevel.DEBUG)
    
    # Run example pipeline
    pipeline = ExampleMemoryOptimizedPipeline(
        name="example",
        config={
            'use_memory_pool': True,
            'gc_threshold_mb': 500,
            'auto_gc': True
        }
    )
    
    results = pipeline.run(year=2023, save=False)
    
    # Check pool stats
    pool_stats = get_pool_stats()
    print("Pool Stats:", pool_stats)
    
    # Get memory report
    memory_report = get_memory_monitor().get_memory_report()
    print("Memory Report:", memory_report)