"""
Backward compatibility wrapper for cache_interface.

This module provides compatibility imports for code that hasn't been migrated yet.
DEPRECATED: Use yemen_trade_diagnostic.hardware.cache instead.
"""

import warnings

warnings.warn(
    "yemen_trade_diagnostic.utils.cache_interface is deprecated. "
    "Use yemen_trade_diagnostic.hardware.cache instead.",
    DeprecationWarning,
    stacklevel=2
)

from yemen_trade_diagnostic.hardware.cache import (
    Cache as CacheInterface,
    Cache as CacheManager,
    get_cache,
    get_cache as get_cache_manager,
    StorageTier as CacheLevel,
)

# Provide old function names for compatibility
cache_interface = CacheInterface
cache_manager = CacheManager

__all__ = [
    'CacheInterface',
    'CacheManager', 
    'get_cache',
    'get_cache_manager',
    'CacheLevel',
    'cache_interface',
    'cache_manager',
]
