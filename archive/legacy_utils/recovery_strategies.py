"""
Recovery Strategies System for Yemen Trade Diagnostic System

This module provides automated recovery strategies for failed components
and operations, enabling self-healing capabilities.
"""

# Standard library imports
import logging
import threading
import time
from dataclasses import dataclass
from enum import Enum
from functools import wraps
from typing import Any, Callable, Dict, List, Optional, TypeVar

T = TypeVar('T')

class RecoveryAction(Enum):
    """Types of recovery actions"""
    RETRY = "retry"
    RESTART = "restart"
    RESET = "reset"
    FALLBACK = "fallback"
    MANUAL = "manual"

@dataclass
class RecoveryStrategy:
    """Defines a recovery strategy for a component"""
    name: str
    component: str
    action: RecoveryAction
    max_attempts: int = 3
    retry_delay_seconds: float = 30
    backoff_multiplier: float = 2.0
    timeout_seconds: float = 300
    recovery_function: Optional[Callable] = None
    conditions: Dict[str, Any] = None

@dataclass
class RecoveryAttempt:
    """Records a recovery attempt"""
    strategy_name: str
    component: str
    timestamp: float
    success: bool
    attempt_number: int
    duration_seconds: float
    error_message: Optional[str] = None

class RecoveryManager:
    """Manages automated recovery strategies"""
    
    def __init__(self):
        self.strategies: Dict[str, List[RecoveryStrategy]] = {}
        self.recovery_history: List[RecoveryAttempt] = []
        self.active_recoveries: Dict[str, threading.Thread] = {}
        self.component_recovery_counts: Dict[str, int] = {}
        self.lock = threading.Lock()
        self.logger = logging.getLogger(__name__)
        
        # Register default recovery strategies
        self._register_default_strategies()
    
    def _register_default_strategies(self):
        """Register default recovery strategies"""
        
        # Data loader recovery
        self.register_strategy(RecoveryStrategy(
            name="data_loader_retry",
            component="data_loader",
            action=RecoveryAction.RETRY,
            max_attempts=3,
            retry_delay_seconds=10,
            recovery_function=self._recover_data_loader
        ))
        
        # Database connection recovery
        self.register_strategy(RecoveryStrategy(
            name="database_reconnect",
            component="database",
            action=RecoveryAction.RESTART,
            max_attempts=5,
            retry_delay_seconds=30,
            recovery_function=self._recover_database_connection
        ))
        
        # Cache system recovery
        self.register_strategy(RecoveryStrategy(
            name="cache_reset",
            component="cache",
            action=RecoveryAction.RESET,
            max_attempts=2,
            retry_delay_seconds=5,
            recovery_function=self._recover_cache_system
        ))
        
        # Hardware acceleration recovery
        self.register_strategy(RecoveryStrategy(
            name="hardware_reset",
            component="hardware_acceleration",
            action=RecoveryAction.RESET,
            max_attempts=2,
            retry_delay_seconds=15,
            recovery_function=self._recover_hardware_acceleration
        ))
        
        # Pipeline recovery
        self.register_strategy(RecoveryStrategy(
            name="pipeline_restart",
            component="pipeline",
            action=RecoveryAction.RESTART,
            max_attempts=3,
            retry_delay_seconds=60,
            recovery_function=self._recover_pipeline
        ))
    
    def register_strategy(self, strategy: RecoveryStrategy):
        """Register a recovery strategy"""
        if strategy.component not in self.strategies:
            self.strategies[strategy.component] = []
        
        self.strategies[strategy.component].append(strategy)
        self.logger.info(f"Registered recovery strategy '{strategy.name}' for component '{strategy.component}'")
    
    def trigger_recovery(self, component: str, error: Exception) -> bool:
        """Trigger recovery for a failed component"""
        if component not in self.strategies:
            self.logger.warning(f"No recovery strategies available for component '{component}'")
            return False
        
        # Check if recovery is already in progress
        with self.lock:
            if component in self.active_recoveries:
                self.logger.info(f"Recovery already in progress for component '{component}'")
                return False
        
        # Start recovery in background thread
        recovery_thread = threading.Thread(
            target=self._execute_recovery,
            args=(component, error),
            name=f"recovery-{component}"
        )
        recovery_thread.daemon = True
        
        with self.lock:
            self.active_recoveries[component] = recovery_thread
        
        recovery_thread.start()
        return True
    
    def _execute_recovery(self, component: str, error: Exception):
        """Execute recovery strategies for a component"""
        try:
            strategies = self.strategies[component]
            
            for strategy in strategies:
                if self._should_attempt_recovery(strategy, component):
                    success = self._attempt_recovery(strategy, component, error)
                    if success:
                        self.logger.info(f"Recovery successful for component '{component}' using strategy '{strategy.name}'")
                        break
                else:
                    self.logger.warning(f"Skipping recovery strategy '{strategy.name}' for '{component}' due to conditions")
            
        except Exception as e:
            self.logger.error(f"Recovery execution failed for component '{component}': {e}")
        finally:
            with self.lock:
                self.active_recoveries.pop(component, None)
    
    def _should_attempt_recovery(self, strategy: RecoveryStrategy, component: str) -> bool:
        """Determine if a recovery strategy should be attempted"""
        # Check recovery count limits
        recovery_count = self.component_recovery_counts.get(component, 0)
        if recovery_count >= strategy.max_attempts:
            return False
        
        # Check if enough time has passed since last recovery attempt
        recent_attempts = [
            attempt for attempt in self.recovery_history
            if attempt.component == component and
            time.time() - attempt.timestamp < strategy.retry_delay_seconds
        ]
        
        if recent_attempts:
            return False
        
        return True
    
    def _attempt_recovery(self, strategy: RecoveryStrategy, component: str, error: Exception) -> bool:
        """Attempt a single recovery strategy"""
        start_time = time.time()
        attempt_number = self.component_recovery_counts.get(component, 0) + 1
        
        try:
            self.logger.info(f"Attempting recovery '{strategy.name}' for component '{component}' (attempt {attempt_number})")
            
            if strategy.recovery_function:
                # Execute custom recovery function
                result = strategy.recovery_function(component, error)
                success = result is not False  # Assume success unless explicitly False
            else:
                # Default recovery based on action type
                success = self._execute_default_recovery(strategy, component, error)
            
            duration = time.time() - start_time
            
            # Record recovery attempt
            attempt = RecoveryAttempt(
                strategy_name=strategy.name,
                component=component,
                timestamp=start_time,
                success=success,
                attempt_number=attempt_number,
                duration_seconds=duration
            )
            
            self.recovery_history.append(attempt)
            
            # Update recovery count
            if success:
                self.component_recovery_counts[component] = 0  # Reset on success
            else:
                self.component_recovery_counts[component] = attempt_number
            
            return success
            
        except Exception as recovery_error:
            duration = time.time() - start_time
            
            attempt = RecoveryAttempt(
                strategy_name=strategy.name,
                component=component,
                timestamp=start_time,
                success=False,
                attempt_number=attempt_number,
                duration_seconds=duration,
                error_message=str(recovery_error)
            )
            
            self.recovery_history.append(attempt)
            self.component_recovery_counts[component] = attempt_number
            
            self.logger.error(f"Recovery attempt failed: {recovery_error}")
            return False
    
    def _execute_default_recovery(self, strategy: RecoveryStrategy, component: str, error: Exception) -> bool:
        """Execute default recovery actions"""
        if strategy.action == RecoveryAction.RETRY:
            # Wait and return success (actual retry handled by caller)
            time.sleep(strategy.retry_delay_seconds)
            return True
        
        elif strategy.action == RecoveryAction.RESTART:
            # Attempt to restart the component
            self.logger.info(f"Restarting component '{component}'")
            # Implementation would restart the specific component
            return True
        
        elif strategy.action == RecoveryAction.RESET:
            # Reset component state
            self.logger.info(f"Resetting component '{component}'")
            # Implementation would reset component state
            return True
        
        elif strategy.action == RecoveryAction.FALLBACK:
            # Switch to fallback mode
            self.logger.info(f"Switching component '{component}' to fallback mode")
            return True
        
        else:
            # Manual intervention required
            self.logger.warning(f"Manual intervention required for component '{component}'")
            return False
    
    def get_recovery_status(self) -> Dict[str, Any]:
        """Get recovery system status"""
        with self.lock:
            active_recoveries = list(self.active_recoveries.keys())
        
        recent_attempts = [
            attempt for attempt in self.recovery_history
            if time.time() - attempt.timestamp < 3600  # Last hour
        ]
        
        successful_recoveries = sum(1 for attempt in recent_attempts if attempt.success)
        failed_recoveries = len(recent_attempts) - successful_recoveries
        
        return {
            "active_recoveries": active_recoveries,
            "recent_attempts": len(recent_attempts),
            "successful_recoveries": successful_recoveries,
            "failed_recoveries": failed_recoveries,
            "success_rate": successful_recoveries / len(recent_attempts) if recent_attempts else 0,
            "component_recovery_counts": self.component_recovery_counts.copy(),
            "available_strategies": {
                component: [s.name for s in strategies]
                for component, strategies in self.strategies.items()
            }
        }
    
    # Default recovery functions
    def _recover_data_loader(self, component: str, error: Exception) -> bool:
        """Recover data loader component"""
        try:
            # Clear any cached connections or state
            # Reinitialize data loader
            self.logger.info("Recovering data loader...")
            return True
        except Exception as e:
            self.logger.error(f"Data loader recovery failed: {e}")
            return False
    
    def _recover_database_connection(self, component: str, error: Exception) -> bool:
        """Recover database connection"""
        try:
            # Close existing connections
            # Establish new connections
            self.logger.info("Recovering database connection...")
            return True
        except Exception as e:
            self.logger.error(f"Database recovery failed: {e}")
            return False
    
    def _recover_cache_system(self, component: str, error: Exception) -> bool:
        """Recover cache system"""
        try:
            # Clear cache
            # Reinitialize cache system
            self.logger.info("Recovering cache system...")
            return True
        except Exception as e:
            self.logger.error(f"Cache recovery failed: {e}")
            return False
    
    def _recover_hardware_acceleration(self, component: str, error: Exception) -> bool:
        """Recover hardware acceleration"""
        try:
            # Reset hardware acceleration state
            # Reinitialize hardware detection
            self.logger.info("Recovering hardware acceleration...")
            return True
        except Exception as e:
            self.logger.error(f"Hardware acceleration recovery failed: {e}")
            return False
    
    def _recover_pipeline(self, component: str, error: Exception) -> bool:
        """Recover pipeline component"""
        try:
            # Clear pipeline state
            # Reinitialize pipeline
            self.logger.info("Recovering pipeline...")
            return True
        except Exception as e:
            self.logger.error(f"Pipeline recovery failed: {e}")
            return False

# Global manager instance
_recovery_manager = None

def get_recovery_manager() -> RecoveryManager:
    """Get the global recovery manager"""
    global _recovery_manager
    if _recovery_manager is None:
        _recovery_manager = RecoveryManager()
    return _recovery_manager

def with_auto_recovery(component: str):
    """Decorator to add automatic recovery to functions"""
    def decorator(func: Callable[..., T]) -> Callable[..., T]:
        @wraps(func)
        def wrapper(*args, **kwargs) -> T:
            try:
                return func(*args, **kwargs)
            except Exception as e:
                manager = get_recovery_manager()
                recovery_triggered = manager.trigger_recovery(component, e)
                
                if recovery_triggered:
                    # Wait a bit for recovery to potentially complete
                    time.sleep(2)
                    # Re-raise the original exception
                    # Recovery will happen in background
                
                raise e
        return wrapper
    return decorator
