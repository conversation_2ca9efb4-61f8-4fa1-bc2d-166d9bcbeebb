"""
Error Handling Integration for Yemen Trade Diagnostic System

Integrates circuit breakers, graceful degradation, and recovery strategies
into the main system components.
"""

# Project imports
from yemen_trade_diagnostic.utils.circuit_breaker import CircuitBreakerConfig, with_circuit_breaker
from yemen_trade_diagnostic.utils.graceful_degradation import with_graceful_degradation
from yemen_trade_diagnostic.utils.recovery_strategies import with_auto_recovery


# Pre-configured decorators for common components
def robust_data_loader(func):
    """Complete error handling for data loader functions"""
    return with_auto_recovery("data_loader")(
        with_graceful_degradation("data_loading")(
            with_circuit_breaker("data_loader", CircuitBreakerConfig(
                failure_threshold=3,
                timeout_seconds=120
            ))(func)
        )
    )

def robust_pipeline(func):
    """Complete error handling for pipeline functions"""
    return with_auto_recovery("pipeline")(
        with_graceful_degradation("pipeline")(
            with_circuit_breaker("pipeline", CircuitBreakerConfig(
                failure_threshold=2,
                timeout_seconds=300
            ))(func)
        )
    )

def robust_chart_generator(func):
    """Complete error handling for chart generation functions"""
    return with_auto_recovery("chart_generation")(
        with_graceful_degradation("chart_generation")(
            with_circuit_breaker("chart_generator", CircuitBreakerConfig(
                failure_threshold=5,
                timeout_seconds=60
            ))(func)
        )
    )

def robust_hardware_operation(func):
    """Complete error handling for hardware acceleration functions"""
    return with_auto_recovery("hardware_acceleration")(
        with_graceful_degradation("hardware_acceleration")(
            with_circuit_breaker("hardware_acceleration", CircuitBreakerConfig(
                failure_threshold=3,
                timeout_seconds=30
            ))(func)
        )
    )
