# Legacy Utils Archive

This directory contains utility files that were safely archived during the unified error handling system migration (May 30, 2025).

## Archived Files and Reasons

### **Error Handling Duplicates**
These files contained functionality that is now handled by the unified error handling system (`yemen_trade_diagnostic.errors`):

- **`robust_decorators.py`** - Contained `@robust_data_loader`, `@robust_pipeline`, `@robust_chart_generator` decorators
  - **Replaced by**: `@protect` decorator with appropriate `OperationType`
  - **Migration**: Use `@protect("operation_name", OperationType.COMPUTATION)` instead

- **`recovery_strategies.py`** - Contained `RecoveryStrategy`, `RecoveryAction` classes and recovery management
  - **Replaced by**: Built-in recovery management in `errors.recovery_manager`
  - **Migration**: Recovery is now automatic with the `@protect` decorator

- **`pipeline_timeout.py`** - Contained `@with_timeout` decorator and timeout utilities
  - **Replaced by**: Built-in timeout handling in error configs
  - **Migration**: Use `ErrorConfig(timeout_seconds=N)` with `@protect`

### **Memory Optimization Files Using Old Patterns**
- **`memory_optimized_pipeline.py`** - Extended Pipeline class with memory optimization
  - **Replaced by**: Memory management is now integrated into the unified error system
  - **Migration**: Use standard Pipeline with `@protect` and `HARDWARE_CONFIG`

### **Deprecated Compatibility Wrappers**
- **`cache_interface.py`** - Backward compatibility wrapper for cache interface
  - **Replaced by**: Direct imports from `yemen_trade_diagnostic.hardware.cache`
  - **Migration**: Import directly from hardware.cache module

- **`distributed_cache.py`** - Placeholder implementation for distributed caching
  - **Status**: Not actively used, placeholder implementation
  - **Migration**: No migration needed (was not functional)

## Migration Examples

### Old Pattern:
```python
from yemen_trade_diagnostic.utils.robust_decorators import robust_pipeline
from yemen_trade_diagnostic.utils.pipeline_timeout import with_timeout

@robust_pipeline
@with_timeout(300)
def my_pipeline_function():
    pass
```

### New Pattern:
```python
from yemen_trade_diagnostic.errors import protect, OperationType, PIPELINE_CONFIG

@protect("my_pipeline_function", OperationType.PIPELINE_EXECUTION, config=PIPELINE_CONFIG)
def my_pipeline_function():
    pass
```

### Cache Migration:
```python
# Old
from yemen_trade_diagnostic.utils.cache_interface import get_cache_manager

# New  
from yemen_trade_diagnostic.hardware.cache import get_cache
```

## Benefits of New System

1. **Unified API**: Single entry point for all error handling
2. **Better Performance**: Optimized for different operation types
3. **Hardware Awareness**: Automatic optimization based on available hardware
4. **Comprehensive Monitoring**: Built-in metrics and health checks
5. **Modern Patterns**: Uses contemporary Python patterns and type hints

## Restoration

If you need to restore any of these files temporarily during migration:

```bash
# Copy back to utils directory
cp archive/legacy_utils/filename.py src/yemen_trade_diagnostic/utils/

# Remember to update imports and migrate to new system
```

**Note**: These files should only be restored temporarily during migration. The new unified error handling system provides all the functionality with better performance and cleaner APIs.

---
**Archive Date**: May 30, 2025  
**Migration**: Unified Error Handling System v2.0  
**Status**: Safe to delete after migration verification complete