"""Progressive caching system for RCA and other data-intensive pipelines.

This module provides a progressive caching system that allows for intermediate 
results to be cached during long-running operations, making subsequent runs faster
and enabling recovery from failures.
"""

# Standard library imports
import hashlib
import json
import os
import pickle
import time
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional, Set, Tuple, Union

# Third-party imports
import pandas as pd

# Project imports
from yemen_trade_diagnostic.interfaces.logging_interface import get_logger

logger = get_logger(__name__)

class ProgressiveCache:
    """Progressive caching system for data-intensive pipelines."""
    
    def __init__(self, cache_dir: Union[str, Path], 
                 max_age_hours: float = 24.0,
                 compression: str = "gzip",
                 invalidation_keys: Optional[Set[str]] = None):
        """Initialize the progressive cache.
        
        Args:
            cache_dir: Directory to store cache files
            max_age_hours: Maximum age of cache files in hours
            compression: Compression method for DataFrame storage ('gzip', 'bz2', 'xz', etc.)
            invalidation_keys: Set of keys to check for invalidation
        """
        self.cache_dir = Path(cache_dir)
        self.max_age_seconds = max_age_hours * 3600
        self.compression = compression
        self.invalidation_keys = invalidation_keys or set()
        
        # Create cache directory if it doesn't exist
        os.makedirs(self.cache_dir, exist_ok=True)
        
        # Create metadata directory
        self.metadata_dir = self.cache_dir / "metadata"
        os.makedirs(self.metadata_dir, exist_ok=True)
        
        # Load cache manifest
        self.manifest_path = self.cache_dir / "manifest.json"
        self.manifest = self._load_manifest()
    
    def _load_manifest(self) -> Dict[str, Any]:
        """Load the cache manifest file."""
        if not self.manifest_path.exists():
            return {"entries": {}, "last_cleaned": time.time()}
            
        try:
            with open(self.manifest_path, 'r') as f:
                return json.load(f)
        except Exception as e:
            logger.warning(f"Failed to load cache manifest: {e}")
            return {"entries": {}, "last_cleaned": time.time()}
    
    def _save_manifest(self):
        """Save the cache manifest file."""
        try:
            with open(self.manifest_path, 'w') as f:
                json.dump(self.manifest, f, indent=2)
        except Exception as e:
            logger.warning(f"Failed to save cache manifest: {e}")
    
    def _generate_key(self, prefix: str, params: Dict[str, Any]) -> str:
        """Generate a cache key from a prefix and parameters."""
        # Create deterministic string representation of parameters
        param_str = json.dumps(params, sort_keys=True)
        
        # Generate hash
        key_hash = hashlib.md5(param_str.encode()).hexdigest()
        
        return f"{prefix}_{key_hash}"
    
    def _get_cache_path(self, key: str, is_df: bool = True) -> Path:
        """Get the path for a cached item."""
        extension = ".parquet" if is_df else ".pkl"
        return self.cache_dir / f"{key}{extension}"
    
    def _get_metadata_path(self, key: str) -> Path:
        """Get the path for metadata about a cached item."""
        return self.metadata_dir / f"{key}.json"
    
    def get(self, prefix: str, params: Dict[str, Any], default=None) -> Any:
        """Get an item from the cache."""
        key = self._generate_key(prefix, params)
        
        # Check if key exists in manifest
        if key not in self.manifest["entries"]:
            return default
            
        entry = self.manifest["entries"][key]
        
        # Check if cache is too old
        if time.time() - entry["timestamp"] > self.max_age_seconds:
            logger.info(f"Cache entry '{key}' expired")
            return default
            
        # Check if cache should be invalidated
        if any(k in self.invalidation_keys for k in entry.get("depends_on", [])):
            logger.info(f"Cache entry '{key}' invalidated due to dependency changes")
            return default
        
        # Get cache path based on type
        is_df = entry.get("type") == "dataframe"
        cache_path = self._get_cache_path(key, is_df)
        
        # Check if cache file exists
        if not cache_path.exists():
            logger.warning(f"Cache file '{cache_path}' not found")
            return default
        
        # Load from cache
        try:
            if is_df:
                return pd.read_parquet(cache_path)
            else:
                with open(cache_path, 'rb') as f:
                    return pickle.load(f)
        except Exception as e:
            logger.warning(f"Failed to load cache entry '{key}': {e}")
            return default
    
    def set(self, prefix: str, params: Dict[str, Any], value: Any, 
            depends_on: Optional[List[str]] = None) -> bool:
        """Set an item in the cache."""
        key = self._generate_key(prefix, params)
        
        # Determine value type
        is_df = isinstance(value, pd.DataFrame)
        value_type = "dataframe" if is_df else "object"
        
        # Store in cache
        cache_path = self._get_cache_path(key, is_df)
        metadata_path = self._get_metadata_path(key)
        
        try:
            # Save the value
            if is_df:
                value.to_parquet(cache_path, compression=self.compression)
            else:
                with open(cache_path, 'wb') as f:
                    pickle.dump(value, f)
            
            # Update manifest
            self.manifest["entries"][key] = {
                "type": value_type,
                "timestamp": time.time(),
                "path": str(cache_path),
                "size_bytes": os.path.getsize(cache_path),
                "depends_on": depends_on or []
            }
            
            # Save metadata
            with open(metadata_path, 'w') as f:
                json.dump({
                    "prefix": prefix,
                    "params": params,
                    "timestamp": time.time(),
                    "type": value_type,
                    "depends_on": depends_on or []
                }, f, indent=2)
            
            # Save manifest
            self._save_manifest()
            
            return True
        except Exception as e:
            logger.warning(f"Failed to set cache entry '{key}': {e}")
            return False
    
    def invalidate(self, prefix: str, params: Dict[str, Any]) -> bool:
        """Invalidate a specific cache entry."""
        key = self._generate_key(prefix, params)
        
        if key not in self.manifest["entries"]:
            return False
            
        # Remove from manifest
        entry = self.manifest["entries"].pop(key)
        
        # Delete cache file if it exists
        try:
            cache_path = Path(entry["path"])
            if cache_path.exists():
                os.remove(cache_path)
                
            # Delete metadata file if it exists
            metadata_path = self._get_metadata_path(key)
            if metadata_path.exists():
                os.remove(metadata_path)
                
            # Save manifest
            self._save_manifest()
            
            return True
        except Exception as e:
            logger.warning(f"Failed to invalidate cache entry '{key}': {e}")
            return False
    
    def invalidate_prefix(self, prefix: str) -> int:
        """Invalidate all cache entries with a given prefix."""
        keys_to_remove = [k for k in self.manifest["entries"] if k.startswith(f"{prefix}_")]
        
        count = 0
        for key in keys_to_remove:
            try:
                entry = self.manifest["entries"].pop(key)
                
                # Delete cache file if it exists
                cache_path = Path(entry["path"])
                if cache_path.exists():
                    os.remove(cache_path)
                    
                # Delete metadata file if it exists
                metadata_path = self._get_metadata_path(key)
                if metadata_path.exists():
                    os.remove(metadata_path)
                    
                count += 1
            except Exception as e:
                logger.warning(f"Failed to invalidate cache entry '{key}': {e}")
        
        # Save manifest
        self._save_manifest()
        
        return count
    
    def clean(self, force: bool = False) -> int:
        """Clean expired cache entries.
        
        Args:
            force: Force cleaning regardless of when last cleaning occurred
            
        Returns:
            Number of entries cleaned
        """
        # Check if cleaning is needed
        last_cleaned = self.manifest.get("last_cleaned", 0)
        if not force and time.time() - last_cleaned < 3600:  # Don't clean more than once per hour
            return 0
            
        # Get expired keys
        now = time.time()
        expired_keys = [
            k for k, v in self.manifest["entries"].items()
            if now - v["timestamp"] > self.max_age_seconds
        ]
        
        count = 0
        for key in expired_keys:
            try:
                entry = self.manifest["entries"].pop(key)
                
                # Delete cache file if it exists
                cache_path = Path(entry["path"])
                if cache_path.exists():
                    os.remove(cache_path)
                    
                # Delete metadata file if it exists
                metadata_path = self._get_metadata_path(key)
                if metadata_path.exists():
                    os.remove(metadata_path)
                    
                count += 1
            except Exception as e:
                logger.warning(f"Failed to clean cache entry '{key}': {e}")
        
        # Update last cleaned timestamp
        self.manifest["last_cleaned"] = now
        
        # Save manifest
        self._save_manifest()
        
        return count
    
    def get_stats(self) -> Dict[str, Any]:
        """Get statistics about the cache."""
        entries = self.manifest["entries"]
        
        # Calculate total size
        total_size = sum(entry.get("size_bytes", 0) for entry in entries.values())
        
        # Count by type
        types = {}
        for entry in entries.values():
            entry_type = entry.get("type", "unknown")
            types[entry_type] = types.get(entry_type, 0) + 1
        
        # Get oldest and newest
        timestamps = [entry["timestamp"] for entry in entries.values()]
        oldest = min(timestamps) if timestamps else None
        newest = max(timestamps) if timestamps else None
        
        return {
            "count": len(entries),
            "total_size_bytes": total_size,
            "total_size_mb": total_size / (1024 * 1024),
            "types": types,
            "oldest_timestamp": oldest,
            "newest_timestamp": newest,
            "last_cleaned": self.manifest.get("last_cleaned")
        }

# Create global cache instance
_progressive_cache = None

def get_progressive_cache() -> ProgressiveCache:
    """Get the global progressive cache instance."""
    global _progressive_cache
    if _progressive_cache is None:
        cache_dir = Path("data/progressive_cache")
        _progressive_cache = ProgressiveCache(cache_dir)
    return _progressive_cache

def cached_dataframe(prefix: str, invalidation_key: Optional[str] = None):
    """Decorator to cache DataFrame results of a function."""
    def decorator(func):
        def wrapper(*args, **kwargs):
            # Get cache
            cache = get_progressive_cache()
            
            # Generate params from args and kwargs
            params = {
                "args": [str(arg) for arg in args],
                "kwargs": {k: str(v) for k, v in kwargs.items()}
            }
            
            # Check for cached result
            cached_result = cache.get(prefix, params)
            if cached_result is not None:
                logger.info(f"Cache hit for {prefix}")
                return cached_result
            
            # Call the original function
            logger.info(f"Cache miss for {prefix}, calculating...")
            result = func(*args, **kwargs)
            
            # Cache the result
            depends_on = [invalidation_key] if invalidation_key else []
            cache.set(prefix, params, result, depends_on=depends_on)
            
            return result
        return wrapper
    return decorator