"""
Intelligent Cache Warming - Predictive Cache Pre-loading for Higher Hit Rates

This module implements intelligent cache warming strategies based on usage patterns,
temporal analysis, and predictive algorithms to proactively load data before it's needed.
"""

# Standard library imports
import json
import os
import threading
import time
from dataclasses import asdict, dataclass
from datetime import datetime, timed<PERSON><PERSON>
from enum import Enum
from typing import Any, Callable, Dict, List, Optional, Set, Tuple

# Project imports
from yemen_trade_diagnostic.interfaces.cache_interface import (
    CacheLevel,
    CachePriority,
    DataLifetime,
    get_cache_manager,
)
from yemen_trade_diagnostic.interfaces.hardware_interface import get_hardware_manager
from yemen_trade_diagnostic.interfaces.logging_interface import get_logger
from yemen_trade_diagnostic.utils.cache_key_optimizer import CacheKeyType, get_cache_key_optimizer

logger = get_logger(__name__)


class WarmingPriority(Enum):
    """Priority levels for cache warming operations."""
    CRITICAL = "critical"  # Must be warmed immediately
    HIGH = "high"  # Should be warmed soon
    MEDIUM = "medium"  # Warm when resources available
    LOW = "low"  # Warm during idle time


@dataclass
class AccessPattern:
    """Represents an access pattern for predictive warming."""
    pattern_id: str
    loader_name: str
    path_pattern: str
    filters_pattern: Dict[str, Any]
    access_count: int
    last_accessed: datetime
    access_times: List[datetime]
    average_interval: float  # Average time between accesses
    next_predicted_access: Optional[datetime]
    warming_priority: WarmingPriority
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        data = asdict(self)
        # Convert datetime objects to ISO strings
        data['last_accessed'] = self.last_accessed.isoformat()
        data['access_times'] = [t.isoformat() for t in self.access_times]
        if self.next_predicted_access:
            data['next_predicted_access'] = self.next_predicted_access.isoformat()
        data['warming_priority'] = self.warming_priority.value
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AccessPattern':
        """Create from dictionary."""
        # Convert ISO strings back to datetime objects
        data['last_accessed'] = datetime.fromisoformat(data['last_accessed'])
        data['access_times'] = [datetime.fromisoformat(t) for t in data['access_times']]
        if data.get('next_predicted_access'):
            data['next_predicted_access'] = datetime.fromisoformat(data['next_predicted_access'])
        data['warming_priority'] = WarmingPriority(data['warming_priority'])
        return cls(**data)


class IntelligentCacheWarmer:
    """Intelligent cache warming system with predictive capabilities."""
    
    def __init__(self, 
                 patterns_file: str = ".cache/warming_patterns.json",
                 max_patterns: int = 1000,
                 warming_threads: int = 2):
        """
        Initialize the intelligent cache warmer.
        
        Args:
            patterns_file: File to store learned access patterns
            max_patterns: Maximum number of patterns to track
            warming_threads: Number of background warming threads
        """
        self.patterns_file = patterns_file
        self.max_patterns = max_patterns
        self.warming_threads = warming_threads
        
        # Core components - delay cache_manager import to avoid circular dependency
        self.cache_manager = None
        self.hardware_manager = get_hardware_manager()
        self.key_optimizer = get_cache_key_optimizer()
        
        # Access pattern tracking
        self.access_patterns: Dict[str, AccessPattern] = {}
        self.pattern_lock = threading.RLock()
        
        # Warming control
        self.warming_active = False
        self.warming_executor = None
        self.warming_queue: List[Tuple[str, Callable, WarmingPriority]] = []
        self.queue_lock = threading.Lock()
        
        # Statistics
        self.stats = {
            "patterns_learned": 0,
            "successful_predictions": 0,
            "warming_operations": 0,
            "cache_hits_from_warming": 0,
            "total_warming_time": 0.0
        }
        
        # Load existing patterns
        self._load_patterns()
        
        # Start background warming if hardware allows
        if self.hardware_manager.get_optimal_thread_count() > 4:
            self._start_background_warming()
        
        logger.info(f"Initialized intelligent cache warmer with {len(self.access_patterns)} learned patterns")
    
    def _get_cache_manager(self):
        """Get cache manager with lazy loading to avoid circular dependency."""
        if self.cache_manager is None:
            # Project imports
            from yemen_trade_diagnostic.interfaces.cache_interface import get_cache_manager
            self.cache_manager = get_cache_manager()
        return self.cache_manager
    
    def record_access(self, 
                     loader_name: str, 
                     path_or_identifier: str,
                     filters: Optional[Dict[str, Any]] = None) -> None:
        """
        Record an access for pattern learning.
        
        Args:
            loader_name: Name of the data loader
            path_or_identifier: Path or identifier accessed
            filters: Filters used in the access
        """
        pattern_id = self._generate_pattern_id(loader_name, path_or_identifier, filters)
        current_time = datetime.now()
        
        with self.pattern_lock:
            if pattern_id in self.access_patterns:
                # Update existing pattern
                pattern = self.access_patterns[pattern_id]
                pattern.access_count += 1
                pattern.access_times.append(current_time)
                pattern.last_accessed = current_time
                
                # Update average interval
                if len(pattern.access_times) > 1:
                    intervals = []
                    for i in range(1, len(pattern.access_times)):
                        interval = (pattern.access_times[i] - pattern.access_times[i-1]).total_seconds()
                        intervals.append(interval)
                    pattern.average_interval = sum(intervals) / len(intervals)
                    
                    # Predict next access
                    pattern.next_predicted_access = current_time + timedelta(seconds=pattern.average_interval)
                    
                    # Update priority based on frequency
                    pattern.warming_priority = self._calculate_warming_priority(pattern)
                
                # Keep only recent access times (last 50)
                if len(pattern.access_times) > 50:
                    pattern.access_times = pattern.access_times[-50:]
            else:
                # Create new pattern
                if len(self.access_patterns) < self.max_patterns:
                    pattern = AccessPattern(
                        pattern_id=pattern_id,
                        loader_name=loader_name,
                        path_pattern=path_or_identifier,
                        filters_pattern=filters or {},
                        access_count=1,
                        last_accessed=current_time,
                        access_times=[current_time],
                        average_interval=0.0,
                        next_predicted_access=None,
                        warming_priority=WarmingPriority.LOW
                    )
                    self.access_patterns[pattern_id] = pattern
                    self.stats["patterns_learned"] += 1
        
        # Schedule pattern persistence (async)
        if len(self.access_patterns) % 10 == 0:  # Persist every 10 accesses
            threading.Thread(target=self._save_patterns, daemon=True).start()
    
    def predict_and_warm(self, 
                        lookahead_minutes: int = 30,
                        max_warming_operations: int = 20) -> Dict[str, Any]:
        """
        Predict future accesses and warm cache proactively.
        
        Args:
            lookahead_minutes: How far ahead to predict
            max_warming_operations: Maximum warming operations to queue
            
        Returns:
            Dictionary with warming results
        """
        current_time = datetime.now()
        lookahead_time = current_time + timedelta(minutes=lookahead_minutes)
        
        warming_candidates = []
        
        with self.pattern_lock:
            for pattern in self.access_patterns.values():
                if (pattern.next_predicted_access and 
                    pattern.next_predicted_access <= lookahead_time and
                    pattern.next_predicted_access >= current_time):
                    
                    # Calculate confidence score
                    confidence = self._calculate_prediction_confidence(pattern)
                    
                    if confidence > 0.6:  # Only warm high-confidence predictions
                        warming_candidates.append((pattern, confidence))
        
        # Sort by priority and confidence
        warming_candidates.sort(key=lambda x: (x[0].warming_priority.value, x[1]), reverse=True)
        
        # Queue warming operations
        warmed_patterns = []
        for pattern, confidence in warming_candidates[:max_warming_operations]:
            warming_func = self._create_warming_function(pattern)
            self._queue_warming_operation(pattern.pattern_id, warming_func, pattern.warming_priority)
            warmed_patterns.append({
                "pattern_id": pattern.pattern_id,
                "confidence": confidence,
                "predicted_time": pattern.next_predicted_access.isoformat(),
                "priority": pattern.warming_priority.value
            })
        
        results = {
            "total_candidates": len(warming_candidates),
            "queued_operations": len(warmed_patterns),
            "patterns": warmed_patterns
        }
        
        logger.info(f"Predicted and queued {len(warmed_patterns)} warming operations")
        return results
    
    def warm_related_data(self, 
                         loader_name: str, 
                         path_or_identifier: str,
                         filters: Optional[Dict[str, Any]] = None) -> int:
        """
        Warm related data based on current access patterns.
        
        Args:
            loader_name: Current loader name
            path_or_identifier: Current path/identifier
            filters: Current filters
            
        Returns:
            Number of related items warmed
        """
        current_pattern_id = self._generate_pattern_id(loader_name, path_or_identifier, filters)
        related_patterns = self._find_related_patterns(current_pattern_id)
        
        warmed_count = 0
        for pattern in related_patterns[:5]:  # Limit to 5 related patterns
            warming_func = self._create_warming_function(pattern)
            self._queue_warming_operation(
                f"related_{pattern.pattern_id}", 
                warming_func, 
                WarmingPriority.MEDIUM
            )
            warmed_count += 1
        
        return warmed_count
    
    def get_warming_suggestions(self) -> List[Dict[str, Any]]:
        """Get suggestions for manual cache warming."""
        suggestions = []
        
        with self.pattern_lock:
            # Find frequently accessed patterns that aren't being predicted well
            for pattern in self.access_patterns.values():
                if (pattern.access_count > 10 and 
                    pattern.warming_priority in [WarmingPriority.HIGH, WarmingPriority.CRITICAL]):
                    
                    suggestion = {
                        "pattern_id": pattern.pattern_id,
                        "loader_name": pattern.loader_name,
                        "access_count": pattern.access_count,
                        "priority": pattern.warming_priority.value,
                        "suggestion": "Consider pre-loading during startup",
                        "estimated_benefit": "high" if pattern.access_count > 50 else "medium"
                    }
                    suggestions.append(suggestion)
        
        return suggestions
    
    def _generate_pattern_id(self, 
                           loader_name: str, 
                           path_or_identifier: str,
                           filters: Optional[Dict[str, Any]] = None) -> str:
        """Generate a pattern ID for tracking."""
        # Use semantic cache key for pattern identification
        semantic_key = self.key_optimizer.optimize_cache_key(
            loader_name, path_or_identifier, filters, CacheKeyType.SEMANTIC
        )
        return f"pattern_{semantic_key}"
    
    def _calculate_warming_priority(self, pattern: AccessPattern) -> WarmingPriority:
        """Calculate warming priority based on access pattern."""
        # High frequency = higher priority
        if pattern.access_count > 100:
            return WarmingPriority.CRITICAL
        elif pattern.access_count > 50:
            return WarmingPriority.HIGH
        elif pattern.access_count > 10:
            return WarmingPriority.MEDIUM
        else:
            return WarmingPriority.LOW
    
    def _calculate_prediction_confidence(self, pattern: AccessPattern) -> float:
        """Calculate confidence in access prediction."""
        if len(pattern.access_times) < 3:
            return 0.0
        
        # Calculate consistency of intervals
        intervals = []
        for i in range(1, len(pattern.access_times)):
            interval = (pattern.access_times[i] - pattern.access_times[i-1]).total_seconds()
            intervals.append(interval)
        
        if not intervals:
            return 0.0
        
        # Calculate coefficient of variation (lower = more predictable)
        mean_interval = sum(intervals) / len(intervals)
        if mean_interval == 0:
            return 0.0
        
        variance = sum((x - mean_interval) ** 2 for x in intervals) / len(intervals)
        std_deviation = variance ** 0.5
        cv = std_deviation / mean_interval
        
        # Convert to confidence (0-1 scale)
        confidence = max(0.0, 1.0 - cv)
        
        # Boost confidence for frequent accesses
        frequency_boost = min(0.3, pattern.access_count / 100)
        confidence = min(1.0, confidence + frequency_boost)
        
        return confidence
    
    def _find_related_patterns(self, pattern_id: str) -> List[AccessPattern]:
        """Find patterns related to the current pattern."""
        related = []
        
        if pattern_id not in self.access_patterns:
            return related
        
        current_pattern = self.access_patterns[pattern_id]
        
        with self.pattern_lock:
            for other_pattern in self.access_patterns.values():
                if other_pattern.pattern_id == pattern_id:
                    continue
                
                # Check for similarity
                similarity_score = self._calculate_pattern_similarity(current_pattern, other_pattern)
                if similarity_score > 0.7:
                    related.append(other_pattern)
        
        # Sort by similarity and recent access
        related.sort(key=lambda p: (self._calculate_pattern_similarity(current_pattern, p), 
                                  p.last_accessed), reverse=True)
        
        return related
    
    def _calculate_pattern_similarity(self, pattern1: AccessPattern, pattern2: AccessPattern) -> float:
        """Calculate similarity between two access patterns."""
        # Loader similarity
        loader_similarity = 1.0 if pattern1.loader_name == pattern2.loader_name else 0.0
        
        # Path similarity (simple string comparison)
        path_similarity = 0.0
        if pattern1.path_pattern and pattern2.path_pattern:
            common_parts = len(set(pattern1.path_pattern.split()) & set(pattern2.path_pattern.split()))
            total_parts = len(set(pattern1.path_pattern.split()) | set(pattern2.path_pattern.split()))
            path_similarity = common_parts / max(1, total_parts)
        
        # Filter similarity
        filter_similarity = 0.0
        if pattern1.filters_pattern and pattern2.filters_pattern:
            common_keys = set(pattern1.filters_pattern.keys()) & set(pattern2.filters_pattern.keys())
            total_keys = set(pattern1.filters_pattern.keys()) | set(pattern2.filters_pattern.keys())
            filter_similarity = len(common_keys) / max(1, len(total_keys))
        
        # Weighted average
        return (loader_similarity * 0.4 + path_similarity * 0.3 + filter_similarity * 0.3)
    
    def _create_warming_function(self, pattern: AccessPattern) -> Callable:
        """Create a warming function for a pattern."""
        def warm_pattern():
            try:
                # Import the appropriate loader
                if "baci" in pattern.loader_name.lower():
                    # Project imports
                    from yemen_trade_diagnostic.data.loaders import get_loader
                    loader = get_loader("baci")
                    data = loader.load(pattern.path_pattern, pattern.filters_pattern)
                    if data is not None:
                        self.stats["successful_predictions"] += 1
                        return True
                else:
                    # Generic warming approach
                    cache_key = self.key_optimizer.optimize_cache_key(
                        pattern.loader_name, 
                        pattern.path_pattern, 
                        pattern.filters_pattern
                    )
                    
                    # Check if already cached
                    if self.cache_manager.get(cache_key) is not None:
                        return True  # Already warmed
                        
                    # Simulate data loading (in practice, would load actual data)
                    logger.debug(f"Would warm cache for pattern: {pattern.pattern_id}")
                    return True
                    
            except Exception as e:
                logger.warning(f"Failed to warm pattern {pattern.pattern_id}: {e}")
                return False
        
        return warm_pattern
    
    def _queue_warming_operation(self, 
                                operation_id: str, 
                                warming_func: Callable,
                                priority: WarmingPriority) -> None:
        """Queue a warming operation for background execution."""
        with self.queue_lock:
            self.warming_queue.append((operation_id, warming_func, priority))
            # Sort queue by priority
            self.warming_queue.sort(key=lambda x: x[2].value, reverse=True)
    
    def _start_background_warming(self) -> None:
        """Start background warming threads."""
        if self.warming_active:
            return
        
        self.warming_active = True
        
        # Use hardware manager for optimal thread count
        thread_count = min(self.warming_threads, self.hardware_manager.get_optimal_thread_count() // 2)
        
        for i in range(thread_count):
            thread = threading.Thread(target=self._warming_worker, daemon=True, name=f"CacheWarmer-{i}")
            thread.start()
        
        logger.info(f"Started {thread_count} cache warming threads")
    
    def _warming_worker(self) -> None:
        """Background worker for cache warming."""
        while self.warming_active:
            operation = None
            
            with self.queue_lock:
                if self.warming_queue:
                    operation = self.warming_queue.pop(0)
            
            if operation:
                operation_id, warming_func, priority = operation
                
                start_time = time.time()
                try:
                    success = warming_func()
                    if success:
                        self.stats["warming_operations"] += 1
                        logger.debug(f"Successfully warmed: {operation_id}")
                    else:
                        logger.debug(f"Failed to warm: {operation_id}")
                except Exception as e:
                    logger.warning(f"Error in warming operation {operation_id}: {e}")
                finally:
                    self.stats["total_warming_time"] += time.time() - start_time
            else:
                # No operations in queue, sleep briefly
                time.sleep(1.0)
    
    def _load_patterns(self) -> None:
        """Load access patterns from file."""
        try:
            if os.path.exists(self.patterns_file):
                with open(self.patterns_file, 'r') as f:
                    data = json.load(f)
                    
                for pattern_data in data.get("patterns", []):
                    try:
                        pattern = AccessPattern.from_dict(pattern_data)
                        self.access_patterns[pattern.pattern_id] = pattern
                    except Exception as e:
                        logger.warning(f"Failed to load pattern: {e}")
                
                logger.info(f"Loaded {len(self.access_patterns)} access patterns")
        except Exception as e:
            logger.warning(f"Failed to load patterns file: {e}")
    
    def _save_patterns(self) -> None:
        """Save access patterns to file."""
        try:
            # Create directory if needed
            os.makedirs(os.path.dirname(self.patterns_file), exist_ok=True)
            
            patterns_data = []
            with self.pattern_lock:
                for pattern in self.access_patterns.values():
                    patterns_data.append(pattern.to_dict())
            
            data = {
                "timestamp": datetime.now().isoformat(),
                "stats": self.stats,
                "patterns": patterns_data
            }
            
            with open(self.patterns_file, 'w') as f:
                json.dump(data, f, indent=2)
                
        except Exception as e:
            logger.warning(f"Failed to save patterns: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get warming statistics."""
        with self.pattern_lock:
            stats = self.stats.copy()
            stats["tracked_patterns"] = len(self.access_patterns)
            stats["queue_size"] = len(self.warming_queue)
            return stats
    
    def shutdown(self) -> None:
        """Shutdown the warming system."""
        self.warming_active = False
        self._save_patterns()
        logger.info("Cache warming system shutdown")


# Global singleton instance
_intelligent_warmer_instance = None


def get_intelligent_cache_warmer() -> IntelligentCacheWarmer:
    """Get or create the singleton intelligent cache warmer instance.
    
    Returns:
        IntelligentCacheWarmer: The intelligent cache warmer instance
    """
    global _intelligent_warmer_instance
    if _intelligent_warmer_instance is None:
        _intelligent_warmer_instance = IntelligentCacheWarmer()
    return _intelligent_warmer_instance