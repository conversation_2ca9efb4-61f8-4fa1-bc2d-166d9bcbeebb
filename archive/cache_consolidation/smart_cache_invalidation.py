"""
Smart Cache Invalidation - Intelligent Cache Invalidation Strategies

This module implements intelligent cache invalidation strategies that improve
cache hit rates by selectively invalidating stale data while preserving
frequently accessed and still-valid cached items.
"""

# Standard library imports
import fnmatch
import re
import threading
import time
from dataclasses import dataclass
from datetime import datetime, timed<PERSON><PERSON>
from enum import Enum
from typing import Any, Callable, Dict, List, Optional, Pattern, Set

# Project imports
from yemen_trade_diagnostic.interfaces.cache_interface import <PERSON>acheLevel, get_cache_manager
from yemen_trade_diagnostic.interfaces.logging_interface import get_logger

logger = get_logger(__name__)


class InvalidationStrategy(Enum):
    """Strategies for cache invalidation."""
    TIME_BASED = "time_based"  # Based on TTL and last access
    DEPENDENCY_BASED = "dependency_based"  # Based on data dependencies
    PATTERN_BASED = "pattern_based"  # Based on key patterns
    USAGE_BASED = "usage_based"  # Based on access frequency
    SELECTIVE = "selective"  # Selective invalidation
    CASCADE = "cascade"  # Cascade invalidation


@dataclass
class InvalidationRule:
    """Rule for cache invalidation."""
    rule_id: str
    strategy: InvalidationStrategy
    pattern: str  # Pattern to match cache keys
    conditions: Dict[str, Any]  # Conditions for invalidation
    priority: int  # Higher priority = processed first
    enabled: bool = True
    
    def matches_key(self, cache_key: str) -> bool:
        """Check if this rule matches a cache key."""
        return fnmatch.fnmatch(cache_key, self.pattern)


@dataclass
class DependencyRelationship:
    """Represents a dependency relationship between cache entries."""
    parent_pattern: str  # Pattern for parent (source) data
    child_pattern: str   # Pattern for dependent (derived) data
    relationship_type: str  # Type of relationship (e.g., "aggregation", "transformation")
    invalidation_delay: float = 0.0  # Delay before invalidating dependent data


class SmartCacheInvalidator:
    """Intelligent cache invalidation system."""
    
    def __init__(self):
        """Initialize the smart cache invalidator."""
        self.cache_manager = get_cache_manager()
        
        # Invalidation rules
        self.invalidation_rules: Dict[str, InvalidationRule] = {}
        self.rules_lock = threading.RLock()
        
        # Dependency tracking
        self.dependencies: List[DependencyRelationship] = []
        self.dependency_lock = threading.RLock()
        
        # Access tracking for usage-based invalidation
        self.access_tracker: Dict[str, Dict[str, Any]] = {}
        self.tracker_lock = threading.RLock()
        
        # Statistics
        self.stats = {
            "total_invalidations": 0,
            "selective_invalidations": 0,
            "cascade_invalidations": 0,
            "time_based_invalidations": 0,
            "dependency_invalidations": 0,
            "pattern_based_invalidations": 0,
            "usage_based_invalidations": 0,
            "dependency_based_invalidations": 0,
            "false_invalidations": 0,  # Invalidations that were unnecessary
            "cache_preserved": 0  # Items that were preserved due to smart invalidation
        }
        
        # Initialize default rules
        self._setup_default_rules()
        
        logger.info("Initialized smart cache invalidator")
    
    def invalidate_by_pattern(self, 
                            pattern: str, 
                            level: CacheLevel = CacheLevel.ALL,
                            strategy: InvalidationStrategy = InvalidationStrategy.PATTERN_BASED) -> int:
        """
        Invalidate cache entries matching a pattern using specified strategy.
        
        Args:
            pattern: Pattern to match cache keys
            level: Cache level to invalidate
            strategy: Invalidation strategy to use
            
        Returns:
            Number of entries invalidated
        """
        invalidated_count = 0
        
        if strategy == InvalidationStrategy.SELECTIVE:
            invalidated_count = self._selective_invalidation(pattern, level)
        elif strategy == InvalidationStrategy.CASCADE:
            invalidated_count = self._cascade_invalidation(pattern, level)
        elif strategy == InvalidationStrategy.USAGE_BASED:
            invalidated_count = self._usage_based_invalidation(pattern, level)
        elif strategy == InvalidationStrategy.DEPENDENCY_BASED:
            invalidated_count = self._dependency_based_invalidation(pattern, level)
        else:
            # Default pattern-based invalidation
            invalidated_count = self._pattern_based_invalidation(pattern, level)
        
        self.stats["total_invalidations"] += invalidated_count
        self.stats[f"{strategy.value}_invalidations"] += invalidated_count
        
        logger.info(f"Invalidated {invalidated_count} entries using {strategy.value} strategy")
        return invalidated_count
    
    def invalidate_stale_data(self, 
                            max_age_hours: float = 24,
                            preserve_frequently_accessed: bool = True) -> int:
        """
        Invalidate stale data based on age and access patterns.
        
        Args:
            max_age_hours: Maximum age for cache entries in hours
            preserve_frequently_accessed: Whether to preserve frequently accessed items
            
        Returns:
            Number of entries invalidated
        """
        current_time = time.time()
        max_age_seconds = max_age_hours * 3600
        invalidated_count = 0
        
        # Get all cache keys (this would need to be implemented in the cache manager)
        # For now, we'll work with tracked access patterns
        with self.tracker_lock:
            stale_keys = []
            
            for cache_key, access_info in self.access_tracker.items():
                last_access = access_info.get("last_access", 0)
                access_count = access_info.get("access_count", 0)
                
                # Check if stale
                age = current_time - last_access
                if age > max_age_seconds:
                    # Decide whether to preserve based on access frequency
                    if preserve_frequently_accessed and access_count > 10:
                        # High-frequency item, extend TTL instead of invalidating
                        self.stats["cache_preserved"] += 1
                        continue
                    else:
                        stale_keys.append(cache_key)
            
            # Invalidate stale keys
            for key in stale_keys:
                if self.cache_manager.delete(key):
                    invalidated_count += 1
                    del self.access_tracker[key]
        
        self.stats["time_based_invalidations"] += invalidated_count
        logger.info(f"Invalidated {invalidated_count} stale cache entries")
        return invalidated_count
    
    def add_dependency_relationship(self, 
                                  parent_pattern: str,
                                  child_pattern: str,
                                  relationship_type: str = "derived",
                                  invalidation_delay: float = 0.0) -> None:
        """
        Add a dependency relationship between cache entries.
        
        Args:
            parent_pattern: Pattern for parent (source) data
            child_pattern: Pattern for dependent (derived) data
            relationship_type: Type of relationship
            invalidation_delay: Delay before invalidating dependent data
        """
        with self.dependency_lock:
            dependency = DependencyRelationship(
                parent_pattern=parent_pattern,
                child_pattern=child_pattern,
                relationship_type=relationship_type,
                invalidation_delay=invalidation_delay
            )
            self.dependencies.append(dependency)
        
        logger.info(f"Added dependency: {parent_pattern} -> {child_pattern}")
    
    def invalidate_dependencies(self, parent_key: str) -> int:
        """
        Invalidate all cache entries that depend on the given parent key.
        
        Args:
            parent_key: Key of the parent data that changed
            
        Returns:
            Number of dependent entries invalidated
        """
        invalidated_count = 0
        
        with self.dependency_lock:
            for dependency in self.dependencies:
                if fnmatch.fnmatch(parent_key, dependency.parent_pattern):
                    # Find and invalidate dependent entries
                    dependent_count = self._invalidate_dependent_entries(
                        dependency.child_pattern,
                        dependency.invalidation_delay
                    )
                    invalidated_count += dependent_count
        
        self.stats["dependency_invalidations"] += invalidated_count
        logger.info(f"Invalidated {invalidated_count} dependent cache entries")
        return invalidated_count
    
    def track_access(self, cache_key: str) -> None:
        """
        Track access to a cache key for usage-based invalidation.
        
        Args:
            cache_key: Key that was accessed
        """
        current_time = time.time()
        
        with self.tracker_lock:
            if cache_key not in self.access_tracker:
                self.access_tracker[cache_key] = {
                    "first_access": current_time,
                    "last_access": current_time,
                    "access_count": 1,
                    "access_frequency": 0.0
                }
            else:
                access_info = self.access_tracker[cache_key]
                access_info["last_access"] = current_time
                access_info["access_count"] += 1
                
                # Calculate access frequency (accesses per hour)
                time_span = current_time - access_info["first_access"]
                if time_span > 0:
                    access_info["access_frequency"] = access_info["access_count"] / (time_span / 3600)
    
    def add_invalidation_rule(self, 
                            rule_id: str,
                            strategy: InvalidationStrategy,
                            pattern: str,
                            conditions: Dict[str, Any],
                            priority: int = 100) -> None:
        """
        Add a custom invalidation rule.
        
        Args:
            rule_id: Unique identifier for the rule
            strategy: Invalidation strategy
            pattern: Pattern to match cache keys
            conditions: Conditions for invalidation
            priority: Rule priority (higher = processed first)
        """
        with self.rules_lock:
            rule = InvalidationRule(
                rule_id=rule_id,
                strategy=strategy,
                pattern=pattern,
                conditions=conditions,
                priority=priority
            )
            self.invalidation_rules[rule_id] = rule
        
        logger.info(f"Added invalidation rule: {rule_id}")
    
    def apply_invalidation_rules(self) -> int:
        """
        Apply all enabled invalidation rules.
        
        Returns:
            Total number of entries invalidated
        """
        total_invalidated = 0
        
        with self.rules_lock:
            # Sort rules by priority
            sorted_rules = sorted(self.invalidation_rules.values(), 
                                key=lambda r: r.priority, reverse=True)
            
            for rule in sorted_rules:
                if rule.enabled:
                    try:
                        count = self._apply_rule(rule)
                        total_invalidated += count
                        logger.debug(f"Rule {rule.rule_id} invalidated {count} entries")
                    except Exception as e:
                        logger.warning(f"Error applying rule {rule.rule_id}: {e}")
        
        return total_invalidated
    
    def _selective_invalidation(self, pattern: str, level: CacheLevel) -> int:
        """
        Selective invalidation that preserves frequently accessed items.
        
        Args:
            pattern: Pattern to match
            level: Cache level
            
        Returns:
            Number of entries invalidated
        """
        invalidated_count = 0
        current_time = time.time()
        
        with self.tracker_lock:
            matching_keys = []
            for key in self.access_tracker.keys():
                if fnmatch.fnmatch(key, pattern):
                    matching_keys.append(key)
            
            for key in matching_keys:
                access_info = self.access_tracker[key]
                
                # Decide whether to invalidate based on access patterns
                should_invalidate = self._should_invalidate_key(key, access_info, current_time)
                
                if should_invalidate:
                    if self.cache_manager.delete(key, level):
                        invalidated_count += 1
                        del self.access_tracker[key]
                else:
                    self.stats["cache_preserved"] += 1
        
        self.stats["selective_invalidations"] += invalidated_count
        return invalidated_count
    
    def _cascade_invalidation(self, pattern: str, level: CacheLevel) -> int:
        """
        Cascade invalidation that follows dependency relationships.
        
        Args:
            pattern: Pattern to match
            level: Cache level
            
        Returns:
            Number of entries invalidated
        """
        invalidated_count = 0
        processed_keys = set()
        
        # Find initial keys to invalidate
        initial_keys = self._find_matching_keys(pattern)
        
        # Process cascade invalidation
        keys_to_process = list(initial_keys)
        
        while keys_to_process:
            key = keys_to_process.pop(0)
            
            if key in processed_keys:
                continue
            
            processed_keys.add(key)
            
            # Invalidate the key
            if self.cache_manager.delete(key, level):
                invalidated_count += 1
                
                # Find dependent keys
                dependent_keys = self._find_dependent_keys(key)
                keys_to_process.extend(dependent_keys)
        
        self.stats["cascade_invalidations"] += invalidated_count
        return invalidated_count
    
    def _usage_based_invalidation(self, pattern: str, level: CacheLevel) -> int:
        """
        Usage-based invalidation that considers access frequency.
        
        Args:
            pattern: Pattern to match
            level: Cache level
            
        Returns:
            Number of entries invalidated
        """
        invalidated_count = 0
        current_time = time.time()
        
        with self.tracker_lock:
            matching_keys = []
            for key in self.access_tracker.keys():
                if fnmatch.fnmatch(key, pattern):
                    matching_keys.append(key)
            
            # Sort by access frequency (lowest first)
            matching_keys.sort(key=lambda k: self.access_tracker[k].get("access_frequency", 0))
            
            # Invalidate least frequently used items
            for key in matching_keys:
                access_info = self.access_tracker[key]
                frequency = access_info.get("access_frequency", 0)
                
                # Only invalidate if access frequency is very low
                if frequency < 0.1:  # Less than 0.1 accesses per hour
                    if self.cache_manager.delete(key, level):
                        invalidated_count += 1
                        del self.access_tracker[key]
                else:
                    self.stats["cache_preserved"] += 1
        
        return invalidated_count
    
    def _dependency_based_invalidation(self, pattern: str, level: CacheLevel) -> int:
        """
        Dependency-based invalidation following dependency relationships.
        
        Args:
            pattern: Pattern to match
            level: Cache level
            
        Returns:
            Number of entries invalidated
        """
        invalidated_count = 0
        
        # Find keys matching the pattern
        matching_keys = self._find_matching_keys(pattern)
        
        for key in matching_keys:
            # Invalidate the key itself
            if self.cache_manager.delete(key, level):
                invalidated_count += 1
            
            # Invalidate dependencies
            dependent_count = self.invalidate_dependencies(key)
            invalidated_count += dependent_count
        
        return invalidated_count
    
    def _pattern_based_invalidation(self, pattern: str, level: CacheLevel) -> int:
        """
        Simple pattern-based invalidation.
        
        Args:
            pattern: Pattern to match
            level: Cache level
            
        Returns:
            Number of entries invalidated
        """
        # This would use the cache manager's invalidate method
        # For now, simulate with tracked keys
        invalidated_count = 0
        
        with self.tracker_lock:
            matching_keys = []
            for key in self.access_tracker.keys():
                if fnmatch.fnmatch(key, pattern):
                    matching_keys.append(key)
            
            for key in matching_keys:
                if self.cache_manager.delete(key, level):
                    invalidated_count += 1
                    del self.access_tracker[key]
        
        return invalidated_count
    
    def _should_invalidate_key(self, 
                             key: str, 
                             access_info: Dict[str, Any], 
                             current_time: float) -> bool:
        """
        Determine if a key should be invalidated based on access patterns.
        
        Args:
            key: Cache key
            access_info: Access information
            current_time: Current timestamp
            
        Returns:
            True if key should be invalidated
        """
        # Time since last access
        last_access = access_info.get("last_access", 0)
        time_since_access = current_time - last_access
        
        # Access frequency
        access_frequency = access_info.get("access_frequency", 0)
        access_count = access_info.get("access_count", 0)
        
        # Invalidate if:
        # 1. Not accessed recently AND low frequency
        # 2. Very old regardless of frequency
        # 3. Never accessed much
        
        if time_since_access > 86400:  # 24 hours
            return True
        elif time_since_access > 3600 and access_frequency < 0.5:  # 1 hour, low frequency
            return True
        elif access_count < 2:  # Barely used
            return True
        else:
            return False
    
    def _find_matching_keys(self, pattern: str) -> List[str]:
        """Find cache keys matching a pattern."""
        matching_keys = []
        
        with self.tracker_lock:
            for key in self.access_tracker.keys():
                if fnmatch.fnmatch(key, pattern):
                    matching_keys.append(key)
        
        return matching_keys
    
    def _find_dependent_keys(self, parent_key: str) -> List[str]:
        """Find keys that depend on the given parent key."""
        dependent_keys = []
        
        with self.dependency_lock:
            for dependency in self.dependencies:
                if fnmatch.fnmatch(parent_key, dependency.parent_pattern):
                    # Find keys matching the child pattern
                    child_keys = self._find_matching_keys(dependency.child_pattern)
                    dependent_keys.extend(child_keys)
        
        return dependent_keys
    
    def _invalidate_dependent_entries(self, 
                                   child_pattern: str,
                                   delay: float = 0.0) -> int:
        """
        Invalidate entries matching child pattern with optional delay.
        
        Args:
            child_pattern: Pattern for dependent entries
            delay: Invalidation delay in seconds
            
        Returns:
            Number of entries invalidated
        """
        if delay > 0:
            # Schedule delayed invalidation
            def delayed_invalidation():
                time.sleep(delay)
                return self._pattern_based_invalidation(child_pattern, CacheLevel.ALL)
            
            thread = threading.Thread(target=delayed_invalidation, daemon=True)
            thread.start()
            return 0  # Will be counted later
        else:
            return self._pattern_based_invalidation(child_pattern, CacheLevel.ALL)
    
    def _apply_rule(self, rule: InvalidationRule) -> int:
        """Apply a specific invalidation rule."""
        if rule.strategy == InvalidationStrategy.TIME_BASED:
            max_age = rule.conditions.get("max_age_hours", 24)
            preserve_frequent = rule.conditions.get("preserve_frequently_accessed", True)
            return self.invalidate_stale_data(max_age, preserve_frequent)
        elif rule.strategy == InvalidationStrategy.SELECTIVE:
            return self._selective_invalidation(rule.pattern, CacheLevel.ALL)
        elif rule.strategy == InvalidationStrategy.CASCADE:
            return self._cascade_invalidation(rule.pattern, CacheLevel.ALL)
        elif rule.strategy == InvalidationStrategy.USAGE_BASED:
            return self._usage_based_invalidation(rule.pattern, CacheLevel.ALL)
        else:
            return self._pattern_based_invalidation(rule.pattern, CacheLevel.ALL)
    
    def _setup_default_rules(self) -> None:
        """Setup default invalidation rules."""
        # Rule 1: Time-based cleanup
        self.add_invalidation_rule(
            "daily_cleanup",
            InvalidationStrategy.TIME_BASED,
            "*",
            {"max_age_hours": 24, "preserve_frequently_accessed": True},
            priority=50
        )
        
        # Rule 2: Selective invalidation for temporary data
        self.add_invalidation_rule(
            "temp_data_cleanup",
            InvalidationStrategy.SELECTIVE,
            "*temp*",
            {},
            priority=80
        )
        
        # Rule 3: Cascade invalidation for derived data
        self.add_invalidation_rule(
            "derived_data_cascade",
            InvalidationStrategy.CASCADE,
            "*derived*",
            {},
            priority=90
        )
        
        # Add common dependency relationships
        self.add_dependency_relationship("*:baci:*", "*:composition:*", "aggregation")
        self.add_dependency_relationship("*:baci:*", "*:concentration:*", "calculation")
        self.add_dependency_relationship("*:worldbank:*", "*:indicators:*", "derived")
    
    def get_invalidation_stats(self) -> Dict[str, Any]:
        """Get invalidation statistics."""
        stats = self.stats.copy()
        stats["tracked_keys"] = len(self.access_tracker)
        stats["active_rules"] = len([r for r in self.invalidation_rules.values() if r.enabled])
        stats["dependency_relationships"] = len(self.dependencies)
        return stats
    
    def optimize_invalidation_rules(self) -> Dict[str, Any]:
        """Analyze and optimize invalidation rules based on performance."""
        optimization_report = {
            "recommendations": [],
            "rule_performance": {},
            "dependency_effectiveness": {}
        }
        
        # Analyze rule effectiveness
        total_invalidations = self.stats["total_invalidations"]
        false_invalidations = self.stats["false_invalidations"]
        
        if total_invalidations > 0:
            effectiveness = 1.0 - (false_invalidations / total_invalidations)
            optimization_report["overall_effectiveness"] = effectiveness
            
            if effectiveness < 0.8:
                optimization_report["recommendations"].append(
                    "Consider tuning invalidation rules - high false invalidation rate"
                )
        
        # Check cache preservation rate
        preserved = self.stats["cache_preserved"]
        if preserved > 0:
            preservation_rate = preserved / (preserved + total_invalidations)
            optimization_report["preservation_rate"] = preservation_rate
            
            if preservation_rate > 0.5:
                optimization_report["recommendations"].append(
                    "Good cache preservation - smart invalidation is working effectively"
                )
        
        return optimization_report


# Global singleton instance
_smart_invalidator_instance = None


def get_smart_cache_invalidator() -> SmartCacheInvalidator:
    """Get or create the singleton smart cache invalidator instance.
    
    Returns:
        SmartCacheInvalidator: The smart cache invalidator instance
    """
    global _smart_invalidator_instance
    if _smart_invalidator_instance is None:
        _smart_invalidator_instance = SmartCacheInvalidator()
    return _smart_invalidator_instance