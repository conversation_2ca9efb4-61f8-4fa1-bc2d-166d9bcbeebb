"""
Unit tests for the cache prewarm utility.

This module contains tests for the cache prewarm utility, which is used
to initialize and populate caches for improved performance.
"""

# Standard library imports
import os
import tempfile
import unittest
from pathlib import Path
from unittest.mock import MagicMock, patch

# Third-party imports
import numpy as np
import pandas as pd
import pytest

# Project imports
from yemen_trade_diagnostic.hardware.cache import (
    CachePrewarmer,
    get_cache_status,
    is_cache_prewarmed,
    prewarm_cache,
    with_prewarmed_cache,
)


class TestCachePrewarm(unittest.TestCase):
    """Tests for the cache prewarm utility."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create a temporary directory for cache
        self.temp_dir = tempfile.mkdtemp()
        self.cache_dir = os.path.join(self.temp_dir, "cache")
        os.makedirs(self.cache_dir, exist_ok=True)
        
        # Create a cache prewarmer
        self.prewarmer = CachePrewarmer(cache_dir=self.cache_dir)
        
        # Create a mock cache prewarmer
        self.mock_prewarmer = MagicMock(spec=CachePrewarmer)
        self.mock_prewarmer.prewarm.return_value = True
        self.mock_prewarmer.is_prewarmed.return_value = True
        self.mock_prewarmer.get_status.return_value = {
            'prewarmed': True,
            'cache_size': 1024 * 1024,
            'cache_items': 10,
            'last_prewarm_time': '2023-01-01T00:00:00'
        }
        
        # Patch the CachePrewarmer class
        self.prewarmer_patcher = patch('yemen_trade_diagnostic.utils.cache_prewarm.CachePrewarmer')
        self.mock_prewarmer_class = self.prewarmer_patcher.start()
        self.mock_prewarmer_class.return_value = self.mock_prewarmer
    
    def tearDown(self):
        """Tear down test fixtures."""
        self.prewarmer_patcher.stop()
        
        # Clean up temporary directory
        for root, dirs, files in os.walk(self.temp_dir, topdown=False):
            for file in files:
                os.remove(os.path.join(root, file))
            for dir in dirs:
                os.rmdir(os.path.join(root, dir))
        os.rmdir(self.temp_dir)
    
    def test_cache_prewarmer_initialization(self):
        """Test cache prewarmer initialization."""
        # Stop the patcher to test the real class
        self.prewarmer_patcher.stop()
        
        # Create a cache prewarmer
        prewarmer = CachePrewarmer(cache_dir=self.cache_dir)
        
        # Check that the prewarmer has the correct attributes
        self.assertEqual(prewarmer.cache_dir, self.cache_dir)
        self.assertFalse(prewarmer.is_prewarmed())
        
        # Restart the patcher
        self.prewarmer_patcher = patch('yemen_trade_diagnostic.utils.cache_prewarm.CachePrewarmer')
        self.mock_prewarmer_class = self.prewarmer_patcher.start()
        self.mock_prewarmer_class.return_value = self.mock_prewarmer
    
    def test_prewarm_cache(self):
        """Test prewarm_cache function."""
        # Prewarm cache
        result = prewarm_cache(cache_dir=self.cache_dir)
        
        # Check that the prewarm was successful
        self.assertTrue(result)
        
        # Check that the prewarmer's prewarm method was called
        self.mock_prewarmer.prewarm.assert_called_once()
    
    def test_is_cache_prewarmed(self):
        """Test is_cache_prewarmed function."""
        # Check if cache is prewarmed
        result = is_cache_prewarmed(cache_dir=self.cache_dir)
        
        # Check that the result is correct
        self.assertTrue(result)
        
        # Check that the prewarmer's is_prewarmed method was called
        self.mock_prewarmer.is_prewarmed.assert_called_once()
    
    def test_get_cache_status(self):
        """Test get_cache_status function."""
        # Get cache status
        status = get_cache_status(cache_dir=self.cache_dir)
        
        # Check that the status is correct
        self.assertEqual(status, {
            'prewarmed': True,
            'cache_size': 1024 * 1024,
            'cache_items': 10,
            'last_prewarm_time': '2023-01-01T00:00:00'
        })
        
        # Check that the prewarmer's get_status method was called
        self.mock_prewarmer.get_status.assert_called_once()
    
    def test_with_prewarmed_cache_decorator(self):
        """Test with_prewarmed_cache decorator."""
        # Define a function with the decorator
        @with_prewarmed_cache(cache_dir=self.cache_dir)
        def process_data(data):
            return data * 2
        
        # Call the function
        data = np.array([1, 2, 3, 4, 5])
        result = process_data(data)
        
        # Check that the result is correct
        np.testing.assert_array_equal(result, data * 2)
        
        # Check that the prewarmer's is_prewarmed and prewarm methods were called
        self.mock_prewarmer.is_prewarmed.assert_called()
        # If the cache is already prewarmed, prewarm should not be called
        self.mock_prewarmer.prewarm.assert_not_called()
    
    def test_with_prewarmed_cache_decorator_not_prewarmed(self):
        """Test with_prewarmed_cache decorator when cache is not prewarmed."""
        # Configure the mock to indicate cache is not prewarmed
        self.mock_prewarmer.is_prewarmed.return_value = False
        
        # Define a function with the decorator
        @with_prewarmed_cache(cache_dir=self.cache_dir)
        def process_data(data):
            return data * 2
        
        # Call the function
        data = np.array([1, 2, 3, 4, 5])
        result = process_data(data)
        
        # Check that the result is correct
        np.testing.assert_array_equal(result, data * 2)
        
        # Check that the prewarmer's is_prewarmed and prewarm methods were called
        self.mock_prewarmer.is_prewarmed.assert_called()
        self.mock_prewarmer.prewarm.assert_called()
    
    def test_cache_prewarmer_prewarm(self):
        """Test cache prewarmer prewarm method."""
        # Stop the patcher to test the real class
        self.prewarmer_patcher.stop()
        
        # Create a cache prewarmer
        prewarmer = CachePrewarmer(cache_dir=self.cache_dir)
        
        # Create some cache data
        cache_data = {
            'data1': pd.DataFrame({'A': [1, 2, 3], 'B': [4, 5, 6]}),
            'data2': np.array([1, 2, 3, 4, 5])
        }
        
        # Mock the _load_cache_data method
        prewarmer._load_cache_data = MagicMock(return_value=cache_data)
        
        # Prewarm the cache
        result = prewarmer.prewarm()
        
        # Check that the prewarm was successful
        self.assertTrue(result)
        
        # Check that the cache is now prewarmed
        self.assertTrue(prewarmer.is_prewarmed())
        
        # Check that the status file was created
        status_file = os.path.join(self.cache_dir, "cache_status.json")
        self.assertTrue(os.path.exists(status_file))
        
        # Restart the patcher
        self.prewarmer_patcher = patch('yemen_trade_diagnostic.utils.cache_prewarm.CachePrewarmer')
        self.mock_prewarmer_class = self.prewarmer_patcher.start()
        self.mock_prewarmer_class.return_value = self.mock_prewarmer


if __name__ == '__main__':
    unittest.main()
