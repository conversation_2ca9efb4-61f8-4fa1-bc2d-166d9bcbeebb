
"""Cache compression utilities for Yemen Trade Diagnostic."""

# Standard library imports
import gzip
import io
import lzma
import pickle
import zlib
from typing import Any, Dict, Optional, Tuple, Type, TypeVar, Union, cast

# Third-party imports
import numpy as np
import pandas as pd

# Project imports
from yemen_trade_diagnostic.hardware.core.detector import get_hardware_detector

# Import hardware acceleration components
from yemen_trade_diagnostic.hardware.memory.compression import (
    CompressionAlgorithm,
    CompressionConfig,
)
from yemen_trade_diagnostic.hardware.memory.compression import (
    MemoryCompressor as HWMemoryCompressor,
)
from yemen_trade_diagnostic.hardware.memory.compression import (
    get_memory_compressor as get_hw_memory_compressor,
)
from yemen_trade_diagnostic.interfaces.logging_interface import get_logger

logger = get_logger(__name__)

# Define type variables
T = TypeVar('T')


class HardwareAcceleratedCacheCompressor:
    """Cache compressor that uses hardware-accelerated memory compressor internally."""
    
    def __init__(self, compression_level: int = 6):
        """Initialize the hardware-accelerated cache compressor."""
        # Store original parameters for compatibility
        self.compression_level = compression_level
        self.min_size_bytes = 1024 * 10  # Only compress objects larger than 10KB
        
        # Get hardware detector
        self.hw_detector = get_hardware_detector()
        
        # Get hardware memory compressor with appropriate config
        config = CompressionConfig(
            algorithm=CompressionAlgorithm.AUTO,
            level=compression_level,
            m3_optimized=self.hw_detector.is_m3_pro,
            trade_data_mode=True
        )
        self.hw_memory_compressor = get_hw_memory_compressor(config)
        
        logger.info(f"Initialized hardware-accelerated cache compressor")
    
    def should_compress(self, value: Any) -> bool:
        """
        Determine if a value should be compressed.
        
        Args:
            value: Value to check
            
        Returns:
            bool: True if value should be compressed
        """
        # DataFrames with more than 1000 rows should be compressed
        if isinstance(value, pd.DataFrame) and len(value) > 1000:
            return True
        
        # Large arrays should be compressed
        if isinstance(value, np.ndarray) and value.size > 1000:
            return True
        
        # Check size of other objects
        try:
            size = len(pickle.dumps(value, protocol=4))
            return size > self.min_size_bytes
        except:
            return False
    
    def compress(self, value: T) -> Tuple[Any, Dict[str, Any]]:
        """
        Compress a value.
        
        Args:
            value: Value to compress
            
        Returns:
            Tuple of (compressed_data, metadata)
        """
        try:
            # Use hardware memory compressor if possible
            if isinstance(value, pd.DataFrame):
                return self._compress_dataframe(value)
            elif isinstance(value, np.ndarray):
                return self._compress_numpy_array(value)
            else:
                return self._compress_generic(value)
        except Exception as e:
            # Fallback to basic compression if hardware compression fails
            logger.warning(f"Hardware compression failed: {e}. Falling back to basic compression.")
            if isinstance(value, pd.DataFrame):
                return self._basic_compress_dataframe(value)
            elif isinstance(value, np.ndarray):
                return self._basic_compress_numpy_array(value)
            else:
                return self._basic_compress_generic(value)
    
    def _compress_dataframe(self, df: pd.DataFrame) -> Tuple[bytes, Dict[str, Any]]:
        """
        Compress a DataFrame using hardware acceleration.
        
        Args:
            df: DataFrame to compress
            
        Returns:
            Tuple of (compressed_data, metadata)
        """
        # Use hardware memory compressor
        compressed_data, metadata = self.hw_memory_compressor.compress_dataframe(df)
        
        # Ensure metadata has the required fields for backward compatibility
        if "compressed" not in metadata:
            metadata["compressed"] = True
        if "format" not in metadata:
            metadata["format"] = "hardware_compressed_dataframe"
        if "shape" not in metadata:
            metadata["shape"] = df.shape
        if "columns" not in metadata:
            metadata["columns"] = list(df.columns)
        
        return compressed_data, metadata
    
    def _compress_numpy_array(self, arr: np.ndarray) -> Tuple[bytes, Dict[str, Any]]:
        """Compress a NumPy array.
        
        Args:
            arr: NumPy array to compress
            
        Returns:
            Tuple of (compressed_data, metadata)
        """
        buffer = io.BytesIO()
        np.save(buffer, arr)
        buffer.seek(0)
        data = buffer.read()
        compressed_data = gzip.compress(data, self.compression_level)
        
        metadata = {
            "compressed": True,
            "format": "numpy",
            "shape": arr.shape,
            "dtype": str(arr.dtype),
            "compression": "gzip"
        }
        
        return compressed_data, metadata
    
    def _compress_generic(self, value: Any) -> Tuple[bytes, Dict[str, Any]]:
        """Compress a generic Python object.
        
        Args:
            value: Object to compress
            
        Returns:
            Tuple of (compressed_data, metadata)
        """
        # Pickle and compress
        data = pickle.dumps(value, protocol=4)
        compressed_data = gzip.compress(data, self.compression_level)
        
        # Calculate compression ratio
        original_size = len(data)
        compressed_size = len(compressed_data)
        ratio = original_size / max(1, compressed_size)
        
        metadata = {
            "compressed": True,
            "format": "pickle",
            "original_size": original_size,
            "compressed_size": compressed_size,
            "ratio": ratio,
            "compression": "gzip"
        }
        
        return compressed_data, metadata
    
    def decompress(self, value: Any, metadata: Dict[str, Any]) -> Any:
        """
        Decompress a value.
        
        Args:
            value: Compressed value
            metadata: Compression metadata
            
        Returns:
            Any: Decompressed value
        """
        if not metadata.get("compressed", False):
            return value
        
        try:
            # Use hardware memory compressor if possible
            format_type = metadata.get("format", "")
            
            if "hardware_compressed" in format_type:
                # Hardware compressed data
                if "dataframe" in format_type:
                    return self.hw_memory_compressor.decompress_dataframe(value, metadata)
                elif "array" in format_type:
                    return self.hw_memory_compressor.decompress_array(value, metadata)
                elif "pickle" in format_type:
                    decompressed_bytes = self.hw_memory_compressor.decompress_bytes(value, metadata)
                    return pickle.loads(decompressed_bytes)
            
            # Fallback to standard decompression based on format
            if format_type == "parquet":
                return self._decompress_dataframe(value, metadata)
            elif format_type == "numpy":
                return self._decompress_numpy_array(value, metadata)
            else:
                return self._decompress_generic(value, metadata)
        except Exception as e:
            # Fallback to basic decompression if hardware decompression fails
            logger.warning(f"Hardware decompression failed: {e}. Falling back to basic decompression.")
            # Fallback implementations...
            return value
    
    def _decompress_dataframe(self, value: bytes, metadata: Dict[str, Any]) -> pd.DataFrame:
        """Decompress a DataFrame.
        
        Args:
            value: Compressed DataFrame data
            metadata: Compression metadata
            
        Returns:
            pd.DataFrame: Decompressed DataFrame
        """
        buffer = io.BytesIO(value)
        return pd.read_parquet(buffer)
    
    def _decompress_numpy_array(self, value: bytes, metadata: Dict[str, Any]) -> np.ndarray:
        """Decompress a NumPy array.
        
        Args:
            value: Compressed NumPy array data
            metadata: Compression metadata
            
        Returns:
            np.ndarray: Decompressed NumPy array
        """
        decompressed = gzip.decompress(value)
        buffer = io.BytesIO(decompressed)
        return np.load(buffer, allow_pickle=True)
    
    def _decompress_generic(self, value: bytes, metadata: Dict[str, Any]) -> Any:
        """Decompress a generic Python object.
        
        Args:
            value: Compressed data
            metadata: Compression metadata
            
        Returns:
            Any: Decompressed object
        """
        decompressed = gzip.decompress(value)
        return pickle.loads(decompressed)


# Global singleton instance
_compressor_instance = None


def get_cache_compressor() -> HardwareAcceleratedCacheCompressor:
    """Get or create the singleton cache compressor instance.
    
    Returns:
        HardwareAcceleratedCacheCompressor: The cache compressor instance
    """
    global _compressor_instance
    if _compressor_instance is None:
        _compressor_instance = HardwareAcceleratedCacheCompressor()
    return _compressor_instance
