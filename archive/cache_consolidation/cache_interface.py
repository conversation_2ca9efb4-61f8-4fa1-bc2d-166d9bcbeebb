"""
Cache Interface for Yemen Trade Diagnostic

This module provides caching interfaces and utilities for the application.
"""

# Standard library imports
import hashlib
import json
import os
import pickle
from abc import ABC, abstractmethod
from pathlib import Path
from typing import Any, Dict, Optional, Union

# Project imports
# Import from the main cache interface
from yemen_trade_diagnostic.interfaces.cache_interface import *


class CachePrewarmer:
    """Cache prewarming utility for common operations."""
    
    def __init__(self, cache_dir=None):
        self.cache_dir = cache_dir or os.path.expanduser("~/.yemen_trade_diagnostic/cache")
        self.common_years = [2021, 2022, 2023]
    
    def pre_warm(self, patterns=None):
        """Pre-warm cache with common data patterns."""
        results = {}
        
        # Simulate cache warming
        for year in self.common_years:
            key = f"baci_data_{year}"
            results[key] = 1
        
        if patterns:
            for pattern in patterns:
                results[f"pattern_{pattern}"] = 1
        
        return results
    
    def warm_pipeline_cache(self, pipeline_name, years=None):
        """Warm cache for specific pipeline."""
        years = years or self.common_years
        cached_items = 0
        
        for year in years:
            # Simulate caching pipeline results
            cache_key = f"{pipeline_name}_{year}"
            cached_items += 1
        
        return cached_items


class CacheEvictionStrategy:
    """Strategy for cache eviction policies."""
    
    def __init__(self, max_size_mb=1000):
        self.max_size_mb = max_size_mb
        self.strategy = 'lru'  # least recently used
    
    def should_evict(self, cache_size_mb):
        """Determine if cache should be evicted."""
        return cache_size_mb > self.max_size_mb
    
    def evict_items(self, cache_items, target_size_mb):
        """Evict items according to strategy."""
        # Sort by last access time (mock implementation)
        sorted_items = sorted(cache_items, key=lambda x: x.get('last_access', 0))
        
        current_size = sum(item.get('size_mb', 1) for item in cache_items)
        items_to_remove = []
        
        for item in sorted_items:
            if current_size <= target_size_mb:
                break
            items_to_remove.append(item['key'])
            current_size -= item.get('size_mb', 1)
        
        return items_to_remove


# Additional cache utilities for backward compatibility
def get_cache_key(data, prefix=""):
    """Generate a cache key for data."""
    if isinstance(data, dict):
        content = json.dumps(data, sort_keys=True)
    else:
        content = str(data)
    
    hash_obj = hashlib.md5(content.encode())
    return f"{prefix}_{hash_obj.hexdigest()[:16]}"


def cache_exists(cache_key, cache_dir=None):
    """Check if cache key exists."""
    cache_dir = cache_dir or os.path.expanduser("~/.yemen_trade_diagnostic/cache")
    cache_file = Path(cache_dir) / f"{cache_key}.pkl"
    return cache_file.exists()


def clear_cache(cache_dir=None):
    """Clear all cache files."""
    cache_dir = cache_dir or os.path.expanduser("~/.yemen_trade_diagnostic/cache")
    cache_path = Path(cache_dir)
    
    if cache_path.exists():
        for file in cache_path.glob("*.pkl"):
            file.unlink()
        return True
    return False