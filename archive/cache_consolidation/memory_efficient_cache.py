#!/usr/bin/env python3
"""
Memory-Efficient Cache Management
Enhanced LRU eviction with memory pressure awareness and adaptive sizing
"""

# Standard library imports
import gc
import logging
import sys
import threading
import time
import weakref
from collections import OrderedDict
from dataclasses import dataclass
from enum import Enum
from typing import Any, Callable, Dict, List, Optional, Tuple

# Third-party imports
import psutil

logger = logging.getLogger(__name__)

class MemoryPressureLevel(Enum):
    """Memory pressure levels for adaptive cache management"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

@dataclass
class CacheEntry:
    """Enhanced cache entry with memory and access tracking"""
    key: str
    value: Any
    access_count: int
    last_access_time: float
    creation_time: float
    memory_size: int
    priority_score: float = 0.0
    is_compressed: bool = False
    compression_ratio: float = 1.0

class MemoryEfficientLRU:
    """Memory-efficient LRU cache with adaptive sizing and pressure awareness"""
    
    def __init__(self, 
                 max_size: int = 1000,
                 memory_limit_mb: int = 512,
                 enable_compression: bool = True,
                 enable_adaptive_sizing: bool = True):
        self.max_size = max_size
        self.memory_limit_bytes = memory_limit_mb * 1024 * 1024
        self.enable_compression = enable_compression
        self.enable_adaptive_sizing = enable_adaptive_sizing
        
        self._cache: Dict[str, CacheEntry] = {}
        self._access_order = OrderedDict()
        self._current_memory_usage = 0
        self._lock = threading.RLock()
        
        # Memory pressure monitoring
        self._memory_pressure_level = MemoryPressureLevel.LOW
        self._pressure_thresholds = {
            MemoryPressureLevel.LOW: 0.7,
            MemoryPressureLevel.MEDIUM: 0.8,
            MemoryPressureLevel.HIGH: 0.9,
            MemoryPressureLevel.CRITICAL: 0.95
        }
        
        # Adaptive parameters
        self._original_max_size = max_size
        self._adaptive_factor = 1.0
        
        # Statistics
        self._stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'compressions': 0,
            'memory_pressure_adjustments': 0,
            'total_memory_saved': 0
        }
        
        # Start memory monitoring thread
        self._monitoring_enabled = True
        self._monitor_thread = threading.Thread(target=self._monitor_memory_pressure, daemon=True)
        self._monitor_thread.start()
        
        logger.info(f"Memory-efficient LRU cache initialized: max_size={max_size}, memory_limit={memory_limit_mb}MB")

    def get(self, key: str) -> Optional[Any]:
        """Get item from cache with memory-aware access tracking"""
        with self._lock:
            if key not in self._cache:
                self._stats['misses'] += 1
                return None
                
            entry = self._cache[key]
            entry.access_count += 1
            entry.last_access_time = time.time()
            entry.priority_score = self._calculate_priority_score(entry)
            
            # Move to end (most recently used)
            self._access_order.move_to_end(key)
            
            self._stats['hits'] += 1
            
            # Decompress if needed
            value = entry.value
            if entry.is_compressed and hasattr(value, 'decompress'):
                try:
                    value = value.decompress()
                except Exception as e:
                    logger.warning(f"Failed to decompress cache value for key {key}: {e}")
                    
            return value

    def set(self, key: str, value: Any, priority: float = 0.0) -> bool:
        """Set item in cache with memory-aware storage"""
        with self._lock:
            memory_size = self._estimate_memory_size(value)
            
            # Check if we can accommodate this item
            if memory_size > self.memory_limit_bytes:
                logger.warning(f"Item too large for cache: {memory_size} bytes > {self.memory_limit_bytes} bytes")
                return False
            
            # Apply compression if enabled and beneficial
            compressed_value = value
            compression_ratio = 1.0
            is_compressed = False
            
            if self.enable_compression and self._should_compress(value, memory_size):
                try:
                    compressed_value, compression_ratio = self._compress_value(value)
                    is_compressed = True
                    memory_size = int(memory_size * compression_ratio)
                    self._stats['compressions'] += 1
                    self._stats['total_memory_saved'] += int(memory_size * (1 - compression_ratio))
                except Exception as e:
                    logger.debug(f"Compression failed for key {key}: {e}")
                    compressed_value = value
            
            # Remove existing entry if present
            if key in self._cache:
                old_entry = self._cache[key]
                self._current_memory_usage -= old_entry.memory_size
                
            # Make space if needed (both memory and size limits)
            space_needed = memory_size
            needs_space = (self._current_memory_usage + space_needed > self.memory_limit_bytes or 
                          len(self._cache) >= self.max_size)
            
            if needs_space:
                freed_space = self._make_space(space_needed)
                if (self._current_memory_usage + space_needed > self.memory_limit_bytes and 
                    freed_space < space_needed):
                    logger.warning(f"Could not free enough space for key {key}")
                    return False
            
            # Create and store entry
            current_time = time.time()
            entry = CacheEntry(
                key=key,
                value=compressed_value,
                access_count=1,
                last_access_time=current_time,
                creation_time=current_time,
                memory_size=memory_size,
                priority_score=priority,
                is_compressed=is_compressed,
                compression_ratio=compression_ratio
            )
            
            self._cache[key] = entry
            self._access_order[key] = None
            self._current_memory_usage += memory_size
            
            # Adaptive size adjustment
            if self.enable_adaptive_sizing:
                self._adjust_cache_size()
                
            return True

    def delete(self, key: str) -> bool:
        """Delete item from cache"""
        with self._lock:
            if key not in self._cache:
                return False
                
            entry = self._cache[key]
            self._current_memory_usage -= entry.memory_size
            del self._cache[key]
            del self._access_order[key]
            
            return True

    def clear(self):
        """Clear all cache entries"""
        with self._lock:
            self._cache.clear()
            self._access_order.clear()
            self._current_memory_usage = 0
            logger.info("Cache cleared")

    def get_stats(self) -> Dict[str, Any]:
        """Get comprehensive cache statistics"""
        with self._lock:
            hit_rate = self._stats['hits'] / max(1, self._stats['hits'] + self._stats['misses'])
            memory_usage_mb = self._current_memory_usage / (1024 * 1024)
            memory_efficiency = 1.0 - (self._current_memory_usage / max(1, self.memory_limit_bytes))
            
            return {
                'size': len(self._cache),
                'max_size': self.max_size,
                'memory_usage_mb': memory_usage_mb,
                'memory_limit_mb': self.memory_limit_bytes / (1024 * 1024),
                'memory_efficiency': memory_efficiency,
                'hit_rate': hit_rate,
                'memory_pressure_level': self._memory_pressure_level.value,
                'adaptive_factor': self._adaptive_factor,
                **self._stats
            }

    def _estimate_memory_size(self, value: Any) -> int:
        """Estimate memory size of an object"""
        try:
            # Use sys.getsizeof for basic estimation
            size = sys.getsizeof(value)
            
            # Add recursive size for containers
            if isinstance(value, (list, tuple)):
                size += sum(sys.getsizeof(item) for item in value)
            elif isinstance(value, dict):
                size += sum(sys.getsizeof(k) + sys.getsizeof(v) for k, v in value.items())
            elif hasattr(value, '__dict__'):
                size += sys.getsizeof(value.__dict__)
                
            return max(size, 64)  # Minimum size
        except Exception:
            return 1024  # Default estimate

    def _should_compress(self, value: Any, memory_size: int) -> bool:
        """Determine if value should be compressed"""
        # Only compress large objects or when under memory pressure
        size_threshold = 1024  # 1KB
        if self._memory_pressure_level in [MemoryPressureLevel.HIGH, MemoryPressureLevel.CRITICAL]:
            size_threshold = 256  # 256B under pressure
            
        return memory_size > size_threshold

    def _compress_value(self, value: Any) -> Tuple[Any, float]:
        """Compress value and return compressed value with compression ratio"""
        try:
            # Standard library imports
            import gzip
            import pickle

            # Serialize and compress
            pickled = pickle.dumps(value)
            compressed = gzip.compress(pickled, compresslevel=6)
            
            compression_ratio = len(compressed) / len(pickled) if len(pickled) > 0 else 1.0
            
            # Only return compressed if it saves significant space
            if compression_ratio < 0.8:
                return CompressedValue(compressed), compression_ratio
            else:
                return value, 1.0
                
        except Exception as e:
            logger.debug(f"Compression failed: {e}")
            return value, 1.0

    def _calculate_priority_score(self, entry: CacheEntry) -> float:
        """Calculate priority score for cache entry (LRU-based)"""
        current_time = time.time()
        
        # For LRU: Lower score = higher priority for eviction = evict first
        # Primary factor: last access time (older = higher eviction priority)
        recency_penalty = current_time - entry.last_access_time
        
        # Secondary factor: user-defined priority (higher = keep longer)
        user_priority_bonus = entry.priority_score * 1000  # Scale up user priority
        
        # Tertiary factor: access frequency (more accesses = keep longer)
        frequency_bonus = min(entry.access_count * 100, 500)  # Cap at 500
        
        # Final score: higher = more likely to be evicted
        return recency_penalty - user_priority_bonus - frequency_bonus

    def _make_space(self, space_needed: int) -> int:
        """Make space by evicting low-priority entries"""
        freed_space = 0
        candidates = []
        
        # Collect eviction candidates with priority scores
        for key, entry in self._cache.items():
            priority = self._calculate_priority_score(entry)
            candidates.append((priority, key, entry))
        
        # Sort by priority (highest eviction priority first)
        candidates.sort(key=lambda x: x[0], reverse=True)
        
        # Evict entries until enough space is freed or size limit met
        for priority, key, entry in candidates:
            # Stop if we have enough memory space and are under size limit
            if (freed_space >= space_needed and 
                len(self._cache) < self.max_size):
                break
                
            self._current_memory_usage -= entry.memory_size
            freed_space += entry.memory_size
            del self._cache[key]
            del self._access_order[key]
            self._stats['evictions'] += 1
            
            logger.debug(f"Evicted cache entry: {key} (priority: {priority:.3f}, size: {entry.memory_size})")
        
        return freed_space

    def _monitor_memory_pressure(self):
        """Monitor system memory pressure and adjust cache behavior"""
        while self._monitoring_enabled:
            try:
                memory_info = psutil.virtual_memory()
                memory_usage_ratio = memory_info.percent / 100.0
                
                # Determine pressure level
                old_level = self._memory_pressure_level
                if memory_usage_ratio >= self._pressure_thresholds[MemoryPressureLevel.CRITICAL]:
                    self._memory_pressure_level = MemoryPressureLevel.CRITICAL
                elif memory_usage_ratio >= self._pressure_thresholds[MemoryPressureLevel.HIGH]:
                    self._memory_pressure_level = MemoryPressureLevel.HIGH
                elif memory_usage_ratio >= self._pressure_thresholds[MemoryPressureLevel.MEDIUM]:
                    self._memory_pressure_level = MemoryPressureLevel.MEDIUM
                else:
                    self._memory_pressure_level = MemoryPressureLevel.LOW
                
                # React to pressure level changes
                if self._memory_pressure_level != old_level:
                    self._handle_pressure_change()
                    
                time.sleep(5)  # Check every 5 seconds
                
            except Exception as e:
                logger.error(f"Memory monitoring error: {e}")
                time.sleep(30)  # Back off on errors

    def _handle_pressure_change(self):
        """Handle memory pressure level changes"""
        with self._lock:
            self._stats['memory_pressure_adjustments'] += 1
            
            if self._memory_pressure_level == MemoryPressureLevel.CRITICAL:
                # Aggressive eviction
                self._adaptive_factor = 0.5
                self._aggressive_cleanup()
                logger.warning("Critical memory pressure - aggressive cache cleanup")
                
            elif self._memory_pressure_level == MemoryPressureLevel.HIGH:
                # Moderate eviction
                self._adaptive_factor = 0.7
                self._moderate_cleanup()
                logger.warning("High memory pressure - moderate cache cleanup")
                
            elif self._memory_pressure_level == MemoryPressureLevel.MEDIUM:
                # Light eviction
                self._adaptive_factor = 0.85
                logger.info("Medium memory pressure - light cache cleanup")
                
            else:
                # Normal operation
                self._adaptive_factor = 1.0

    def _adjust_cache_size(self):
        """Adjust cache size based on memory pressure"""
        if not self.enable_adaptive_sizing:
            return
            
        new_max_size = int(self._original_max_size * self._adaptive_factor)
        if new_max_size != self.max_size:
            self.max_size = new_max_size
            
            # Trim cache if it's now too large
            while len(self._cache) > self.max_size:
                # Remove least recently used item
                oldest_key = next(iter(self._access_order))
                entry = self._cache[oldest_key]
                self._current_memory_usage -= entry.memory_size
                del self._cache[oldest_key]
                del self._access_order[oldest_key]
                self._stats['evictions'] += 1

    def _aggressive_cleanup(self):
        """Aggressive cache cleanup under critical memory pressure"""
        # Remove 50% of cache entries, keeping only highest priority
        target_size = len(self._cache) // 2
        candidates = []
        
        for key, entry in self._cache.items():
            priority = self._calculate_priority_score(entry)
            candidates.append((priority, key, entry))
        
        # Sort by priority (lowest first) and remove lowest priority entries
        candidates.sort(key=lambda x: x[0])
        
        for i, (priority, key, entry) in enumerate(candidates):
            if i >= target_size:
                break
                
            self._current_memory_usage -= entry.memory_size
            del self._cache[key]
            del self._access_order[key]
            self._stats['evictions'] += 1

    def _moderate_cleanup(self):
        """Moderate cache cleanup under high memory pressure"""
        # Remove 25% of cache entries
        target_size = int(len(self._cache) * 0.75)
        candidates = []
        
        for key, entry in self._cache.items():
            priority = self._calculate_priority_score(entry)
            candidates.append((priority, key, entry))
        
        candidates.sort(key=lambda x: x[0])
        
        for i, (priority, key, entry) in enumerate(candidates):
            if len(self._cache) <= target_size:
                break
                
            self._current_memory_usage -= entry.memory_size
            del self._cache[key]
            del self._access_order[key]
            self._stats['evictions'] += 1

    def shutdown(self):
        """Shutdown the cache and stop monitoring"""
        self._monitoring_enabled = False
        if self._monitor_thread.is_alive():
            self._monitor_thread.join(timeout=1)
        self.clear()

class CompressedValue:
    """Wrapper for compressed cache values"""
    
    def __init__(self, compressed_data: bytes):
        self.compressed_data = compressed_data
        self._decompressed_cache = None
        self._lock = threading.Lock()

    def decompress(self) -> Any:
        """Decompress and return the original value"""
        if self._decompressed_cache is not None:
            return self._decompressed_cache
            
        with self._lock:
            if self._decompressed_cache is None:
                # Standard library imports
                import gzip
                import pickle
                
                decompressed = gzip.decompress(self.compressed_data)
                self._decompressed_cache = pickle.loads(decompressed)
                
            return self._decompressed_cache

# Singleton instance for global use
_memory_efficient_cache = None
_cache_lock = threading.Lock()

def get_memory_efficient_cache(max_size: int = 1000, 
                             memory_limit_mb: int = 512,
                             enable_compression: bool = True,
                             enable_adaptive_sizing: bool = True) -> MemoryEfficientLRU:
    """Get or create the global memory-efficient cache instance"""
    global _memory_efficient_cache
    
    with _cache_lock:
        if _memory_efficient_cache is None:
            _memory_efficient_cache = MemoryEfficientLRU(
                max_size=max_size,
                memory_limit_mb=memory_limit_mb,
                enable_compression=enable_compression,
                enable_adaptive_sizing=enable_adaptive_sizing
            )
        return _memory_efficient_cache

def shutdown_memory_efficient_cache():
    """Shutdown the global memory-efficient cache"""
    global _memory_efficient_cache
    
    with _cache_lock:
        if _memory_efficient_cache is not None:
            _memory_efficient_cache.shutdown()
            _memory_efficient_cache = None