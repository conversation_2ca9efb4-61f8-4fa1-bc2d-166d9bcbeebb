"""Cache prewarming utilities for Yemen Trade Diagnostic."""

# Standard library imports
import fnmatch
import os
import threading
import time
from typing import Any, Callable, Dict, List, Optional

# Project imports
from yemen_trade_diagnostic.interfaces.error_interface import (
    ErrorCategory,
    ErrorSeverity,
    with_error_handling,
)
from yemen_trade_diagnostic.interfaces.hardware_interface import get_hardware_manager
from yemen_trade_diagnostic.interfaces.logging_interface import get_logger

logger = get_logger(__name__)


class CachePreWarmer:
    """Pre-warms cache for common operations to improve initial performance."""
    
    def __init__(self):
        """Initialize the cache pre-warmer."""
        self.hardware_manager = get_hardware_manager()
        self.max_threads = self.hardware_manager.get_optimal_thread_count()
        self.patterns = [
            # Default patterns to pre-warm
            "trade_balance_*",
            "composition_main_*",
            "concentration_hhi_*",
            "growth_trend_*",
            "rca_matrix_*"
        ]
        self.common_years = [2018, 2019, 2020, 2021, 2022, 2023]
        
    @with_error_handling(category=ErrorCategory.DATA_ACCESS, severity=ErrorSeverity.WARNING)
    def pre_warm(self, patterns: Optional[List[str]] = None) -> Dict[str, int]:
        """Pre-warm cache with common operations.
        
        Args:
            patterns: Additional glob patterns to match cache keys
            
        Returns:
            Dict mapping categories to number of pre-warmed entries
        """
        if patterns:
            self.patterns.extend(patterns)
            
        results = {}
        
        # Pre-warm trade data
        results["trade_data"] = self._pre_warm_trade_data()
        
        # Pre-warm visualization data
        results["visualizations"] = self._pre_warm_visualizations()
        
        # Pre-warm analyses
        results["analyses"] = self._pre_warm_analyses()
        
        # Log summary
        total = sum(results.values())
        logger.info(f"Pre-warmed {total} cache entries")
        
        return results
    
    def _pre_warm_trade_data(self) -> int:
        """Pre-warm trade data cache.
        
        Returns:
            int: Number of entries pre-warmed
        """
        count = 0
        
        try:
            # Use hardware acceleration for parallel processing
            if self.hardware_manager.is_hardware_acceleration_available():
                # Get parallel executor
                executor = self.hardware_manager.get_parallel_executor()
                
                # Pre-warm BACI data for multiple years in parallel
                warmed = executor.map(self._warm_baci_data, self.common_years)
                count += sum(warmed)
                
                # Pre-warm world bank data
                warmed = executor.map(self._warm_worldbank_data, self.common_years)
                count += sum(warmed)
            else:
                # Sequential fallback
                for year in self.common_years:
                    count += self._warm_baci_data(year)
                    count += self._warm_worldbank_data(year)
        except Exception as e:
            logger.warning(f"Error pre-warming trade data: {e}")
        
        return count
    
    def _warm_baci_data(self, year: int) -> int:
        """Pre-warm BACI data for a specific year.
        
        Args:
            year: Year to pre-warm
            
        Returns:
            int: Number of entries pre-warmed
        """
        try:
            # Project imports
            from yemen_trade_diagnostic.data.loaders.baci_loader import load_baci_data

            # Just touch the data to ensure it's cached
            data = load_baci_data(year)
            if data is not None:
                return 1
        except Exception as e:
            logger.debug(f"Could not pre-warm BACI data for {year}: {e}")
        
        return 0
    
    def _warm_worldbank_data(self, year: int) -> int:
        """Pre-warm World Bank data for a specific year.
        
        Args:
            year: Year to pre-warm
            
        Returns:
            int: Number of entries pre-warmed
        """
        try:
            # Project imports
            from yemen_trade_diagnostic.data.loaders.worldbank_loader import load_worldbank_data

            # Just touch the data to ensure it's cached
            data = load_worldbank_data(year)
            if data is not None:
                return 1
        except Exception as e:
            logger.debug(f"Could not pre-warm World Bank data for {year}: {e}")
        
        return 0
    
    def _pre_warm_visualizations(self) -> int:
        """Pre-warm visualization cache.
        
        Returns:
            int: Number of entries pre-warmed
        """
        count = 0
        
        try:
            # Pre-warm chart templates
            # Project imports
            from yemen_trade_diagnostic.visualization.core.manager import VisualizationManager
            vm = VisualizationManager.get_instance()
            
            # Common chart types to pre-warm
            chart_types = ["line", "bar", "heatmap", "scatter", "dual_axis"]
            
            # Warm chart factory
            for chart_type in chart_types:
                try:
                    # Just initialize the factory for each type
                    factory = vm.chart_factory
                    if factory:
                        count += 1
                except Exception as e:
                    logger.debug(f"Could not pre-warm chart factory for {chart_type}: {e}")
        except Exception as e:
            logger.warning(f"Error pre-warming visualizations: {e}")
        
        return count
    
    def _pre_warm_analyses(self) -> int:
        """Pre-warm analysis cache.
        
        Returns:
            int: Number of entries pre-warmed
        """
        count = 0
        
        try:
            # Try to load common analyses
            # Project imports
            from yemen_trade_diagnostic.interfaces.cache_interface import get_cache_manager
            
            cache = get_cache_manager()
            # Just check if the cache manager is available
            if cache:
                count += 1
                
        except Exception as e:
            logger.warning(f"Error pre-warming analyses: {e}")
        
        return count


# Global singleton instance
_pre_warmer_instance = None


def get_cache_pre_warmer() -> CachePreWarmer:
    """Get or create the singleton cache pre-warmer instance.
    
    Returns:
        CachePreWarmer: The cache pre-warmer instance
    """
    global _pre_warmer_instance
    if _pre_warmer_instance is None:
        _pre_warmer_instance = CachePreWarmer()
    return _pre_warmer_instance