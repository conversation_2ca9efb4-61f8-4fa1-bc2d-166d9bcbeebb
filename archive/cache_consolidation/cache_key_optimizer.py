"""
Cache Key Optimizer - Semantic Cache Key Generation for Higher Hit Rates

This module implements intelligent cache key generation strategies to improve
cache hit rates by grouping similar queries and enabling hierarchical caching.
"""

# Standard library imports
import hashlib
import json
import re
from dataclasses import dataclass
from enum import Enum
from typing import Any, Dict, List, Optional, Set, Tuple

# Project imports
from yemen_trade_diagnostic.interfaces.logging_interface import get_logger

logger = get_logger(__name__)


class CacheKeyType(Enum):
    """Types of cache keys for different optimization strategies."""
    EXACT = "exact"  # Exact match required
    HIERARCHICAL = "hierarchical"  # Allow subset matching
    SEMANTIC = "semantic"  # Semantic grouping
    TEMPORAL = "temporal"  # Time-based grouping


@dataclass
class CacheKeyComponents:
    """Components of a cache key for analysis and optimization."""
    loader_name: str
    data_type: str
    country: Optional[str] = None
    year: Optional[int] = None
    products: Optional[List[str]] = None
    aggregation_level: Optional[str] = None
    filters: Optional[Dict[str, Any]] = None
    
    def to_hierarchical_key(self) -> str:
        """Generate hierarchical cache key."""
        key_parts = [self.loader_name, self.data_type]
        
        if self.country:
            key_parts.append(f"country:{self.country}")
        if self.year:
            key_parts.append(f"year:{self.year}")
        if self.aggregation_level:
            key_parts.append(f"agg:{self.aggregation_level}")
            
        return ":".join(key_parts)
    
    def to_semantic_key(self) -> str:
        """Generate semantic cache key for similar queries."""
        # Group by data type and basic parameters
        key_parts = [self.loader_name, self.data_type]
        
        # Add semantic groupings
        if self.country:
            key_parts.append(f"geo:{self.country}")
        
        # Group years into ranges for better cache reuse
        if self.year:
            year_group = self._get_year_group(self.year)
            key_parts.append(f"period:{year_group}")
        
        # Group products by categories
        if self.products:
            product_category = self._get_product_category(self.products)
            key_parts.append(f"products:{product_category}")
            
        return ":".join(key_parts)
    
    def _get_year_group(self, year: int) -> str:
        """Group years for semantic caching."""
        if year >= 2020:
            return "recent"
        elif year >= 2015:
            return "mid_term"
        else:
            return "historical"
    
    def _get_product_category(self, products: List[str]) -> str:
        """Categorize products for semantic caching."""
        if len(products) == 1:
            return "single"
        elif len(products) <= 5:
            return "few"
        elif len(products) <= 20:
            return "many"
        else:
            return "all"


class CacheKeyOptimizer:
    """Optimizes cache keys for improved hit rates through semantic grouping."""
    
    def __init__(self):
        """Initialize the cache key optimizer."""
        self.access_patterns = {}  # Track access patterns for optimization
        self.similarity_threshold = 0.8  # Threshold for query similarity
        
        # Pre-defined semantic groups for common patterns
        self.semantic_groups = {
            "trade_balance": ["exports", "imports", "balance"],
            "composition": ["sectoral", "product", "partner"],
            "concentration": ["hhi", "market_share", "dominance"],
            "growth": ["trends", "rates", "volatility"],
            "sophistication": ["complexity", "product_space", "capabilities"]
        }
        
        logger.info("Initialized cache key optimizer for semantic grouping")
    
    def optimize_cache_key(self, 
                          loader_name: str, 
                          path_or_identifier: str,
                          filters: Optional[Dict[str, Any]] = None,
                          optimization_type: CacheKeyType = CacheKeyType.HIERARCHICAL) -> str:
        """
        Generate optimized cache key based on the specified strategy.
        
        Args:
            loader_name: Name of the data loader
            path_or_identifier: Path or identifier for the data
            filters: Optional filters to apply
            optimization_type: Type of optimization to apply
            
        Returns:
            Optimized cache key string
        """
        # Parse components from inputs
        components = self._parse_key_components(loader_name, path_or_identifier, filters)
        
        # Track access pattern
        self._track_access_pattern(components)
        
        # Generate key based on optimization type
        if optimization_type == CacheKeyType.HIERARCHICAL:
            base_key = components.to_hierarchical_key()
            return self._generate_hierarchical_key(base_key, components)
        elif optimization_type == CacheKeyType.SEMANTIC:
            base_key = components.to_semantic_key()
            return self._generate_semantic_key(base_key, components)
        elif optimization_type == CacheKeyType.TEMPORAL:
            base_key = components.to_hierarchical_key()
            return self._generate_temporal_key(base_key, components)
        else:
            # Default to exact key (original behavior)
            return self._generate_exact_key(loader_name, path_or_identifier, filters)
    
    def _parse_key_components(self, 
                             loader_name: str, 
                             path_or_identifier: str,
                             filters: Optional[Dict[str, Any]] = None) -> CacheKeyComponents:
        """Parse input parameters into structured components."""
        
        # Extract data type from loader name or path
        data_type = self._extract_data_type(loader_name, path_or_identifier)
        
        # Extract country from path or filters
        country = self._extract_country(path_or_identifier, filters)
        
        # Extract year from path or filters
        year = self._extract_year(path_or_identifier, filters)
        
        # Extract products from filters
        products = self._extract_products(filters)
        
        # Determine aggregation level
        aggregation_level = self._determine_aggregation_level(filters)
        
        return CacheKeyComponents(
            loader_name=loader_name,
            data_type=data_type,
            country=country,
            year=year,
            products=products,
            aggregation_level=aggregation_level,
            filters=filters
        )
    
    def _extract_data_type(self, loader_name: str, path_or_identifier: str) -> str:
        """Extract data type from loader name or path."""
        # Check for common data types in loader name
        for group, keywords in self.semantic_groups.items():
            if any(keyword in loader_name.lower() for keyword in keywords):
                return group
        
        # Check path for data type indicators
        path_lower = path_or_identifier.lower()
        if "baci" in path_lower or "trade" in path_lower:
            return "trade_data"
        elif "worldbank" in path_lower or "wb" in path_lower:
            return "indicators"
        elif "composition" in path_lower:
            return "composition"
        elif "concentration" in path_lower:
            return "concentration"
        else:
            return "general"
    
    def _extract_country(self, path_or_identifier: str, filters: Optional[Dict[str, Any]]) -> Optional[str]:
        """Extract country information."""
        # Check filters first
        if filters:
            for key in ["country", "reporter", "partner", "i", "j"]:
                if key in filters:
                    country_val = filters[key]
                    if isinstance(country_val, str):
                        return country_val.lower()
                    elif isinstance(country_val, (int, list)) and key in ["i", "j"]:
                        # Convert country codes to names if needed
                        return f"code_{country_val}"
        
        # Check path for Yemen (common case)
        if "yemen" in path_or_identifier.lower():
            return "yemen"
        
        return None
    
    def _extract_year(self, path_or_identifier: str, filters: Optional[Dict[str, Any]]) -> Optional[int]:
        """Extract year information."""
        # Check filters first
        if filters:
            for key in ["year", "t", "time"]:
                if key in filters and isinstance(filters[key], int):
                    return filters[key]
        
        # Check path for year patterns
        year_pattern = r'(?:19|20)\d{2}'
        matches = re.findall(year_pattern, path_or_identifier)
        if matches:
            return int(matches[-1])  # Take the last year found
        
        return None
    
    def _extract_products(self, filters: Optional[Dict[str, Any]]) -> Optional[List[str]]:
        """Extract product information from filters."""
        if not filters:
            return None
        
        for key in ["products", "k", "product_codes", "hs"]:
            if key in filters:
                products = filters[key]
                if isinstance(products, list):
                    return [str(p) for p in products]
                else:
                    return [str(products)]
        
        return None
    
    def _determine_aggregation_level(self, filters: Optional[Dict[str, Any]]) -> Optional[str]:
        """Determine the aggregation level of the query."""
        if not filters:
            return "raw"
        
        # Check for aggregation indicators
        if any(key in filters for key in ["group_by", "aggregate", "sum", "mean"]):
            return "aggregated"
        elif any(key in filters for key in ["detailed", "full", "complete"]):
            return "detailed"
        else:
            return "filtered"
    
    def _generate_hierarchical_key(self, base_key: str, components: CacheKeyComponents) -> str:
        """Generate hierarchical cache key that supports subset matching."""
        # Create hierarchy levels for better cache reuse
        hierarchy_levels = [base_key]
        
        # Add filter-specific levels
        if components.filters:
            # Sort filters for consistent keys
            sorted_filters = sorted(components.filters.items())
            filter_str = json.dumps(sorted_filters, sort_keys=True)
            filter_hash = hashlib.md5(filter_str.encode()).hexdigest()[:8]
            hierarchy_levels.append(f"filters:{filter_hash}")
        
        return ":".join(hierarchy_levels)
    
    def _generate_semantic_key(self, base_key: str, components: CacheKeyComponents) -> str:
        """Generate semantic cache key for similar query grouping."""
        # Use semantic base key and add minimal distinguishing info
        semantic_parts = [base_key]
        
        # Add critical distinguishing features only
        if components.filters:
            critical_filters = self._extract_critical_filters(components.filters)
            if critical_filters:
                filter_str = json.dumps(critical_filters, sort_keys=True)
                filter_hash = hashlib.md5(filter_str.encode()).hexdigest()[:6]
                semantic_parts.append(f"crit:{filter_hash}")
        
        return ":".join(semantic_parts)
    
    def _generate_temporal_key(self, base_key: str, components: CacheKeyComponents) -> str:
        """Generate temporal cache key with time-based grouping."""
        temporal_parts = [base_key]
        
        # Group by time periods for better reuse
        if components.year:
            # Group recent years together for better cache reuse
            if components.year >= 2020:
                temporal_parts.append("period:recent")
            else:
                temporal_parts.append(f"year:{components.year}")
        
        return ":".join(temporal_parts)
    
    def _generate_exact_key(self, 
                           loader_name: str, 
                           path_or_identifier: str,
                           filters: Optional[Dict[str, Any]] = None) -> str:
        """Generate exact cache key (original behavior)."""
        key_parts = [loader_name, path_or_identifier]
        if filters:
            sorted_filters = sorted(filters.items())
            filter_str = json.dumps(sorted_filters)
            key_parts.append(filter_str)
        
        joined_parts = "::".join(key_parts)
        return f"{loader_name}:{hashlib.md5(joined_parts.encode()).hexdigest()}"
    
    def _extract_critical_filters(self, filters: Dict[str, Any]) -> Dict[str, Any]:
        """Extract only critical filters that significantly change the result."""
        critical_keys = {
            "country", "reporter", "partner", "i", "j",  # Geographic filters
            "year", "t", "time",  # Temporal filters
            "limit", "top_n",  # Size filters
            "threshold", "min_value", "max_value"  # Value filters
        }
        
        return {k: v for k, v in filters.items() if k in critical_keys}
    
    def _track_access_pattern(self, components: CacheKeyComponents) -> None:
        """Track access patterns for future optimization."""
        pattern_key = f"{components.data_type}:{components.country}:{components.year}"
        
        if pattern_key not in self.access_patterns:
            self.access_patterns[pattern_key] = {
                "count": 0,
                "last_accessed": None,
                "variations": set()
            }
        
        self.access_patterns[pattern_key]["count"] += 1
        self.access_patterns[pattern_key]["last_accessed"] = str(components.filters)
        
        # Track filter variations
        if components.filters:
            filter_signature = frozenset(components.filters.keys())
            self.access_patterns[pattern_key]["variations"].add(filter_signature)
    
    def get_cache_suggestions(self) -> List[Dict[str, Any]]:
        """Generate cache optimization suggestions based on access patterns."""
        suggestions = []
        
        for pattern_key, stats in self.access_patterns.items():
            if stats["count"] > 5:  # Frequently accessed patterns
                suggestions.append({
                    "pattern": pattern_key,
                    "access_count": stats["count"],
                    "suggestion": "Consider pre-warming this pattern",
                    "priority": "high" if stats["count"] > 20 else "medium"
                })
        
        return suggestions
    
    def should_use_hierarchical_key(self, 
                                   components: CacheKeyComponents) -> bool:
        """Determine if hierarchical key would benefit this query."""
        # Use hierarchical keys for queries that commonly have subsets
        if components.products and len(components.products) > 1:
            return True
        
        if components.aggregation_level in ["filtered", "detailed"]:
            return True
        
        return False
    
    def find_similar_cache_keys(self, 
                               target_key: str, 
                               existing_keys: List[str]) -> List[Tuple[str, float]]:
        """Find similar cache keys that might be reusable."""
        similar_keys = []
        
        for existing_key in existing_keys:
            similarity = self._calculate_key_similarity(target_key, existing_key)
            if similarity > self.similarity_threshold:
                similar_keys.append((existing_key, similarity))
        
        # Sort by similarity score
        similar_keys.sort(key=lambda x: x[1], reverse=True)
        
        return similar_keys
    
    def _calculate_key_similarity(self, key1: str, key2: str) -> float:
        """Calculate similarity between two cache keys."""
        parts1 = set(key1.split(":"))
        parts2 = set(key2.split(":"))
        
        if not parts1 or not parts2:
            return 0.0
        
        intersection = len(parts1.intersection(parts2))
        union = len(parts1.union(parts2))
        
        return intersection / union if union > 0 else 0.0


# Global singleton instance
_key_optimizer_instance = None


def get_cache_key_optimizer() -> CacheKeyOptimizer:
    """Get or create the singleton cache key optimizer instance.
    
    Returns:
        CacheKeyOptimizer: The cache key optimizer instance
    """
    global _key_optimizer_instance
    if _key_optimizer_instance is None:
        _key_optimizer_instance = CacheKeyOptimizer()
    return _key_optimizer_instance


def optimize_cache_key(loader_name: str, 
                      path_or_identifier: str,
                      filters: Optional[Dict[str, Any]] = None,
                      optimization_type: CacheKeyType = CacheKeyType.HIERARCHICAL) -> str:
    """
    Convenience function to optimize a cache key.
    
    Args:
        loader_name: Name of the data loader
        path_or_identifier: Path or identifier for the data  
        filters: Optional filters to apply
        optimization_type: Type of optimization to apply
        
    Returns:
        Optimized cache key string
    """
    optimizer = get_cache_key_optimizer()
    return optimizer.optimize_cache_key(loader_name, path_or_identifier, filters, optimization_type)