"""
Refactored error handling interface for Yemen Trade Diagnostic project.

This module serves as a clean interface that delegates to the comprehensive
error handling system in the errors module. It provides:
- Standardized error categories and severity levels
- Decorators for error handling
- Integration with enhanced error features
"""

# Standard library imports
import functools
import logging
from typing import Any, Callable, Optional, TypeVar, cast

# Import all error handling components from the errors module
# We need to be careful about circular imports
try:
    from yemen_trade_diagnostic.errors import (
    # Base error types
    ErrorCategory,
    ErrorSeverity,
    ErrorContext,
    CircuitBreakerState,
    RetryStrategy,
    RecoveryMode,
    MetricType,
    
    # Circuit breakers
    EnhancedCircuitBreaker as CircuitBreaker,
    CircuitBreakerConfig,
    get_enhanced_circuit_breaker,
    CircuitBreakerOpenError,
    
    # State management
    StateManager,
    CircuitBreakerStateData,
    
    # Health checks
    HealthCheck,
    HealthCheckResult,
    HealthStatus,
    
    # Graceful degradation
    DegradationLevel,
    DegradationStrategy,
    GracefulDegradationManager,
    get_degradation_manager,
    
    # Fallback strategies
    FallbackStrategy,
    FallbackChain,
    create_data_pipeline_fallback_chain,
    create_visualization_fallback_chain,
    create_analysis_fallback_chain,
    
    # Resource management
    ResourceManager,
    get_resource_manager,
    ResourceError,
    ResourceConstraintError,
    
    # Recovery management
    RecoveryManager,
    get_recovery_manager,
    
    # Error analytics
    ErrorAnalytics,
    ErrorPattern,
    ErrorTrend,
    
    # Decorators
    with_error_handling as base_with_error_handling,
    with_fallback as base_with_fallback,
    with_retry as base_with_retry,
)
    IMPORTS_SUCCESSFUL = True
except ImportError as e:
    # If imports fail due to circular dependencies, we'll define minimal stubs
    IMPORTS_SUCCESSFUL = False
    logging.warning(f"Failed to import from errors module: {e}")

# Type variables
T = TypeVar('T')
F = TypeVar('F', bound=Callable)

# Logger
_error_logger = logging.getLogger("yemen_trade_diagnostic.error")


class ErrorManager:
    """
    Central manager for error handling functionality.
    
    This is a thin wrapper around the various managers in the errors module.
    """
    
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
            
        self.degradation_manager = get_degradation_manager()
        self.recovery_manager = get_recovery_manager()
        self.resource_manager = get_resource_manager()
        self.error_analytics = ErrorAnalytics()
        self.metrics_collector = ErrorMetricsCollector()
        
        self._initialized = True
    
    def report_error(self, exception: Exception, **kwargs) -> ErrorContext:
        """Report an error with context."""
        # Create error context
        context = ErrorContext(
            exception=exception,
            category=kwargs.get('category', ErrorCategory.from_exception(exception)),
            severity=kwargs.get('severity', ErrorSeverity.from_exception(exception)),
            component=kwargs.get('component', 'unknown'),
            operation=kwargs.get('operation', 'unknown'),
            extra=kwargs.get('extra', {})
        )
        
        # Record metrics
        self.metrics_collector.record_error(context)
        
        # Log the error
        _error_logger.log(
            logging.ERROR if context.severity.value >= ErrorSeverity.ERROR.value else logging.WARNING,
            f"{context}"
        )
        
        return context
    
    def execute_with_degradation(self, func: Callable, component: str, operation: str, *args, **kwargs):
        """Execute a function with graceful degradation."""
        return self.degradation_manager.execute_with_degradation(
            func, component, operation, *args, **kwargs
        )
    
    def check_resources(self, operation: str) -> bool:
        """Check if resources are available for an operation."""
        return self.resource_manager.can_execute_operation(operation)


# Global error manager instance
_error_manager = None

def get_error_manager() -> ErrorManager:
    """Get the singleton error manager instance."""
    global _error_manager
    if _error_manager is None:
        _error_manager = ErrorManager()
    return _error_manager


def report_error(exception: Exception, **kwargs) -> ErrorContext:
    """Report an error to the error manager."""
    return get_error_manager().report_error(exception, **kwargs)


# Decorators

def with_error_handling(
    category: ErrorCategory,
    severity: ErrorSeverity,
    fallback_value: Any = None,
    retry_count: int = 0,
    retry_delay: float = 1.0,
    enable_circuit_breaker: bool = False,
    enable_degradation: bool = True
) -> Callable[[F], F]:
    """
    Decorator for comprehensive error handling.
    
    Args:
        category: Error category for classification
        severity: Error severity level
        fallback_value: Value to return on error
        retry_count: Number of retry attempts
        retry_delay: Delay between retries in seconds
        enable_circuit_breaker: Whether to use circuit breaker
        enable_degradation: Whether to enable graceful degradation
        
    Returns:
        Decorated function with error handling
    """
    def decorator(func: F) -> F:
        # Extract component and operation from function
        component = func.__module__.split('.')[-1] if func.__module__ else "unknown"
        operation = func.__qualname__
        
        # Create circuit breaker if requested
        circuit_breaker = None
        if enable_circuit_breaker:
            circuit_breaker = get_enhanced_circuit_breaker(
                f"{component}.{operation}",
                CircuitBreakerConfig(failure_threshold=5, recovery_timeout=60)
            )
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            error_manager = get_error_manager()
            
            # Check circuit breaker
            if circuit_breaker and not circuit_breaker.allow_request():
                _error_logger.warning(f"Circuit breaker open for {component}.{operation}")
                if enable_degradation:
                    try:
                        return error_manager.execute_with_degradation(
                            func, component, operation, *args, **kwargs
                        )
                    except Exception:
                        pass
                return fallback_value
            
            # Check resources
            if not error_manager.check_resources(operation):
                _error_logger.warning(f"Insufficient resources for {component}.{operation}")
                if enable_degradation:
                    return error_manager.execute_with_degradation(
                        func, component, operation, *args, **kwargs
                    )
                return fallback_value
            
            # Execute with retries
            last_exception = None
            for attempt in range(retry_count + 1):
                try:
                    result = func(*args, **kwargs)
                    if circuit_breaker:
                        circuit_breaker.record_success()
                    return result
                    
                except Exception as e:
                    last_exception = e
                    if circuit_breaker:
                        circuit_breaker.record_failure()
                    
                    if attempt < retry_count:
                        _error_logger.info(
                            f"Retry {attempt+1}/{retry_count} for {component}.{operation}"
                        )
                        if retry_delay > 0:
                            import time
                            time.sleep(retry_delay)
                    else:
                        # Report error
                        report_error(
                            exception=e,
                            category=category,
                            severity=severity,
                            component=component,
                            operation=operation
                        )
                        
                        # Try degradation
                        if enable_degradation:
                            try:
                                return error_manager.execute_with_degradation(
                                    func, component, operation, *args, **kwargs
                                )
                            except Exception:
                                pass
                        
                        if fallback_value is not None:
                            return fallback_value
                        raise
            
            if last_exception:
                raise last_exception
                
        return cast(F, wrapper)
    
    return decorator


def with_circuit_breaker(
    name: Optional[str] = None,
    failure_threshold: int = 5,
    recovery_timeout: float = 60.0,
    fallback_value: Any = None
) -> Callable[[F], F]:
    """
    Decorator for circuit breaker pattern.
    
    Args:
        name: Circuit breaker name
        failure_threshold: Number of failures before opening
        recovery_timeout: Seconds before trying half-open state
        fallback_value: Value to return when circuit is open
        
    Returns:
        Decorated function with circuit breaker
    """
    def decorator(func: F) -> F:
        # Generate name if not provided
        cb_name = name or f"{func.__module__}.{func.__qualname__}"
        
        # Get circuit breaker
        circuit_breaker = get_enhanced_circuit_breaker(
            cb_name,
            CircuitBreakerConfig(
                failure_threshold=failure_threshold,
                recovery_timeout=int(recovery_timeout)
            )
        )
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            if not circuit_breaker.allow_request():
                _error_logger.warning(f"Circuit breaker open for {cb_name}")
                return fallback_value
                
            try:
                result = func(*args, **kwargs)
                circuit_breaker.record_success()
                return result
            except Exception as e:
                circuit_breaker.record_failure()
                raise
                
        return cast(F, wrapper)
    
    return decorator


def with_fallback(
    fallback_value: Any = None,
    fallback_function: Optional[Callable] = None
) -> Callable[[F], F]:
    """
    Decorator for providing fallback on failure.
    
    Args:
        fallback_value: Value to return on error
        fallback_function: Function to call for fallback
        
    Returns:
        Decorated function with fallback
    """
    def decorator(func: F) -> F:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                _error_logger.info(f"Using fallback for {func.__name__}: {e}")
                if fallback_function:
                    return fallback_function(*args, **kwargs)
                return fallback_value
                
        return cast(F, wrapper)
    
    return decorator


def with_retry(
    retry_count: int = 3,
    retry_delay: float = 1.0,
    exponential_backoff: bool = True
) -> Callable[[F], F]:
    """
    Decorator for retrying functions on failure.
    
    Args:
        retry_count: Number of retry attempts
        retry_delay: Base delay between retries
        exponential_backoff: Whether to use exponential backoff
        
    Returns:
        Decorated function with retry
    """
    def decorator(func: F) -> F:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            import time
            
            for attempt in range(retry_count + 1):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    if attempt < retry_count:
                        delay = retry_delay * (2 ** attempt if exponential_backoff else 1)
                        _error_logger.info(
                            f"Retry {attempt+1}/{retry_count} for {func.__name__} "
                            f"after {delay}s"
                        )
                        time.sleep(delay)
                    else:
                        raise
                        
        return cast(F, wrapper)
    
    return decorator


def with_resource_check(operation: str) -> Callable[[F], F]:
    """
    Decorator to check resources before execution.
    
    Args:
        operation: Operation name for resource checking
        
    Returns:
        Decorated function with resource checking
    """
    def decorator(func: F) -> F:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            resource_manager = get_resource_manager()
            if not resource_manager.can_execute_operation(operation):
                raise ResourceError(f"Insufficient resources for {operation}")
            return func(*args, **kwargs)
            
        return cast(F, wrapper)
    
    return decorator


# Re-export key classes and functions for backward compatibility
__all__ = [
    # Error types
    'ErrorCategory',
    'ErrorSeverity',
    'ErrorContext',
    'ErrorCollector',
    'CircuitBreaker',
    'CircuitBreakerState',
    
    # Managers
    'ErrorManager',
    'get_error_manager',
    
    # Functions
    'report_error',
    
    # Decorators
    'with_error_handling',
    'with_circuit_breaker',
    'with_fallback',
    'with_retry',
    'with_resource_check',
    
    # Degradation and recovery
    'DegradationLevel',
    'get_degradation_manager',
    'get_recovery_manager',
    'get_resource_manager',
]