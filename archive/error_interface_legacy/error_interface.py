"""
Error handling interface for Yemen Trade Diagnostic project.

This module serves as a compatibility layer that re-exports error handling
functionality from the errors module to maintain backward compatibility.
"""

# Re-export everything from the errors module
from yemen_trade_diagnostic.errors import *

# Additional convenience imports for backward compatibility
from yemen_trade_diagnostic.errors import (
    # Ensure specific imports are available
    ErrorCategory,
    ErrorSeverity,
    ErrorContext,
    ErrorCollector,
    ErrorMetricsCollector,
    CircuitBreakerState,
    
    # Enhanced components
    EnhancedCircuitBreaker as CircuitBreaker,
    get_enhanced_circuit_breaker,
    get_degradation_manager,
    get_recovery_manager,
    get_resource_manager,
    
    # Decorators
    with_error_handling,
    with_fallback,
    with_retry,
)

# For backward compatibility, provide get_error_manager
import logging

_logger = logging.getLogger(__name__)


class ErrorManager:
    """Simple error manager for backward compatibility."""
    
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        self.degradation_manager = get_degradation_manager()
        self.recovery_manager = get_recovery_manager()
        self.resource_manager = get_resource_manager()
        self.metrics_collector = ErrorMetricsCollector()
    
    def report_error(self, exception: Exception, **kwargs):
        """Report an error."""
        context = ErrorContext(
            exception=exception,
            category=kwargs.get('category', ErrorCategory.from_exception(exception)),
            severity=kwargs.get('severity', ErrorSeverity.from_exception(exception)),
            component=kwargs.get('component', 'unknown'),
            operation=kwargs.get('operation', 'unknown'),
            extra=kwargs.get('extra', {})
        )
        
        self.metrics_collector.record_error(context)
        
        _logger.log(
            logging.ERROR if context.severity.value >= ErrorSeverity.ERROR.value else logging.WARNING,
            f"{context}"
        )
        
        return context


def get_error_manager() -> ErrorManager:
    """Get the error manager instance."""
    return ErrorManager()


def report_error(exception: Exception, **kwargs):
    """Report an error to the error manager."""
    return get_error_manager().report_error(exception, **kwargs)