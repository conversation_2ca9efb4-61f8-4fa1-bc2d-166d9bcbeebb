"""
Standardized error handling interface for Yemen Trade Diagnostic project.

This module provides a comprehensive error handling framework with standardized
error categories, severity levels, decorators, reporting mechanisms, retry logic,
fallback mechanisms, circuit breakers, and metrics collection.

This interface now leverages enhanced error handling capabilities from the
errors module, including:
- Enhanced circuit breakers with state persistence
- Graceful degradation strategies
- Recovery orchestration
- Error analytics and pattern detection
- Resource-aware error handling
"""

# Standard library imports
import functools
import inspect
import json
import logging
import threading
import time
import traceback
from abc import ABC, abstractmethod
from collections import defaultdict, deque
from contextlib import contextmanager
from enum import Enum, auto
from typing import Any, Callable, Dict, Generic, List, Optional, Set, Type, TypeVar, Union, cast

# Import enhanced error handling components
try:
    from yemen_trade_diagnostic.errors import (
        EnhancedCircuitBreaker,
        CircuitBreakerConfig,
        get_enhanced_circuit_breaker,
        CircuitBreakerOpenError,
        StateManager,
        HealthCheck,
        HealthCheckResult,
        HealthStatus,
        DegradationLevel,
        DegradationStrategy,
        GracefulDegradationManager,
        get_degradation_manager,
        FallbackStrategy,
        Fallback<PERSON>hai<PERSON>,
        create_data_pipeline_fallback_chain,
        ResourceManager,
        get_resource_manager,
        RecoveryManager,
        get_recovery_manager,
        ErrorAnalytics,
        ErrorPattern as AnalyticsErrorPattern,
        ErrorTrend
    )
    ENHANCED_ERRORS_AVAILABLE = True
except ImportError:
    ENHANCED_ERRORS_AVAILABLE = False
    logging.warning("Enhanced error handling components not available, using basic implementations")

# Type variables for generic error handling
T = TypeVar('T')  # Return type
F = TypeVar('F', bound=Callable)  # Function type
E = TypeVar('E', bound=Exception)  # Exception type

# Logger for the error interface itself
_error_logger = logging.getLogger("yemen_trade_diagnostic.error")

class ErrorCategory(Enum):
    """Categories of errors for classification and policy application."""
    VALIDATION = "validation"        # Data validation errors
    DATA_ACCESS = "data_access"      # Problems accessing data sources
    CALCULATION = "calculation"      # Errors in computation or analysis
    HARDWARE = "hardware"            # Hardware-related failures
    PIPELINE = "pipeline"            # Pipeline execution issues
    VISUALIZATION = "visualization"  # Chart generation errors
    CONFIGURATION = "configuration"  # Configuration problems
    NETWORK = "network"              # Network-related errors
    TIMEOUT = "timeout"              # Time limit exceeded errors
    MEMORY = "memory"                # Memory-related issues
    SYSTEM = "system"                # System-level problems
    UNKNOWN = "unknown"              # Unclassified errors

    @classmethod
    def from_exception(cls, exc: Exception) -> 'ErrorCategory':
        """
        Determine error category from exception type.

        Args:
            exc: The exception to categorize

        Returns:
            Appropriate error category
        """
        exc_type = type(exc).__name__

        # Map common exception types to categories
        if exc_type in ('ValueError', 'ValidationError', 'SchemaError'):
            return cls.VALIDATION
        elif exc_type in ('FileNotFoundError', 'PermissionError', 'IOError', 'OSError'):
            return cls.DATA_ACCESS
        elif exc_type in ('ArithmeticError', 'OverflowError', 'ZeroDivisionError'):
            return cls.CALCULATION
        elif exc_type in ('CudaError', 'CuDNNError', 'MetalError', 'HardwareError'):
            return cls.HARDWARE
        elif exc_type in ('PipelineError', 'DependencyError', 'OrchestratorError'):
            return cls.PIPELINE
        elif exc_type in ('ChartError', 'MatplotlibError', 'PlotlyError'):
            return cls.VISUALIZATION
        elif exc_type in ('ConfigError', 'MissingConfigurationError'):
            return cls.CONFIGURATION
        elif exc_type in ('ConnectionError', 'ConnectionRefusedError', 'HTTPError'):
            return cls.NETWORK
        elif exc_type in ('TimeoutError', 'TimeoutExpired'):
            return cls.TIMEOUT
        elif exc_type in ('MemoryError', 'OutOfMemoryError'):
            return cls.MEMORY
        else:
            return cls.UNKNOWN

class ErrorSeverity(Enum):
    """Severity levels for classifying error impact."""
    DEBUG = 10    # Informational errors for debugging
    INFO = 20     # Minor issues with minimal impact
    WARNING = 30  # Important issues that don't affect critical functionality
    ERROR = 40    # Serious issues affecting functionality but not fatal
    CRITICAL = 50 # Fatal errors that prevent system operation

    @classmethod
    def from_exception(cls, exc: Exception) -> 'ErrorSeverity':
        """
        Determine error severity from exception type.

        Args:
            exc: The exception to evaluate

        Returns:
            Appropriate error severity
        """
        # Extract any severity hint from the exception's attributes
        if hasattr(exc, 'severity'):
            severity_hint = getattr(exc, 'severity')
            if isinstance(severity_hint, ErrorSeverity):
                return severity_hint
            elif isinstance(severity_hint, str) and hasattr(cls, severity_hint.upper()):
                return getattr(cls, severity_hint.upper())

        # Determine based on exception type
        exc_type = type(exc).__name__

        if exc_type in ('SystemExit', 'KeyboardInterrupt', 'MemoryError'):
            return cls.CRITICAL
        elif exc_type in ('RuntimeError', 'IOError', 'ConnectionError', 'TimeoutError'):
            return cls.ERROR
        elif exc_type in ('ValueError', 'KeyError', 'TypeError', 'AttributeError'):
            return cls.WARNING
        else:
            return cls.INFO

class CircuitBreakerState(Enum):
    """
    Circuit breaker states for fault isolation.

    - CLOSED: Normal operation, calls go through
    - OPEN: Circuit breaker is tripped, calls fail fast
    - HALF_OPEN: Testing if the issue is resolved
    """
    CLOSED = auto()     # Normal operation
    OPEN = auto()       # Circuit breaker tripped
    HALF_OPEN = auto()  # Testing recovery state

class RetryStrategy(Enum):
    """
    Strategies for retrying operations after failures.

    - IMMEDIATE: Retry immediately without delay
    - FIXED_DELAY: Retry with a constant delay
    - EXPONENTIAL_BACKOFF: Retry with exponential delay
    - RANDOM_DELAY: Retry with randomized delay
    """
    IMMEDIATE = "immediate"
    FIXED_DELAY = "fixed_delay"
    EXPONENTIAL_BACKOFF = "exponential_backoff"
    RANDOM_DELAY = "random_delay"

class RecoveryMode(Enum):
    """
    Recovery modes for handling errors.

    - RETRY: Attempt to retry the operation
    - FALLBACK: Use fallback value or function
    - PARTIAL_RESULT: Return partial result if available
    - RECONFIGURE: Try with different configuration
    - HARDWARE_FALLBACK: Fallback to non-hardware implementation
    - FAIL_FAST: Fail quickly without recovery attempts
    """
    RETRY = "retry"
    FALLBACK = "fallback"
    PARTIAL_RESULT = "partial_result"
    RECONFIGURE = "reconfigure"
    HARDWARE_FALLBACK = "hardware_fallback"
    FAIL_FAST = "fail_fast"

class MetricType(Enum):
    """Types of metrics for error tracking."""
    COUNTER = "counter"  # Cumulative count
    GAUGE = "gauge"      # Value that can go up or down
    HISTOGRAM = "histogram"  # Distribution of values

class ErrorContext:
    """
    Context information for an error.

    Contains details about the error, including the exception, component,
    operation, timestamp, and any additional context.
    """

    def __init__(self,
                 exception: Exception,
                 category: ErrorCategory,
                 severity: ErrorSeverity,
                 component: str,
                 operation: str,
                 timestamp: Optional[float] = None,
                 traceback_str: Optional[str] = None,
                 extra: Optional[Dict[str, Any]] = None) -> None:
        """
        Initialize an error context.

        Args:
            exception: The exception that occurred
            category: Error category
            severity: Error severity
            component: Component where the error occurred
            operation: Operation that failed
            timestamp: Time when the error occurred (default: current time)
            traceback_str: Exception traceback as string
            extra: Additional contextual information
        """
        self.exception = exception
        self.exception_type = type(exception).__name__
        self.message = str(exception)
        self.category = category
        self.severity = severity
        self.component = component
        self.operation = operation
        self.timestamp = timestamp or time.time()
        self.traceback = traceback_str or ''.join(traceback.format_exception(
            type(exception), exception, exception.__traceback__
        ))
        self.extra = extra or {}
        self.handled = False
        self.recovery_attempted = False
        self.recovery_succeeded = False
        self.recovery_strategy = None

    def to_dict(self) -> Dict[str, Any]:
        """
        Convert to dictionary representation.

        Returns:
            Dictionary with error details
        """
        return {
            "exception_type": self.exception_type,
            "message": self.message,
            "category": self.category.value,
            "severity": self.severity.value,
            "component": self.component,
            "operation": self.operation,
            "timestamp": self.timestamp,
            "traceback": self.traceback,
            "extra": self.extra,
            "handled": self.handled,
            "recovery_attempted": self.recovery_attempted,
            "recovery_succeeded": self.recovery_succeeded,
            "recovery_strategy": self.recovery_strategy
        }

    def __str__(self) -> str:
        """String representation of the error context."""
        return (f"{self.severity.value.upper()} {self.category.value} error in "
                f"{self.component}.{self.operation}: {self.message}")

class CircuitBreaker:
    """
    Implementation of the circuit breaker pattern for fault isolation.

    Tracks failures and prevents operation execution when failure threshold
    is exceeded, providing fault isolation and preventing cascading failures.
    
    This class now uses the EnhancedCircuitBreaker when available for
    additional features like state persistence and adaptive thresholds.
    """

    def __init__(self,
                failure_threshold: int = 5,
                recovery_timeout: float = 60.0,
                half_open_max_calls: int = 1,
                name: Optional[str] = None,
                enable_persistence: bool = True) -> None:
        """
        Initialize the circuit breaker.

        Args:
            failure_threshold: Number of failures before opening the circuit
            recovery_timeout: Seconds before trying half-open state
            half_open_max_calls: Maximum calls in half-open state
            name: Optional name for the circuit breaker (used for persistence)
            enable_persistence: Whether to enable state persistence
        """
        # If enhanced circuit breaker is available and name is provided, use it
        if ENHANCED_ERRORS_AVAILABLE and name and enable_persistence:
            config = CircuitBreakerConfig(
                failure_threshold=failure_threshold,
                recovery_timeout=int(recovery_timeout),
                max_calls_half_open=half_open_max_calls,
                state_persistence_interval=10
            )
            self._enhanced_breaker = get_enhanced_circuit_breaker(name, config)
            self._use_enhanced = True
        else:
            self._enhanced_breaker = None
            self._use_enhanced = False
            
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.half_open_max_calls = half_open_max_calls

        self._state = CircuitBreakerState.CLOSED
        self._failure_count = 0
        self._last_failure_time = 0.0
        self._lock = threading.RLock()
        self._half_open_calls = 0

        # Metrics
        self._total_failures = 0
        self._total_successes = 0
        self._open_count = 0
        self._last_state_change_time = time.time()

    @property
    def state(self) -> CircuitBreakerState:
        """Get current circuit state."""
        if self._use_enhanced:
            return self._enhanced_breaker.state
        return self._state

    @property
    def failure_count(self) -> int:
        """Get current failure count."""
        if self._use_enhanced:
            return self._enhanced_breaker.failure_count
        return self._failure_count

    def reset(self) -> None:
        """Reset the circuit breaker to closed state."""
        if self._use_enhanced:
            self._enhanced_breaker.reset()
        else:
            with self._lock:
                self._state = CircuitBreakerState.CLOSED
                self._failure_count = 0
                self._half_open_calls = 0
                self._last_state_change_time = time.time()

    def _trip(self) -> None:
        """Trip the circuit breaker to open state."""
        with self._lock:
            if self._state != CircuitBreakerState.OPEN:
                self._state = CircuitBreakerState.OPEN
                self._last_failure_time = time.time()
                self._last_state_change_time = time.time()
                self._open_count += 1

    def record_failure(self) -> None:
        """Record a failure and potentially open the circuit."""
        if self._use_enhanced:
            self._enhanced_breaker.record_failure()
        else:
            with self._lock:
                self._total_failures += 1

                if self._state == CircuitBreakerState.CLOSED:
                    self._failure_count += 1
                    if self._failure_count >= self.failure_threshold:
                        _error_logger.warning(
                            f"Circuit breaker tripped after {self._failure_count} failures"
                        )
                        self._trip()

                elif self._state == CircuitBreakerState.HALF_OPEN:
                    _error_logger.info("Circuit breaker returning to open state after half-open failure")
                    self._trip()

    def record_success(self) -> None:
        """Record a success and potentially reset the circuit."""
        if self._use_enhanced:
            self._enhanced_breaker.record_success()
        else:
            with self._lock:
                self._total_successes += 1

                if self._state == CircuitBreakerState.HALF_OPEN:
                    self._half_open_calls += 1

                    if self._half_open_calls >= self.half_open_max_calls:
                        _error_logger.info("Circuit breaker reset after successful half-open calls")
                        self.reset()

    def allow_request(self) -> bool:
        """
        Check if a request is allowed based on circuit state.

        Returns:
            Whether the request should be allowed
        """
        if self._use_enhanced:
            return self._enhanced_breaker.allow_request()
            
        with self._lock:
            if self._state == CircuitBreakerState.CLOSED:
                return True

            elif self._state == CircuitBreakerState.OPEN:
                # Check if recovery timeout has elapsed
                elapsed = time.time() - self._last_failure_time
                if elapsed >= self.recovery_timeout:
                    _error_logger.info(
                        f"Circuit breaker entering half-open state after {elapsed:.1f}s"
                    )
                    self._state = CircuitBreakerState.HALF_OPEN
                    self._half_open_calls = 0
                    self._last_state_change_time = time.time()
                    return True
                return False

            elif self._state == CircuitBreakerState.HALF_OPEN:
                # Only allow limited calls in half-open state
                return self._half_open_calls < self.half_open_max_calls

            return False  # Shouldn't happen

    def get_metrics(self) -> Dict[str, Any]:
        """
        Get circuit breaker metrics.

        Returns:
            Metrics dictionary
        """
        with self._lock:
            return {
                "state": self._state.name,
                "failure_count": self._failure_count,
                "total_failures": self._total_failures,
                "total_successes": self._total_successes,
                "open_count": self._open_count,
                "half_open_calls": self._half_open_calls,
                "state_age": time.time() - self._last_state_change_time
            }

class ErrorMetric:
    """
    Metric for tracking error statistics.

    Provides counter and gauge metrics for error monitoring,
    with support for different error categories and components.
    """

    def __init__(self, name: str, description: str, metric_type: MetricType) -> None:
        """
        Initialize a metric.

        Args:
            name: Metric name
            description: Metric description
            metric_type: Type of metric
        """
        self.name = name
        self.description = description
        self.metric_type = metric_type
        self._lock = threading.RLock()

        # Counter metrics
        if metric_type == MetricType.COUNTER:
            self._counter = 0
            self._counters = defaultdict(int)  # For labeled counters

        # Gauge metrics
        elif metric_type == MetricType.GAUGE:
            self._value = 0.0
            self._values = defaultdict(float)  # For labeled gauges

        # Histogram metrics
        elif metric_type == MetricType.HISTOGRAM:
            self._samples = []
            self._samples_by_label = defaultdict(list)
            self._sample_limit = 1000  # Max samples to keep

    def inc(self, amount: float = 1.0, labels: Optional[Dict[str, str]] = None) -> None:
        """
        Increment counter metric.

        Args:
            amount: Amount to increment (default: 1)
            labels: Optional labels for multi-dimensional metrics
        """
        if self.metric_type != MetricType.COUNTER:
            raise ValueError(f"Cannot increment metric of type {self.metric_type}")

        with self._lock:
            if labels:
                label_key = self._labels_to_key(labels)
                self._counters[label_key] += amount
            else:
                self._counter += amount

    def set(self, value: float, labels: Optional[Dict[str, str]] = None) -> None:
        """
        Set gauge metric value.

        Args:
            value: Value to set
            labels: Optional labels for multi-dimensional metrics
        """
        if self.metric_type != MetricType.GAUGE:
            raise ValueError(f"Cannot set value for metric of type {self.metric_type}")

        with self._lock:
            if labels:
                label_key = self._labels_to_key(labels)
                self._values[label_key] = value
            else:
                self._value = value

    def observe(self, value: float, labels: Optional[Dict[str, str]] = None) -> None:
        """
        Observe a value for histogram metric.

        Args:
            value: Value to observe
            labels: Optional labels for multi-dimensional metrics
        """
        if self.metric_type != MetricType.HISTOGRAM:
            raise ValueError(f"Cannot observe value for metric of type {self.metric_type}")

        with self._lock:
            if labels:
                label_key = self._labels_to_key(labels)
                self._samples_by_label[label_key].append(value)

                # Trim if needed
                if len(self._samples_by_label[label_key]) > self._sample_limit:
                    self._samples_by_label[label_key] = (
                        self._samples_by_label[label_key][-self._sample_limit:]
                    )
            else:
                self._samples.append(value)

                # Trim if needed
                if len(self._samples) > self._sample_limit:
                    self._samples = self._samples[-self._sample_limit:]

    def get_value(self, labels: Optional[Dict[str, str]] = None) -> Union[float, List[float]]:
        """
        Get metric value.

        Args:
            labels: Optional labels for multi-dimensional metrics

        Returns:
            Current metric value
        """
        with self._lock:
            if self.metric_type == MetricType.COUNTER:
                if labels:
                    label_key = self._labels_to_key(labels)
                    return self._counters.get(label_key, 0)
                return self._counter

            elif self.metric_type == MetricType.GAUGE:
                if labels:
                    label_key = self._labels_to_key(labels)
                    return self._values.get(label_key, 0.0)
                return self._value

            elif self.metric_type == MetricType.HISTOGRAM:
                if labels:
                    label_key = self._labels_to_key(labels)
                    return self._samples_by_label.get(label_key, [])
                return self._samples

    def _labels_to_key(self, labels: Dict[str, str]) -> str:
        """
        Convert labels dictionary to string key.

        Args:
            labels: Labels dictionary

        Returns:
            String key for labels
        """
        return "|".join(f"{k}={v}" for k, v in sorted(labels.items()))

    def get_snapshot(self) -> Dict[str, Any]:
        """
        Get a snapshot of the metric.

        Returns:
            Dictionary with metric data
        """
        with self._lock:
            result = {
                "name": self.name,
                "description": self.description,
                "type": self.metric_type.value
            }

            if self.metric_type == MetricType.COUNTER:
                result["value"] = self._counter
                result["values_by_label"] = dict(self._counters)

            elif self.metric_type == MetricType.GAUGE:
                result["value"] = self._value
                result["values_by_label"] = dict(self._values)

            elif self.metric_type == MetricType.HISTOGRAM:
                samples = self._samples
                result["count"] = len(samples)
                result["sum"] = sum(samples) if samples else 0
                result["min"] = min(samples) if samples else 0
                result["max"] = max(samples) if samples else 0
                result["avg"] = sum(samples) / len(samples) if samples else 0

                # Add samples by label summaries
                label_summaries = {}
                for label, samples in self._samples_by_label.items():
                    label_summaries[label] = {
                        "count": len(samples),
                        "sum": sum(samples),
                        "min": min(samples) if samples else 0,
                        "max": max(samples) if samples else 0,
                        "avg": sum(samples) / len(samples) if samples else 0
                    }
                result["summaries_by_label"] = label_summaries

            return result

class ErrorMetricsCollector:
    """
    Collector for error-related metrics.

    Tracks various error metrics including counts, rates, durations,
    and recovery statistics.
    """

    _instance = None

    def __new__(cls) -> 'ErrorMetricsCollector':
        """Implement the Singleton pattern."""
        if cls._instance is None:
            cls._instance = super(ErrorMetricsCollector, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self) -> None:
        """Initialize the metrics collector."""
        # Skip initialization if already initialized
        if getattr(self, '_initialized', False):
            return

        self._lock = threading.RLock()
        self._metrics = {}

        # Create standard metrics
        self._create_standard_metrics()

        self._initialized = True

    def _create_standard_metrics(self) -> None:
        """Create standard error metrics."""
        # Error counts
        self.register_metric(
            "error_count",
            "Total number of errors",
            MetricType.COUNTER
        )

        # Error counts by category
        self.register_metric(
            "error_count_by_category",
            "Number of errors by category",
            MetricType.COUNTER
        )

        # Error counts by severity
        self.register_metric(
            "error_count_by_severity",
            "Number of errors by severity",
            MetricType.COUNTER
        )

        # Error counts by component
        self.register_metric(
            "error_count_by_component",
            "Number of errors by component",
            MetricType.COUNTER
        )

        # Recovery attempts
        self.register_metric(
            "recovery_attempts",
            "Number of recovery attempts",
            MetricType.COUNTER
        )

        # Successful recoveries
        self.register_metric(
            "successful_recoveries",
            "Number of successful recoveries",
            MetricType.COUNTER
        )

        # Retry counts
        self.register_metric(
            "retry_count",
            "Number of retry attempts",
            MetricType.COUNTER
        )

        # Fallback uses
        self.register_metric(
            "fallback_uses",
            "Number of fallback uses",
            MetricType.COUNTER
        )

        # Circuit breaker trips
        self.register_metric(
            "circuit_breaker_trips",
            "Number of circuit breaker trips",
            MetricType.COUNTER
        )

        # Hardware errors
        self.register_metric(
            "hardware_errors",
            "Number of hardware-related errors",
            MetricType.COUNTER
        )

        # Error durations
        self.register_metric(
            "error_duration",
            "Time spent handling errors (milliseconds)",
            MetricType.HISTOGRAM
        )

        # Current circuit breaker state (per component)
        self.register_metric(
            "circuit_breaker_state",
            "Current circuit breaker state (0=closed, 1=half-open, 2=open)",
            MetricType.GAUGE
        )

    def register_metric(self, name: str, description: str, metric_type: MetricType) -> ErrorMetric:
        """
        Register a new metric.

        Args:
            name: Metric name
            description: Metric description
            metric_type: Type of metric

        Returns:
            Created metric
        """
        with self._lock:
            if name in self._metrics:
                return self._metrics[name]

            metric = ErrorMetric(name, description, metric_type)
            self._metrics[name] = metric
            return metric

    def get_metric(self, name: str) -> Optional[ErrorMetric]:
        """
        Get a metric by name.

        Args:
            name: Metric name

        Returns:
            Metric or None if not found
        """
        return self._metrics.get(name)

    def record_error(self, error_context: ErrorContext) -> None:
        """
        Record metrics for an error.

        Args:
            error_context: Error context to record
        """
        # Increment total error count
        self.get_metric("error_count").inc()

        # Increment error count by category
        self.get_metric("error_count_by_category").inc(
            labels={"category": error_context.category.value}
        )

        # Increment error count by severity
        self.get_metric("error_count_by_severity").inc(
            labels={"severity": error_context.severity.value}
        )

        # Increment error count by component
        self.get_metric("error_count_by_component").inc(
            labels={"component": error_context.component}
        )

        # Record hardware errors separately
        if error_context.category == ErrorCategory.HARDWARE:
            self.get_metric("hardware_errors").inc()

    def record_recovery_attempt(self,
                              error_context: ErrorContext,
                              strategy: str,
                              success: bool) -> None:
        """
        Record a recovery attempt.

        Args:
            error_context: Error context
            strategy: Recovery strategy used
            success: Whether recovery was successful
        """
        # Increment recovery attempts
        self.get_metric("recovery_attempts").inc(
            labels={
                "category": error_context.category.value,
                "component": error_context.component,
                "strategy": strategy
            }
        )

        # Increment successful recoveries if applicable
        if success:
            self.get_metric("successful_recoveries").inc(
                labels={
                    "category": error_context.category.value,
                    "component": error_context.component,
                    "strategy": strategy
                }
            )

        # Record specific metrics for different strategies
        if strategy == "retry":
            self.get_metric("retry_count").inc()
        elif strategy == "fallback":
            self.get_metric("fallback_uses").inc()

    def record_circuit_breaker_trip(self, component: str, operation: str) -> None:
        """
        Record a circuit breaker trip.

        Args:
            component: Component name
            operation: Operation name
        """
        self.get_metric("circuit_breaker_trips").inc(
            labels={"component": component, "operation": operation}
        )

    def update_circuit_breaker_state(self,
                                   component: str,
                                   operation: str,
                                   state: CircuitBreakerState) -> None:
        """
        Update circuit breaker state metric.

        Args:
            component: Component name
            operation: Operation name
            state: Current circuit breaker state
        """
        # Map state to numeric value for gauge
        state_value = {
            CircuitBreakerState.CLOSED: 0,
            CircuitBreakerState.HALF_OPEN: 1,
            CircuitBreakerState.OPEN: 2
        }[state]

        self.get_metric("circuit_breaker_state").set(
            state_value,
            labels={"component": component, "operation": operation}
        )

    def record_error_duration(self,
                            category: ErrorCategory,
                            component: str,
                            duration_ms: float) -> None:
        """
        Record error handling duration.

        Args:
            category: Error category
            component: Component name
            duration_ms: Duration in milliseconds
        """
        self.get_metric("error_duration").observe(
            duration_ms,
            labels={"category": category.value, "component": component}
        )

    def get_all_metrics(self) -> Dict[str, Dict[str, Any]]:
        """
        Get snapshots of all metrics.

        Returns:
            Dictionary of metric snapshots
        """
        return {name: metric.get_snapshot() for name, metric in self._metrics.items()}


class ErrorCollector:
    """
    Collector for validation and processing errors.
    
    Aggregates errors during validation or processing operations,
    allowing for batch error reporting and analysis.
    """
    
    def __init__(self):
        """Initialize the error collector."""
        self._errors: List[ErrorContext] = []
        self._warnings: List[ErrorContext] = []
        self._lock = threading.RLock()
        self._error_count_by_severity = defaultdict(int)
        self._error_count_by_category = defaultdict(int)
    
    def add_error(self, 
                  error: Exception,
                  context: Optional[Dict[str, Any]] = None,
                  severity: Optional[ErrorSeverity] = None,
                  category: Optional[ErrorCategory] = None) -> None:
        """
        Add an error to the collection.
        
        Args:
            error: The exception to add
            context: Additional context about the error
            severity: Error severity (auto-detected if not provided)
            category: Error category (auto-detected if not provided)
        """
        with self._lock:
            if severity is None:
                severity = ErrorSeverity.from_exception(error)
            if category is None:
                category = ErrorCategory.from_exception(error)
                
            error_context = ErrorContext(
                error=error,
                category=category,
                severity=severity,
                component=context.get('component', 'unknown') if context else 'unknown',
                context=context or {}
            )
            
            if severity.value >= ErrorSeverity.ERROR.value:
                self._errors.append(error_context)
            else:
                self._warnings.append(error_context)
                
            self._error_count_by_severity[severity] += 1
            self._error_count_by_category[category] += 1
    
    def add_validation_error(self, 
                           field: str,
                           message: str,
                           value: Any = None,
                           row: Optional[int] = None) -> None:
        """
        Add a validation error.
        
        Args:
            field: Field name where error occurred
            message: Error message
            value: The invalid value
            row: Row number (if applicable)
        """
        context = {
            'field': field,
            'value': value,
            'row': row
        }
        error = ValueError(f"{field}: {message}")
        self.add_error(error, context, ErrorSeverity.ERROR, ErrorCategory.VALIDATION)
    
    def clear(self) -> None:
        """Clear all collected errors."""
        with self._lock:
            self._errors.clear()
            self._warnings.clear()
            self._error_count_by_severity.clear()
            self._error_count_by_category.clear()
    
    def has_errors(self) -> bool:
        """Check if any errors have been collected."""
        return len(self._errors) > 0
    
    def has_warnings(self) -> bool:
        """Check if any warnings have been collected."""
        return len(self._warnings) > 0
    
    def get_errors(self) -> List[ErrorContext]:
        """Get all collected errors."""
        with self._lock:
            return self._errors.copy()
    
    def get_warnings(self) -> List[ErrorContext]:
        """Get all collected warnings."""
        with self._lock:
            return self._warnings.copy()
    
    def get_error_count(self) -> int:
        """Get total error count."""
        return len(self._errors)
    
    def get_warning_count(self) -> int:
        """Get total warning count."""
        return len(self._warnings)
    
    def get_summary(self) -> Dict[str, Any]:
        """
        Get a summary of collected errors.
        
        Returns:
            Dictionary with error statistics
        """
        with self._lock:
            return {
                'total_errors': len(self._errors),
                'total_warnings': len(self._warnings),
                'errors_by_severity': dict(self._error_count_by_severity),
                'errors_by_category': dict(self._error_count_by_category),
                'error_messages': [str(e.error) for e in self._errors[:10]],  # First 10
                'warning_messages': [str(e.error) for e in self._warnings[:10]]
            }
    
    def raise_if_errors(self) -> None:
        """Raise an exception if any errors have been collected."""
        if self.has_errors():
            summary = self.get_summary()
            error_msg = f"Validation failed with {summary['total_errors']} errors"
            if summary['error_messages']:
                error_msg += f": {'; '.join(summary['error_messages'][:3])}"
                if len(summary['error_messages']) > 3:
                    error_msg += f" and {len(summary['error_messages']) - 3} more"
            raise ValueError(error_msg)
    
    def __str__(self) -> str:
        """String representation of error collector."""
        summary = self.get_summary()
        return f"ErrorCollector(errors={summary['total_errors']}, warnings={summary['total_warnings']})"
    
    def __repr__(self) -> str:
        """Detailed representation of error collector."""
        return str(self)


class ErrorPolicy(ABC):
    """
    Abstract base class for error handling policies.

    Defines how errors should be handled for specific categories,
    components, or operations.
    """

    @abstractmethod
    def should_handle(self, error_context: ErrorContext) -> bool:
        """
        Check if this policy should handle the error.

        Args:
            error_context: Error context

        Returns:
            Whether this policy applies
        """
        pass

    @abstractmethod
    def handle_error(self, error_context: ErrorContext) -> bool:
        """
        Handle an error according to the policy.

        Args:
            error_context: Error context

        Returns:
            Whether handling was successful
        """
        pass

class RetryPolicy(ErrorPolicy):
    """
    Policy for retrying operations on failure.

    Attempts to retry the operation that failed, with
    configurable retry count, delay, and backoff strategy.
    """

    def __init__(self,
                 categories: Set[ErrorCategory],
                 max_retries: int = 3,
                 retry_delay: float = 1.0,
                 strategy: RetryStrategy = RetryStrategy.EXPONENTIAL_BACKOFF,
                 jitter: float = 0.1) -> None:
        """
        Initialize the retry policy.

        Args:
            categories: Error categories this policy applies to
            max_retries: Maximum number of retry attempts
            retry_delay: Base delay between retries (seconds)
            strategy: Retry strategy to use
            jitter: Random factor for delay variation
        """
        self.categories = categories
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.strategy = strategy
        self.jitter = jitter

    def should_handle(self, error_context: ErrorContext) -> bool:
        """
        Check if this policy should handle the error.

        Args:
            error_context: Error context

        Returns:
            Whether this policy applies
        """
        return error_context.category in self.categories

    def handle_error(self, error_context: ErrorContext) -> bool:
        """
        Handle an error by retrying the operation.

        Args:
            error_context: Error context

        Returns:
            Whether handling was successful
        """
        # Placeholder for actual retry implementation
        # In real usage, this would be combined with the retry decorator
        error_context.recovery_attempted = True
        error_context.recovery_strategy = "retry"

        # Simulate retry logic for demonstration
        # In real usage, the original function would be called
        _error_logger.info(f"Retry policy would retry operation {error_context.operation} "
                         f"up to {self.max_retries} times with {self.strategy.value} strategy")

        # Record the retry attempt
        metrics = ErrorMetricsCollector()
        metrics.record_recovery_attempt(
            error_context,
            strategy="retry",
            success=False  # We don't actually retry here
        )

        return False  # Indicate handling wasn't successful in this context

class FallbackPolicy(ErrorPolicy):
    """
    Policy for using fallback values or functions on failure.

    Provides a fallback mechanism when operations fail,
    with support for different fallback strategies.
    """

    def __init__(self,
                 categories: Set[ErrorCategory],
                 default_value: Any = None,
                 fallback_function: Optional[Callable[..., Any]] = None) -> None:
        """
        Initialize the fallback policy.

        Args:
            categories: Error categories this policy applies to
            default_value: Default value to return on error
            fallback_function: Function to call for fallback value
        """
        self.categories = categories
        self.default_value = default_value
        self.fallback_function = fallback_function

    def should_handle(self, error_context: ErrorContext) -> bool:
        """
        Check if this policy should handle the error.

        Args:
            error_context: Error context

        Returns:
            Whether this policy applies
        """
        return error_context.category in self.categories

    def handle_error(self, error_context: ErrorContext) -> bool:
        """
        Handle an error by providing a fallback.

        Args:
            error_context: Error context

        Returns:
            Whether handling was successful
        """
        # Placeholder for actual fallback implementation
        # In real usage, this would be combined with the fallback decorator
        error_context.recovery_attempted = True
        error_context.recovery_strategy = "fallback"

        # Simulate fallback logic for demonstration
        # In real usage, the fallback value or function would be used
        _error_logger.info(f"Fallback policy would provide fallback for operation {error_context.operation}")

        # Record the fallback use
        metrics = ErrorMetricsCollector()
        metrics.record_recovery_attempt(
            error_context,
            strategy="fallback",
            success=True  # Assume fallback is successful
        )

        error_context.recovery_succeeded = True
        return True

class CircuitBreakerPolicy(ErrorPolicy):
    """
    Policy for implementing circuit breaker pattern.

    Prevents operation execution when the circuit is open
    due to repeated failures.
    """

    def __init__(self,
                 categories: Set[ErrorCategory],
                 failure_threshold: int = 5,
                 recovery_timeout: float = 60.0) -> None:
        """
        Initialize the circuit breaker policy.

        Args:
            categories: Error categories this policy applies to
            failure_threshold: Number of failures before opening the circuit
            recovery_timeout: Seconds before trying half-open state
        """
        self.categories = categories
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self._circuit_breakers = {}
        self._lock = threading.RLock()

    def should_handle(self, error_context: ErrorContext) -> bool:
        """
        Check if this policy should handle the error.

        Args:
            error_context: Error context

        Returns:
            Whether this policy applies
        """
        return error_context.category in self.categories

    def handle_error(self, error_context: ErrorContext) -> bool:
        """
        Handle an error using circuit breaker pattern.

        Args:
            error_context: Error context

        Returns:
            Whether handling was successful
        """
        # Get or create circuit breaker for this component/operation
        circuit_key = f"{error_context.component}.{error_context.operation}"
        circuit_breaker = self._get_circuit_breaker(circuit_key)

        # Record the failure
        circuit_breaker.record_failure()

        # Get metrics collector
        metrics = ErrorMetricsCollector()

        # Check if circuit breaker is now open
        if circuit_breaker.state == CircuitBreakerState.OPEN:
            # Circuit is now open, record the trip if it just happened
            metrics.record_circuit_breaker_trip(
                error_context.component,
                error_context.operation
            )

        # Update circuit breaker state metric
        metrics.update_circuit_breaker_state(
            error_context.component,
            error_context.operation,
            circuit_breaker.state
        )

        # Mark error as handled by circuit breaker
        error_context.recovery_attempted = True
        error_context.recovery_strategy = "circuit_breaker"
        error_context.recovery_succeeded = False

        return False  # Indicate the error wasn't actually recovered

    def _get_circuit_breaker(self, key: str) -> CircuitBreaker:
        """
        Get or create a circuit breaker for a component/operation.

        Args:
            key: Component/operation key

        Returns:
            Circuit breaker instance
        """
        with self._lock:
            if key not in self._circuit_breakers:
                self._circuit_breakers[key] = CircuitBreaker(
                    failure_threshold=self.failure_threshold,
                    recovery_timeout=self.recovery_timeout
                )
            return self._circuit_breakers[key]

    def allow_request(self, component: str, operation: str) -> bool:
        """
        Check if a request is allowed for a component/operation.

        Args:
            component: Component name
            operation: Operation name

        Returns:
            Whether the request should be allowed
        """
        circuit_key = f"{component}.{operation}"
        circuit_breaker = self._get_circuit_breaker(circuit_key)
        return circuit_breaker.allow_request()

    def get_circuit_breaker_metrics(self) -> Dict[str, Dict[str, Any]]:
        """
        Get metrics for all circuit breakers.

        Returns:
            Dictionary of circuit breaker metrics
        """
        with self._lock:
            return {
                key: breaker.get_metrics()
                for key, breaker in self._circuit_breakers.items()
            }

class PolicyManager:
    """
    Manager for error handling policies.

    Coordinates different error policies and applies them
    based on error context.
    """

    _instance = None

    def __new__(cls) -> 'PolicyManager':
        """Implement the Singleton pattern."""
        if cls._instance is None:
            cls._instance = super(PolicyManager, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self) -> None:
        """Initialize the policy manager."""
        # Skip initialization if already initialized
        if getattr(self, '_initialized', False):
            return

        self._policies = []
        self._lock = threading.RLock()

        # Create standard policies
        self._create_standard_policies()

        self._initialized = True

    def _create_standard_policies(self) -> None:
        """Create standard error policies."""
        # Retry policy for network errors
        self.register_policy(RetryPolicy(
            categories={
                ErrorCategory.NETWORK,
                ErrorCategory.TIMEOUT
            },
            max_retries=3,
            retry_delay=1.0,
            strategy=RetryStrategy.EXPONENTIAL_BACKOFF
        ))

        # Fallback policy for hardware errors
        self.register_policy(FallbackPolicy(
            categories={ErrorCategory.HARDWARE},
            default_value=None
        ))

        # Circuit breaker for repeated failures
        self.register_policy(CircuitBreakerPolicy(
            categories={
                ErrorCategory.NETWORK,
                ErrorCategory.DATA_ACCESS,
                ErrorCategory.TIMEOUT
            },
            failure_threshold=5,
            recovery_timeout=60.0
        ))

    def register_policy(self, policy: ErrorPolicy) -> None:
        """
        Register an error policy.

        Args:
            policy: Error policy to register
        """
        with self._lock:
            self._policies.append(policy)

    def get_policies_for_error(self, error_context: ErrorContext) -> List[ErrorPolicy]:
        """
        Get policies applicable to an error.

        Args:
            error_context: Error context

        Returns:
            List of applicable policies
        """
        with self._lock:
            return [p for p in self._policies if p.should_handle(error_context)]

    def apply_policies(self, error_context: ErrorContext) -> bool:
        """
        Apply all applicable policies to an error.

        Args:
            error_context: Error context

        Returns:
            Whether any policy handled the error successfully
        """
        policies = self.get_policies_for_error(error_context)

        if not policies:
            return False

        # Apply all policies
        success = False
        for policy in policies:
            policy_success = policy.handle_error(error_context)
            success = success or policy_success

        return success

    def get_circuit_breaker_policy(self) -> Optional[CircuitBreakerPolicy]:
        """
        Get the circuit breaker policy.

        Returns:
            Circuit breaker policy or None
        """
        with self._lock:
            for policy in self._policies:
                if isinstance(policy, CircuitBreakerPolicy):
                    return policy
        return None

    def get_retry_policy(self) -> Optional[RetryPolicy]:
        """
        Get the retry policy.

        Returns:
            Retry policy or None
        """
        with self._lock:
            for policy in self._policies:
                if isinstance(policy, RetryPolicy):
                    return policy
        return None

    def get_fallback_policy(self) -> Optional[FallbackPolicy]:
        """
        Get the fallback policy.

        Returns:
            Fallback policy or None
        """
        with self._lock:
            for policy in self._policies:
                if isinstance(policy, FallbackPolicy):
                    return policy
        return None

class ErrorManager:
    """
    Central manager for error handling functionality.

    Coordinates error reporting, tracking, policy application,
    and metrics collection. Now includes enhanced features:
    - Graceful degradation
    - Recovery orchestration
    - Error analytics
    - Resource-aware execution
    """

    _instance = None

    def __new__(cls) -> 'ErrorManager':
        """Implement the Singleton pattern."""
        if cls._instance is None:
            cls._instance = super(ErrorManager, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self) -> None:
        """Initialize the error manager."""
        # Skip initialization if already initialized
        if getattr(self, '_initialized', False):
            return

        self._lock = threading.RLock()
        self._error_history = deque(maxlen=100)  # Keep last 100 errors
        self._error_counts = defaultdict(int)  # Count by category

        # Track callbacks
        self._error_callbacks = []

        # Get policy manager and metrics collector
        self._policy_manager = PolicyManager()
        self._metrics_collector = ErrorMetricsCollector()
        
        # Initialize enhanced components if available
        if ENHANCED_ERRORS_AVAILABLE:
            self._degradation_manager = get_degradation_manager()
            self._recovery_manager = get_recovery_manager()
            self._resource_manager = get_resource_manager()
            self._error_analytics = ErrorAnalytics()
        else:
            self._degradation_manager = None
            self._recovery_manager = None
            self._resource_manager = None
            self._error_analytics = None

        self._initialized = True

    def report_error(self,
                    exception: Exception,
                    category: Optional[ErrorCategory] = None,
                    severity: Optional[ErrorSeverity] = None,
                    component: str = "unknown",
                    operation: str = "unknown",
                    extra: Optional[Dict[str, Any]] = None) -> ErrorContext:
        """
        Report an error.

        Args:
            exception: Exception that occurred
            category: Error category (auto-detected if None)
            severity: Error severity (auto-detected if None)
            component: Component where the error occurred
            operation: Operation that failed
            extra: Additional contextual information

        Returns:
            Error context object
        """
        # Determine category and severity
        if category is None:
            category = ErrorCategory.from_exception(exception)

        if severity is None:
            severity = ErrorSeverity.from_exception(exception)

        # Create error context
        error_context = ErrorContext(
            exception=exception,
            category=category,
            severity=severity,
            component=component,
            operation=operation,
            extra=extra or {}
        )

        # Record in history
        with self._lock:
            self._error_history.append(error_context)
            self._error_counts[category] += 1

        # Record metrics
        self._metrics_collector.record_error(error_context)

        # Log the error
        log_level = {
            ErrorSeverity.DEBUG: logging.DEBUG,
            ErrorSeverity.INFO: logging.INFO,
            ErrorSeverity.WARNING: logging.WARNING,
            ErrorSeverity.ERROR: logging.ERROR,
            ErrorSeverity.CRITICAL: logging.CRITICAL
        }[severity]

        _error_logger.log(
            log_level,
            f"{category.value} error in {component}.{operation}: {str(exception)}",
            exc_info=(log_level >= logging.ERROR)
        )

        # Apply policies
        start_time = time.time()
        recovery_success = self._policy_manager.apply_policies(error_context)
        duration_ms = (time.time() - start_time) * 1000.0

        # Record error handling duration
        self._metrics_collector.record_error_duration(
            category=category,
            component=component,
            duration_ms=duration_ms
        )

        # Invoke callbacks
        for callback in self._error_callbacks:
            try:
                callback(error_context)
            except Exception as e:
                _error_logger.warning(f"Error callback failed: {str(e)}")

        return error_context

    def register_error_callback(self, callback: Callable[[ErrorContext], None]) -> None:
        """
        Register a callback for error notifications.

        Args:
            callback: Function to call when an error is reported
        """
        with self._lock:
            self._error_callbacks.append(callback)

    def get_recent_errors(self,
                        count: int = 10,
                        category: Optional[ErrorCategory] = None,
                        severity: Optional[ErrorSeverity] = None,
                        component: Optional[str] = None) -> List[ErrorContext]:
        """
        Get recent errors with optional filtering.

        Args:
            count: Maximum number of errors to return
            category: Filter by error category
            severity: Filter by error severity
            component: Filter by component

        Returns:
            List of error contexts
        """
        with self._lock:
            # Apply filters
            filtered_errors = list(self._error_history)

            if category:
                filtered_errors = [e for e in filtered_errors if e.category == category]

            if severity:
                filtered_errors = [e for e in filtered_errors if e.severity == severity]

            if component:
                filtered_errors = [e for e in filtered_errors if e.component == component]

            # Return the most recent errors
            return list(reversed(filtered_errors))[:count]

    def get_error_counts(self) -> Dict[ErrorCategory, int]:
        """
        Get error counts by category.

        Returns:
            Dictionary of error counts
        """
        with self._lock:
            return dict(self._error_counts)

    def get_metrics(self) -> Dict[str, Dict[str, Any]]:
        """
        Get all error metrics.

        Returns:
            Dictionary of metric snapshots
        """
        return self._metrics_collector.get_all_metrics()

    def allow_operation(self, component: str, operation: str) -> bool:
        """
        Check if an operation is allowed based on circuit breaker state.

        Args:
            component: Component name
            operation: Operation name

        Returns:
            Whether the operation should be allowed
        """
        circuit_breaker_policy = self._policy_manager.get_circuit_breaker_policy()

        if circuit_breaker_policy:
            return circuit_breaker_policy.allow_request(component, operation)

        return True  # Allow if no circuit breaker policy
    
    def can_degrade(self, component: str, operation: str, 
                   error: Exception) -> Optional[DegradationLevel]:
        """
        Check if graceful degradation is available for an operation.
        
        Args:
            component: Component name
            operation: Operation name
            error: The error that occurred
            
        Returns:
            Degradation level if available, None otherwise
        """
        if self._degradation_manager:
            return self._degradation_manager.get_degradation_level(
                component, operation, error
            )
        return None
    
    def execute_with_degradation(self, func: Callable[..., T],
                               component: str, operation: str,
                               *args, **kwargs) -> T:
        """
        Execute a function with graceful degradation support.
        
        Args:
            func: Function to execute
            component: Component name
            operation: Operation name
            *args, **kwargs: Arguments for the function
            
        Returns:
            Function result or degraded result
        """
        if self._degradation_manager:
            return self._degradation_manager.execute_with_degradation(
                func, component, operation, *args, **kwargs
            )
        else:
            # Fallback to simple execution
            return func(*args, **kwargs)
    
    def check_resources(self, operation: str) -> bool:
        """
        Check if resources are available for an operation.
        
        Args:
            operation: Operation name
            
        Returns:
            Whether resources are available
        """
        if self._resource_manager:
            return self._resource_manager.can_execute_operation(operation)
        return True
    
    def get_error_analytics(self) -> Optional[ErrorAnalytics]:
        """Get error analytics instance if available."""
        return self._error_analytics
    
    def predict_failures(self, component: str, 
                        time_window_hours: int = 1) -> List[Any]:
        """
        Predict potential failures based on error patterns.
        
        Args:
            component: Component to analyze
            time_window_hours: Time window for prediction
            
        Returns:
            List of predicted failures
        """
        if self._error_analytics:
            return self._error_analytics.predict_failures(
                component, time_window_hours
            )
        return []
    
    def get_recovery_manager(self) -> Optional[Any]:
        """Get recovery manager if available."""
        return self._recovery_manager

# Global error manager instance
_error_manager = None

def get_error_manager() -> ErrorManager:
    """
    Get the singleton error manager instance.

    Returns:
        Error manager instance
    """
    global _error_manager
    if _error_manager is None:
        _error_manager = ErrorManager()
    return _error_manager

def report_error(exception: Exception,
                category: Optional[ErrorCategory] = None,
                severity: Optional[ErrorSeverity] = None,
                component: str = "unknown",
                operation: str = "unknown",
                extra: Optional[Dict[str, Any]] = None) -> ErrorContext:
    """
    Report an error to the error manager.

    Args:
        exception: Exception that occurred
        category: Error category (auto-detected if None)
        severity: Error severity (auto-detected if None)
        component: Component where the error occurred
        operation: Operation that failed
        extra: Additional contextual information

    Returns:
        Error context object
    """
    manager = get_error_manager()
    return manager.report_error(
        exception=exception,
        category=category,
        severity=severity,
        component=component,
        operation=operation,
        extra=extra
    )

def with_error_handling(category: ErrorCategory,
                      severity: ErrorSeverity,
                      fallback_value: Any = None,
                      retry_count: int = 0,
                      retry_delay: float = 1.0,
                      require_circuit_breaker: bool = False,
                      enable_degradation: bool = True,
                      resource_aware: bool = True,
                      use_enhanced_circuit_breaker: bool = True) -> Callable[[F], F]:
    """
    Decorator for comprehensive error handling.

    Wraps a function with error handling, including reporting,
    retries, fallbacks, circuit breaker integration, graceful degradation,
    and resource awareness.

    Args:
        category: Error category for classification
        severity: Error severity level
        fallback_value: Value to return on error
        retry_count: Number of retry attempts
        retry_delay: Delay between retries in seconds
        require_circuit_breaker: Whether to require circuit breaker check
        enable_degradation: Whether to enable graceful degradation
        resource_aware: Whether to check resources before execution
        use_enhanced_circuit_breaker: Whether to use enhanced circuit breaker

    Returns:
        Decorated function with error handling
    """
    def decorator(func: F) -> F:
        # Get function information for error reporting
        module_name = func.__module__
        func_name = func.__qualname__

        # Component name is the module
        component = module_name.split('.')[-1] if module_name else "unknown"
        
        # Create enhanced circuit breaker if requested
        circuit_breaker = None
        if require_circuit_breaker and use_enhanced_circuit_breaker and ENHANCED_ERRORS_AVAILABLE:
            circuit_breaker_name = f"{component}.{func_name}"
            circuit_breaker = CircuitBreaker(
                name=circuit_breaker_name,
                failure_threshold=5,
                recovery_timeout=60.0,
                enable_persistence=True
            )

        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            error_manager = get_error_manager()
            
            # Check resources if enabled
            if resource_aware and not error_manager.check_resources(func_name):
                _error_logger.warning(f"Insufficient resources for {component}.{func_name}")
                if enable_degradation:
                    # Try degraded execution
                    return error_manager.execute_with_degradation(
                        func, component, func_name, *args, **kwargs
                    )
                return fallback_value
            
            # Check circuit breaker if required
            if circuit_breaker and not circuit_breaker.allow_request():
                _error_logger.warning(f"Circuit breaker open for {component}.{func_name}")
                if enable_degradation:
                    # Try degraded execution
                    return error_manager.execute_with_degradation(
                        func, component, func_name, *args, **kwargs
                    )
                return fallback_value
            elif require_circuit_breaker and not use_enhanced_circuit_breaker:
                # Use standard circuit breaker check
                if not error_manager.allow_operation(component, func_name):
                    _error_logger.warning(f"Circuit breaker open for {component}.{func_name}")
                    return fallback_value

            # Try to call the function with retries
            last_exception = None

            for attempt in range(retry_count + 1):
                try:
                    result = func(*args, **kwargs)

                    # Record success for circuit breaker
                    if circuit_breaker:
                        circuit_breaker.record_success()
                    elif attempt > 0:
                        circuit_breaker_policy = PolicyManager().get_circuit_breaker_policy()
                        if circuit_breaker_policy:
                            circuit_key = f"{component}.{func_name}"
                            cb = circuit_breaker_policy._get_circuit_breaker(circuit_key)
                            cb.record_success()

                    return result

                except Exception as e:
                    last_exception = e

                    # Record failure for circuit breaker
                    if circuit_breaker:
                        circuit_breaker.record_failure()

                    # If not the last attempt, retry
                    if attempt < retry_count:
                        # Log retry attempt
                        _error_logger.info(
                            f"Retry {attempt+1}/{retry_count} for {component}.{func_name} after error: {str(e)}"
                        )

                        # Delay before retry
                        if retry_delay > 0:
                            time.sleep(retry_delay)
                    else:
                        # Report the final error
                        error_context = report_error(
                            exception=e,
                            category=category,
                            severity=severity,
                            component=component,
                            operation=func_name,
                            extra={
                                "args": str(args),
                                "kwargs": str(kwargs),
                                "retries": retry_count
                            }
                        )
                        
                        # Try graceful degradation if enabled
                        if enable_degradation:
                            degradation_level = error_manager.can_degrade(
                                component, func_name, e
                            )
                            if degradation_level:
                                _error_logger.info(
                                    f"Attempting graceful degradation at level {degradation_level} "
                                    f"for {component}.{func_name}"
                                )
                                try:
                                    return error_manager.execute_with_degradation(
                                        func, component, func_name, *args, **kwargs
                                    )
                                except Exception as deg_error:
                                    _error_logger.error(
                                        f"Graceful degradation failed: {deg_error}"
                                    )

                        # Return fallback value if provided
                        if fallback_value is not None:
                            return fallback_value

                        # Re-raise the exception
                        raise

            # Should not reach here, but if it does, re-raise the last exception
            if last_exception is not None:
                raise last_exception

            return None  # Should not reach here

        return cast(F, wrapper)

    return decorator

def with_retry(retry_count: int = 3,
             retry_delay: float = 1.0,
             strategy: RetryStrategy = RetryStrategy.EXPONENTIAL_BACKOFF,
             exception_types: Optional[List[Type[Exception]]] = None) -> Callable[[F], F]:
    """
    Decorator for retrying functions on failure.

    Args:
        retry_count: Number of retry attempts
        retry_delay: Base delay between retries in seconds
        strategy: Retry strategy to use
        exception_types: Types of exceptions to retry on (None for all)

    Returns:
        Decorated function with retry functionality
    """
    def decorator(func: F) -> F:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None

            for attempt in range(retry_count + 1):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    # Check if we should retry this exception type
                    if exception_types and not any(isinstance(e, exc_type) for exc_type in exception_types):
                        raise

                    last_exception = e

                    # If not the last attempt, retry
                    if attempt < retry_count:
                        # Calculate delay based on strategy
                        delay = retry_delay

                        if strategy == RetryStrategy.EXPONENTIAL_BACKOFF:
                            delay = retry_delay * (2 ** attempt)
                        elif strategy == RetryStrategy.RANDOM_DELAY:
                            # Standard library imports
                            import random
                            delay = retry_delay * (0.5 + random.random())

                        # Log retry attempt
                        _error_logger.info(
                            f"Retry {attempt+1}/{retry_count} for {func.__name__} "
                            f"after error: {str(e)}, delay: {delay:.2f}s"
                        )

                        # Delay before retry
                        if delay > 0:
                            time.sleep(delay)

            # Retries exhausted, re-raise the last exception
            if last_exception is not None:
                raise last_exception

            return None  # Should not reach here

        return cast(F, wrapper)

    return decorator

def with_fallback(fallback_value: Any = None,
                fallback_function: Optional[Callable] = None,
                exception_types: Optional[List[Type[Exception]]] = None) -> Callable[[F], F]:
    """
    Decorator for providing fallback on failure.

    Args:
        fallback_value: Value to return on error
        fallback_function: Function to call for fallback value
        exception_types: Types of exceptions to handle (None for all)

    Returns:
        Decorated function with fallback functionality
    """
    def decorator(func: F) -> F:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                # Check if we should handle this exception type
                if exception_types and not any(isinstance(e, exc_type) for exc_type in exception_types):
                    raise

                # Log fallback use
                _error_logger.info(
                    f"Using fallback for {func.__name__} after error: {str(e)}"
                )

                # Use fallback function if provided
                if fallback_function is not None:
                    return fallback_function(*args, **kwargs)

                # Otherwise return fallback value
                return fallback_value

        return cast(F, wrapper)

    return decorator

def with_circuit_breaker(circuit_breaker_name: str = None,
                       name: str = None,
                       failure_threshold: int = 5,
                       recovery_timeout: float = 60.0,
                       fallback_value: Any = None,
                       handle_exception: bool = False,
                       use_enhanced: bool = True,
                       enable_persistence: bool = True,
                       enable_health_checks: bool = True) -> Callable[[F], F]:
    """
    Decorator for implementing circuit breaker pattern.
    
    Now supports both basic and enhanced circuit breakers with
    state persistence, health checks, and adaptive thresholds.

    Args:
        circuit_breaker_name: Name for the circuit breaker (legacy parameter)
        name: Name for the circuit breaker 
        failure_threshold: Number of failures before opening the circuit
        recovery_timeout: Seconds before trying half-open state
        fallback_value: Value to return when circuit is open
        handle_exception: Whether to handle exceptions (return fallback) or re-raise
        use_enhanced: Whether to use enhanced circuit breaker if available
        enable_persistence: Whether to enable state persistence
        enable_health_checks: Whether to enable health check integration

    Returns:
        Decorated function with circuit breaker functionality
    """
    def decorator(func: F) -> F:
        # Create a circuit breaker for this function
        module_name = func.__module__
        func_name = func.__qualname__
        component = module_name.split('.')[-1] if module_name else "unknown"
        
        # Use provided name or generate one
        cb_name = name or circuit_breaker_name or f"{component}.{func_name}"

        # Create appropriate circuit breaker
        if use_enhanced and ENHANCED_ERRORS_AVAILABLE:
            circuit_breaker = CircuitBreaker(
                name=cb_name,
                failure_threshold=failure_threshold,
                recovery_timeout=recovery_timeout,
                enable_persistence=enable_persistence
            )
        else:
            circuit_breaker = CircuitBreaker(
                failure_threshold=failure_threshold,
                recovery_timeout=recovery_timeout
            )

        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Check if circuit is closed
            if not circuit_breaker.allow_request():
                _error_logger.info(
                    f"Circuit breaker open for {func.__name__}, skipping call"
                )

                # Update circuit breaker state metric
                metrics = ErrorMetricsCollector()
                metrics.update_circuit_breaker_state(
                    component,
                    func_name,
                    circuit_breaker.state
                )
                
                # If enhanced features are available, check for degradation
                if ENHANCED_ERRORS_AVAILABLE and handle_exception:
                    error_manager = get_error_manager()
                    try:
                        # Create a synthetic exception for circuit breaker open
                        cb_error = CircuitBreakerOpenError(
                            f"Circuit breaker open for {cb_name}"
                        ) if 'CircuitBreakerOpenError' in globals() else RuntimeError(
                            f"Circuit breaker open for {cb_name}"
                        )
                        
                        # Try graceful degradation
                        degraded_result = error_manager.execute_with_degradation(
                            func, component, func_name, *args, **kwargs
                        )
                        if degraded_result is not None:
                            return degraded_result
                    except Exception:
                        pass  # Fall back to returning fallback_value

                return fallback_value

            try:
                result = func(*args, **kwargs)

                # Record success
                circuit_breaker.record_success()

                return result

            except Exception as e:
                # Record failure
                circuit_breaker.record_failure()

                # Update circuit breaker state metric
                metrics = ErrorMetricsCollector()
                metrics.update_circuit_breaker_state(
                    component,
                    func_name,
                    circuit_breaker.state
                )

                # If the circuit just opened, log it
                if circuit_breaker.state == CircuitBreakerState.OPEN:
                    _error_logger.warning(
                        f"Circuit breaker tripped for {func.__name__} "
                        f"after {failure_threshold} failures"
                    )

                    # Record the circuit breaker trip
                    metrics.record_circuit_breaker_trip(
                        component,
                        func_name
                    )
                
                # Handle exception if requested
                if handle_exception:
                    _error_logger.error(f"Error in {func.__name__}: {e}")
                    return fallback_value
                else:
                    # Re-raise the exception
                    raise

        return cast(F, wrapper)

    return decorator

def with_hardware_error_handling(cpu_fallback_function: Optional[Callable] = None,
                               fallback_value: Any = None) -> Callable[[F], F]:
    """
    Decorator for handling hardware acceleration errors.

    Attempts to run a hardware-accelerated function and falls back to
    CPU implementation on hardware errors.

    Args:
        cpu_fallback_function: CPU implementation to use on hardware error
        fallback_value: Value to return if both hardware and CPU fallback fail

    Returns:
        Decorated function with hardware error handling
    """
    def decorator(func: F) -> F:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                # Try hardware implementation
                return func(*args, **kwargs)
            except Exception as e:
                # Check if this is a hardware-related error
                is_hw_error = (
                    'cuda' in str(e).lower() or
                    'gpu' in str(e).lower() or
                    'metal' in str(e).lower() or
                    'mps' in str(e).lower() or
                    'hardware' in str(e).lower()
                )

                if not is_hw_error:
                    # Not a hardware error, re-raise
                    raise

                # Log hardware error
                module_name = func.__module__
                component = module_name.split('.')[-1] if module_name else "unknown"

                _error_logger.warning(
                    f"Hardware error in {func.__name__}: {str(e)}, falling back to CPU implementation"
                )

                # Report the hardware error
                report_error(
                    exception=e,
                    category=ErrorCategory.HARDWARE,
                    severity=ErrorSeverity.WARNING,
                    component=component,
                    operation=func.__name__,
                    extra={"hardware_function": func.__name__}
                )

                # Try CPU fallback if provided
                if cpu_fallback_function is not None:
                    try:
                        return cpu_fallback_function(*args, **kwargs)
                    except Exception as cpu_error:
                        # CPU fallback also failed
                        _error_logger.error(
                            f"CPU fallback for {func.__name__} also failed: {str(cpu_error)}"
                        )

                        # Report the CPU fallback error
                        report_error(
                            exception=cpu_error,
                            category=ErrorCategory.CALCULATION,
                            severity=ErrorSeverity.ERROR,
                            component=component,
                            operation=f"{func.__name__}_cpu_fallback",
                            extra={"original_error": str(e)}
                        )

                        # Return fallback value if provided
                        if fallback_value is not None:
                            return fallback_value

                        # Otherwise re-raise the CPU error
                        raise

                # No CPU fallback provided, return fallback value
                if fallback_value is not None:
                    return fallback_value

                # No fallback value, re-raise the hardware error
                raise

        return cast(F, wrapper)

    return decorator

@contextmanager
def error_context(category: ErrorCategory,
                 severity: ErrorSeverity,
                 component: str,
                 operation: str,
                 extra: Optional[Dict[str, Any]] = None):
    """
    Context manager for error handling.

    Provides a clean way to wrap blocks of code with error handling.

    Args:
        category: Error category for classification
        severity: Error severity level
        component: Component where the error might occur
        operation: Operation being performed
        extra: Additional contextual information

    Yields:
        Nothing, this is a context manager
    """
    try:
        yield
    except Exception as e:
        # Report the error
        report_error(
            exception=e,
            category=category,
            severity=severity,
            component=component,
            operation=operation,
            extra=extra
        )

        # Re-raise to propagate
        raise

# Examples of usage:

# 1. Basic error handling
@with_error_handling(
    category=ErrorCategory.CALCULATION,
    severity=ErrorSeverity.ERROR
)
def calculate_value(x: float, y: float) -> float:
    """Example function with error handling."""
    if y == 0:
        raise ValueError("Division by zero")
    return x / y

# 2. Error handling with fallback
@with_error_handling(
    category=ErrorCategory.CALCULATION,
    severity=ErrorSeverity.WARNING,
    fallback_value=0.0
)
def calculate_with_fallback(x: float, y: float) -> float:
    """Example function with error handling and fallback."""
    if y == 0:
        raise ValueError("Division by zero")
    return x / y

# 3. Error handling with retry
@with_error_handling(
    category=ErrorCategory.NETWORK,
    severity=ErrorSeverity.WARNING,
    retry_count=3,
    retry_delay=1.0
)
def fetch_data(url: str) -> Dict[str, Any]:
    """Example function with error handling and retry."""
    # Simulated fetch function
    # Standard library imports
    import random
    if random.random() < 0.5:  # Simulate random failures
        raise ConnectionError("Network error")
    return {"data": "example"}

# 4. Hardware acceleration error handling
@with_hardware_error_handling(
    cpu_fallback_function=lambda x, y: x + y,  # CPU implementation
    fallback_value=None
)
def hardware_accelerated_add(x: float, y: float) -> float:
    """Example function with hardware error handling."""
    # Simulated hardware implementation
    # Standard library imports
    import random
    if random.random() < 0.1:  # Simulate occasional hardware errors
        raise RuntimeError("CUDA error: out of memory")
    return x + y

# 5. Circuit breaker pattern
@with_circuit_breaker(
    failure_threshold=3,
    recovery_timeout=10.0,
    fallback_value=None
)
def external_service_call(request_data: Dict[str, Any]) -> Dict[str, Any]:
    """Example function with circuit breaker pattern."""
    # Simulated external service call
    # Standard library imports
    import random
    if random.random() < 0.7:  # Simulate high failure rate
        raise ConnectionError("Service unavailable")
    return {"status": "success"}

# 6. Complete error handling example
@with_error_handling(
    category=ErrorCategory.DATA_ACCESS,
    severity=ErrorSeverity.ERROR,
    fallback_value={"data": []},
    retry_count=2,
    require_circuit_breaker=True
)
def fetch_and_process_data(source_id: str, options: Dict[str, Any]) -> Dict[str, Any]:
    """Example function with comprehensive error handling."""
    # Simulated data fetch and processing
    # Standard library imports
    import random

    if random.random() < 0.3:  # Simulate network errors
        raise ConnectionError(f"Failed to connect to data source {source_id}")

    if random.random() < 0.2:  # Simulate processing errors
        raise ValueError(f"Invalid data format in source {source_id}")

    # Simulated successful processing
    return {
        "data": [1, 2, 3],
        "source": source_id,
        "processed": True
    }


def collect_context(**kwargs):
    """
    Collect context information for error reporting and debugging.
    
    This function gathers contextual information that can be used
    for error reporting, debugging, and analysis.
    
    Args:
        **kwargs: Additional context parameters
        
    Returns:
        Dict containing context information
    """
    # Standard library imports
    import os
    import sys
    from datetime import datetime

    # Third-party imports
    import psutil
    
    context = {
        'timestamp': datetime.now().isoformat(),
        'python_version': sys.version,
        'platform': sys.platform,
        'process_id': os.getpid(),
        'working_directory': os.getcwd(),
        'memory_usage_mb': psutil.Process().memory_info().rss / 1024 / 1024,
        'cpu_percent': psutil.cpu_percent(),
    }
    
    # Add any additional context provided
    context.update(kwargs)
    
    # Add stack trace information if available
    try:
        # Standard library imports
        import traceback
        context['stack_trace'] = traceback.format_stack()
    except Exception:
        context['stack_trace'] = None
    
    return context


def with_enhanced_error_handling(
    component: str,
    operation: str,
    fallback_chain: Optional[List[Callable]] = None,
    enable_analytics: bool = True,
    enable_recovery: bool = True
) -> Callable[[F], F]:
    """
    Advanced error handling decorator using enhanced features.
    
    This decorator provides comprehensive error handling with:
    - Fallback chains for multi-level degradation
    - Error analytics and pattern detection
    - Recovery orchestration
    - Resource-aware execution
    
    Args:
        component: Component name for error tracking
        operation: Operation name for error tracking
        fallback_chain: List of fallback functions to try in order
        enable_analytics: Whether to enable error analytics
        enable_recovery: Whether to enable recovery management
        
    Returns:
        Decorated function with enhanced error handling
    """
    def decorator(func: F) -> F:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            if not ENHANCED_ERRORS_AVAILABLE:
                # Fall back to simple execution
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    if fallback_chain:
                        for fallback in fallback_chain:
                            try:
                                return fallback(*args, **kwargs)
                            except Exception:
                                continue
                    raise
            
            error_manager = get_error_manager()
            
            # Create fallback chain if available
            if fallback_chain and hasattr(FallbackChain, '__init__'):
                chain = FallbackChain(component)
                for fb in fallback_chain:
                    chain.add_fallback(
                        DefaultValueFallback(fb) if not callable(fb) else fb
                    )
            else:
                chain = None
            
            # Check resources
            if not error_manager.check_resources(operation):
                _error_logger.warning(
                    f"Insufficient resources for {component}.{operation}"
                )
                # Try degraded execution
                return error_manager.execute_with_degradation(
                    func, component, operation, *args, **kwargs
                )
            
            try:
                # Execute with recovery management if enabled
                if enable_recovery and error_manager.get_recovery_manager():
                    recovery_manager = error_manager.get_recovery_manager()
                    result = recovery_manager.execute_with_recovery(
                        func, component, operation, *args, **kwargs
                    )
                else:
                    result = func(*args, **kwargs)
                
                return result
                
            except Exception as e:
                # Report error
                error_context = report_error(
                    exception=e,
                    category=ErrorCategory.from_exception(e),
                    severity=ErrorSeverity.from_exception(e),
                    component=component,
                    operation=operation
                )
                
                # Analyze error patterns if enabled
                if enable_analytics and error_manager.get_error_analytics():
                    analytics = error_manager.get_error_analytics()
                    analytics.analyze_error(error_context)
                    
                    # Check for predicted failures
                    predictions = error_manager.predict_failures(component)
                    if predictions:
                        _error_logger.warning(
                            f"Predicted failures detected for {component}: "
                            f"{len(predictions)} potential issues"
                        )
                
                # Try fallback chain if available
                if chain:
                    try:
                        return chain.execute(func, error_context, *args, **kwargs)
                    except Exception as chain_error:
                        _error_logger.error(
                            f"Fallback chain failed: {chain_error}"
                        )
                
                # Try graceful degradation
                try:
                    return error_manager.execute_with_degradation(
                        func, component, operation, *args, **kwargs
                    )
                except Exception as deg_error:
                    _error_logger.error(
                        f"Graceful degradation failed: {deg_error}"
                    )
                
                # Re-raise original exception
                raise
        
        return cast(F, wrapper)
    
    return decorator