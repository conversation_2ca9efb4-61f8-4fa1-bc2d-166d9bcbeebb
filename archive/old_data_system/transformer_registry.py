"""
Transformer Registry for Yemen Trade Diagnostic V2

This module provides a registry system for data transformers,
allowing easy registration and retrieval of transformers by chart type or ID.
"""

# Standard library imports
import inspect
from pathlib import Path
from typing import Any, Dict, List, Optional, Type

# Third-party imports
import pandas as pd

# Project imports
from yemen_trade_diagnostic.interfaces.error_interface import (
    ErrorCategory,
    ErrorSeverity,
    with_error_handling,
)

# V2 interface imports
from yemen_trade_diagnostic.interfaces.logging_interface import get_logger

# Column mapping for standardization
BACI_TO_SEMANTIC_COLUMN_MAPPING = {
    'i': 'exporter_iso',
    'j': 'importer_iso',
    'k': 'product_code',
    'v': 'trade_value_usd',
    't': 'year'
}

# Project imports
# Import transformer base and implementations
from yemen_trade_diagnostic.data.transformers import (
    CompositionTransformer,
    ConcentrationTransformer,
    DataTransformer,
    GrowthTransformer,
)

logger = get_logger(__name__)


class TransformerRegistry:
    """
    Registry for data transformers.
    
    Manages registration, discovery, and instantiation of data transformers.
    """
    
    def __init__(self):
        self._transformers: Dict[str, Type[DataTransformer]] = {}
        self._chart_mappings: Dict[str, str] = {}
        self.logger = logger
        
        # Register default transformers
        self._register_defaults()
    
    def _register_defaults(self):
        """Register default transformers."""
        # Analysis type mappings
        self.register_transformer("composition", CompositionTransformer)
        self.register_transformer("growth", GrowthTransformer)
        self.register_transformer("concentration", ConcentrationTransformer)
        
        # Specific chart ID mappings
        self.register_chart_mapping("export_composition", "composition")
        self.register_chart_mapping("import_composition", "composition")
        self.register_chart_mapping("sector_composition", "composition")
        self.register_chart_mapping("trade_growth", "growth")
        self.register_chart_mapping("export_growth", "growth")
        self.register_chart_mapping("market_concentration", "concentration")
        self.register_chart_mapping("product_concentration", "concentration")
        
        self.logger.info(f"Registered {len(self._transformers)} default transformers")
    
    def register_transformer(self, analysis_type: str, 
                           transformer_class: Type[DataTransformer]):
        """
        Register a transformer class for an analysis type.
        
        Args:
            analysis_type: Type of analysis (e.g., 'composition', 'growth')
            transformer_class: The transformer class to register
        """
        if not inspect.isclass(transformer_class) or not issubclass(transformer_class, DataTransformer):
            raise ValueError(f"{transformer_class} must be a subclass of DataTransformer")
        
        self._transformers[analysis_type] = transformer_class
        self.logger.info(f"Registered transformer '{transformer_class.__name__}' for '{analysis_type}'")
    
    def register_chart_mapping(self, chart_id: str, analysis_type: str):
        """
        Map a specific chart ID to an analysis type.
        
        Args:
            chart_id: The chart identifier
            analysis_type: The analysis type that handles this chart
        """
        if analysis_type not in self._transformers:
            self.logger.warning(f"Analysis type '{analysis_type}' not registered")
        
        self._chart_mappings[chart_id] = analysis_type
        self.logger.debug(f"Mapped chart '{chart_id}' to analysis type '{analysis_type}'")
    
    @with_error_handling(
        category=ErrorCategory.DATA_ACCESS,
        severity=ErrorSeverity.ERROR,
        fallback_value=None
    )
    def get_transformer(self, chart_id: str, 
                       config: Optional[Dict[str, Any]] = None) -> Optional[DataTransformer]:
        """
        Get a transformer instance for a specific chart.
        
        Args:
            chart_id: The chart identifier
            config: Optional configuration for the transformer
            
        Returns:
            Transformer instance or None if not found
        """
        # First check direct chart mappings
        if chart_id in self._chart_mappings:
            analysis_type = self._chart_mappings[chart_id]
        else:
            # Try to extract analysis type from chart config
            # Project imports
            from yemen_trade_diagnostic.utils.config import get_chart_config
            chart_config = get_chart_config(chart_id)
            analysis_type = chart_config.get("analysis_type", "").lower()
        
        # Get transformer class
        transformer_class = self._transformers.get(analysis_type)
        if not transformer_class:
            self.logger.error(f"No transformer found for chart '{chart_id}' (type: '{analysis_type}')")
            return None
        
        # Create instance
        return transformer_class(chart_id, config)
    
    def list_transformers(self) -> List[str]:
        """List all registered transformer types."""
        return list(self._transformers.keys())
    
    def list_chart_mappings(self) -> Dict[str, str]:
        """List all chart ID to analysis type mappings."""
        return self._chart_mappings.copy()
    
    def create_transformer(self, analysis_type: str, chart_id: str,
                         config: Optional[Dict[str, Any]] = None) -> Optional[DataTransformer]:
        """
        Create a transformer instance directly by analysis type.
        
        Args:
            analysis_type: The type of analysis
            chart_id: The chart identifier
            config: Optional configuration
            
        Returns:
            Transformer instance or None if not found
        """
        transformer_class = self._transformers.get(analysis_type)
        if not transformer_class:
            self.logger.error(f"No transformer registered for analysis type '{analysis_type}'")
            return None
        
        return transformer_class(chart_id, config)


# Global registry instance
_registry = TransformerRegistry()


# Public API functions
def register_transformer(analysis_type: str, transformer_class: Type[DataTransformer]):
    """Register a transformer with the global registry."""
    _registry.register_transformer(analysis_type, transformer_class)


def register_chart_mapping(chart_id: str, analysis_type: str):
    """Register a chart ID mapping with the global registry."""
    _registry.register_chart_mapping(chart_id, analysis_type)


def get_transformer(chart_id: str, config: Optional[Dict[str, Any]] = None) -> Optional[DataTransformer]:
    """Get a transformer from the global registry."""
    return _registry.get_transformer(chart_id, config)


def list_transformers() -> List[str]:
    """List all registered transformers."""
    return _registry.list_transformers()


def list_chart_mappings() -> Dict[str, str]:
    """List all chart mappings."""
    return _registry.list_chart_mappings()


# Additional transformer implementations
class SophisticationTransformer(DataTransformer):
    """Transformer for economic sophistication analysis."""
    
    @with_error_handling(
        category=ErrorCategory.DATA_ACCESS,
        severity=ErrorSeverity.ERROR,
        fallback_value={"data": pd.DataFrame(), "metadata": {}}
    )
    def transform(self, year: int = 2023) -> Dict[str, Any]:
        """Transform data for sophistication analysis."""
        # Third-party imports
        import pandas as pd

        # Project imports
        from yemen_trade_diagnostic.data.unified_loader_api import load_data
        
        self.logger.info(f"Transforming data for sophistication analysis", extra={"year": year})
        
        try:
            # Load trade data
            trade_data = load_data("baci", year=year)
            
            # Calculate sophistication metrics (simplified)
            sophistication_data = self._calculate_sophistication(trade_data)
            
            return {
                "data": sophistication_data,
                "metadata": self._prepare_metadata()
            }
        except Exception as e:
            self.logger.error(f"Sophistication transformation failed: {str(e)}")
            raise
    
    def _calculate_sophistication(self, data: pd.DataFrame) -> pd.DataFrame:
        """Calculate product sophistication metrics."""
        # This is a simplified version - real implementation would use PRODY/EXPY
        data = self._standardize_columns(data, BACI_TO_SEMANTIC_COLUMN_MAPPING)
        
        # Group by product
        product_values = self._accelerate_computation(
            data,
            operation="groupby",
            columns=['product_code'],
            agg_functions={'trade_value_usd': 'sum'}
        )
        
        # Calculate simple sophistication proxy (value per unit)
        product_values['sophistication_score'] = product_values['trade_value_usd'].rank(pct=True)
        
        return product_values.reset_index()


class MarketTransformer(DataTransformer):
    """Transformer for market analysis charts."""
    
    @with_error_handling(
        category=ErrorCategory.DATA_ACCESS,
        severity=ErrorSeverity.ERROR,
        fallback_value={"data": pd.DataFrame(), "metadata": {}}
    )
    def transform(self, year: int = 2023, market_type: str = "destinations") -> Dict[str, Any]:
        """Transform data for market analysis."""
        # Third-party imports
        import pandas as pd

        # Project imports
        from yemen_trade_diagnostic.data.unified_loader_api import load_data
        
        self.logger.info(
            f"Transforming data for market analysis",
            extra={"year": year, "market_type": market_type}
        )
        
        try:
            # Load appropriate data
            if market_type == "destinations":
                data = load_data("baci", year=year, filters={"exporter": "YEM"})
            else:  # sources
                data = load_data("baci", year=year, filters={"importer": "YEM"})
            
            # Transform to market format
            market_data = self._analyze_markets(data, market_type)
            
            return {
                "data": market_data,
                "metadata": self._prepare_metadata()
            }
        except Exception as e:
            self.logger.error(f"Market transformation failed: {str(e)}")
            raise
    
    def _analyze_markets(self, data: pd.DataFrame, market_type: str) -> pd.DataFrame:
        """Analyze market data."""
        data = self._standardize_columns(data, BACI_TO_SEMANTIC_COLUMN_MAPPING)
        
        # Determine grouping column
        group_col = 'importer_iso' if market_type == "destinations" else 'exporter_iso'
        
        # Group by market
        market_analysis = self._accelerate_computation(
            data,
            operation="groupby",
            columns=[group_col],
            agg_functions={
                'trade_value_usd': ['sum', 'mean'],
                'product_code': 'count'
            }
        )
        
        # Flatten column names
        market_analysis.columns = ['total_value', 'avg_value', 'product_count']
        
        # Calculate market share
        total = market_analysis['total_value'].sum()
        market_analysis['market_share'] = (market_analysis['total_value'] / total) * 100
        
        # Sort by total value
        market_analysis = market_analysis.sort_values('total_value', ascending=False)
        
        return market_analysis.reset_index()


# Register additional transformers
register_transformer("sophistication", SophisticationTransformer)
register_transformer("market", MarketTransformer)

# Additional chart mappings
register_chart_mapping("product_sophistication", "sophistication")
register_chart_mapping("export_sophistication", "sophistication")
register_chart_mapping("market_destinations", "market")
register_chart_mapping("market_sources", "market")
register_chart_mapping("top_export_partners", "market")
register_chart_mapping("top_import_sources", "market")