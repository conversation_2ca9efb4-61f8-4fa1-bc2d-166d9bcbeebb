"""
Parallel Data Loading System for Yemen Trade Diagnostic V2

This module provides a parallel data loading framework that leverages hardware
acceleration, CPU/GPU optimization, and memory management to efficiently load
and preprocess large datasets, optimized for Apple Silicon M3 Pro chips.
"""

# Standard library imports
import asyncio
import concurrent.futures
import glob
import os
from dataclasses import dataclass
from enum import Enum
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional, Tuple, Union

# Third-party imports
import numpy as np
import pandas as pd

# Project imports
from yemen_trade_diagnostic.interfaces.cache_interface import (
    CacheLevel,
    DataLifetime,
    cache_get,
    cache_set,
    get_cache_manager,
)
from yemen_trade_diagnostic.interfaces.error_interface import (
    ErrorCategory,
    ErrorSeverity,
    report_error,
    with_error_handling,
)

# V2 interface imports
from yemen_trade_diagnostic.interfaces.hardware_interface import (
    AccelerationType,
    get_hardware_manager,
)
from yemen_trade_diagnostic.interfaces.logging_interface import get_logger, log_execution_time
from yemen_trade_diagnostic.interfaces.validation_interface import (
    ValidationMode,
    ValidationResult,
    validate_dataframe,
)

# V2 data module imports
# Comment out imports that don't exist
# from yemen_trade_diagnostic.data.validation import DataValidationManager  
from yemen_trade_diagnostic.utils.config import get_processed_data_dir, get_raw_data_dir

# from yemen_trade_diagnostic.utils.memory_optimizer import optimize_dataframe_dtypes
# from yemen_trade_diagnostic.utils.progress_tracker import ProgressTracker

logger = get_logger(__name__)


class LoadingStrategy(Enum):
    """Loading strategies for different data types and sizes."""
    SEQUENTIAL = "sequential"
    PARALLEL_THREAD = "parallel_thread"
    PARALLEL_PROCESS = "parallel_process"
    ASYNC = "async"
    HYBRID = "hybrid"


@dataclass
class LoadingConfig:
    """Configuration for parallel loading operations."""
    max_workers: Optional[int] = None
    memory_limit_gb: Optional[float] = None
    enable_compression: bool = True
    enable_caching: bool = True
    cache_ttl: int = 3600
    strategy: LoadingStrategy = LoadingStrategy.HYBRID
    batch_size: Optional[int] = None
    prefetch_size: int = 2
    optimize_for_m3: bool = True


class ParallelDataLoader:
    """
    Parallel data loading system that leverages hardware acceleration.
    
    This class provides methods for efficiently loading and preprocessing data
    using parallel processing and hardware acceleration techniques, with special
    optimization for Apple M3 Pro chips.
    """
    
    def __init__(self, config: Optional[LoadingConfig] = None):
        """Initialize the parallel data loader."""
        self.config = config or LoadingConfig()
        
        # Initialize managers
        self.hw_manager = get_hardware_manager()
        # self.validation_manager = DataValidationManager() # Doesn't exist
        self.validation_manager = None
        self.cache_manager = get_cache_manager()
        self.logger = logger
        
        # Set up hardware configuration
        self._configure_hardware()
        
        # Initialize executors
        self.thread_executor = concurrent.futures.ThreadPoolExecutor(
            max_workers=self.config.max_workers or self._get_optimal_workers()
        )
        
        # For CPU-intensive tasks, use ProcessPoolExecutor
        self.process_executor = concurrent.futures.ProcessPoolExecutor(
            max_workers=self.config.max_workers or os.cpu_count()
        )
        
        # Initialize metrics
        self.metrics = {
            "loading_times": {},
            "preprocessing_times": {},
            "peak_memory_usage": 0,
            "cache_hit_ratio": 0.0,
            "hardware_acceleration_used": False,
            "errors": 0,
            "warnings": 0
        }
        
        # Performance profile - commented out as function doesn't exist
        # self.performance_profile = create_performance_profile(
        #     workload_type="data_loading",
        #     optimization_target="throughput"
        # )
        self.performance_profile = {"workload_type": "data_loading", "optimization_target": "throughput"}
    
    def _configure_hardware(self):
        """Configure hardware based on available capabilities."""
        capabilities = self.hw_manager.get_capabilities()
        
        # Special optimization for M3 Pro
        if self.config.optimize_for_m3:
            # Check for Neural Engine availability - commented out as HardwareCapability doesn't exist
            # if HardwareCapability.NEURAL_ENGINE in capabilities:
            #     self.logger.info("Detected Apple Neural Engine - enabling optimizations")
            #     # Configure for neural engine usage
            #     self.hw_manager.configure({
            #         "use_neural_engine": True,
            #         "memory_optimization": "aggressive"
            #     })
            
            # Check for unified memory - commented out as HardwareCapability doesn't exist
            # if HardwareCapability.UNIFIED_MEMORY in capabilities:
            #     self.logger.info("Detected unified memory architecture")
            #     # Optimize for unified memory
            #     self.hw_manager.configure({
            #         "unified_memory_mode": True
            
            # Use basic check instead
            if hasattr(capabilities, 'get') and capabilities.get('neural_engine'):
                self.logger.info("Detected Neural Engine - enabling optimizations")
            if hasattr(capabilities, 'get') and capabilities.get('unified_memory'):
                self.logger.info("Detected unified memory architecture")
    
    def _get_optimal_workers(self) -> int:
        """Determine optimal number of workers based on hardware."""
        cpu_count = os.cpu_count() or 4
        
        # For M3 Pro, optimize based on performance/efficiency cores
        if self.config.optimize_for_m3:
            # Typically M3 Pro has 6 performance + 6 efficiency cores
            # Use performance cores for heavy tasks
            return min(6, cpu_count)
        
        return min(cpu_count, 8)
    
    @with_error_handling(
        category=ErrorCategory.DATA_ACCESS,
        severity=ErrorSeverity.ERROR,
        fallback_value=pd.DataFrame()
    )
    @log_execution_time
    async def load_files_async(self, file_paths: List[Union[str, Path]], 
                              file_type: str = "csv",
                              **kwargs) -> Dict[str, pd.DataFrame]:
        """
        Load multiple files asynchronously.
        
        Args:
            file_paths: List of file paths to load
            file_type: Type of files to load
            **kwargs: Additional arguments for file reading
            
        Returns:
            Dictionary mapping file names to DataFrames
        """
        self.logger.info(f"Loading {len(file_paths)} files asynchronously")
        
        # Create loading tasks
        tasks = []
        for file_path in file_paths:
            task = asyncio.create_task(
                self._load_single_file_async(file_path, file_type, **kwargs)
            )
            tasks.append(task)
        
        # Wait for all tasks to complete
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results
        loaded_data = {}
        for file_path, result in zip(file_paths, results):
            if isinstance(result, Exception):
                self.logger.error(f"Failed to load {file_path}: {str(result)}")
                self.metrics["errors"] += 1
            else:
                file_name = Path(file_path).stem
                loaded_data[file_name] = result
        
        return loaded_data
    
    async def _load_single_file_async(self, file_path: Union[str, Path],
                                    file_type: str, **kwargs) -> pd.DataFrame:
        """Load a single file asynchronously."""
        file_path = Path(file_path)
        
        # Check cache first
        if self.config.enable_caching:
            # create_cache_key doesn't exist, use simple string key
            cache_key = f"file_{file_path}_{file_type}"
            cached_data = cache_get(cache_key, level=CacheLevel.DISK)
            if cached_data is not None:
                self.logger.debug(f"Cache hit for {file_path}")
                return cached_data
        
        # Load file based on type
        loop = asyncio.get_event_loop()
        
        if file_type == "csv":
            df = await loop.run_in_executor(
                self.thread_executor,
                self._load_csv,
                file_path,
                kwargs
            )
        elif file_type == "parquet":
            df = await loop.run_in_executor(
                self.thread_executor,
                self._load_parquet,
                file_path,
                kwargs
            )
        else:
            raise ValueError(f"Unsupported file type: {file_type}")
        
        # Cache the result
        if self.config.enable_caching and df is not None:
            cache_set(
                cache_key,
                df,
                level=CacheLevel.DISK,
                ttl=self.config.cache_ttl
            )
        
        return df
    
    def _load_csv(self, file_path: Path, kwargs: Dict[str, Any]) -> pd.DataFrame:
        """Load a CSV file with optimizations."""
        # Extract relevant kwargs
        columns = kwargs.get("columns")
        dtype = kwargs.get("dtype")
        
        # Use hardware acceleration if available
        if self.hw_manager.is_hardware_available():
            try:
                # Use hardware-accelerated CSV reading if possible
                df = self.hw_manager.accelerate_file_read(
                    file_path,
                    file_type="csv",
                    columns=columns,
                    dtype=dtype
                )
                self.metrics["hardware_acceleration_used"] = True
            except Exception as e:
                self.logger.warning(f"Hardware acceleration failed, using fallback: {e}")
                df = pd.read_csv(file_path, usecols=columns, dtype=dtype, **kwargs)
        else:
            df = pd.read_csv(file_path, usecols=columns, dtype=dtype, **kwargs)
        
        # Optimize memory usage
        # df = optimize_dataframe_dtypes(df) # Doesn't exist
        
        # Validate data
        if self.validation_manager is not None:
            validation_result = self.validation_manager.validate_dataset(
                df, dataset_type="raw"
            )
            if not validation_result.is_valid():
                self.logger.warning(f"Validation issues for {file_path}")
                self.metrics["warnings"] += 1
        
        return df
    
    def _load_parquet(self, file_path: Path, kwargs: Dict[str, Any]) -> pd.DataFrame:
        """Load a Parquet file with optimizations."""
        columns = kwargs.get("columns")
        
        # Use hardware acceleration if available
        if self.hw_manager.is_hardware_available():
            try:
                df = self.hw_manager.accelerate_file_read(
                    file_path,
                    file_type="parquet",
                    columns=columns
                )
                self.metrics["hardware_acceleration_used"] = True
            except Exception as e:
                self.logger.warning(f"Hardware acceleration failed, using fallback: {e}")
                df = pd.read_parquet(file_path, columns=columns, **kwargs)
        else:
            df = pd.read_parquet(file_path, columns=columns, **kwargs)
        
        return df
    
    @with_error_handling(
        category=ErrorCategory.DATA_ACCESS,
        severity=ErrorSeverity.ERROR,
        fallback_value=pd.DataFrame()
    )
    def load_baci_parallel(self, year: int, chunk_size: int = 1000000) -> pd.DataFrame:
        """
        Load BACI data in parallel with chunking.
        
        Args:
            year: Year of BACI data to load
            chunk_size: Size of chunks for parallel processing
            
        Returns:
            Complete BACI dataset for the year
        """
        self.logger.info(f"Loading BACI data for year {year} in parallel")
        
        # Get BACI file path
        baci_dir = get_raw_data_dir() / "baci"
        file_path = baci_dir / f"BACI_HS02_Y{year}_V202501.csv"
        
        if not file_path.exists():
            raise FileNotFoundError(f"BACI file not found: {file_path}")
        
        # Determine file size and chunk count
        file_size = file_path.stat().st_size
        estimated_chunks = max(1, file_size // (chunk_size * 100))  # Rough estimate
        
        self.logger.info(f"Processing {file_path} ({file_size} bytes) in ~{estimated_chunks} chunks")
        
        # Create chunk processor
        def process_chunk(chunk_id: int, start_row: int, nrows: int) -> pd.DataFrame:
            """Process a single chunk of data."""
            try:
                chunk_df = pd.read_csv(
                    file_path,
                    skiprows=start_row if start_row > 0 else None,
                    nrows=nrows,
                    header=0 if start_row == 0 else None
                )
                
                # Optimize memory
                # chunk_df = optimize_dataframe_dtypes(chunk_df) # Doesn't exist
                
                # Basic validation
                if chunk_df.empty:
                    self.logger.warning(f"Chunk {chunk_id} is empty")
                
                return chunk_df
                
            except Exception as e:
                self.logger.error(f"Error processing chunk {chunk_id}: {e}")
                self.metrics["errors"] += 1
                return pd.DataFrame()
        
        # Process chunks in parallel
        chunks = []
        futures = []
        
        with self.process_executor as executor:
            # Submit chunk processing tasks
            for i in range(estimated_chunks):
                start_row = i * chunk_size
                future = executor.submit(process_chunk, i, start_row, chunk_size)
                futures.append(future)
            
            # Collect results with progress tracking
            # progress = ProgressTracker(total=len(futures), description="Loading BACI chunks") # Doesn't exist
            
            for future in concurrent.futures.as_completed(futures):
                chunk = future.result()
                if not chunk.empty:
                    chunks.append(chunk)
                # progress.update(1) # Doesn't exist
                pass
            
            # progress.close() # Doesn't exist
        
        # Combine chunks
        if not chunks:
            self.logger.error("No chunks loaded successfully")
            return pd.DataFrame()
        
        self.logger.info(f"Combining {len(chunks)} chunks")
        combined_df = pd.concat(chunks, ignore_index=True)
        
        # Final validation
        validation_result = self.validation_manager.validate_dataset(
            combined_df, dataset_type="baci"
        )
        
        self.logger.info(f"Loaded {len(combined_df)} rows for year {year}")
        
        return combined_df
    
    def load_multiple_years_parallel(self, years: List[int], 
                                   data_type: str = "baci") -> Dict[int, pd.DataFrame]:
        """
        Load data for multiple years in parallel.
        
        Args:
            years: List of years to load
            data_type: Type of data to load
            
        Returns:
            Dictionary mapping years to DataFrames
        """
        self.logger.info(f"Loading {data_type} data for years {years} in parallel")
        
        results = {}
        
        # Create loading tasks
        with concurrent.futures.ThreadPoolExecutor(max_workers=len(years)) as executor:
            # Submit loading tasks
            future_to_year = {
                executor.submit(self._load_year_data, year, data_type): year
                for year in years
            }
            
            # Collect results
            # progress = ProgressTracker(total=len(years), description=f"Loading {data_type} data") # Doesn't exist
            
            for future in concurrent.futures.as_completed(future_to_year):
                year = future_to_year[future]
                try:
                    data = future.result()
                    results[year] = data
                    self.logger.info(f"Loaded data for year {year}")
                except Exception as e:
                    self.logger.error(f"Failed to load data for year {year}: {e}")
                    self.metrics["errors"] += 1
                    results[year] = pd.DataFrame()
                
                # progress.update(1) # Doesn't exist
                pass
            
            # progress.close() # Doesn't exist
        
        return results
    
    def _load_year_data(self, year: int, data_type: str) -> pd.DataFrame:
        """Load data for a specific year."""
        if data_type == "baci":
            return self.load_baci_parallel(year)
        else:
            # Use standard loaders for other data types
            # Project imports
            from yemen_trade_diagnostic.data.unified_loader_api import load_data
            return load_data(data_type, year=year)
    
    def preprocess_parallel(self, df: pd.DataFrame, 
                          operations: List[Callable]) -> pd.DataFrame:
        """
        Apply preprocessing operations in parallel.
        
        Args:
            df: Input DataFrame
            operations: List of operations to apply
            
        Returns:
            Preprocessed DataFrame
        """
        if df.empty:
            return df
        
        # Split DataFrame into chunks for parallel processing
        n_chunks = self.config.max_workers or self._get_optimal_workers()
        chunks = np.array_split(df, n_chunks)
        
        # Process chunks in parallel
        with self.thread_executor as executor:
            futures = []
            
            for chunk in chunks:
                future = executor.submit(self._apply_operations, chunk, operations)
                futures.append(future)
            
            # Collect results
            processed_chunks = []
            for future in concurrent.futures.as_completed(futures):
                processed_chunk = future.result()
                processed_chunks.append(processed_chunk)
        
        # Combine processed chunks
        result = pd.concat(processed_chunks, ignore_index=True)
        
        return result
    
    def _apply_operations(self, df: pd.DataFrame, 
                         operations: List[Callable]) -> pd.DataFrame:
        """Apply a list of operations to a DataFrame."""
        result = df.copy()
        
        for operation in operations:
            try:
                result = operation(result)
            except Exception as e:
                self.logger.error(f"Error applying operation: {e}")
                self.metrics["errors"] += 1
        
        return result
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get performance metrics."""
        return self.metrics
    
    def close(self):
        """Clean up resources."""
        self.thread_executor.shutdown(wait=True)
        self.process_executor.shutdown(wait=True)


# Create a singleton instance
_parallel_loader = None


def get_parallel_data_loader(config: Optional[LoadingConfig] = None) -> ParallelDataLoader:
    """Get or create the parallel data loader instance."""
    global _parallel_loader
    
    if _parallel_loader is None:
        _parallel_loader = ParallelDataLoader(config)
    
    return _parallel_loader


# Convenience functions
async def load_files_async(file_paths: List[Union[str, Path]], **kwargs) -> Dict[str, pd.DataFrame]:
    """Load multiple files asynchronously."""
    loader = get_parallel_data_loader()
    return await loader.load_files_async(file_paths, **kwargs)


def load_baci_parallel(year: int, **kwargs) -> pd.DataFrame:
    """Load BACI data in parallel."""
    loader = get_parallel_data_loader()
    return loader.load_baci_parallel(year, **kwargs)


def load_multiple_years_parallel(years: List[int], **kwargs) -> Dict[int, pd.DataFrame]:
    """Load data for multiple years in parallel."""
    loader = get_parallel_data_loader()
    return loader.load_multiple_years_parallel(years, **kwargs)