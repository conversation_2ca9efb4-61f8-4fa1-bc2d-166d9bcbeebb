"""
Provides a unified API entry point for loading data using the V2 loader system.

DEPRECATED: This module is deprecated and will be removed in version 3.0.
Please use yemen_trade_diagnostic.data.api instead.
"""
# Standard library imports
import concurrent.futures
import warnings

# Show deprecation warning on import
warnings.warn(
    "unified_loader_api.py is deprecated and will be removed in version 3.0. "
    "Please use yemen_trade_diagnostic.data.api instead. "
    "See docs/data/UNIFIED_API_MIGRATION_GUIDE.md for migration instructions.",
    DeprecationWarning,
    stacklevel=2
)
import os  # For os.cpu_count()
import time
import traceback
from typing import Any, Dict, List, Optional, Type, Union

# Third-party imports
import pandas as pd

# Project imports
from yemen_trade_diagnostic.data.loader_base import DataLoaderBase
from yemen_trade_diagnostic.data.loader_registry import (
    get_loader,
    list_available_loaders,
    register_loader,
)
from yemen_trade_diagnostic.interfaces.error_interface import (
    ErrorCategory,
    ErrorSeverity,
    report_error,
    with_error_handling,
)
from yemen_trade_diagnostic.interfaces.logging_interface import get_logger

# Initialize logger
logger = get_logger(__name__)

# Registry for custom data sources
_data_source_registry: Dict[str, Dict[str, Any]] = {}

@with_error_handling(category=ErrorCategory.CONFIGURATION, severity=ErrorSeverity.WARNING)
def register_data_source(source_name: str, loader_class: Optional[Type[DataLoaderBase]] = None, config: Optional[Dict[str, Any]] = None) -> None:
    """
    Register a data source with an optional custom loader class and configuration.
    
    Args:
        source_name: The unique name for the data source
        loader_class: Optional custom loader class to use for this data source
        config: Optional configuration specific to this data source
        
    Raises:
        ValueError: If source_name is already registered
    """
    if source_name in _data_source_registry:
        error_msg = f"Data source '{source_name}' is already registered"
        logger.warning(error_msg)
        raise ValueError(error_msg)
    
    _data_source_registry[source_name] = {
        "loader_class": loader_class,
        "config": config or {}
    }
    
    # If a loader_class is provided and not already registered through the decorator
    if loader_class is not None:
        register_loader(source_name)(loader_class)
    
    logger.info(f"Registered data source '{source_name}'")


def get_registered_data_sources() -> List[str]:
    """
    Get a list of registered data source names.
    
    Returns:
        List of registered data source names
    """
    registered_sources = list(_data_source_registry.keys())
    # Also include any sources registered directly with the loader registry
    registered_sources.extend([source for source in list_available_loaders() 
                              if source not in registered_sources])
    return registered_sources


def create_loader_instance(source_name: str, config: Optional[Dict[str, Any]] = None) -> DataLoaderBase:
    """
    Create a loader instance for the specified data source.
    
    Args:
        source_name: The name of the data source
        config: Optional configuration to override the registered config
        
    Returns:
        DataLoaderBase instance
        
    Raises:
        KeyError: If no loader is registered for the data source
    """
    # Get loader either from custom registry or default registry
    if source_name in _data_source_registry:
        source_config = _data_source_registry[source_name].get("config", {})
        if config:
            # Merge configs, with passed config taking precedence
            merged_config = {**source_config, **(config or {})}
        else:
            merged_config = source_config
        
        # Use custom loader class if provided
        loader_class = _data_source_registry[source_name].get("loader_class")
        if loader_class:
            return loader_class(source_name, merged_config)
    
    # Fall back to standard loader registry
    return get_loader(source_name, config=config)


@with_error_handling(category=ErrorCategory.DATA_ACCESS, severity=ErrorSeverity.ERROR)
def load_data(source_name: str, config: Dict[str, Any] = None, **kwargs) -> pd.DataFrame:
    """
    Loads data from the specified source using the registered DataLoaderBase.

    This is the primary entry point for data loading in the V2 system.

    Args:
        source_name: The unique name of the data source to load (e.g., 'baci', 'worldbank').
        config: Optional configuration dictionary specific to the loader or loading process.
        **kwargs: Additional keyword arguments to pass to the loader's `process` method.
                  These can be structured like:
                  `load_kwargs={...}` for `load` method,
                  `transform_kwargs={...}` for `transform` method,
                  `validate_kwargs={...}` for `validate_data` method.
                  
                  Standard parameter names to use:
                  - file_path: Path to specific file (instead of path/filepath)
                  - data_path: Path to directory (instead of path/data_dir)
                  - filters: Filtering criteria (instead of filter/query)
                  - apply_transformations: Whether to transform (instead of transform)
                  - validate_data: Whether to validate (instead of validate)
                  - data_format: Format of data (instead of format)                  
                  
                  If not structured, they are passed directly to `process`.

    Returns:
        A pandas DataFrame containing the loaded, transformed, and validated data.

    Raises:
        KeyError: If no loader is registered for the given source_name.
        ValueError: If data validation fails within the loader.
        Any other exceptions raised by the specific loader during its process.
    """
    start_time = time.time()
    logger.info(f"Unified API: Request to load data for source '{source_name}' with config: {config}")
    
    try:
        # Get loader instance
        try:
            loader_instance = get_loader(source_name, config=config)
            logger.debug(f"Retrieved loader: {loader_instance.__class__.__name__} for source '{source_name}'")
        except KeyError as e:
            available_loaders = list_available_loaders()
            error_msg = f"Failed to find loader for source '{source_name}'. Available: {available_loaders}"
            logger.error(error_msg)
            report_error(
                KeyError(error_msg),
                category=ErrorCategory.DATA_ACCESS,
                severity=ErrorSeverity.ERROR,
                component="data",
                operation="get_loader",
                extra={"source_name": source_name, "available_loaders": available_loaders}
            )
            raise KeyError(error_msg) from e
        
        # Standardize parameter names to ensure backward compatibility
        standardized_kwargs = _standardize_parameter_names(kwargs)
        
        # Process data
        try:
            # The process method in DataLoaderBase class handles the sequence:
            # load -> transform -> validate_data
            # kwargs are passed to the process method, which can then dispatch them further.
            processed_df = loader_instance.process(**standardized_kwargs)
            
            # Log success with performance metrics
            elapsed_time = time.time() - start_time
            
            # Calculate memory usage if it's a DataFrame
            if isinstance(processed_df, pd.DataFrame):
                shape = processed_df.shape
                memory_usage_mb = processed_df.memory_usage(deep=True).sum() / (1024**2)
                logger.info(
                    f"Successfully loaded and processed data for source '{source_name}' in {elapsed_time:.2f} seconds. "
                    f"Shape: {shape}, Memory: {memory_usage_mb:.2f} MB"
                )
            else:
                logger.info(
                    f"Successfully loaded and processed data for source '{source_name}' in {elapsed_time:.2f} seconds. "
                    f"Result is not a DataFrame."
                )
                
            return processed_df
        except ValueError as e:
            error_msg = f"Data processing or validation error for source '{source_name}': {e}"
            logger.error(error_msg)
            report_error(
                e,
                category=ErrorCategory.VALIDATION,
                severity=ErrorSeverity.ERROR,
                component="data",
                operation="process_data",
                extra={"source_name": source_name}
            )
            raise  # Re-raise ValueError related to data issues
        except Exception as e:
            error_msg = f"An unexpected error occurred while loading '{source_name}': {e}"
            logger.error(error_msg, exc_info=True)
            report_error(
                e,
                category=ErrorCategory.DATA_ACCESS,
                severity=ErrorSeverity.ERROR,
                component="data",
                operation="process_data",
                extra={"source_name": source_name}
            )
            raise  # Re-raise other unexpected exceptions
            
    except Exception as e:
        # This catch-all adds an additional layer of error handling/reporting
        # in case something unexpected happens outside the specific error handling above
        if not isinstance(e, (KeyError, ValueError)):
            logger.error(f"Unhandled exception in load_data for '{source_name}': {e}")
            logger.error(traceback.format_exc())
            report_error(
                e,
                category=ErrorCategory.DATA_ACCESS,
                severity=ErrorSeverity.ERROR,
                component="data",
                operation="load_data",
                extra={"source_name": source_name}
            )
        raise  # Re-raise the exception

def load_data_range(
    source_name: str, 
    start_year: int, 
    end_year: int, 
    parallel_execution: bool = True, 
    max_workers: Optional[int] = None,
    fail_fast: bool = False, # If True, an error in one year's load stops all.
    **kwargs # Passed to each call of load_data(source_name, year=year, **kwargs)
) -> Dict[int, Union[pd.DataFrame, Exception]]:
    """
    Loads data for a range of years for the specified source, optionally in parallel.

    Args:
        source_name: The unique name of the data source to load.
        start_year: The first year in the range (inclusive).
        end_year: The last year in the range (inclusive).
        parallel_execution: If True, load years in parallel using ThreadPoolExecutor.
        max_workers: Maximum number of parallel workers. Defaults to min(os.cpu_count(), num_years) / 2.
        fail_fast: If True, an exception during one year's load will stop and raise immediately.
                   If False (default), errors are collected in the result dict for that year.
        **kwargs: Additional keyword arguments passed to each `load_data` call 
                  (e.g., config, use_cache, chunksize for specific loaders).

    Returns:
        A dictionary mapping each year to the loaded DataFrame. 
        If an error occurs for a specific year and fail_fast is False, the value for that year 
        will be the Exception object. 
    """
    # logger.info(f"Unified API: Request to load data range for source '{source_name}' from {start_year} to {end_year}.")
    
    years = list(range(start_year, end_year + 1))
    results: Dict[int, Union[pd.DataFrame, Exception]] = {}

    def _load_single_year(year_to_load: int) -> Union[pd.DataFrame, Exception]:
        try:
            # logger.debug(f"Loading data for year {year_to_load} for source {source_name}")
            # Pass through the original kwargs from load_data_range to load_data
            return load_data(source_name=source_name, year=year_to_load, **kwargs)
        except Exception as e:
            # logger.error(f"Failed to load data for source '{source_name}' year {year_to_load}: {e}", exc_info=True)
            if fail_fast:
                raise
            return e # Store exception if not failing fast

    if parallel_execution and len(years) > 1:
        num_years = len(years)
        if max_workers is None:
            cpu_count = os.cpu_count() or 4
            # Heuristic: don't use more workers than years, and cap at half of CPUs for I/O bound tasks
            # to avoid excessive context switching or resource contention if loaders are heavy.
            max_workers = min(num_years, cpu_count // 2 if cpu_count > 1 else 1)
            max_workers = max(1, max_workers) # Ensure at least one worker
        
        # logger.info(f"Loading {num_years} years in parallel with {max_workers} workers.")
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_year = {executor.submit(_load_single_year, year): year for year in years}
            
            for future in concurrent.futures.as_completed(future_to_year):
                year_completed = future_to_year[future]
                try:
                    data_or_exc = future.result()
                    results[year_completed] = data_or_exc
                    if isinstance(data_or_exc, Exception):
                        # logger.warning(f"Error captured for year {year_completed} (parallel): {data_or_exc}")
                        pass # Error already logged by _load_single_year or will be handled by caller
                    # else:
                        # logger.debug(f"Successfully loaded data for year {year_completed} (parallel).")
                except Exception as e: # Should ideally be caught by _load_single_year if fail_fast=False
                    # logger.error(f"Unexpected error processing future for year {year_completed}: {e}", exc_info=True)
                    if fail_fast:
                        # Cancel remaining futures if possible (though ThreadPoolExecutor doesn't make this easy directly for running tasks)
                        # And re-raise to stop everything.
                        raise
                    results[year_completed] = e 
    else:
        # logger.info(f"Loading {len(years)} years sequentially.")
        for year in years:
            results[year] = _load_single_year(year)
            if fail_fast and isinstance(results[year], Exception):
                # Error already raised by _load_single_year due to fail_fast=True in that path (if it were passed)
                # This path implies fail_fast in _load_single_year would have already raised.
                # If _load_single_year *didn't* raise (e.g. fail_fast=False there), then we would raise here.
                # The current _load_single_year re-raises if fail_fast is true *for the load_data_range call*.
                pass # If it got here and fail_fast, it means _load_single_year handled its fail_fast.

    return results

# Example of how one might list loaders, useful for diagnostics or UIs
# def get_all_registered_loaders() -> list[str]:
#     return list_available_loaders() 

# Function to aid backward compatibility during parameter name standardization transition
def _standardize_parameter_names(kwargs: Dict[str, Any]) -> Dict[str, Any]:
    """
    Standardize parameter names to ensure backward compatibility during transition.
    
    Maps old parameter names to their standard equivalents following the conventions
    defined in docs_v2/migration/parameter_naming_convention.md.
    
    Args:
        kwargs: Dictionary of keyword arguments that may contain legacy parameter names
        
    Returns:
        Dictionary with standardized parameter names
    """
    # Create a mapping of old names to new standardized names
    mapping = {
        # Path parameters
        'path': 'file_path',
        'filepath': 'file_path',
        'file_name': 'file_path',
        'data_dir': 'data_path',
        'datapath': 'data_path',
        'out_path': 'output_path',
        'output_dir': 'output_path',
        'base_dir': 'base_path',
        'root_dir': 'base_path',
        
        # Data loading parameters
        'filter': 'filters',
        'query': 'filters',
        'conditions': 'filters',
        'cols': 'columns',
        'fields': 'columns',
        'column_list': 'columns',
        'exclude': 'exclude_columns',
        'skip_cols': 'exclude_columns',
        'format': 'data_format',  # Most common collision
        'file_format': 'data_format',
        'chunksize': 'chunk_size',
        'batch_size': 'chunk_size',
        'encode': 'encoding',
        'charset': 'encoding',
        'sep': 'delimiter',
        'separator': 'delimiter',
        
        # Processing parameters
        'transform': 'apply_transformations',
        'process': 'apply_transformations',
        'preprocess': 'apply_transformations',
        'validate': 'validate_data',
        'check': 'validate_data',
        'verification': 'validate_data',
        'verify_mode': 'validation_mode',
        'check_mode': 'validation_mode',
        'y': 'year',
        'yr': 'year',
        'period': 'year',
        'use_cache': 'cache_enabled',
        'caching': 'cache_enabled',
        'out_format': 'output_format',
        'accelerate': 'hardware_acceleration',
        'use_acceleration': 'hardware_acceleration',
        
        # Cache parameters
        'level': 'cache_level',
        'cache_type': 'cache_level',
        'ttl': 'cache_ttl',
        'timeout': 'cache_ttl',
        'expiry': 'cache_ttl',
        'key': 'cache_key',
        'hash_key': 'cache_key',
        
        # Other parameters that appear in our codebase
        'parallel': 'parallel_execution',
    }
    
    # Create new kwargs with standardized names
    standardized = {}
    
    # Process each key in the input
    for key, value in kwargs.items():
        # If this is an old parameter name, use the standardized version
        if key in mapping:
            standardized[mapping[key]] = value
        else:
            # Otherwise, keep the original
            standardized[key] = value
    
    return standardized