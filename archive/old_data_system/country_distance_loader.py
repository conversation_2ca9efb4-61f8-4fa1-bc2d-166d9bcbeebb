"""
Country Distance Loader for Yemen Trade Diagnostic
"""
# Standard library imports
import json
import os
from pathlib import Path
from typing import Any, Dict, List, Optional

# Third-party imports
import numpy as np
import pandas as pd
import requests

# Project imports
from yemen_trade_diagnostic.data.loader_base import DataLoaderBase
from yemen_trade_diagnostic.data.loader_registry import register_loader
from yemen_trade_diagnostic.interfaces.cache_interface import <PERSON><PERSON><PERSON>evel, memoize
from yemen_trade_diagnostic.interfaces.error_interface import (
    ErrorCategory,
    ErrorSeverity,
    report_error,
    with_error_handling,
)
from yemen_trade_diagnostic.interfaces.logging_interface import get_logger

logger = get_logger(__name__)

# CEPII GeoDist source URL
GEODIST_URL = "http://www.cepii.fr/CEPII/en/bdd_modele/download.asp?id=6"
DEFAULT_DISTANCE_MATRIX_PATH = "data/processed/country_distance_matrix.csv"
FALLBACK_DISTANCE = 5000  # Default distance in KM

@register_loader("country_distances")
class CountryDistanceLoader(DataLoaderBase):
    """
    Loader for country distance data, supporting both pairwise distance matrix
    and distance-to-Yemen calculations for gravity models.

    This loader can:
    1. Load distances from an existing matrix file
    2. Generate a distance matrix from country coordinates
    3. Pull the CEPII GeoDist dataset if configured
    """

    def __init__(self, source_name: str, config: Optional[Dict[str, Any]] = None):
        """Initialize the loader with config for data paths."""
        super().__init__(source_name, config)
        self.config = config or {}
        self.distance_matrix_path = self.config.get('distance_matrix_path', DEFAULT_DISTANCE_MATRIX_PATH)
        self.country_codes_path = self.config.get('country_codes_path', 'config/country_codes.json')
        self.baci_country_codes_path = self.config.get('baci_country_codes_path', 'data/raw/baci/country_codes_V202501.csv')
        self.yemen_iso_numeric = self.config.get('yemen_iso_numeric', 887)  # Yemen's numeric ISO code

        # Column mappings
        self.origin_col = self.config.get('origin_col', 'origin')
        self.destination_col = self.config.get('destination_col', 'destination')
        self.distance_col = self.config.get('distance_col', 'distance')

        # Create data directory if it doesn't exist
        os.makedirs(os.path.dirname(self.distance_matrix_path), exist_ok=True)

    @with_error_handling(category=ErrorCategory.DATA_ACCESS, severity=ErrorSeverity.ERROR)
    def load(self, **kwargs) -> pd.DataFrame:
        """
        Load country distance data from file or generate if not available.

        Args:
            **kwargs: Additional arguments including:
                year: Not used but accepted for API compatibility

        Returns:
            DataFrame with origin country, destination country and distances
        """
        # Year parameter isn't used but accepted for API compatibility
        _ = kwargs.get('year')

        # Try to load existing distance matrix if available
        if os.path.exists(self.distance_matrix_path):
            logger.info(f"Loading country distance matrix from {self.distance_matrix_path}")
            try:
                distance_df = pd.read_csv(self.distance_matrix_path)
                logger.info(f"Loaded distance data with {len(distance_df)} country pairs")
                return distance_df
            except Exception as e:
                logger.warning(f"Error loading distance matrix file: {e}. Will generate new matrix.")

        # If we reach here, we need to generate the distance matrix
        logger.info("Generating country distance matrix from country codes and coordinates")
        return self._generate_distance_matrix()

    @with_error_handling(category=ErrorCategory.DATA_ACCESS, severity=ErrorSeverity.WARNING)
    def transform(self, data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """
        Transform the distance data for use in the gravity model.

        This ensures that:
        1. Column names are standardized
        2. We have distances to Yemen for all countries
        3. Convert any string country codes to numeric if needed

        Args:
            data: Raw distance data DataFrame
            **kwargs: Additional transformation arguments

        Returns:
            Processed distance DataFrame
        """
        df = data.copy()

        # Standardize column names if they don't match expected names
        column_mapping = {}
        origin_candidates = ['iso1', 'country1', 'origin', 'from', 'exporter', 'i', 'origin_iso']
        for col in df.columns:
            if col.lower() in origin_candidates:
                column_mapping[col] = self.origin_col
                break

        dest_candidates = ['iso2', 'country2', 'destination', 'to', 'importer', 'j', 'destination_iso']
        for col in df.columns:
            if col.lower() in dest_candidates:
                column_mapping[col] = self.destination_col
                break

        dist_candidates = ['dist', 'km', 'distance', 'distw', 'distwces']
        for col in df.columns:
            if col.lower() in dist_candidates:
                column_mapping[col] = self.distance_col
                break

        # Add additional mappings for compatibility with market data provider
        if 'origin' in df.columns and 'reporter_code' not in df.columns:
            column_mapping['origin'] = 'reporter_code'

        if 'destination' in df.columns and 'partner_code' not in df.columns:
            column_mapping['destination'] = 'partner_code'

        # Load ISO mapping to convert numeric codes to ISO alpha-3
        try:
            iso_mapping_path = os.path.join('data', 'processed', 'country_iso_mapping.csv')
            if os.path.exists(iso_mapping_path):
                iso_mapping = pd.read_csv(iso_mapping_path)
                # Create mapping dictionaries
                numeric_to_alpha3 = {}
                for _, row in iso_mapping.iterrows():
                    if pd.notna(row['iso_numeric']):
                        try:
                            numeric_code = int(float(row['iso_numeric']))
                            numeric_to_alpha3[numeric_code] = row['iso_alpha3']
                        except (ValueError, TypeError):
                            pass

                logger.info(f"Created mapping with {len(numeric_to_alpha3)} entries")

                # Add ISO alpha-3 columns if numeric codes are present
                if 'origin' in df.columns:
                    # Convert to int first to ensure proper mapping
                    df['origin'] = pd.to_numeric(df['origin'], errors='coerce')
                    df['origin'] = df['origin'].fillna(0).astype(int)

                    # Map to ISO alpha-3
                    df['exporter_iso'] = df['origin'].map(numeric_to_alpha3)
                    logger.info(f"Added 'exporter_iso' column by mapping from 'origin'")

                    # For debugging
                    unique_origins = df['origin'].unique()[:10]
                    logger.info(f"Sample origin codes: {unique_origins}")
                    logger.info(f"Corresponding alpha3: {[numeric_to_alpha3.get(code) for code in unique_origins]}")

                if 'destination' in df.columns:
                    # Convert to int first to ensure proper mapping
                    df['destination'] = pd.to_numeric(df['destination'], errors='coerce')
                    df['destination'] = df['destination'].fillna(0).astype(int)

                    # Map to ISO alpha-3
                    df['importer_iso'] = df['destination'].map(numeric_to_alpha3)
                    logger.info(f"Added 'importer_iso' column by mapping from 'destination'")

                    # For debugging
                    unique_destinations = df['destination'].unique()[:10]
                    logger.info(f"Sample destination codes: {unique_destinations}")
                    logger.info(f"Corresponding alpha3: {[numeric_to_alpha3.get(code) for code in unique_destinations]}")
            else:
                logger.warning(f"ISO mapping file not found at {iso_mapping_path}")
        except Exception as e:
            logger.error(f"Error adding ISO alpha-3 columns: {str(e)}")
            # Continue without ISO mapping

        if column_mapping:
            df = df.rename(columns=column_mapping)

        # Ensure required columns exist
        for col in [self.origin_col, self.destination_col, self.distance_col]:
            if col not in df.columns:
                if col in [self.origin_col, self.destination_col]:
                    df[col] = 0  # Default country code
                else:
                    df[col] = FALLBACK_DISTANCE

        # Ensure numeric ISO codes
        for col in [self.origin_col, self.destination_col]:
            if col in df.columns and df[col].dtype == 'object':
                try:
                    df[col] = pd.to_numeric(df[col], errors='coerce')
                except Exception as e:
                    logger.warning(f"Could not convert {col} to numeric: {e}")

        # Fill missing values with default distance
        if self.distance_col in df.columns:
            df[self.distance_col] = df[self.distance_col].fillna(FALLBACK_DISTANCE)

        # Add distance to Yemen for all countries that don't have it
        df = self._ensure_yemen_distances(df)

        return df

    @with_error_handling(category=ErrorCategory.VALIDATION, severity=ErrorSeverity.WARNING)
    def validate_data(self, data: pd.DataFrame, **kwargs) -> bool:
        """
        Validate the distance data, checking for required columns and valid values.

        Args:
            data: The processed distance DataFrame
            **kwargs: Additional validation arguments

        Returns:
            True if validation passes, potentially with data modified in place
        """
        # Check if required columns exist
        required_columns = [self.origin_col, self.destination_col, self.distance_col]
        missing_columns = [col for col in required_columns if col not in data.columns]

        if missing_columns:
            logger.warning(f"Distance data missing required columns: {missing_columns}")
            # For missing columns, add them with default values
            for col in missing_columns:
                if col in [self.origin_col, self.destination_col]:
                    data[col] = 0  # Default country code
                elif col == self.distance_col:
                    data[col] = FALLBACK_DISTANCE

        # Check for negative distances
        if self.distance_col in data.columns and (data[self.distance_col] < 0).any():
            logger.warning("Found negative distances. Converting to absolute values.")
            data[self.distance_col] = data[self.distance_col].abs()

        # Check for zero distances between different countries (not including self-distance)
        if all(col in data.columns for col in [self.origin_col, self.destination_col, self.distance_col]):
            zero_dist_mask = (
                (data[self.distance_col] == 0) &
                (data[self.origin_col] != data[self.destination_col])
            )

            if zero_dist_mask.any():
                logger.warning(f"Found {zero_dist_mask.sum()} zero distances between different countries. Replacing with default.")
                data.loc[zero_dist_mask, self.distance_col] = FALLBACK_DISTANCE

        return True

    def _generate_distance_matrix(self) -> pd.DataFrame:
        """
        Generate a distance matrix for countries using their coordinates.

        Returns:
            DataFrame with country pairs and distances
        """
        # Load country codes and their information
        country_data = self._load_country_info()

        # Create empty distance matrix
        pairs = []

        # Generate all country pairs
        countries = list(country_data.keys())

        for origin in countries:
            origin_data = country_data.get(origin, {})

            # Handle self-distance (set to 0)
            pairs.append({
                self.origin_col: origin,
                self.destination_col: origin,
                self.distance_col: 0,
                'origin_name': origin_data.get('name', f"Country_{origin}"),
                'destination_name': origin_data.get('name', f"Country_{origin}")
            })

            for destination in countries:
                if origin == destination:
                    continue  # Skip self (already added)

                destination_data = country_data.get(destination, {})

                # If we have coordinates for both countries, calculate distance
                if 'latitude' in origin_data and 'longitude' in origin_data and \
                   'latitude' in destination_data and 'longitude' in destination_data:
                    distance = self._haversine_distance(
                        origin_data['latitude'], origin_data['longitude'],
                        destination_data['latitude'], destination_data['longitude']
                    )
                else:
                    # Otherwise use a fallback distance
                    distance = FALLBACK_DISTANCE

                pairs.append({
                    self.origin_col: origin,
                    self.destination_col: destination,
                    self.distance_col: distance,
                    'origin_name': origin_data.get('name', f"Country_{origin}"),
                    'destination_name': destination_data.get('name', f"Country_{destination}")
                })

        # Create DataFrame from pairs
        df = pd.DataFrame(pairs)

        # Save the matrix for future use
        try:
            Path(os.path.dirname(self.distance_matrix_path)).mkdir(parents=True, exist_ok=True)
            df.to_csv(self.distance_matrix_path, index=False)
            logger.info(f"Saved distance matrix to {self.distance_matrix_path}")
        except Exception as e:
            logger.warning(f"Could not save distance matrix: {e}")

        return df

    def _load_country_info(self) -> Dict[int, Dict[str, Any]]:
        """
        Load country information from country code files.

        Returns:
            Dictionary mapping country ISO codes to data including coordinates
        """
        country_data = {}

        # First try to load country codes from config/country_codes.json
        if os.path.exists(self.country_codes_path):
            try:
                with open(self.country_codes_path, 'r') as f:
                    codes_json = json.load(f)
                # Expecting an array of country objects or a dict mapping
                country_data = self._process_country_code_json(codes_json)
            except Exception as e:
                logger.warning(f"Error loading country codes from {self.country_codes_path}: {e}")

        # Now try to load from BACI country codes file
        if os.path.exists(self.baci_country_codes_path):
            try:
                # Read the CSV file
                baci_countries = pd.read_csv(self.baci_country_codes_path)

                # Process each row
                for _, row in baci_countries.iterrows():
                    country_code = int(row.get('country_code', 0))
                    if country_code > 0:
                        # Create or update country data
                        if country_code not in country_data:
                            country_data[country_code] = {}

                        # Add/update with BACI data
                        country_data[country_code].update({
                            'name': row.get('country_name', f"Country_{country_code}"),
                            'iso2': row.get('country_iso2', ''),
                            'iso3': row.get('country_iso3', '')
                        })
            except Exception as e:
                logger.warning(f"Error loading BACI country codes from {self.baci_country_codes_path}: {e}")

        # If we still don't have Yemen data, add it
        if self.yemen_iso_numeric not in country_data:
            country_data[self.yemen_iso_numeric] = {
                'name': 'Yemen',
                'iso2': 'YE',
                'iso3': 'YEM',
                'latitude': 15.552727,  # Default Yemen coordinates
                'longitude': 48.516388
            }

        # Add fixed coordinates for key countries if missing
        # We'll add coordinates for some major countries if they're missing
        # In a real implementation, we'd load these from a more complete dataset
        # This is just for demonstration purposes
        key_coordinates = {
            self.yemen_iso_numeric: (15.552727, 48.516388),  # Yemen
            840: (37.09024, -95.712891),  # USA
            276: (51.165691, 10.451526),  # Germany
            156: (35.86166, 104.195397),  # China
            124: (56.130366, -106.346771),  # Canada
            826: (55.378051, -3.435973),  # UK
            392: (36.204824, 138.252924),  # Japan
            250: (46.227638, 2.213749),  # France
            380: (41.87194, 12.56738),  # Italy
            642: (41.377491, 64.585262)   # Romania
        }

        # Fill in missing coordinates for key countries
        for code, (lat, lon) in key_coordinates.items():
            if code in country_data:
                country = country_data[code]
                if 'latitude' not in country or 'longitude' not in country:
                    country['latitude'] = lat
                    country['longitude'] = lon

        # For any remaining countries without coordinates, try to assign reasonable positions
        # In a real implementation, we'd use a more complete geocoding solution
        for code, country in country_data.items():
            if 'latitude' not in country or 'longitude' not in country:
                # Just assign a position in a grid based on code number
                # This is highly approximate and just for example purposes
                idx = int(code) % 1000
                row = idx // 30
                col = idx % 30
                country['latitude'] = (row - 15) * 5  # -75 to +80 degrees
                country['longitude'] = (col - 15) * 12  # -180 to +168 degrees

        return country_data

    def _process_country_code_json(self, data: Any) -> Dict[int, Dict[str, Any]]:
        """
        Process country code JSON into a standardized dictionary format.

        Args:
            data: JSON data loaded from country codes file

        Returns:
            Dictionary mapping country codes to their data
        """
        result = {}

        # Handle different JSON structures
        if isinstance(data, dict):
            # If it's a simple mapping of code -> name
            if all(isinstance(k, (str, int)) and isinstance(v, str) for k, v in data.items()):
                for code_str, name in data.items():
                    # Convert string codes to int if needed
                    try:
                        code = int(code_str)
                        result[code] = {'name': name}
                    except ValueError:
                        # Skip non-numeric codes
                        pass
            # If it's a code -> object mapping
            else:
                for code_str, country_data in data.items():
                    try:
                        code = int(code_str)
                        if isinstance(country_data, dict):
                            result[code] = country_data
                        elif isinstance(country_data, str):
                            result[code] = {'name': country_data}
                    except ValueError:
                        # Skip non-numeric codes
                        pass

        # If it's an array of country objects
        elif isinstance(data, list):
            for item in data:
                if isinstance(item, dict):
                    # Try to find a numeric country code field
                    code = None
                    for field in ['code', 'id', 'iso_numeric', 'country_code', 'numeric_code']:
                        if field in item:
                            try:
                                code = int(item[field])
                                break
                            except (ValueError, TypeError):
                                pass

                    if code is not None:
                        result[code] = item

        return result

    def _ensure_yemen_distances(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Ensure that distances to Yemen exist for all countries in the dataset.

        Args:
            df: Distance DataFrame

        Returns:
            DataFrame with Yemen distances added if needed
        """
        if self.origin_col not in df.columns or self.destination_col not in df.columns:
            logger.warning("Missing origin or destination columns, cannot add Yemen distances")
            return df

        # Get all unique country codes
        countries = pd.concat([
            df[self.origin_col].dropna(),
            df[self.destination_col].dropna()
        ]).unique()

        # Filter out non-numeric values
        countries = [c for c in countries if isinstance(c, (int, float))]

        # Check which countries don't have distances to Yemen
        existing_pairs = df[
            ((df[self.origin_col] == self.yemen_iso_numeric) & df[self.destination_col].isin(countries)) |
            ((df[self.destination_col] == self.yemen_iso_numeric) & df[self.origin_col].isin(countries))
        ]

        existing_countries = pd.concat([
            existing_pairs[existing_pairs[self.origin_col] == self.yemen_iso_numeric][self.destination_col],
            existing_pairs[existing_pairs[self.destination_col] == self.yemen_iso_numeric][self.origin_col]
        ]).unique()

        missing_countries = [c for c in countries if c != self.yemen_iso_numeric and c not in existing_countries]

        if not missing_countries:
            return df

        # Add missing pairs with a default distance
        new_pairs = []
        for country in missing_countries:
            # Add both directions
            new_pairs.append({
                self.origin_col: self.yemen_iso_numeric,
                self.destination_col: country,
                self.distance_col: FALLBACK_DISTANCE
            })
            new_pairs.append({
                self.origin_col: country,
                self.destination_col: self.yemen_iso_numeric,
                self.distance_col: FALLBACK_DISTANCE
            })

        if new_pairs:
            new_df = pd.DataFrame(new_pairs)
            # Add name columns if they exist in original df
            if 'origin_name' in df.columns and 'destination_name' in df.columns:
                new_df['origin_name'] = new_df[self.origin_col].apply(
                    lambda x: 'Yemen' if x == self.yemen_iso_numeric else f"Country_{x}"
                )
                new_df['destination_name'] = new_df[self.destination_col].apply(
                    lambda x: 'Yemen' if x == self.yemen_iso_numeric else f"Country_{x}"
                )

            # Combine with original
            df = pd.concat([df, new_df], ignore_index=True)
            logger.info(f"Added {len(new_pairs)} missing Yemen distance pairs")

        return df

    @staticmethod
    def _haversine_distance(lat1: float, lon1: float, lat2: float, lon2: float) -> float:
        """
        Calculate the great circle distance between two points
        on the earth (specified in decimal degrees)

        Args:
            lat1, lon1: Coordinates of first point
            lat2, lon2: Coordinates of second point

        Returns:
            Distance in kilometers
        """
        # Convert decimal degrees to radians
        lat1, lon1, lat2, lon2 = map(np.radians, [lat1, lon1, lat2, lon2])

        # Haversine formula
        dlon = lon2 - lon1
        dlat = lat2 - lat1
        a = np.sin(dlat/2)**2 + np.cos(lat1) * np.cos(lat2) * np.sin(dlon/2)**2
        c = 2 * np.arcsin(np.sqrt(a))
        r = 6371  # Radius of earth in kilometers
        return c * r