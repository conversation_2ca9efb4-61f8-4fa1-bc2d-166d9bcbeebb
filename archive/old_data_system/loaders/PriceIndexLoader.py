"""
Loaders for import and export price indices.
"""

# Standard library imports
from pathlib import Path
from typing import Any, Dict, Optional

# Third-party imports
import pandas as pd

# Project imports
from yemen_trade_diagnostic.data.loader_registry import register_loader

# Import the base class (use relative imports for internal modules)
from yemen_trade_diagnostic.data.memory_optimized_loader_base import MemoryOptimizedDataLoader
from yemen_trade_diagnostic.data.schema_management import (
    ValidationMode,
    ValidationResult,
    validate_schema,
)
from yemen_trade_diagnostic.interfaces.cache_interface import (
    CacheLevel,
    CachePriority,
    DataLifetime,
    cache_get_or_compute,
)
from yemen_trade_diagnostic.interfaces.error_interface import (
    ErrorCategory,
    ErrorSeverity,
    with_error_handling,
)

# Import interfaces (use interfaces instead of direct utils)
from yemen_trade_diagnostic.interfaces.logging_interface import get_logger

# Import from yemen_trade_diagnostic.utils
from yemen_trade_diagnostic.utils.config import get_processed_data_dir


@register_loader("export_price_index")
class ExportPriceIndexLoader(MemoryOptimizedDataLoader):
    """Loads export price index data."""

    def __init__(self, source_name: str = "export_price_index", config: Dict[str, Any] = None):
        super().__init__(source_name, config)
        self.logger = get_logger(__name__ + ".ExportPriceIndexLoader")
        self.file_name = self.config.get("export_price_index_file", "export_price_index.csv")

    def _get_file_path(self, **kwargs) -> Path:
        """
        Implements required method from MemoryOptimizedDataLoader.
        Returns the path to the export price index file.
        """
        processed_data_dir = get_processed_data_dir()
        return processed_data_dir / self.file_name

    @with_error_handling(
        category=ErrorCategory.DATA_ACCESS,
        severity=ErrorSeverity.ERROR
    )
    def load(self, year: Optional[int] = None, use_cache: bool = True, **kwargs) -> pd.DataFrame:
        """
        Load export price index data.
        
        Args:
            year: Optional year to filter data (might not be used if full dataset is needed)
            use_cache: Whether to use cache
            **kwargs: Additional arguments
            
        Returns:
            DataFrame containing export price index data
        """
        self.logger.info(f"ExportPriceIndexLoader: Loading data (year: {year}, use_cache: {use_cache})")
        
        file_path = self._get_file_path(**kwargs)
        
        def _compute_data():
            try:
                if file_path.exists():
                    df = pd.read_csv(file_path)
                    self.logger.info(f"ExportPriceIndexLoader: Successfully loaded data from {file_path}")
                    return df
                else:
                    self.logger.warning(f"ExportPriceIndexLoader: File not found: {file_path}")
                    # Return empty DataFrame with expected columns
                    return pd.DataFrame(columns=['year', 'price_index'])
            except Exception as e:
                self.logger.error(f"ExportPriceIndexLoader: Error loading data: {e}", exc_info=True)
                return pd.DataFrame(columns=['year', 'price_index'])
        
        if use_cache:
            cache_key = f"export_price_index_data_{year if year else 'all'}"
            ttl_seconds = self.config.get("cache_ttl", 7 * 24 * 60 * 60)  # 7 days by default
            
            df = cache_get_or_compute(
                key=cache_key,
                compute_func=_compute_data,
                ttl=ttl_seconds,
                level=CacheLevel.DISK,
                lifetime=DataLifetime.PERSISTENT,
                priority=CachePriority.HIGH
            )
            self.logger.info(f"ExportPriceIndexLoader: Data obtained via cache/compute.")
        else:
            df = _compute_data()
            self.logger.info(f"ExportPriceIndexLoader: Data loaded directly.")
        
        # Filter by year if specified
        if year is not None and 'year' in df.columns and not df.empty:
            df = df[df['year'] == year]
            self.logger.info(f"ExportPriceIndexLoader: Filtered data for year {year}. Shape: {df.shape}")
        
        return df
    
    def transform(self, df: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """
        Transform export price index data.
        
        Args:
            df: DataFrame to transform
            **kwargs: Additional arguments
            
        Returns:
            Transformed DataFrame
        """
        self.logger.info(f"ExportPriceIndexLoader: Transforming data. Initial shape: {df.shape}")
        
        if df.empty:
            self.logger.warning("ExportPriceIndexLoader: Input DataFrame is empty.")
            return df
        
        # Ensure required columns exist
        required_cols = ['year', 'price_index']
        if not all(col in df.columns for col in required_cols):
            self.logger.error(f"ExportPriceIndexLoader: Missing required columns. Required: {required_cols}, Found: {df.columns.tolist()}")
            # Try to adapt if possible
            if 'year' not in df.columns and 'date' in df.columns:
                df['year'] = pd.to_datetime(df['date']).dt.year
                self.logger.info("ExportPriceIndexLoader: Created 'year' column from 'date'.")
            
            if 'price_index' not in df.columns and 'value' in df.columns:
                df['price_index'] = df['value']
                self.logger.info("ExportPriceIndexLoader: Created 'price_index' column from 'value'.")
            
            # Check again after adaptations
            if not all(col in df.columns for col in required_cols):
                self.logger.error("ExportPriceIndexLoader: Could not adapt DataFrame to required format.")
                return pd.DataFrame(columns=required_cols)
        
        # Ensure correct data types
        if 'year' in df.columns:
            df['year'] = pd.to_numeric(df['year'], errors='coerce').astype('Int64')
        
        if 'price_index' in df.columns:
            df['price_index'] = pd.to_numeric(df['price_index'], errors='coerce')
        
        # Remove duplicates
        df = df.drop_duplicates(subset=['year'], keep='first')
        
        self.logger.info(f"ExportPriceIndexLoader: Transformation complete. Final shape: {df.shape}")
        return df
    
    def validate_data(self, df: pd.DataFrame, **kwargs) -> bool:
        """
        Validate export price index data.
        
        Args:
            df: DataFrame to validate
            **kwargs: Additional arguments
            
        Returns:
            True if validation passes, False otherwise
        """
        self.logger.info(f"ExportPriceIndexLoader: Validating data. Shape: {df.shape}")
        
        if df is None:
            self.logger.warning("ExportPriceIndexLoader: Input DataFrame is None. Validation skipped.")
            return True
        
        if df.empty:
            self.logger.warning("ExportPriceIndexLoader: Input DataFrame is empty. Validation skipped.")
            return True
        
        # Basic validation
        required_cols = ['year', 'price_index']
        if not all(col in df.columns for col in required_cols):
            self.logger.error(f"ExportPriceIndexLoader: Missing required columns. Required: {required_cols}, Found: {df.columns.tolist()}")
            return False
        
        # Check for missing values
        if df['year'].isna().any() or df['price_index'].isna().any():
            self.logger.warning("ExportPriceIndexLoader: DataFrame contains missing values.")
            # This might be acceptable, so not failing validation
        
        self.logger.info("ExportPriceIndexLoader: Data passed validation.")
        return True


@register_loader("import_price_index")
class ImportPriceIndexLoader(MemoryOptimizedDataLoader):
    """Loads import price index data."""

    def __init__(self, source_name: str = "import_price_index", config: Dict[str, Any] = None):
        super().__init__(source_name, config)
        self.logger = get_logger(__name__ + ".ImportPriceIndexLoader")
        self.file_name = self.config.get("import_price_index_file", "import_price_index.csv")

    def _get_file_path(self, **kwargs) -> Path:
        """
        Implements required method from MemoryOptimizedDataLoader.
        Returns the path to the import price index file.
        """
        processed_data_dir = get_processed_data_dir()
        return processed_data_dir / self.file_name

    @with_error_handling(
        category=ErrorCategory.DATA_ACCESS,
        severity=ErrorSeverity.ERROR
    )
    def load(self, year: Optional[int] = None, use_cache: bool = True, **kwargs) -> pd.DataFrame:
        """
        Load import price index data.
        
        Args:
            year: Optional year to filter data (might not be used if full dataset is needed)
            use_cache: Whether to use cache
            **kwargs: Additional arguments
            
        Returns:
            DataFrame containing import price index data
        """
        self.logger.info(f"ImportPriceIndexLoader: Loading data (year: {year}, use_cache: {use_cache})")
        
        file_path = self._get_file_path(**kwargs)
        
        def _compute_data():
            try:
                if file_path.exists():
                    df = pd.read_csv(file_path)
                    self.logger.info(f"ImportPriceIndexLoader: Successfully loaded data from {file_path}")
                    return df
                else:
                    self.logger.warning(f"ImportPriceIndexLoader: File not found: {file_path}")
                    # Return empty DataFrame with expected columns
                    return pd.DataFrame(columns=['year', 'price_index'])
            except Exception as e:
                self.logger.error(f"ImportPriceIndexLoader: Error loading data: {e}", exc_info=True)
                return pd.DataFrame(columns=['year', 'price_index'])
        
        if use_cache:
            cache_key = f"import_price_index_data_{year if year else 'all'}"
            ttl_seconds = self.config.get("cache_ttl", 7 * 24 * 60 * 60)  # 7 days by default
            
            df = cache_get_or_compute(
                key=cache_key,
                compute_func=_compute_data,
                ttl=ttl_seconds,
                level=CacheLevel.DISK,
                lifetime=DataLifetime.PERSISTENT,
                priority=CachePriority.HIGH
            )
            self.logger.info(f"ImportPriceIndexLoader: Data obtained via cache/compute.")
        else:
            df = _compute_data()
            self.logger.info(f"ImportPriceIndexLoader: Data loaded directly.")
        
        # Filter by year if specified
        if year is not None and 'year' in df.columns and not df.empty:
            df = df[df['year'] == year]
            self.logger.info(f"ImportPriceIndexLoader: Filtered data for year {year}. Shape: {df.shape}")
        
        return df
    
    def transform(self, df: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """
        Transform import price index data.
        
        Args:
            df: DataFrame to transform
            **kwargs: Additional arguments
            
        Returns:
            Transformed DataFrame
        """
        self.logger.info(f"ImportPriceIndexLoader: Transforming data. Initial shape: {df.shape}")
        
        if df.empty:
            self.logger.warning("ImportPriceIndexLoader: Input DataFrame is empty.")
            return df
        
        # Ensure required columns exist
        required_cols = ['year', 'price_index']
        if not all(col in df.columns for col in required_cols):
            self.logger.error(f"ImportPriceIndexLoader: Missing required columns. Required: {required_cols}, Found: {df.columns.tolist()}")
            # Try to adapt if possible
            if 'year' not in df.columns and 'date' in df.columns:
                df['year'] = pd.to_datetime(df['date']).dt.year
                self.logger.info("ImportPriceIndexLoader: Created 'year' column from 'date'.")
            
            if 'price_index' not in df.columns and 'value' in df.columns:
                df['price_index'] = df['value']
                self.logger.info("ImportPriceIndexLoader: Created 'price_index' column from 'value'.")
            
            # Check again after adaptations
            if not all(col in df.columns for col in required_cols):
                self.logger.error("ImportPriceIndexLoader: Could not adapt DataFrame to required format.")
                return pd.DataFrame(columns=required_cols)
        
        # Ensure correct data types
        if 'year' in df.columns:
            df['year'] = pd.to_numeric(df['year'], errors='coerce').astype('Int64')
        
        if 'price_index' in df.columns:
            df['price_index'] = pd.to_numeric(df['price_index'], errors='coerce')
        
        # Remove duplicates
        df = df.drop_duplicates(subset=['year'], keep='first')
        
        self.logger.info(f"ImportPriceIndexLoader: Transformation complete. Final shape: {df.shape}")
        return df
    
    def validate_data(self, df: pd.DataFrame, **kwargs) -> bool:
        """
        Validate import price index data.
        
        Args:
            df: DataFrame to validate
            **kwargs: Additional arguments
            
        Returns:
            True if validation passes, False otherwise
        """
        self.logger.info(f"ImportPriceIndexLoader: Validating data. Shape: {df.shape}")
        
        if df is None:
            self.logger.warning("ImportPriceIndexLoader: Input DataFrame is None. Validation skipped.")
            return True
        
        if df.empty:
            self.logger.warning("ImportPriceIndexLoader: Input DataFrame is empty. Validation skipped.")
            return True
        
        # Basic validation
        required_cols = ['year', 'price_index']
        if not all(col in df.columns for col in required_cols):
            self.logger.error(f"ImportPriceIndexLoader: Missing required columns. Required: {required_cols}, Found: {df.columns.tolist()}")
            return False
        
        # Check for missing values
        if df['year'].isna().any() or df['price_index'].isna().any():
            self.logger.warning("ImportPriceIndexLoader: DataFrame contains missing values.")
            # This might be acceptable, so not failing validation
        
        self.logger.info("ImportPriceIndexLoader: Data passed validation.")
        return True
