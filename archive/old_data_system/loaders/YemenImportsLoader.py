"""
DataLoader for Yemen Imports data.
This loader fetches BACI data for a given year and then filters it for Yemen's imports.
"""

# Standard library imports
from typing import Any, Dict, Optional

# Third-party imports
import pandas as pd

# Project imports
from yemen_trade_diagnostic.data.loader_base import DataLoaderBase
from yemen_trade_diagnostic.data.loader_registry import register_loader
from yemen_trade_diagnostic.data.loaders.baci_loader import <PERSON><PERSON><PERSON>oader
from yemen_trade_diagnostic.data.loaders.CountryCodesLoader import (
    CountryCodesLoader,  # For source names
)
from yemen_trade_diagnostic.data.loaders.ProductCodesLoader import (
    ProductCodesLoader,  # For product names
)

# Import the base class (use relative imports for internal modules)
from yemen_trade_diagnostic.data.memory_optimized_loader_base import MemoryOptimizedDataLoader
from yemen_trade_diagnostic.data.schema_management import (
    ValidationMode,
    ValidationResult,
    get_yemen_imports_schema,
    validate_schema,
)
from yemen_trade_diagnostic.interfaces.cache_interface import CacheLevel, cache_get_or_compute
from yemen_trade_diagnostic.interfaces.error_interface import (
    Error<PERSON><PERSON><PERSON><PERSON>,
    ErrorSeverity,
    with_error_handling,
)

# Import interfaces (use interfaces instead of direct utils)
from yemen_trade_diagnostic.interfaces.logging_interface import get_logger
from yemen_trade_diagnostic.models.common.constants import (
    PARTNER_COL,
    PRODUCT_COL,
    QUANTITY_COL,
    REPORTER_COL,
    VALUE_COL,
    YEAR_COL,
    YEMEN_COUNTRY_CODE_NUMERIC,
)


@register_loader(name="yemen_imports")
class YemenImportsLoader(DataLoaderBase):
    """Loads Yemen's import data, derived from BACI data."""

    def __init__(self, name: str = "yemen_imports", config: Optional[Dict[str, Any]] = None):
        super().__init__(name, config)
        self.logger = get_logger(self.__class__.__name__)
        baci_loader_config = self.config.get('baci_loader_config', {})
        self.baci_loader = BaciLoader(name="baci", config=baci_loader_config)
        self.product_codes_loader = ProductCodesLoader(name="product_codes", config=config)
        self.country_codes_loader = CountryCodesLoader(name="country_codes", config=config)
        self.yemen_iso_numeric = YEMEN_COUNTRY_CODE_NUMERIC

    def load(self, year: int, use_cache: bool = True, **kwargs) -> pd.DataFrame:
        """Delegates loading of raw BACI data to BaciLoader."""
        self.logger.info(f"YemenImportsLoader: Requesting BACI data for year {year} to derive Yemen imports.")
        try:
            baci_df = self.baci_loader.process(year=year, use_cache=use_cache, **kwargs)
            if baci_df is None or baci_df.empty:
                self.logger.warning(f"YemenImportsLoader: Underlying BACI data for year {year} is empty or not available.")
                return pd.DataFrame()
            return baci_df
        except Exception as e:
            self.logger.error(f"YemenImportsLoader: Failed to load underlying BACI data for year {year}. Error: {e}", exc_info=True)
            return pd.DataFrame()

    def transform(self, df: pd.DataFrame, year: int, **kwargs) -> pd.DataFrame:
        """Filters the loaded (and already standardized) BACI data for Yemen's imports."""
        self.logger.info(f"YemenImportsLoader: Filtering for Yemen imports from BACI data (year {year}). Initial BACI shape: {df.shape if df is not None else 'None'}")
        if df is None or df.empty:
            self.logger.warning("YemenImportsLoader: Input DataFrame for transform is empty, cannot filter Yemen imports.")
            return pd.DataFrame(columns=[YEAR_COL, REPORTER_COL, PARTNER_COL, PRODUCT_COL, VALUE_COL, QUANTITY_COL])

        if PARTNER_COL not in df.columns:
            self.logger.error(f"YemenImportsLoader: Critical - Standardized partner column '{PARTNER_COL}' not found in input data. Available: {df.columns.tolist()}")
            return pd.DataFrame(columns=[YEAR_COL, REPORTER_COL, PARTNER_COL, PRODUCT_COL, VALUE_COL, QUANTITY_COL])

        yemen_imports_df = df[df[PARTNER_COL] == self.yemen_iso_numeric].copy()
        self.logger.info(f"YemenImportsLoader: Filtered Yemen imports. Shape: {yemen_imports_df.shape}")

        expected_cols = [YEAR_COL, REPORTER_COL, PARTNER_COL, PRODUCT_COL, VALUE_COL, QUANTITY_COL]

        if not yemen_imports_df.empty:
            # Create a new DataFrame with the same index as yemen_imports_df
            final_df = pd.DataFrame(index=yemen_imports_df.index)

            # Add each column, or empty column if missing
            for col in expected_cols:
                if col in yemen_imports_df.columns:
                    # Get the values as a 1D array
                    values = yemen_imports_df[col].values
                    if hasattr(values, 'flatten') and values.ndim > 1:
                        values = values.flatten()

                    # Make sure the length matches
                    if len(values) != len(final_df.index):
                        self.logger.warning(f"YemenImportsLoader: Column '{col}' has {len(values)} values but index has {len(final_df.index)} entries. Truncating.")
                        values = values[:len(final_df.index)]

                    final_df[col] = values
                else:
                    self.logger.warning(f"YemenImportsLoader: Expected column '{col}' missing after filtering, adding as empty.")
                    final_df[col] = pd.NA  # Or appropriate type like np.nan for numeric, None for object

            return final_df[expected_cols]
        else:
            self.logger.info("YemenImportsLoader: No import data found for Yemen after filtering.")
            # Return empty DataFrame with correct columns
            return pd.DataFrame(columns=expected_cols)

    def validate_data(self, df: pd.DataFrame, **kwargs) -> bool:
        """Validates the transformed Yemen import data."""
        if df is None:
            self.logger.warning("Validation skipped for None DataFrame in YemenImportsLoader")
            return False
        self.logger.info(f"YemenImportsLoader: Validating Yemen imports data. Shape: {df.shape}")
        if df.empty:
            self.logger.info("YemenImportsLoader: Data is empty but considered valid.")
            return True
        if PARTNER_COL in df.columns and not df[df[PARTNER_COL] != self.yemen_iso_numeric].empty:
            self.logger.error(f"Validation Error: Found non-Yemen partner codes in YemenImports data: {df[df[PARTNER_COL] != self.yemen_iso_numeric][PARTNER_COL].unique()}")
            return False
        return True