"""
DataLoader for Country Codes reference data.
"""

# Standard library imports
from pathlib import Path
from typing import Any, Dict, Optional

# Third-party imports
import pandas as pd

# Project imports
from yemen_trade_diagnostic.data.loader_registry import register_loader

# Import the base class (use relative imports for internal modules)
from yemen_trade_diagnostic.data.memory_optimized_loader_base import MemoryOptimizedDataLoader
from yemen_trade_diagnostic.data.schema_management import (
    ValidationMode,
    get_country_codes_schema,
    validate_schema,
)
from yemen_trade_diagnostic.interfaces.cache_interface import (
    CacheLevel,
    CachePriority,
    DataLifetime,
    cache_get_or_compute,
)

# Import interfaces (use interfaces instead of direct utils)
from yemen_trade_diagnostic.interfaces.error_interface import (
    <PERSON>rror<PERSON>ate<PERSON>y,
    ErrorSeverity,
    RetryStrategy,
    with_circuit_breaker,
    with_error_handling,
    with_retry,
)
from yemen_trade_diagnostic.interfaces.logging_interface import get_logger

# Import from yemen_trade_diagnostic.utils
from yemen_trade_diagnostic.utils.config import get_raw_data_dir
from yemen_trade_diagnostic.utils.file_utils import load_dataframe_from_csv  # Updated import

# Constants
COUNTRY_CODES_FILE_NAME = "country_codes_V202501.csv"

@register_loader("country_codes", name="country_codes")
class CountryCodesLoader(MemoryOptimizedDataLoader):
    """
    DataLoader for Country Codes reference data.
    """
    def __init__(self, source_name: str, config: Dict[str, Any] = None):
        super().__init__(source_name, config)
        self.logger = get_logger(__name__ + ".CountryCodesLoader")
        self.country_codes_file_name = self.config.get("country_codes_file_name", COUNTRY_CODES_FILE_NAME)

    @with_error_handling(
        category=ErrorCategory.DATA_ACCESS,
        severity=ErrorSeverity.ERROR
    )
    @with_circuit_breaker(name="country_codes_source_class_v2", handle_exception=True)
    @with_retry(
        retry_count=3, retry_delay=1.5, strategy=RetryStrategy.EXPONENTIAL_BACKOFF,
        exception_types=[FileNotFoundError, IOError]
    )
    def load(self, use_cache: bool = True, **kwargs) -> pd.DataFrame:
        """
        Loads raw country codes data.
        """
        self.logger.info(f"CountryCodesLoader: Initiating load (use_cache={use_cache})")
        raw_data_dir = get_raw_data_dir()
        file_path = raw_data_dir / "baci" / self.country_codes_file_name # Assuming it's still in 'baci' subdir

        def _compute_country_codes_df_from_source(source_file_path: Path) -> pd.DataFrame:
            self.logger.info(f"CountryCodesLoader: Cache miss or direct load from {source_file_path}")
            return load_dataframe_from_csv(source_file_path)

        if use_cache:
            cache_key = "country_codes_loader_raw"
            ttl_seconds = self.config.get("cache_ttl_raw_country_codes", 7 * 24 * 60 * 60) # 7 days
            df = cache_get_or_compute(
                key=cache_key,
                compute_func=lambda: _compute_country_codes_df_from_source(file_path),
                ttl=ttl_seconds, level=CacheLevel.DISK, lifetime=DataLifetime.PERSISTENT,
                priority=CachePriority.HIGH # Reference data, high priority
            )
            self.logger.info(f"CountryCodesLoader: Raw data obtained via cache/compute.")
        else:
            df = _compute_country_codes_df_from_source(file_path)
            self.logger.info(f"CountryCodesLoader: Raw data loaded directly from source.")
        return df

    def transform(self, df: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """
        Transforms the raw country codes DataFrame to the expected schema.
        """
        self.logger.info(f"CountryCodesLoader: Performing transformations. Initial shape: {df.shape}")
        df_processed = df.copy()

        # The actual CSV has columns: country_code, country_name, country_iso2, country_iso3
        # Rename columns from CSV to match schema expectations
        rename_map = {
            'country_code': 'code',
            'country_name': 'name',
            'country_iso3': 'iso3'
            # 'country_iso2' is present in CSV but schema says it's dropped, so we'll drop it
        }

        # Check if all source columns for renaming exist
        source_cols_for_rename = list(rename_map.keys())
        missing_source_cols = [col for col in source_cols_for_rename if col not in df_processed.columns]
        if missing_source_cols:
            self.logger.warning(f"CountryCodesLoader: Raw CSV has different column names than expected. Attempting to map based on position.")
            # Try to map based on position instead of names
            if len(df_processed.columns) >= 4:  # Assuming we have at least 4 columns
                column_mapping = {
                    df_processed.columns[0]: 'code',      # First column is country_code
                    df_processed.columns[1]: 'name',     # Second column is country_name
                    df_processed.columns[3]: 'iso3'      # Fourth column is country_iso3
                }
                df_processed = df_processed.rename(columns=column_mapping)
            else:
                self.logger.error("CountryCodesLoader: CSV doesn't have enough columns to map properly.")
                # Return a DataFrame that will fail schema validation clearly
                return pd.DataFrame(columns=['code', 'name', 'iso3', 'region', 'income_group'])
        else:
            df_processed = df_processed.rename(columns=rename_map)

        # Drop country_iso2 if it exists (either original or if rename didn't happen)
        if 'country_iso2' in df_processed.columns:
            df_processed = df_processed.drop(columns=['country_iso2'])
        elif 'iso2' in df_processed.columns: # If it was renamed to iso2 by some other logic
            df_processed = df_processed.drop(columns=['iso2'])

        # Add columns required by schema but not in source CSV, with defaults
        df_processed['region'] = 'Unknown Region'
        df_processed['income_group'] = 'Unknown Income Group'

        # Ensure correct data types as per schema
        if 'code' in df_processed.columns:
            # Make sure code is properly converted to integer
            try:
                df_processed['code'] = pd.to_numeric(df_processed['code'], errors='coerce').astype('Int64')
                self.logger.info(f"CountryCodesLoader: Successfully converted 'code' column to Int64 type.")
            except Exception as e:
                self.logger.error(f"CountryCodesLoader: Failed to convert 'code' column to Int64: {e}")
                # Try a different approach if the standard conversion fails
                try:
                    df_processed['code'] = df_processed['code'].apply(lambda x: int(x) if pd.notna(x) and str(x).strip() else pd.NA)
                    self.logger.info(f"CountryCodesLoader: Successfully converted 'code' column to Int64 using alternative method.")
                except Exception as e2:
                    self.logger.error(f"CountryCodesLoader: Failed alternative conversion for 'code' column: {e2}")

        if 'name' in df_processed.columns:
            df_processed['name'] = df_processed['name'].astype(str)
        if 'iso3' in df_processed.columns:
            df_processed['iso3'] = df_processed['iso3'].astype(str)
        if 'region' in df_processed.columns:
             df_processed['region'] = df_processed['region'].astype(str)
        if 'income_group' in df_processed.columns:
            df_processed['income_group'] = df_processed['income_group'].astype(str)

        # Select and reorder columns to match schema expectations
        final_columns = ['code', 'name', 'iso3', 'region', 'income_group']
        # Ensure all final columns are present
        for col in final_columns:
            if col not in df_processed.columns:
                self.logger.warning(f"CountryCodesLoader: Final column '{col}' unexpectedly missing, adding with NA.")
                df_processed[col] = pd.NA

        df_processed = df_processed[final_columns]

        self.logger.info(f"CountryCodesLoader: Transformations complete. Final shape: {df_processed.shape}, Columns: {df_processed.columns.tolist()}")
        return df_processed

    def validate_data(self, df: pd.DataFrame, **kwargs) -> bool:
        """
        Validates the country codes DataFrame against its schema.
        """
        self.logger.info(f"CountryCodesLoader: Validating data. Shape: {df.shape}")
        schema = get_country_codes_schema()
        validation_result = validate_schema(df, schema.to_dict(), mode=ValidationMode.STRICT)

        if not validation_result.is_valid:
            issues_str = "\n".join([str(i) for i in validation_result.issues])
            error_summary = (
                f"CountryCodesLoader: Data failed validation. "
                f"Issues:\n{issues_str}"
            )
            self.logger.error(error_summary)
            return False

        self.logger.info("CountryCodesLoader: Data passed validation.")
        return True