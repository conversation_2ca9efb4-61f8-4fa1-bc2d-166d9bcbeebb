"""
DataLoader for Yemen Exports data.
This loader fetches BACI data for a given year and then filters it for Yemen's exports.
"""

# Standard library imports
from typing import Any, Dict, List, Optional

# Third-party imports
import pandas as pd

# Project imports
# Import the base class
from yemen_trade_diagnostic.data.loader_base import (  # Updated import
    DataFormat,
    DataLoaderBase,
    DataSource,
)
from yemen_trade_diagnostic.data.loader_registry import register_loader
from yemen_trade_diagnostic.data.loaders.baci_loader import (  # Import default dtypes for BACI config
    DEFAULT_BACI_DTYPE_SPEC,
    BaciLoader,
)
from yemen_trade_diagnostic.data.schema_management import (
    get_yemen_exports_schema,  # Assuming this returns the final schema
)
from yemen_trade_diagnostic.interfaces.error_interface import (
    ErrorCategory,
    ErrorSeverity,
    with_error_handling,
)

# Import interfaces
from yemen_trade_diagnostic.interfaces.logging_interface import get_logger
from yemen_trade_diagnostic.models.common.constants import (
    PARTNER_COL,
    PRODUCT_COL,
    QUANTITY_COL,
    REPORTER_COL,
    VALUE_COL,
    YEAR_COL,
    YEMEN_COUNTRY_CODE_NUMERIC,
)

# ProductCodesLoader and CountryCodesLoader might be used in a separate enrichment step, not directly in load/transform of core data
# from .ProductCodesLoader import ProductCodesLoader 
# from .CountryCodesLoader import CountryCodesLoader 

# cache_get_or_compute might not be directly used if delegation to BaciLoader which has its own caching.


@register_loader(name="yemen_exports")
class YemenExportsLoader(DataLoaderBase[pd.DataFrame]): # Changed base class
    """
    DataLoader for Yemen Exports data.
    This loader derives Yemen's export data from underlying BACI data for a given year.
    It leverages BaciLoader for fetching and initial processing of BACI data.
    """
    YEMEN_COUNTRY_CODE = YEMEN_COUNTRY_CODE_NUMERIC # Using constant

    def __init__(self, name: str = "yemen_exports", config: Optional[Dict[str, Any]] = None):
        config = config or {}
        super().__init__(name=name, source_type=DataSource.CUSTOM) # Or DataSource.DERIVED if more appropriate
        self.logger = get_logger(__name__ + ".YemenExportsLoader")
        
        # Configure BaciLoader instance
        # Pass relevant parts of YemenExportsLoader's config to BaciLoader
        baci_loader_config = config.get('baci_loader_config', {}).copy() # Get specific config for baci_loader if provided
        
        # Ensure memory_options are passed to BaciLoader for efficient loading
        baci_memory_options = baci_loader_config.get('memory_options', {}).copy()
        baci_memory_options.setdefault('dtype_spec', DEFAULT_BACI_DTYPE_SPEC) # Default dtypes for BACI
        
        # Example: enable hardware pool for BACI by default if YemenExportsLoader wants it generally
        # This assumes 'use_hardware_pool' in the main config applies to underlying BACI load
        if config.get('use_hardware_pool', True) and 'use_hardware_pool' not in baci_memory_options:
            baci_memory_options['use_hardware_pool'] = True 
        
        # Pass other relevant memory options if needed, e.g., chunksize
        if 'chunksize' in config and 'chunksize' not in baci_memory_options:
            baci_memory_options['chunksize'] = config['chunksize']
        
        baci_loader_config['memory_options'] = baci_memory_options
        if 'base_dir' not in baci_loader_config and 'base_dir' in config:
             baci_loader_config['base_dir'] = config['base_dir']


        self.baci_loader = BaciLoader(name="baci_for_yemen_exports", config=baci_loader_config)
        
        # Product and Country code loaders might be used in a separate enrichment step
        # self.product_codes_loader = ProductCodesLoader(name="product_codes", config=config)
        # self.country_codes_loader = CountryCodesLoader(name="country_codes", config=config)
        
        self.yemen_iso_numeric = YEMEN_COUNTRY_CODE_NUMERIC # Already defined as class var

    # --- Implementation of abstract methods from DataLoaderBase ---

    def supported_formats(self) -> List[DataFormat]:
        """YemenExportsLoader is a derived loader and does not directly support file formats."""
        return [] 

    def supports(self, path_or_identifier: str) -> bool:
        """
        YemenExportsLoader supports a conceptual identifier, typically the year for which data is requested.
        It does not directly load from a file path in the way FileDataLoaders do.
        The `path_or_identifier` here could be structured, e.g., "yemen_exports_YYYY" or just the year as string.
        For simplicity, if `load` is called with a year, that's the identifier.
        If `super().load()` is to be used, `path_or_identifier` must convey the year.
        """
        # This loader is identified by its name and parameters to its load method (i.e., year).
        # It doesn't support arbitrary file paths.
        return path_or_identifier == self.name # Or a more specific check if path_or_identifier encodes year.

    def _load_from_source(self, path_or_identifier: str, filters: Optional[Dict[str, Any]] = None) -> pd.DataFrame:
        """
        Loads data by deriving it from the BaciLoader.
        This method is called by `DataLoaderBase.load()`.
        `path_or_identifier` could be used to pass the year, e.g. "yemen_exports_2022" or just "2022".
        Alternatively, `year` can be passed via `filters`.
        """
        year = None
        if filters and 'year' in filters:
            year = filters['year']
        elif path_or_identifier.startswith(self.name + "_"): # e.g. yemen_exports_2022
            try:
                year = int(path_or_identifier.split('_')[-1])
            except ValueError:
                self.logger.error(f"Could not parse year from path_or_identifier: {path_or_identifier}")
        
        if year is None:
            raise ValueError("Year must be provided either in path_or_identifier or filters for YemenExportsLoader.")

        self.logger.info(f"YemenExportsLoader._load_from_source: Deriving data for year {year}.")
        baci_loader_kwargs = filters.get('baci_loader_kwargs', {}) if filters else {}

        try:
            self.logger.debug(f"YemenExportsLoader: Calling self.baci_loader.load(year={year})")
            # BaciLoader.load is the refactored one, which itself calls its parent's load,
            # eventually invoking _load_with_memory_optimization.
            # This will return the raw, optimized BACI data.
            raw_baci_df = self.baci_loader.load(year=year, **baci_loader_kwargs)

            if raw_baci_df is None or raw_baci_df.empty:
                self.logger.warning(f"YemenExportsLoader: Raw BACI data for year {year} is empty.")
                return pd.DataFrame()

            # Transform raw BACI data to standardized BACI format (column names, etc.)
            self.logger.debug(f"YemenExportsLoader: Calling self.baci_loader.transform on raw BACI data. Shape: {raw_baci_df.shape}")
            standardized_baci_df = self.baci_loader.transform(data=raw_baci_df, year=year)

            if standardized_baci_df is None or standardized_baci_df.empty:
                self.logger.warning(f"YemenExportsLoader: Standardized BACI data for year {year} is empty.")
                return pd.DataFrame()
            
            self.logger.info(f"YemenExportsLoader: Successfully obtained standardized BACI for year {year} in _load_from_source. Shape: {standardized_baci_df.shape}")
            return standardized_baci_df

        except Exception as e:
            self.logger.error(f"YemenExportsLoader: Error in _load_from_source for year {year}. Error: {e}", exc_info=True)
            return pd.DataFrame()


    def get_schema(self, path_or_identifier: Optional[str] = None) -> Dict[str, Any]:
        """Returns the schema for the final Yemen exports data."""
        return get_yemen_exports_schema()

    def get_available_years(self, path_or_identifier: Optional[str] = None) -> List[int]:
        """Delegates to BaciLoader to find available years, as Yemen exports depend on BACI data."""
        return self.baci_loader.get_available_years(path_or_identifier=None) 

    # --- Public load method ---
    # This is the primary method users would call.
    # It now leverages the DataLoaderBase.load() mechanism.
    @with_error_handling(category=ErrorCategory.DATA_ACCESS, severity=ErrorSeverity.ERROR)
    def load(self, year: int, **kwargs) -> pd.DataFrame:
        """
        Loads Yemen Exports data for a specific year.
        This method uses the DataLoaderBase's load mechanism, which calls 
        _load_from_source and then _transform_data (which calls self.transform).
        """
        self.logger.info(f"YemenExportsLoader.load: Requesting data for year {year}.")
        
        # We need to pass the year to _load_from_source. This can be done via path_or_identifier or filters.
        # Using filters is cleaner here as path_or_identifier is often a file path.
        # Let's use a conceptual path_or_identifier for this loader.
        conceptual_path = f"{self.name}_year_{year}" 
        
        # kwargs might include baci_loader_kwargs or other future options.
        # The `transform` and `validate` flags for `super().load` default to True.
        # If they need to be controlled, pass them from kwargs.
        load_filters = {'year': year, **kwargs.get('filters', {})}
        if 'baci_loader_kwargs' in kwargs: # Pass baci specific kwargs if any
            load_filters['baci_loader_kwargs'] = kwargs['baci_loader_kwargs']

        try:
            # DataLoaderBase.load will call:
            # 1. self._load_from_source(conceptual_path, load_filters) -> gets standardized BACI
            # 2. self._transform_data(standardized_baci_df, conceptual_path) -> which calls self.transform for Yemen filtering
            df = super().load(path_or_identifier=conceptual_path, filters=load_filters)
            return df
        except Exception as e:
            self.logger.error(f"YemenExportsLoader.load: Failed for year {year}. Error: {e}", exc_info=True)
            # Ensure an empty DataFrame is returned on failure, matching previous behavior.
            return pd.DataFrame(columns=self.get_schema().get("columns", {}).keys())


    def _transform_data(self, data: pd.DataFrame, path_or_identifier: str) -> pd.DataFrame:
        """
        This method is called by DataLoaderBase.load() after _load_from_source.
        It should perform the Yemen-specific transformations.
        It delegates to the existing `transform` method.
        The `year` parameter needs to be extracted from `path_or_identifier` or passed if available.
        """
        year = None
        # Attempt to parse year from path_or_identifier if it follows a pattern
        if path_or_identifier.startswith(self.name + "_year_"):
            try:
                year = int(path_or_identifier.split('_')[-1])
            except ValueError:
                self.logger.warning(f"Could not parse year from path_or_identifier '{path_or_identifier}' in _transform_data.")
        
        if year is None:
            # Fallback or raise error if year is crucial and not found
            # For now, let's see if self.transform can handle year=None or if it's always passed in kwargs
            self.logger.warning(f"_transform_data called without a clear year in path_or_identifier: {path_or_identifier}. "
                                 "Ensure 'year' is available to the 'transform' method if needed, possibly via filters in load().")
            # This implies that the `transform` method needs to be robust or year must be passed some other way
            # For now, we'll assume `transform` can get it from `kwargs` if needed, or it's not strictly necessary
            # for the transformation logic itself if already filtered.
            # However, the current transform signature is `transform(self, df: pd.DataFrame, year: int, ...)`
            # This means `year` IS required by `transform`.

            # To resolve this, `_transform_data` MUST get the year.
            # The year was passed in `filters` to `super().load()`.
            # However, `_transform_data` signature is `(self, data, path_or_identifier)`.
            # This is a limitation of the current `DataLoaderBase._transform_data` call.
            # A common pattern is to pass such context via `**kwargs` in the main `load` call
            # and then retrieve it in `_transform_data` or `transform` if `DataLoaderBase` passes them.
            # For now, let's assume `transform` will be robust or we adjust `DataLoaderBase` later.
            # A HACK for now: if year is not in path, it means the transform is on already loaded data
            # where year context might be implicit or handled by `transform` if it gets it from `kwargs`
            # Let's call transform with a placeholder or make it optional if possible.
            # Given transform signature `transform(self, df, year: int, ...)` year is NOT optional.
            # This points to a design consideration for DataLoaderBase or how derived loaders pass context.

            # For now, let's assume if year isn't in path_or_identifier, it's an issue.
            # The `filters` argument in `super().load()` is not directly passed to `_transform_data`.
            # This is a design flaw in the current hypothetical DataLoaderBase.
            # Given the tools, I can't change DataLoaderBase.
            # So, the `load` method should NOT call `super().load()` but do the orchestration itself.
            # Reverting the `load` method to direct orchestration as in the previous diff.
            # The `_load_from_source` defined above will be used if some external system calls `super().load()`.
            pass # Year parsing logic will be handled in the main `load` method for now.

        # The `data` here is the standardized BACI data.
        # The `year` is crucial for the `transform` method.
        # Since `load` is the main entry point, `_transform_data` might not be called directly in the intended flow.
        # The `transform` method will be called by `load`.
        # If `DataLoaderBase.load` calls this, it needs year.
        # For now, let's assume `transform` is called by `self.load` directly.
        # If this `_transform_data` is indeed called by `super().load()`, it needs a way to get `year`.
        # This function will effectively call the main `transform` method.
        
        # This is tricky. If `super().load()` calls this, `year` isn't passed.
        # The `transform` method (semantic one) requires `year`.
        # The `load` method should call `transform` directly.
        # `_transform_data` should be the one that calls `self.transform(data, year=parsed_year)`.
        if year is None:
             raise ValueError("_transform_data for YemenExportsLoader requires 'year' to be parsable from path_or_identifier or passed via filters that are accessible here.")
        return self.transform(data, year=year)


    def transform(self, df: pd.DataFrame, year: int, use_cache: bool = True, **kwargs) -> pd.DataFrame:
        """Filters the loaded (and already standardized) BACI data for Yemen's exports."""
        self.logger.info(f"YemenExportsLoader: Filtering for Yemen exports from BACI data (year {year}). Initial BACI shape: {df.shape if df is not None else 'None'}")
        if df is None or df.empty:
            self.logger.warning("YemenExportsLoader: Input DataFrame for transform is empty, cannot filter Yemen exports.")
            return pd.DataFrame(columns=[YEAR_COL, REPORTER_COL, PARTNER_COL, PRODUCT_COL, VALUE_COL, QUANTITY_COL])

        # The input df here comes from self.load(), which returns already standardized BACI data.
        # We expect REPORTER_COL to be present.
        if REPORTER_COL not in df.columns:
            self.logger.error(f"YemenExportsLoader: Critical - Standardized reporter column '{REPORTER_COL}' not found in input data. Available: {df.columns.tolist()}")
            return pd.DataFrame(columns=[YEAR_COL, REPORTER_COL, PARTNER_COL, PRODUCT_COL, VALUE_COL, QUANTITY_COL])

        yemen_exports_df = df[df[REPORTER_COL] == self.yemen_iso_numeric].copy()
        self.logger.info(f"YemenExportsLoader: Filtered Yemen exports. Shape: {yemen_exports_df.shape}")

        # Ensure all expected semantic columns are present, even if filtering resulted in an empty frame for Yemen
        expected_cols = [YEAR_COL, REPORTER_COL, PARTNER_COL, PRODUCT_COL, VALUE_COL, QUANTITY_COL]

        if not yemen_exports_df.empty:
            # Create a new DataFrame with the same index as yemen_exports_df
            final_df = pd.DataFrame(index=yemen_exports_df.index)

            # Add each column, or empty column if missing
            for col in expected_cols:
                if col in yemen_exports_df.columns:
                    # Get the values as a 1D array
                    values = yemen_exports_df[col].values
                    if hasattr(values, 'flatten') and values.ndim > 1:
                        values = values.flatten()

                    # Make sure the length matches
                    if len(values) != len(final_df.index):
                        self.logger.warning(f"YemenExportsLoader: Column '{col}' has {len(values)} values but index has {len(final_df.index)} entries. Truncating.")
                        values = values[:len(final_df.index)]

                    final_df[col] = values
                else:
                    self.logger.warning(f"YemenExportsLoader: Expected column '{col}' missing after filtering, adding as empty.")
                    final_df[col] = pd.NA  # Or appropriate type like np.nan for numeric, None for object

            return final_df[expected_cols]
        else:
            self.logger.info("YemenExportsLoader: No export data found for Yemen after filtering.")
            # Return empty DataFrame with correct columns
            return pd.DataFrame(columns=expected_cols)

    def validate_data(self, df: pd.DataFrame, **kwargs) -> bool:
        """Validates the transformed Yemen export data."""
        if df is None:
            self.logger.warning("Validation skipped for None DataFrame in YemenExportsLoader")
            return False # Or True if None is acceptable post-transform but results in no data
        self.logger.info(f"YemenExportsLoader: Validating Yemen exports data. Shape: {df.shape}")
        # Add specific validation for Yemen exports if needed (e.g., only one reporter_code==YEMEN_ISO)
        # For now, rely on schema validation from base or specific schema if defined
        if df.empty:
            self.logger.info("YemenExportsLoader: Data is empty but considered valid as no specific rows were expected after filtering.")
            return True

        # Example: Check if all reporter_code are indeed Yemen
        if REPORTER_COL in df.columns and not df[df[REPORTER_COL] != self.yemen_iso_numeric].empty:
            self.logger.error(f"Validation Error: Found non-Yemen reporter codes in YemenExports data: {df[df[REPORTER_COL] != self.yemen_iso_numeric][REPORTER_COL].unique()}")
            return False
        return True
