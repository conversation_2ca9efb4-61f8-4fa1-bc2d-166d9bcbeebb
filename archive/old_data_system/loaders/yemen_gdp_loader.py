"""
Specialized loader for Yemen GDP data from the yemen_worldbank_data.csv file.
This loader is designed to be a direct drop-in replacement for the GdpPerCapitaLoader
when focusing only on Yemen data.
"""

# Standard library imports
from pathlib import Path
from typing import Any, Dict, Optional

# Third-party imports
import pandas as pd

# Project imports
from yemen_trade_diagnostic.data.loader_registry import register_loader

# Import the base class
from yemen_trade_diagnostic.data.memory_optimized_loader_base import MemoryOptimizedDataLoader
from yemen_trade_diagnostic.data.schema_management import (
    ValidationMode,
    get_gdp_schema,
    validate_schema,
)
from yemen_trade_diagnostic.interfaces.cache_interface import (
    CacheLevel,
    CachePriority,
    DataLifetime,
    cache_get_or_compute,
)
from yemen_trade_diagnostic.interfaces.error_interface import (
    ErrorCategory,
    ErrorSeverity,
    with_error_handling,
)

# Import interfaces
from yemen_trade_diagnostic.interfaces.logging_interface import get_logger

# Import from yemen_trade_diagnostic.utils
from yemen_trade_diagnostic.utils.config import get_processed_data_dir


@register_loader("gdp_per_capita")  # Register as gdp_per_capita to act as replacement
class YemenGdpLoader(MemoryOptimizedDataLoader):
    """
    Loads Yemen GDP data directly from the yemen_worldbank_data.csv file.
    This replaces the standard GdpPerCapitaLoader to ensure Yemen's GDP data
    is correctly loaded with proper ISO codes.
    """

    def __init__(self, source_name: str = "gdp_per_capita", config: Dict[str, Any] = None):
        super().__init__(source_name, config)
        self.logger = get_logger(__name__ + ".YemenGdpLoader")
        self.file_name = "yemen_worldbank_data.csv"  # Fixed filename for Yemen GDP data

    def _get_file_path(self, **kwargs) -> Path:
        """Get the path to the yemen_worldbank_data.csv file."""
        processed_data_dir = get_processed_data_dir()
        return processed_data_dir / self.file_name

    @with_error_handling(
        category=ErrorCategory.DATA_ACCESS,
        severity=ErrorSeverity.WARNING
    )
    def load(self, year: Optional[int] = None, use_cache: bool = True, **kwargs) -> pd.DataFrame:
        """
        Load Yemen GDP data from the dedicated file.

        Args:
            year: Optional year to filter (applied during transform)
            use_cache: Whether to use cache
            **kwargs: Additional arguments

        Returns:
            DataFrame containing Yemen GDP data
        """
        self.logger.debug(f"YemenGdpLoader: Loading Yemen GDP data (year context: {year})")

        cache_key = f"yemen_gdp_data_{year if year else 'all'}"
        ttl_seconds = self.config.get("cache_ttl_gdp_data", 24 * 60 * 60)  # 1 day by default

        def _load_yemen_gdp_file():
            file_path = self._get_file_path(**kwargs)
            if not file_path.exists():
                self.logger.warning(f"YemenGdpLoader: File does not exist: {file_path}")
                return pd.DataFrame()

            try:
                df = pd.read_csv(file_path)
                self.logger.info(f"YemenGdpLoader: Successfully loaded data from {file_path}. Shape: {df.shape}")
                return df
            except Exception as e:
                self.logger.error(f"YemenGdpLoader: Failed to load data from {file_path}: {e}")
                return pd.DataFrame()

        if use_cache:
            df = cache_get_or_compute(
                key=cache_key,
                compute_func=_load_yemen_gdp_file,
                ttl=ttl_seconds,
                level=CacheLevel.MEMORY,  # Keep in memory as it's small
                lifetime=DataLifetime.SESSION,
                priority=CachePriority.HIGH  # GDP data is important reference data
            )
        else:
            df = _load_yemen_gdp_file()

        return df

    def transform(self, df: pd.DataFrame, year: Optional[int] = None, **kwargs) -> pd.DataFrame:
        """
        Transform the Yemen GDP data to match expected format.

        Args:
            df: The loaded DataFrame
            year: Optional year to filter
            **kwargs: Additional arguments

        Returns:
            Transformed DataFrame with standardized columns
        """
        if df.empty:
            self.logger.warning("YemenGdpLoader: Input DataFrame is empty. Returning empty result.")
            return pd.DataFrame(columns=['exporter_iso', 'year', 'gdp_per_capita', 'gdp', 'country_code'])

        # Make a copy to avoid modifying the original
        result_df = df.copy()

        # Convert data types
        if 'year' in result_df.columns:
            result_df['year'] = pd.to_numeric(result_df['year'], errors='coerce').fillna(0).astype('Int64')

        if 'gdp_per_capita' in result_df.columns:
            result_df['gdp_per_capita'] = pd.to_numeric(result_df['gdp_per_capita'], errors='coerce')

        if 'gdp' in result_df.columns:
            result_df['gdp'] = pd.to_numeric(result_df['gdp'], errors='coerce')

        if 'exporter_iso' in result_df.columns:
            result_df['exporter_iso'] = pd.to_numeric(result_df['exporter_iso'], errors='coerce').fillna(887).astype('Int64')

        # Filter by year if specified
        if year is not None and 'year' in result_df.columns:
            self.logger.info(f"YemenGdpLoader: Filtering data for year {year}")
            result_df = result_df[result_df['year'] == year]
            if result_df.empty:
                self.logger.warning(f"YemenGdpLoader: No data found for year {year}")

        # Debug log
        if not result_df.empty:
            self.logger.info(f"YemenGdpLoader: Found data for year(s): {result_df['year'].tolist()}")
            if 'gdp' in result_df.columns:
                self.logger.info(f"YemenGdpLoader: GDP values: {result_df['gdp'].tolist()}")

        # Ensure all required columns exist
        required_cols = ['exporter_iso', 'year', 'gdp_per_capita', 'gdp', 'country_code']
        for col in required_cols:
            if col not in result_df.columns:
                if col == 'exporter_iso':
                    result_df[col] = 887  # Yemen's numeric ISO code
                elif col == 'country_code':
                    result_df[col] = 'YEM'  # Yemen's ISO3 code
                else:
                    result_df[col] = None

        # Return all columns including gdp
        return result_df[required_cols]

    def validate_data(self, df: pd.DataFrame, year: Optional[int] = None, **kwargs) -> bool:
        """
        Validate the transformed Yemen GDP data.

        Args:
            df: The transformed DataFrame
            year: Optional year context
            **kwargs: Additional arguments

        Returns:
            True if validation passes, False otherwise
        """
        if df.empty:
            self.logger.warning(f"YemenGdpLoader: No data available for validation (year: {year})")
            return True  # Consider empty as valid for this special case

        # Basic validation
        required_cols = ['exporter_iso', 'year', 'gdp_per_capita']
        if not all(col in df.columns for col in required_cols):
            missing = [col for col in required_cols if col not in df.columns]
            self.logger.error(f"YemenGdpLoader: Missing required columns: {missing}")
            return False

        # More specific validation can be added here
        self.logger.info(f"YemenGdpLoader: Validation passed for data with shape {df.shape}")
        return True