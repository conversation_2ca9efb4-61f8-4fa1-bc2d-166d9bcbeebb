"""
World Bank Data Loader

This module implements a data loader for World Bank data, including indicators
such as GDP, population, and other socioeconomic statistics.
"""

# Standard library imports
import json
import os
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union

# Third-party imports
import numpy as np
import pandas as pd

# Project imports
# Import base classes and interfaces
from yemen_trade_diagnostic.data.loader_base import DataLoaderBase, HardwareAcceleratedLoader
from yemen_trade_diagnostic.data.loader_registry import register_loader
from yemen_trade_diagnostic.interfaces.error_interface import (
    Error<PERSON>ategory,
    ErrorSeverity,
    RetryStrategy,
    with_circuit_breaker,
    with_error_handling,
    with_retry,
)


# Custom error for data loading
class DataLoadError(Exception):
    """Exception raised when data loading fails."""
    pass
# Project imports
from yemen_trade_diagnostic.interfaces.cache_interface import (
    CacheLevel,
    CachePriority,
    DataLifetime,
    cache_get_or_compute,
)
from yemen_trade_diagnostic.interfaces.logging_interface import get_logger, log_execution_time
from yemen_trade_diagnostic.interfaces.validation_interface import (
    ValidationIssueLevel,
    ValidationMode,
    ValidationResult,
)

# Define constants
WORLDBANK_FILE_NAME = "worldbank_data_all_countries.csv"

# Define column mappings
WORLDBANK_TO_SEMANTIC_COLUMN_MAPPING = {
    'Country Name': 'country_name',
    'CountryName': 'country_name',
    'country': 'country_name',
    'Country': 'country_name',
    
    'Country Code': 'country_code',
    'CountryCode': 'country_code',
    'country_id': 'country_code',
    'iso3': 'country_code',
    
    'Indicator Name': 'indicator_name',
    'IndicatorName': 'indicator_name',
    'indicator': 'indicator_name',
    'Indicator': 'indicator_name',
    
    'Indicator Code': 'indicator_code',
    'IndicatorCode': 'indicator_code',
    'indicator_id': 'indicator_code',
    
    'Year': 'year',
    'year': 'year',
    'date': 'year',
    'Date': 'year',
    
    'Value': 'value',
    'value': 'value',
    'Val': 'value',
    'Amount': 'value',
    'amount': 'value'
}

SEMANTIC_TO_WORLDBANK_COLUMN_MAPPING = {
    'country_name': 'Country Name',
    'country_code': 'Country Code',
    'indicator_name': 'Indicator Name',
    'indicator_code': 'Indicator Code',
    'year': 'Year',
    'value': 'Value'
}


@register_loader('worldbank')
class WorldBankLoader(HardwareAcceleratedLoader):
    """
    Loader for World Bank data.
    
    This loader handles the loading, transformation, and validation of World Bank
    data, which contains various indicators for countries over time.
    """
    
    def __init__(self, source_name: str = 'worldbank', config: Optional[Dict[str, Any]] = None, **kwargs):
        """
        Initialize the World Bank data loader.
        
        Args:
            source_name: Identifier for the data source (should be 'worldbank')
            config: Configuration parameters for the loader
            **kwargs: Additional keyword arguments for compatibility
        """
        # Merge config with kwargs, with config taking precedence
        merged_config = {**kwargs, **(config or {})}
        super().__init__(source_name, merged_config)
        
        # Set default file name
        self.file_name = self.config.get('worldbank_file_name', WORLDBANK_FILE_NAME)
        
        # Set circuit breaker names
        self.cb_name = self.config.get('circuit_breaker_name', 'worldbank_source_v2')
        
        # Load schema
        self.schema = self._load_schema()
    
    def _load_schema(self) -> Dict[str, Any]:
        """
        Load the World Bank data schema from the schema file.
        
        Returns:
            Dictionary containing the schema definition
        """
        try:
            schema_path = Path(self.config.get('schema_path', 'config/schemas/worldbank_schema.json'))
            
            if not schema_path.exists():
                self.logger.warning(f"World Bank schema file not found at {schema_path}. Using default schema.")
                return {
                    "name": "worldbank_data",
                    "description": "Schema for World Bank data",
                    "required_columns": [
                        "country_name", "country_code", "indicator_name", 
                        "indicator_code", "year", "value"
                    ],
                    "column_types": {
                        "country_name": "string",
                        "country_code": "string",
                        "indicator_name": "string",
                        "indicator_code": "string",
                        "year": "integer",
                        "value": "numeric"
                    },
                    "value_ranges": {
                        "year": {"min": 1960, "max": 2030}
                    }
                }
            
            with open(schema_path, 'r') as f:
                return json.load(f)
        
        except Exception as e:
            self.logger.warning(f"Failed to load World Bank schema: {e}. Using default schema.")
            return {
                "name": "worldbank_data",
                "description": "Schema for World Bank data",
                "required_columns": [
                    "country_name", "country_code", "indicator_name", 
                    "indicator_code", "year", "value"
                ],
                "column_types": {
                    "country_name": "string",
                    "country_code": "string",
                    "indicator_name": "string",
                    "indicator_code": "string",
                    "year": "integer",
                    "value": "numeric"
                },
                "value_ranges": {
                    "year": {"min": 1960, "max": 2030}
                }
            }
    
    def get_file_path(self, **kwargs) -> Path:
        """
        Get the path to the World Bank data file.
        
        Args:
            **kwargs: Additional parameters for file path construction
            
        Returns:
            Path to the World Bank data file
        """
        # Get the processed data directory
        processed_data_dir = Path(self.config.get('processed_data_dir', self.processed_data_dir))
        
        # Construct the full path
        file_path = processed_data_dir / self.file_name
        
        return file_path
    
    @with_error_handling(category=ErrorCategory.DATA_ACCESS, severity=ErrorSeverity.ERROR)
    @with_circuit_breaker(circuit_breaker_name="worldbank_source_v2")
    @with_retry(retry_count=3, 
               retry_delay=1.0, 
               strategy=RetryStrategy.EXPONENTIAL_BACKOFF,
               exception_types=(FileNotFoundError, IOError, pd.errors.EmptyDataError))
    @log_execution_time(message="{func_name} executed in {elapsed}s")
    def load(self, use_cache: bool = True, **kwargs) -> pd.DataFrame:
        """
        Load World Bank data.
        
        Args:
            use_cache: Whether to use cache for data loading
            **kwargs: Additional parameters for loading
            
        Returns:
            DataFrame containing raw World Bank data
            
        Raises:
            DataLoadError: If loading fails
        """
        # Get the file path
        file_path = self.get_file_path(**kwargs)
        
        # Log the operation
        self.logger.info(f"Loading World Bank data from {file_path}")
        
        # Define a function to load from source
        def _load_from_source(file_path: Path) -> pd.DataFrame:
            # Check if the file exists
            if not file_path.exists():
                # Handle missing file gracefully
                self.logger.warning(f"World Bank data file not found at {file_path}")
                
                # Return empty DataFrame with correct column structure
                return pd.DataFrame(columns=self.schema['required_columns'])
            
            try:
                # Load the CSV file
                df = self.load_csv(
                    file_path, 
                    low_memory=False,
                    dtype={
                        'year': 'Int64',
                        'value': 'float64',
                        'country_code': 'str',
                        'indicator_code': 'str'
                    }
                )
                
                # Log success
                self.logger.info(f"Successfully loaded World Bank data. Shape: {df.shape}")
                
                return df
                
            except Exception as e:
                # Handle loading errors
                error_msg = f"Failed to load World Bank data from {file_path}: {str(e)}"
                self.logger.error(error_msg, exc_info=True)
                
                # Raise a DataLoadError
                raise DataLoadError(error_msg) from e
        
        # Use caching if requested
        if use_cache:
            cache_key = f"worldbank_raw_data_{file_path.stem}"
            ttl_seconds = self.config.get('cache_ttl_raw_worldbank', 7 * 24 * 60 * 60)  # 7 days
            
            return cache_get_or_compute(
                key=cache_key,
                compute_func=lambda: _load_from_source(file_path),
                ttl=ttl_seconds,
                level=CacheLevel.DISK,
                lifetime=DataLifetime.PERSISTENT,
                priority=CachePriority.HIGH  # Reference data, high priority
            )
        else:
            # Load directly without cache
            return _load_from_source(file_path)
    
    @with_error_handling(category=ErrorCategory.DATA_ACCESS, severity=ErrorSeverity.ERROR)
    def transform(self, data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """
        Transform raw World Bank data to a standardized format.
        
        Args:
            data: Raw World Bank DataFrame
            **kwargs: Additional parameters for transformation
            
        Returns:
            Transformed DataFrame
            
        Raises:
            DataLoadError: If transformation fails
        """
        # Skip empty DataFrames
        if data is None or data.empty:
            self.logger.warning("No data to transform")
            return data
        
        try:
            # Make a copy to avoid modifying the original
            df = data.copy()
            
            # Standardize column names
            rename_map = {}
            for orig_col, target_col in WORLDBANK_TO_SEMANTIC_COLUMN_MAPPING.items():
                if orig_col in df.columns and target_col not in df.columns:
                    rename_map[orig_col] = target_col
            
            if rename_map:
                self.logger.info(f"Renaming columns: {rename_map}")
                df = df.rename(columns=rename_map)
            
            # Ensure all required columns are present
            required_cols = self.schema['required_columns']
            
            # Check for missing columns
            missing_cols = [col for col in required_cols if col not in df.columns]
            if missing_cols:
                self.logger.warning(f"Missing columns: {missing_cols}. Creating with default values.")
                
                # Create missing columns with default values
                for col in missing_cols:
                    if col == 'country_code':
                        df[col] = 'ZZZ'  # Default country code
                    elif col == 'country_name':
                        df[col] = 'Unknown'
                    elif col == 'indicator_name':
                        df[col] = 'Unknown Indicator'
                    elif col == 'indicator_code':
                        df[col] = 'UNKNOWN'
                    elif col == 'year':
                        # Standard library imports
                        from datetime import datetime
                        df[col] = datetime.now().year  # Current year
                    elif col == 'value':
                        df[col] = 0.0  # Default value
                    else:
                        df[col] = None
            
            # Ensure correct data types
            if 'year' in df.columns:
                df['year'] = pd.to_numeric(df['year'], errors='coerce').astype('Int64')
            
            if 'value' in df.columns:
                df['value'] = pd.to_numeric(df['value'], errors='coerce')
            
            if 'country_code' in df.columns:
                df['country_code'] = df['country_code'].astype(str)
            
            if 'indicator_code' in df.columns:
                df['indicator_code'] = df['indicator_code'].astype(str)
            
            # Handle duplicate records
            duplicate_cols = ['country_code', 'indicator_code', 'year']
            if all(col in df.columns for col in duplicate_cols):
                dupe_count = df.duplicated(subset=duplicate_cols, keep=False).sum()
                
                if dupe_count > 0:
                    self.logger.warning(
                        f"Found {dupe_count} duplicate country/indicator/year combinations. "
                        "Keeping first occurrence."
                    )
                    
                    df = df.drop_duplicates(subset=duplicate_cols, keep='first')
            
            # Log transformation results
            self.logger.info(f"Transformed World Bank data. Final shape: {df.shape}")
            
            return df
            
        except Exception as e:
            # Handle transformation errors
            error_msg = f"Failed to transform World Bank data: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            
            # Raise a DataLoadError
            raise DataLoadError(error_msg) from e
    
    @with_error_handling(category=ErrorCategory.VALIDATION, severity=ErrorSeverity.ERROR)
    def validate(self, data: pd.DataFrame, **kwargs) -> ValidationResult:
        """
        Validate the transformed World Bank data.
        
        Args:
            data: Transformed World Bank DataFrame
            **kwargs: Additional parameters for validation
            
        Returns:
            ValidationResult object
            
        Raises:
            DataValidationError: If validation fails
        """
        # Create a validation result
        result = ValidationResult()
        
        # Skip empty DataFrames
        if data is None or data.empty:
            self.logger.warning("No data to validate")
            return result
        
        try:
            # Check for required columns
            missing_columns = [col for col in self.schema['required_columns'] if col not in data.columns]
            if missing_columns:
                result.add_issue(
                    f"Missing required columns: {', '.join(missing_columns)}",
                    ValidationIssueLevel.ERROR
                )
                return result
            
            # Check data types
            for col, expected_type in self.schema['column_types'].items():
                if col not in data.columns:
                    continue
                
                # Adapt expected type to pandas dtype names
                if expected_type == 'integer':
                    valid = pd.api.types.is_integer_dtype(data[col])
                elif expected_type == 'numeric':
                    valid = pd.api.types.is_numeric_dtype(data[col])
                elif expected_type == 'string':
                    valid = pd.api.types.is_string_dtype(data[col]) or pd.api.types.is_object_dtype(data[col])
                else:
                    valid = True  # Unknown type, assume valid
                
                if not valid:
                    result.add_issue(
                        f"Column '{col}' has wrong type: expected {expected_type}, got {data[col].dtype}",
                        ValidationIssueLevel.WARNING,
                        location=col
                    )
            
            # Check value ranges
            for col, range_def in self.schema.get('value_ranges', {}).items():
                if col not in data.columns:
                    continue
                
                if not pd.api.types.is_numeric_dtype(data[col]):
                    continue
                
                # Check minimum value
                if 'min' in range_def:
                    min_val = range_def['min']
                    below_min = (data[col] < min_val)
                    
                    # Handle nullable types
                    if pd.api.types.is_nullable_dtype(data[col]):
                        below_min = below_min.fillna(False)
                    
                    below_min_count = below_min.sum()
                    
                    if below_min_count > 0:
                        result.add_issue(
                            f"Column '{col}' has {below_min_count} values below minimum {min_val}",
                            ValidationIssueLevel.WARNING,
                            location=col
                        )
                
                # Check maximum value
                if 'max' in range_def:
                    max_val = range_def['max']
                    above_max = (data[col] > max_val)
                    
                    # Handle nullable types
                    if pd.api.types.is_nullable_dtype(data[col]):
                        above_max = above_max.fillna(False)
                    
                    above_max_count = above_max.sum()
                    
                    if above_max_count > 0:
                        result.add_issue(
                            f"Column '{col}' has {above_max_count} values above maximum {max_val}",
                            ValidationIssueLevel.WARNING,
                            location=col
                        )
            
            # Check for missing values in key columns
            for col in ['country_code', 'indicator_code', 'year']:
                if col not in data.columns:
                    continue
                
                missing_count = data[col].isna().sum()
                if missing_count > 0:
                    result.add_issue(
                        f"Column '{col}' has {missing_count} missing values",
                        ValidationIssueLevel.WARNING,
                        location=col
                    )
            
            # Check for duplicate combinations
            key_cols = ['country_code', 'indicator_code', 'year']
            if all(col in data.columns for col in key_cols):
                duplicate_count = data.duplicated(subset=key_cols, keep=False).sum()
                if duplicate_count > 0:
                    result.add_issue(
                        f"Found {duplicate_count} duplicate country/indicator/year combinations",
                        ValidationIssueLevel.WARNING
                    )
            
            # Log validation results
            if result.has_issues:
                if result.is_valid:
                    self.logger.warning(f"Validation completed with {result.warning_count()} warnings")
                else:
                    self.logger.error(f"Validation failed with {result.error_count()} errors and {result.warning_count()} warnings")
            else:
                self.logger.info("Validation completed successfully with no issues")
            
            return result
            
        except Exception as e:
            # Handle validation errors
            error_msg = f"Failed to validate World Bank data: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            
            # Add the error to the validation result
            result.add_issue(
                f"Validation error: {str(e)}",
                ValidationIssueLevel.ERROR
            )
            
            return result
    
    @log_execution_time(message="{func_name} executed in {elapsed}s")
    def get_indicator_data(self, data: pd.DataFrame, indicator_code: str) -> pd.DataFrame:
        """
        Extract data for a specific indicator.
        
        Args:
            data: World Bank DataFrame
            indicator_code: Indicator code to extract
            
        Returns:
            DataFrame with data for the specified indicator
        """
        if data is None or data.empty:
            return pd.DataFrame(columns=self.schema['required_columns'])
        
        # Filter by indicator code
        filtered_data = data[data['indicator_code'] == indicator_code]
        
        self.logger.info(f"Extracted data for indicator '{indicator_code}'. Shape: {filtered_data.shape}")
        
        return filtered_data
    
    @log_execution_time(message="{func_name} executed in {elapsed}s")
    def get_country_data(self, data: pd.DataFrame, country_code: str) -> pd.DataFrame:
        """
        Extract data for a specific country.
        
        Args:
            data: World Bank DataFrame
            country_code: Country code to extract
            
        Returns:
            DataFrame with data for the specified country
        """
        if data is None or data.empty:
            return pd.DataFrame(columns=self.schema['required_columns'])
        
        # Filter by country code
        filtered_data = data[data['country_code'] == country_code]
        
        self.logger.info(f"Extracted data for country '{country_code}'. Shape: {filtered_data.shape}")
        
        return filtered_data
    
    @log_execution_time(message="{func_name} executed in {elapsed}s")
    def get_year_data(self, data: pd.DataFrame, year: int) -> pd.DataFrame:
        """
        Extract data for a specific year.
        
        Args:
            data: World Bank DataFrame
            year: Year to extract
            
        Returns:
            DataFrame with data for the specified year
        """
        if data is None or data.empty:
            return pd.DataFrame(columns=self.schema['required_columns'])
        
        # Filter by year
        filtered_data = data[data['year'] == year]
        
        self.logger.info(f"Extracted data for year {year}. Shape: {filtered_data.shape}")
        
        return filtered_data
    
    @log_execution_time(message="{func_name} executed in {elapsed}s")
    def get_time_series(self, data: pd.DataFrame, country_code: str, 
                       indicator_code: str) -> pd.DataFrame:
        """
        Extract a time series for a specific country and indicator.
        
        Args:
            data: World Bank DataFrame
            country_code: Country code to extract
            indicator_code: Indicator code to extract
            
        Returns:
            DataFrame with time series data
        """
        if data is None or data.empty:
            return pd.DataFrame(columns=self.schema['required_columns'])
        
        # Filter by country and indicator
        filtered_data = data[
            (data['country_code'] == country_code) & 
            (data['indicator_code'] == indicator_code)
        ]
        
        # Sort by year
        filtered_data = filtered_data.sort_values('year')
        
        self.logger.info(
            f"Extracted time series for country '{country_code}' and indicator '{indicator_code}'. "
            f"Shape: {filtered_data.shape}"
        )
        
        return filtered_data
    
    def get_available_indicators(self, data: pd.DataFrame) -> List[Tuple[str, str]]:
        """
        Get a list of available indicators in the data.
        
        Args:
            data: World Bank DataFrame
            
        Returns:
            List of (indicator_code, indicator_name) tuples
        """
        if data is None or data.empty:
            return []
        
        # Get unique indicators
        indicators = data[['indicator_code', 'indicator_name']].drop_duplicates()
        
        return list(indicators.itertuples(index=False, name=None))
    
    def get_available_countries(self, data: pd.DataFrame) -> List[Tuple[str, str]]:
        """
        Get a list of available countries in the data.
        
        Args:
            data: World Bank DataFrame
            
        Returns:
            List of (country_code, country_name) tuples
        """
        if data is None or data.empty:
            return []
        
        # Get unique countries
        countries = data[['country_code', 'country_name']].drop_duplicates()
        
        return list(countries.itertuples(index=False, name=None))
    
    def get_available_years(self, data: pd.DataFrame) -> List[int]:
        """
        Get a list of available years in the data.
        
        Args:
            data: World Bank DataFrame
            
        Returns:
            List of years
        """
        if data is None or data.empty:
            return []
        
        # Get unique years
        years = sorted(data['year'].dropna().unique().tolist())
        
        return years