"""
BACI Trade Data Loader

This module implements a data loader for BACI (Base pour l'Analyse du Commerce International)
trade data. BACI is an international trade database developed by CEPII, providing
harmonized bilateral trade flows at the product level.
"""

# Standard library imports
import json
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

# Third-party imports
import numpy as np  # Keep for any specific numpy usage if present
import pandas as pd  # Keep for DataFrame operations in transform etc.

# Project imports
# Import base classes and interfaces
from yemen_trade_diagnostic.data.loader_base import (  # Changed base class
    DataSource,
    MemoryOptimizedFileLoader,
)
from yemen_trade_diagnostic.data.loader_registry import register_loader
from yemen_trade_diagnostic.data.validation import DataValidationManager
from yemen_trade_diagnostic.interfaces.error_interface import (
    ErrorCategory,
    ErrorSeverity,
    RetryStrategy,
    with_error_handling,
    with_retry,
)
from yemen_trade_diagnostic.interfaces.logging_interface import get_logger, log_execution_time
from yemen_trade_diagnostic.interfaces.validation_interface import (
    ValidationIssueLevel,
    ValidationMode,
    ValidationResult,
)


# Add custom exception for data loading failures
class DataLoadError(Exception):
    """Custom exception for data loading failures."""
    pass

DEFAULT_BACI_DTYPE_SPEC = {
    't': 'category',  # Year
    'i': 'category',  # Exporter code
    'j': 'category',  # Importer code
    'k': 'category',  # Product code
    'v': 'float32',   # Trade value (USD)
    'q': 'float32'    # Quantity
}

# Define column mappings from raw BACI data to semantic format
BACI_TO_SEMANTIC_COLUMN_MAPPING = {
    't': 'year',
    'i': 'exporter_code',
    'j': 'importer_code',
    'k': 'product_code',
    'v': 'trade_value_usd',
    'q': 'quantity'
}

SEMANTIC_TO_BACI_COLUMN_MAPPING = {v: k for k, v in BACI_TO_SEMANTIC_COLUMN_MAPPING.items()}


@register_loader('baci')
class BaciLoader(MemoryOptimizedFileLoader):
    """
    Loader for BACI international trade data.
    
    This loader handles the loading, transformation, and validation of BACI trade data,
    which contains bilateral trade flows at the product level.
    """
    
    def __init__(self, source_name: str = 'baci', config: Optional[Dict[str, Any]] = None, **kwargs):
        """
        Initialize the BACI data loader.
        
        Args:
            source_name: Identifier for the data source (should be 'baci')
            config: Configuration parameters for the loader
            **kwargs: Additional keyword arguments for compatibility
        """
        # Merge config with kwargs, with config taking precedence
        self.config = {**kwargs, **(config or {})}
        
        # Prepare memory_options for MemoryOptimizedFileLoader
        # Start with any memory_options provided in the config
        baci_memory_options = self.config.get('memory_options', {}).copy()
        
        # Ensure dtype_spec is set, defaulting to BACI specific dtypes
        baci_memory_options['dtype_spec'] = baci_memory_options.get('dtype_spec', DEFAULT_BACI_DTYPE_SPEC)
        
        # Ensure chunksize is set (can be overridden by config's memory_options)
        # MemoryOptimizedFileLoader has its own default chunksize, but we can set one here if BACI needs a specific default.
        # For now, let MemoryOptimizedFileLoader's default apply if not in config.
        if 'chunksize' not in baci_memory_options and self.config.get('use_chunked_loading', True): # Consider old config flag
             baci_memory_options['chunksize'] = self.config.get('chunk_size', 100000) # from old BaciLoader.load

        # Pass through other relevant options if they exist in the main config or memory_options
        # These would be understood by MemoryOptimizedFileLoader or optimize_dataframe
        for opt_key in ['use_hardware_pool', 'category_threshold', 'sparse_threshold']:
            if opt_key in self.config and opt_key not in baci_memory_options: # If in main config but not already in mem_opts
                baci_memory_options[opt_key] = self.config[opt_key]
            # If already in baci_memory_options (from config's memory_options), it takes precedence.

        # Initialize parent MemoryOptimizedFileLoader
        super().__init__(
            name=source_name or "baci",
            source_type=DataSource.BACI,
            base_dir=self.config.get('base_dir'), # Use self.config now
            memory_options=baci_memory_options # Pass the prepared memory_options
        )
        
        # Set default file name pattern
        self.file_pattern = self.config.get('file_pattern', 'BACI_HS02_Y{year}_V202501.csv')
        
        # Load schema
        self.schema = self._load_schema()
    
    def _load_schema(self) -> Dict[str, Any]:
        """
        Load the BACI data schema from the schema file.
        
        Returns:
            Dictionary containing the schema definition
        """
        try:
            schema_path = Path(self.config.get('schema_path', 'config/schemas/baci_schema.json'))
            
            if not schema_path.exists():
                self.logger.warning(f"BACI schema file not found at {schema_path}. Using default schema.")
                return {
                    "name": "baci_data",
                    "description": "Schema for BACI trade data",
                    "required_columns": ["t", "i", "j", "k", "v", "q"],
                    "column_types": {
                        "t": "integer",
                        "i": "integer",
                        "j": "integer",
                        "k": "integer",
                        "v": "numeric",
                        "q": "numeric"
                    },
                    "value_ranges": {
                        "t": {"min": 1995, "max": 2025},
                        "v": {"min": 0},
                        "q": {"min": 0}
                    }
                }
            
            with open(schema_path, 'r') as f:
                return json.load(f)
        
        except Exception as e:
            self.logger.warning(f"Failed to load BACI schema: {e}. Using default schema.")
            return {
                "name": "baci_data",
                "description": "Schema for BACI trade data",
                "required_columns": ["t", "i", "j", "k", "v", "q"],
                "column_types": {
                    "t": "integer",
                    "i": "integer",
                    "j": "integer",
                    "k": "integer",
                    "v": "numeric",
                    "q": "numeric"
                },
                "value_ranges": {
                    "t": {"min": 1995, "max": 2025},
                    "v": {"min": 0},
                    "q": {"min": 0}
                }
            }
    
    def get_file_path(self, year: int, **kwargs) -> Path:
        """
        Get the path to the BACI data file for a specific year.
        
        Args:
            year: Year of the trade data
            **kwargs: Additional parameters for file path construction
            
        Returns:
            Path to the BACI data file
        """
        # Get the raw data directory
        raw_data_dir = Path(self.config.get('raw_data_dir', self.base_dir or 'data/raw'))
        
        # Construct the file name
        file_name = self.file_pattern.format(year=year)
        
        # Construct the full path
        file_path = raw_data_dir / 'baci' / file_name
        
        return file_path
    
    @with_error_handling(category=ErrorCategory.DATA_ACCESS, severity=ErrorSeverity.ERROR)
    @with_retry(retry_count=3, 
               retry_delay=1.0, 
               strategy=RetryStrategy.EXPONENTIAL_BACKOFF,
               exception_types=(FileNotFoundError, IOError)) # Removed pd.errors.EmptyDataError as super().load should handle it
    @log_execution_time(message="{func_name} for year {kwargs[year]} executed in {elapsed}s")
    def load(self, year: int, **kwargs) -> pd.DataFrame: # kwargs are kept for compatibility but largely unused now
        """
        Load BACI trade data for a specific year using MemoryOptimizedFileLoader.
        
        Args:
            year: Year of the trade data
            **kwargs: Kept for API compatibility, but most options (chunksize, dtypes)
                      are now set via memory_options in __init__.
                      'filters' can be passed via kwargs if super().load is extended for it.
            
        Returns:
            DataFrame containing raw BACI data (as MemoryOptimizedFileLoader does not apply BACI-specific transforms).
            The actual BACI transformation (renaming columns, etc.) happens in BaciLoader's `transform` method,
            which is called by DataLoaderBase's `load` if `transform=True`.
            
        Raises:
            DataLoadError: If loading fails
        """
        file_path = self.get_file_path(year=year)
        self.logger.info(f"Attempting to load BACI data for year {year} from {file_path} via MemoryOptimizedFileLoader.")

        if not file_path.exists():
            self.logger.warning(f"BACI data file not found for year {year} at {file_path}. Returning empty DataFrame.")
            # Return empty DataFrame with raw BACI column names (matching the schema before transformation)
            return pd.DataFrame(columns=self.schema.get('required_columns', DEFAULT_BACI_DTYPE_SPEC.keys()))
        
        try:
            # Delegate to MemoryOptimizedFileLoader.load (which is DataLoaderBase.load)
            # memory_options (dtypes, chunking, hardware_pool, etc.) are already set in self.memory_options
            # and will be used by self._load_with_memory_optimization internally.
            # The `transform=True` default in `super().load` will call `self._transform_data`,
            # which for BaciLoader will call its `transform` method.
            # The `validate=True` default will call `self._validate_data`.
            
            # The `filters` argument in `super().load` can be used if BaciLoader needs to pass them.
            # Currently, BaciLoader.load itself doesn't receive `filters`.
            # If `kwargs` contains `filters`, it would be passed here.
            passed_filters = kwargs.get('filters')

            df = super().load(path_or_identifier=str(file_path), filters=passed_filters)
            
            # The specific hw_manager call previously here is now commented out,
            # as MemoryOptimizedFileLoader handles hardware acceleration aspects
            # (both memory pooling and operational acceleration via hw_manager).
            # TODO: Final check if hw_manager.optimize_dataframe_memory provided any unique BACI-specific optimization
            # not covered by the generic optimize_dataframe or hardware pooling in the parent.

            self.logger.info(f"Successfully loaded BACI data for year {year} via MemoryOptimizedFileLoader. Shape: {df.shape}, Memory: {df.memory_usage(deep=True).sum() / 1024**2:.2f} MB")
            return df
            
        except DataLoadError: # Re-raise DataLoadError if it's already that type
            raise
        except Exception as e:
            error_msg = f"Failed to load BACI data for year {year} from {file_path} using MemoryOptimizedFileLoader: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            raise DataLoadError(error_msg) from e
    
    # Note: The _transform_data method in DataLoaderBase calls self.transform.
    # So, BaciLoader.transform will be called if transform=True in super().load().
    # This is where BACI-specific transformations (column renaming, etc.) should occur.
    @with_error_handling(category=ErrorCategory.DATA_ACCESS, severity=ErrorSeverity.ERROR)
    def transform(self, data: pd.DataFrame, year: Optional[int] = None, **kwargs) -> pd.DataFrame:
        """
        Transform raw BACI data to a standardized format with optimized memory usage.
        
        Args:
            data: Raw BACI DataFrame
            year: Year of the trade data (for validation)
            **kwargs: Additional parameters for transformation
            
        Returns:
            Transformed DataFrame
            
        Raises:
            DataLoadError: If transformation fails
        """
        # Skip empty DataFrames
        if data is None or data.empty:
            self.logger.warning("No data to transform")
            return data
        
        try:
            # Use a view instead of copy when possible to save memory
            df = data
            
            # Rename columns to semantic names
            df = df.rename(columns=BACI_TO_SEMANTIC_COLUMN_MAPPING)
            
            # Ensure correct data types while preserving categorical efficiency
            if 'year' in df.columns:
                # If year is already categorical, convert to numeric directly
                if pd.api.types.is_categorical_dtype(df['year']):
                    # Extract categories and convert to int
                    df['year'] = df['year'].cat.codes.astype('Int64')
                else:
                    df['year'] = pd.to_numeric(df['year'], errors='coerce').astype('Int64')
                
                # If year was provided, validate and filter
                if year is not None and not df['year'].isna().all():
                    # Find rows with incorrect year
                    incorrect_year_rows = df[df['year'] != year]
                    if len(incorrect_year_rows) > 0:
                        self.logger.warning(
                            f"Found {len(incorrect_year_rows)} rows with year != {year}. "
                            f"Years present: {sorted(df['year'].unique())}"
                        )
                        # Filter to keep only rows with the correct year
                        df = df[df['year'] == year]
            
            # Optimize product_code handling for categorical data
            if 'product_code' in df.columns:
                if pd.api.types.is_categorical_dtype(df['product_code']):
                    # Convert categorical to string, then apply string operations
                    product_codes = df['product_code'].astype(str).str.zfill(6)
                    # Convert back to categorical for memory efficiency
                    df['product_code'] = pd.Categorical(product_codes)
                else:
                    df['product_code'] = df['product_code'].astype(str).str.zfill(6)
                    # Convert to categorical for memory savings
                    df['product_code'] = pd.Categorical(df['product_code'])
            
            # Optimize country codes as categorical
            for col in ['exporter_code', 'importer_code']:
                if col in df.columns:
                    if not pd.api.types.is_categorical_dtype(df[col]):
                        df[col] = pd.Categorical(df[col].astype(str))
            
            # Convert float columns to float32 for memory efficiency if not already
            for col in ['trade_value_usd', 'quantity']:
                if col in df.columns and df[col].dtype == 'float64':
                    df[col] = df[col].astype('float32')
            
            # Log transformation results with memory usage
            memory_mb = df.memory_usage(deep=True).sum() / 1024**2
            self.logger.info(f"Transformed BACI data. Shape: {df.shape}, Memory: {memory_mb:.2f} MB")
            
            return df
            
        except Exception as e:
            # Handle transformation errors
            error_msg = f"Failed to transform BACI data: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            
            # Raise a DataLoadError
            raise DataLoadError(error_msg) from e
    
    @with_error_handling(category=ErrorCategory.VALIDATION, severity=ErrorSeverity.ERROR)
    def validate(self, data: pd.DataFrame, **kwargs) -> ValidationResult:
        """
        Validate the transformed BACI data.
        
        Args:
            data: Transformed BACI DataFrame
            **kwargs: Additional parameters for validation
            
        Returns:
            ValidationResult object
            
        Raises:
            DataValidationError: If validation fails
        """
        # Create a validation result
        result = ValidationResult()
        
        # Skip empty DataFrames
        if data is None or data.empty:
            self.logger.warning("No data to validate")
            return result
        
        try:
            # Check for required columns
            semantic_required_columns = [BACI_TO_SEMANTIC_COLUMN_MAPPING.get(col, col) 
                                      for col in self.schema['required_columns']]
            
            missing_columns = [col for col in semantic_required_columns if col not in data.columns]
            if missing_columns:
                result.add_issue(
                    f"Missing required columns: {', '.join(missing_columns)}",
                    ValidationIssueLevel.ERROR
                )
                return result
            
            # Check data types
            for col, expected_type in self.schema['column_types'].items():
                # Get semantic column name
                semantic_col = BACI_TO_SEMANTIC_COLUMN_MAPPING.get(col, col)
                
                if semantic_col not in data.columns:
                    continue
                
                # Adapt expected type to pandas dtype names
                if expected_type == 'integer':
                    valid = pd.api.types.is_integer_dtype(data[semantic_col])
                elif expected_type == 'numeric':
                    valid = pd.api.types.is_numeric_dtype(data[semantic_col])
                elif expected_type == 'string':
                    valid = pd.api.types.is_string_dtype(data[semantic_col]) or pd.api.types.is_object_dtype(data[semantic_col])
                else:
                    valid = True  # Unknown type, assume valid
                
                if not valid:
                    result.add_issue(
                        f"Column '{semantic_col}' has wrong type: expected {expected_type}, got {data[semantic_col].dtype}",
                        ValidationIssueLevel.WARNING,
                        location=semantic_col
                    )
            
            # Check value ranges
            for col, range_def in self.schema.get('value_ranges', {}).items():
                # Get semantic column name
                semantic_col = BACI_TO_SEMANTIC_COLUMN_MAPPING.get(col, col)
                
                if semantic_col not in data.columns:
                    continue
                
                if not pd.api.types.is_numeric_dtype(data[semantic_col]):
                    continue
                
                # Check minimum value
                if 'min' in range_def:
                    min_val = range_def['min']
                    below_min = (data[semantic_col] < min_val)
                    
                    # Handle nullable types
                    if pd.api.types.is_nullable_dtype(data[semantic_col]):
                        below_min = below_min.fillna(False)
                    
                    below_min_count = below_min.sum()
                    
                    if below_min_count > 0:
                        result.add_issue(
                            f"Column '{semantic_col}' has {below_min_count} values below minimum {min_val}",
                            ValidationIssueLevel.WARNING,
                            location=semantic_col
                        )
                
                # Check maximum value
                if 'max' in range_def:
                    max_val = range_def['max']
                    above_max = (data[semantic_col] > max_val)
                    
                    # Handle nullable types
                    if pd.api.types.is_nullable_dtype(data[semantic_col]):
                        above_max = above_max.fillna(False)
                    
                    above_max_count = above_max.sum()
                    
                    if above_max_count > 0:
                        result.add_issue(
                            f"Column '{semantic_col}' has {above_max_count} values above maximum {max_val}",
                            ValidationIssueLevel.WARNING,
                            location=semantic_col
                        )
            
            # Check for missing values
            for col in self.schema.get('required_columns', []):
                # Get semantic column name
                semantic_col = BACI_TO_SEMANTIC_COLUMN_MAPPING.get(col, col)
                
                if semantic_col not in data.columns:
                    continue
                
                missing_count = data[semantic_col].isna().sum()
                if missing_count > 0:
                    result.add_issue(
                        f"Column '{semantic_col}' has {missing_count} missing values",
                        ValidationIssueLevel.WARNING,
                        location=semantic_col
                    )
            
            # Check unique combinations if specified
            for combo in self.schema.get('unique_combinations', []):
                # Convert to semantic column names
                semantic_combo = [BACI_TO_SEMANTIC_COLUMN_MAPPING.get(col, col) for col in combo]
                
                # Skip if any column is missing
                if not all(col in data.columns for col in semantic_combo):
                    continue
                
                # Check for duplicates
                duplicate_count = len(data) - data.duplicated(subset=semantic_combo, keep='first').value_counts().get(False, 0)
                if duplicate_count > 0:
                    result.add_issue(
                        f"Found {duplicate_count} duplicate rows for combination: {', '.join(semantic_combo)}",
                        ValidationIssueLevel.WARNING
                    )
            
            # Log validation results
            if result.has_issues:
                if result.is_valid:
                    self.logger.warning(f"Validation completed with {result.warning_count()} warnings")
                else:
                    self.logger.error(f"Validation failed with {result.error_count()} errors and {result.warning_count()} warnings")
            else:
                self.logger.info("Validation completed successfully with no issues")
            
            return result
            
        except Exception as e:
            # Handle validation errors
            error_msg = f"Failed to validate BACI data: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            
            # Add the error to the validation result
            result.add_issue(
                f"Validation error: {str(e)}",
                ValidationIssueLevel.ERROR
            )
            
            return result
    
    @log_execution_time(message="{func_name} for year {kwargs[year]} executed in {elapsed}s")
    def filter_by_countries(self, data: pd.DataFrame, 
                          exporter_codes: Optional[List[str]] = None,
                          importer_codes: Optional[List[str]] = None) -> pd.DataFrame:
        """
        Filter the BACI data by exporter and/or importer country codes.
        
        Args:
            data: BACI DataFrame
            exporter_codes: List of exporter country codes to include
            importer_codes: List of importer country codes to include
            
        Returns:
            Filtered DataFrame
        """
        if data is None or data.empty:
            return data
        
        df = data.copy()
        
        # Filter by exporter codes if provided
        if exporter_codes:
            if isinstance(exporter_codes, str):
                exporter_codes = [exporter_codes]
                
            exporter_col = 'exporter_code'
            # Convert exporter codes to string if the column is string
            if pd.api.types.is_string_dtype(df[exporter_col]):
                exporter_codes = [str(code) for code in exporter_codes]
                
            df = df[df[exporter_col].isin(exporter_codes)]
            self.logger.info(f"Filtered to exporters: {exporter_codes}. Remaining rows: {len(df)}")
        
        # Filter by importer codes if provided
        if importer_codes:
            if isinstance(importer_codes, str):
                importer_codes = [importer_codes]
                
            importer_col = 'importer_code'
            # Convert importer codes to string if the column is string
            if pd.api.types.is_string_dtype(df[importer_col]):
                importer_codes = [str(code) for code in importer_codes]
                
            df = df[df[importer_col].isin(importer_codes)]
            self.logger.info(f"Filtered to importers: {importer_codes}. Remaining rows: {len(df)}")
        
        return df
    
    @log_execution_time(message="{func_name} for year {kwargs[year]} executed in {elapsed}s")
    def filter_by_products(self, data: pd.DataFrame, 
                         product_codes: Optional[List[str]] = None) -> pd.DataFrame:
        """
        Filter the BACI data by product codes.
        
        Args:
            data: BACI DataFrame
            product_codes: List of product codes to include
            
        Returns:
            Filtered DataFrame
        """
        if data is None or data.empty:
            return data
        
        if not product_codes:
            return data
        
        df = data.copy()
        
        if isinstance(product_codes, str):
            product_codes = [product_codes]
            
        product_col = 'product_code'
        # Convert product codes to string if the column is string
        if pd.api.types.is_string_dtype(df[product_col]):
            product_codes = [str(code) for code in product_codes]
            # Ensure 6-digit format
            product_codes = [code.zfill(6) for code in product_codes]
            
        df = df[df[product_col].isin(product_codes)]
        self.logger.info(f"Filtered to {len(product_codes)} products. Remaining rows: {len(df)}")
        
        return df
    
    def get_exporters(self, data: pd.DataFrame) -> pd.Series:
        """
        Get a list of unique exporter codes from the data.
        
        Args:
            data: BACI DataFrame
            
        Returns:
            Series of unique exporter codes
        """
        if data is None or data.empty:
            return pd.Series(dtype=str)
        
        return data['exporter_code'].unique()
    
    def get_importers(self, data: pd.DataFrame) -> pd.Series:
        """
        Get a list of unique importer codes from the data.
        
        Args:
            data: BACI DataFrame
            
        Returns:
            Series of unique importer codes
        """
        if data is None or data.empty:
            return pd.Series(dtype=str)
        
        return data['importer_code'].unique()
    
    def get_products(self, data: pd.DataFrame) -> pd.Series:
        """
        Get a list of unique product codes from the data.
        
        Args:
            data: BACI DataFrame
            
        Returns:
            Series of unique product codes
        """
        if data is None or data.empty:
            return pd.Series(dtype=str)
        
        return data['product_code'].unique()
    
    def get_schema(self, path_or_identifier: str) -> Dict[str, Any]:
        """
        Return schema information for the BACI data source.
        
        Args:
            path_or_identifier: Path or identifier for the data (unused for BACI)
            
        Returns:
            Schema dictionary
        """
        return self.schema
    
    def get_available_years(self, path_or_identifier: str) -> List[int]:
        """
        Return available years for BACI time-series data.
        
        Args:
            path_or_identifier: Path or identifier for the data (unused for BACI)
            
        Returns:
            List of available years
        """
        # Check the baci directory for available year files
        raw_data_dir = Path(self.config.get('raw_data_dir', self.base_dir or 'data/raw'))
        baci_dir = raw_data_dir / 'baci'
        
        available_years = []
        
        if baci_dir.exists():
            # Look for files matching the BACI pattern
            for file_path in baci_dir.glob('BACI_*.csv'):
                # Extract year from filename
                file_name = file_path.name
                # Pattern: BACI_HS02_Y{year}_V*.csv
                # Standard library imports
                import re
                match = re.search(r'BACI_.*_Y(\d{4})_', file_name)
                if match:
                    year = int(match.group(1))
                    available_years.append(year)
        
        # If no files found, return a default range
        if not available_years:
            self.logger.warning(f"No BACI files found in {baci_dir}. Returning default years.")
            available_years = [2019, 2020, 2021, 2022, 2023]
        
        return sorted(available_years)