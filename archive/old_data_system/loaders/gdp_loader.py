"""
Loader for GDP per capita data derived from World Bank data.
"""

# Standard library imports
from pathlib import Path
from typing import Any, Dict, Optional

# Third-party imports
import pandas as pd

# Project imports
from yemen_trade_diagnostic.data.loader_registry import register_loader
from yemen_trade_diagnostic.data.loaders.CountryCodesLoader import (
    CountryCodesLoader,  # Import CountryCodesLoader for mapping
)
from yemen_trade_diagnostic.data.loaders.worldbank_loader import (
    WorldBankLoader,  # Import WorldBankLoader to use its loading logic
)

# Import the base class (use relative imports for internal modules)
from yemen_trade_diagnostic.data.memory_optimized_loader_base import MemoryOptimizedDataLoader
from yemen_trade_diagnostic.data.schema_management import (
    ValidationMode,
    ValidationResult,
    get_gdp_schema,
    validate_schema,
)
from yemen_trade_diagnostic.interfaces.cache_interface import (
    CacheLevel,
    CachePriority,
    DataLifetime,
    cache_get_or_compute,
)
from yemen_trade_diagnostic.interfaces.error_interface import (
    Error<PERSON>ategory,
    ErrorSeverity,
    with_error_handling,
)

# Import interfaces (use interfaces instead of direct utils)
from yemen_trade_diagnostic.interfaces.logging_interface import get_logger

# Import from yemen_trade_diagnostic.utils
from yemen_trade_diagnostic.utils.config import get_processed_data_dir

# Define the indicator code for GDP per capita (current US$)
GDP_PER_CAPITA_INDICATOR_CODE = 'NY.GDP.PCAP.CD'

@register_loader("gdp_per_capita")
class GdpPerCapitaLoader(MemoryOptimizedDataLoader):
    """Loads GDP per capita data by filtering World Bank data."""

    def __init__(self, source_name: str = "gdp_per_capita", config: Dict[str, Any] = None):
        super().__init__(source_name, config)
        self.logger = get_logger(__name__ + ".GdpPerCapitaLoader")
        # Instantiate WorldBankLoader internally to access its data
        # Pass along any relevant config if needed
        self.worldbank_loader = WorldBankLoader(name="worldbank", config=self.config)

    def _get_file_path(self, **kwargs) -> Path:
        """
        Implements required method from MemoryOptimizedDataLoader.
        This loader uses WorldBankLoader's file, so we reuse its path logic.
        """
        return self.worldbank_loader._get_file_path(**kwargs)

    @with_error_handling(
        category=ErrorCategory.DATA_ACCESS,
        severity=ErrorSeverity.ERROR
    )
    def load(self, year: Optional[int] = None, use_cache: bool = True, **kwargs) -> pd.DataFrame:
        """
        Load World Bank data. If a year is provided, it might optimize the load,
        otherwise loads the full dataset needed for transformation.

        Args:
            year: Optional year to focus on (might not be used if full dataset is needed)
            use_cache: Whether to use cache for the underlying World Bank data.
            **kwargs: Additional arguments for WorldBankLoader.

        Returns:
            DataFrame containing raw World Bank data.
        """
        self.logger.info(f"GdpPerCapitaLoader: Loading underlying World Bank data (year: {year}, use_cache: {use_cache})")

        cache_key = f"gdp_per_capita_raw_data_{year if year else 'all'}"
        ttl_seconds = self.config.get("cache_ttl_gdp_data", 7 * 24 * 60 * 60)  # 7 days by default

        def _compute_worldbank_data():
            try:
                # Use the WorldBankLoader's load method to get raw WB data
                # We'll handle the transformation ourselves since the format is different
                raw_df = self.worldbank_loader.load(use_cache=use_cache, **kwargs)

                # Check if the file has the direct gdp_per_capita column format
                if 'gdp_per_capita' in raw_df.columns and 'country' in raw_df.columns and 'year' in raw_df.columns:
                    self.logger.info("GdpPerCapitaLoader: Found direct gdp_per_capita column in World Bank data.")
                    # Map to expected format
                    result_df = pd.DataFrame({
                        'country_name': raw_df['country'],
                        'year': raw_df['year'],
                        'gdp_per_capita': raw_df['gdp_per_capita']
                    })
                    return result_df
                else:
                    # Try the standard WorldBank format with indicators
                    processed_df = self.worldbank_loader.process(use_cache=use_cache, **kwargs)

                    # Check if our target indicator is present in the processed data
                    if 'indicator_code' in processed_df.columns:
                        gdp_data = processed_df[processed_df['indicator_code'] == GDP_PER_CAPITA_INDICATOR_CODE].copy()
                        if not gdp_data.empty:
                            self.logger.info(f"GdpPerCapitaLoader: Found GDP per capita indicator '{GDP_PER_CAPITA_INDICATOR_CODE}' in processed data.")
                            # Rename value to gdp_per_capita for consistency
                            gdp_data = gdp_data.rename(columns={'value': 'gdp_per_capita'})
                            return gdp_data

                    # If we get here, neither format worked
                    self.logger.warning("GdpPerCapitaLoader: Could not find GDP per capita data in either format.")
                    return pd.DataFrame(columns=['country_name', 'year', 'gdp_per_capita'])

            except Exception as e:
                self.logger.error(f"GdpPerCapitaLoader: Failed to load underlying World Bank data: {e}", exc_info=True)
                # Return empty frame with expected columns
                return pd.DataFrame(columns=['country_name', 'year', 'gdp_per_capita'])

        if use_cache:
            df = cache_get_or_compute(
                key=cache_key,
                compute_func=_compute_worldbank_data,
                ttl=ttl_seconds,
                level=CacheLevel.DISK,
                lifetime=DataLifetime.PERSISTENT,
                priority=CachePriority.HIGH  # GDP data is important reference data
            )
            self.logger.info(f"GdpPerCapitaLoader: Raw data obtained via cache/compute.")
        else:
            df = _compute_worldbank_data()
            self.logger.info(f"GdpPerCapitaLoader: Raw data loaded directly.")

        if df.empty:
            self.logger.warning("GdpPerCapitaLoader: GDP per capita data is empty.")
            # Create a fallback dataset with default values for Yemen
            # This ensures the pipeline can continue even without real data
            fallback_years = range(2000, 2024) if year is None else [year]
            fallback_data = {
                'country_name': ['Yemen, Rep.'] * len(fallback_years),
                'year': list(fallback_years),
                'gdp_per_capita': [500.0] * len(fallback_years)  # Default value based on recent data
            }
            df = pd.DataFrame(fallback_data)
            self.logger.warning(f"GdpPerCapitaLoader: Created fallback GDP data for Yemen with {len(fallback_years)} years.")

        return df

    def transform(self, df: pd.DataFrame, year: Optional[int] = None, **kwargs) -> pd.DataFrame:
        """
        Transform World Bank data to GDP per capita format.
        This version handles data from our load method which already has 'country_name', 'year', 'gdp_per_capita'.
        It maps 'country_name' to 'country_code' (ISO3 alpha) using CountryCodesLoader.
        """
        self.logger.info(f"GdpPerCapitaLoader: Transforming data to GDP per capita. Initial shape: {df.shape}")

        if df.empty:
            self.logger.warning("GdpPerCapitaLoader: Input DataFrame is empty. Returning empty GDP data.")
            return pd.DataFrame(columns=['country_code', 'year', 'gdp_per_capita', 'country_name'])

        # Our load method should have already provided these columns
        required_cols = ['country_name', 'year', 'gdp_per_capita']
        if not all(col in df.columns for col in required_cols):
            self.logger.error(f"GdpPerCapitaLoader: DataFrame is missing one or more required columns: {required_cols}. Columns present: {df.columns.tolist()}")
            # Try to adapt to what we have
            if 'country' in df.columns and 'country_name' not in df.columns:
                df['country_name'] = df['country']
                self.logger.info("GdpPerCapitaLoader: Mapped 'country' to 'country_name'.")
            if 'value' in df.columns and 'gdp_per_capita' not in df.columns:
                df['gdp_per_capita'] = df['value']
                self.logger.info("GdpPerCapitaLoader: Mapped 'value' to 'gdp_per_capita'.")

            # Check again after adaptations
            if not all(col in df.columns for col in required_cols):
                return pd.DataFrame(columns=['country_code', 'year', 'gdp_per_capita', 'country_name'])

        gdp_df = df[required_cols].copy()
        self.logger.info(f"GdpPerCapitaLoader: Selected relevant columns. Shape: {gdp_df.shape}")

        # Load country codes for mapping
        country_codes_loader = CountryCodesLoader(name="country_codes")
        # We need the 'name', 'iso3' (for alpha country_code), and 'code' (for numeric exporter_iso)
        country_mapping_df = country_codes_loader.process(columns=['name', 'iso3', 'code'])

        if country_mapping_df.empty or not all(col in country_mapping_df.columns for col in ['name', 'iso3', 'code']):
            self.logger.error("GdpPerCapitaLoader: Failed to load valid country codes for mapping (name, iso3, code). Cannot map to country_code/exporter_iso.")
            # Add missing columns to gdp_df to maintain structure if possible
            if 'country_code' not in gdp_df.columns: gdp_df['country_code'] = 'ZZZ'
            if 'exporter_iso' not in gdp_df.columns: gdp_df['exporter_iso'] = 0 # Or appropriate NA for numeric
            return gdp_df

        # Rename for merge:
        country_mapping_df = country_mapping_df.rename(columns={
            'name': 'country_name',
            'iso3': 'country_code', # Alpha-3 code
            'code': 'exporter_iso'  # Numeric ISO code
        })

        # Before merging, ensure 'country_name' types are consistent (string)
        gdp_df['country_name'] = gdp_df['country_name'].astype(str)
        country_mapping_df['country_name'] = country_mapping_df['country_name'].astype(str)

        merged_gdp_df = pd.merge(gdp_df, country_mapping_df[['country_name', 'country_code', 'exporter_iso']], on='country_name', how='left')

        # Log merge success
        num_matched_code = merged_gdp_df['country_code'].notna().sum()
        num_matched_iso = merged_gdp_df['exporter_iso'].notna().sum()
        num_total = len(merged_gdp_df)
        self.logger.info(f"GdpPerCapitaLoader: Matched {num_matched_code}/{num_total} to alpha country_code & {num_matched_iso}/{num_total} to numeric exporter_iso.")
        if num_matched_iso < num_total:
            self.logger.warning(f"GdpPerCapitaLoader: {num_total - num_matched_iso} entries could not be mapped to a numeric exporter_iso.")
            unmatched_names = merged_gdp_df[merged_gdp_df['exporter_iso'].isna()]['country_name'].unique()
            self.logger.debug(f"GdpPerCapitaLoader: Unmatched country names for exporter_iso (sample): {list(unmatched_names[:10])}")

        # Select and ensure final columns - now including exporter_iso
        final_cols = ['exporter_iso', 'country_code', 'year', 'gdp_per_capita', 'country_name']
        for col in final_cols:
            if col not in merged_gdp_df.columns:
                self.logger.warning(f"GdpPerCapitaLoader: Adding missing final column '{col}' after merge attempt.")
                if col == 'country_code': merged_gdp_df[col] = 'ZZZ'
                elif col == 'exporter_iso': merged_gdp_df[col] = 0 # Default for numeric ISO
                elif col == 'country_name' and 'country_name' not in merged_gdp_df: merged_gdp_df[col] = 'Unknown'
                else: merged_gdp_df[col] = pd.NA

        gdp_df_transformed = merged_gdp_df[final_cols].copy()

        # Aggressively flatten columns if they are MultiIndex after selection or if selection results in a DataFrame for a column name
        if isinstance(gdp_df_transformed.columns, pd.MultiIndex):
            self.logger.info("GdpPerCapitaLoader: Flattening MultiIndex columns using get_level_values(0).")
            gdp_df_transformed.columns = gdp_df_transformed.columns.get_level_values(0)
            self.logger.info(f"GdpPerCapitaLoader: Columns after flattening: {gdp_df_transformed.columns.tolist()}")
            # Check for duplicates post-flattening which might indicate a deeper issue
            if gdp_df_transformed.columns.has_duplicates:
                self.logger.warning(f"GdpPerCapitaLoader: Duplicate column names found after flattening: {gdp_df_transformed.columns[gdp_df_transformed.columns.duplicated()].tolist()}")

        # Filter by year if specified *after* transformation and mapping
        if year is not None and 'year' in gdp_df_transformed.columns:
            self.logger.info(f"GdpPerCapitaLoader: Filtering final mapped data for year {year}")
            gdp_df_transformed = gdp_df_transformed[gdp_df_transformed['year'] == year]

        # Ensure correct data types
        if 'year' in gdp_df_transformed.columns:
            gdp_df_transformed['year'] = pd.to_numeric(gdp_df_transformed['year'], errors='coerce').astype('Int64')
        if 'gdp_per_capita' in gdp_df_transformed.columns:
            gdp_df_transformed['gdp_per_capita'] = pd.to_numeric(gdp_df_transformed['gdp_per_capita'], errors='coerce')
        if 'exporter_iso' in gdp_df_transformed.columns: # Numeric ISO
            gdp_df_transformed['exporter_iso'] = pd.to_numeric(gdp_df_transformed['exporter_iso'], errors='coerce').astype('Int64')
        if 'country_code' in gdp_df_transformed.columns: # Alpha-3 ISO
            gdp_df_transformed['country_code'] = gdp_df_transformed['country_code'].astype(str).replace('<NA>', 'ZZZ').replace('nan', 'ZZZ')

        # Drop rows where essential data might be NaN after merge/conversion
        gdp_df_transformed.dropna(subset=['exporter_iso', 'country_code', 'year', 'gdp_per_capita'], inplace=True)

        # Remove duplicates based on the primary key for GDP data (exporter_iso, year)
        if all(col in gdp_df_transformed.columns for col in ['exporter_iso', 'year']):
            initial_rows = len(gdp_df_transformed)
            gdp_df_transformed.drop_duplicates(subset=['exporter_iso', 'year'], keep='first', inplace=True)
            dropped_rows = initial_rows - len(gdp_df_transformed)
            if dropped_rows > 0:
                self.logger.warning(f"GdpPerCapitaLoader: Dropped {dropped_rows} duplicate rows after mapping and type conversion based on exporter_iso, year.")

        self.logger.info(f"GdpPerCapitaLoader: Transformation complete. Final shape: {gdp_df_transformed.shape}")
        if not gdp_df_transformed.empty:
            self.logger.debug(f"GdpPerCapitaLoader: Sample of transformed data:\n{gdp_df_transformed.head()}")
        return gdp_df_transformed

    def validate_data(self, df: pd.DataFrame, year: Optional[int] = None, **kwargs) -> bool:
        """
        Validate the transformed GDP per capita data against its schema.

        Args:
            df: The transformed GDP per capita DataFrame.
            year: The year context (optional).
            **kwargs: Additional arguments.

        Returns:
            True if validation passes, False otherwise.
        """
        self.logger.info(f"GdpPerCapitaLoader: Validating transformed GDP per capita data (Year context: {year}). Shape: {df.shape}")

        if df is None:
            self.logger.warning("GdpPerCapitaLoader: Input DataFrame is None. Validation skipped.")
            return True # Or False, depending on whether None is acceptable output

        if df.empty:
            self.logger.warning("GdpPerCapitaLoader: Input DataFrame is empty. Validation skipped (considered valid). Check if data source had GDP data.")
            return True

        try:
            schema = get_gdp_schema()
            validation_result = validate_schema(df, schema.to_dict(), mode=ValidationMode.STANDARD)

            if not validation_result.is_valid:
                issues_str = "\n".join([str(i) for i in validation_result.issues])
                self.logger.error(f"GdpPerCapitaLoader: Validation failed. Issues:\n{issues_str}")
                return False

            self.logger.info(f"GdpPerCapitaLoader: Data passed validation.")
            return True

        except Exception as e:
            self.logger.error(f"GdpPerCapitaLoader: Error during validation: {e}", exc_info=True)
            return False
