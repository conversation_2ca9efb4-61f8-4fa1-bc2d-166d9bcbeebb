"""
Validation Integration Manager

Ensures consistent validation across all data loading paths and workflows,
providing standardized validation integration for the entire system.
"""

# Standard library imports
import time
from dataclasses import dataclass
from enum import Enum
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional, Set, Tuple, Type, Union

# Third-party imports
import pandas as pd

# Project imports
# Import schema management
from yemen_trade_diagnostic.data.schema_management import get_schema_registry, validate_schema
from yemen_trade_diagnostic.data.validation.error_recovery import (
    ErrorRecoveryStrategy,
    ValidationErrorRecovery,
    recover_validation_errors,
)

# Import validation components
from yemen_trade_diagnostic.data.validation.performance_validator import (
    PerformanceValidator,
    ValidationPerformanceConfig,
    ValidationPerformanceLevel,
)
from yemen_trade_diagnostic.data.validation.schema_optimizer import (
    OptimizedSchemaRegistry,
    get_optimized_schema_registry,
)
from yemen_trade_diagnostic.data.validation.validation_monitoring import (
    ValidationMonitor,
    get_validation_monitor,
    monitor_validation_operation,
)
from yemen_trade_diagnostic.interfaces.cache_interface import CacheLevel, get_cache_manager
from yemen_trade_diagnostic.interfaces.error_interface import (
    ErrorCategory,
    ErrorSeverity,
    with_error_handling,
)
from yemen_trade_diagnostic.interfaces.logging_interface import get_logger

# V2 interface imports
from yemen_trade_diagnostic.interfaces.validation_interface import (
    ValidationIssueLevel,
    ValidationMode,
    ValidationResult,
    get_validation_manager,
)

logger = get_logger(__name__)


class ValidationIntegrationLevel(Enum):
    """Integration levels for validation across the system."""
    BASIC = "basic"              # Basic validation only
    STANDARD = "standard"        # Standard validation with some optimization
    ENHANCED = "enhanced"        # Enhanced validation with performance optimization
    COMPREHENSIVE = "comprehensive"  # Full validation suite with all features


@dataclass
class ValidationConfiguration:
    """Configuration for integrated validation system."""
    integration_level: ValidationIntegrationLevel = ValidationIntegrationLevel.STANDARD
    performance_config: Optional[ValidationPerformanceConfig] = None
    enable_monitoring: bool = True
    enable_error_recovery: bool = True
    enable_caching: bool = True
    enable_hardware_acceleration: bool = True
    cross_validation_enabled: bool = True
    preprocessing_validation: bool = True
    post_processing_validation: bool = True
    validation_hooks: List[str] = None
    
    def __post_init__(self):
        if self.validation_hooks is None:
            self.validation_hooks = []
        
        if self.performance_config is None:
            perf_level = {
                ValidationIntegrationLevel.BASIC: ValidationPerformanceLevel.MAXIMUM_SPEED,
                ValidationIntegrationLevel.STANDARD: ValidationPerformanceLevel.BALANCED,
                ValidationIntegrationLevel.ENHANCED: ValidationPerformanceLevel.BALANCED,
                ValidationIntegrationLevel.COMPREHENSIVE: ValidationPerformanceLevel.COMPREHENSIVE
            }[self.integration_level]
            
            self.performance_config = ValidationPerformanceConfig(
                level=perf_level,
                use_hardware_acceleration=self.enable_hardware_acceleration,
                cache_validation_results=self.enable_caching
            )


class ValidationIntegrationManager:
    """
    Central manager for validation integration across all data workflows.
    
    Features:
    - Consistent validation across all data loading paths
    - Cross-validation between related datasets
    - Integration with preprocessing and transformation workflows
    - Validation hooks for custom workflows
    - Performance optimization and monitoring
    - Error recovery and reporting
    """
    
    def __init__(self, config: Optional[ValidationConfiguration] = None):
        """
        Initialize validation integration manager.
        
        Args:
            config: Validation configuration
        """
        self.config = config or ValidationConfiguration()
        self.logger = logger
        
        # Initialize components based on configuration
        self.schema_registry = get_optimized_schema_registry()
        self.performance_validator = PerformanceValidator(self.config.performance_config)
        
        if self.config.enable_error_recovery:
            self.error_recovery = ValidationErrorRecovery()
        else:
            self.error_recovery = None
        
        if self.config.enable_monitoring:
            self.monitor = get_validation_monitor()
        else:
            self.monitor = None
        
        # Standard validation manager for fallback
        self.standard_validator = get_validation_manager()
        
        # Validation hooks registry
        self.validation_hooks: Dict[str, List[Callable]] = {
            'pre_validation': [],
            'post_validation': [],
            'error_encountered': [],
            'validation_complete': []
        }
        
        # Cross-validation rules
        self.cross_validation_rules: List[Dict[str, Any]] = []
        
        # Integration statistics
        self.integration_stats = {
            "validations_performed": 0,
            "cross_validations_performed": 0,
            "errors_recovered": 0,
            "hook_executions": 0,
            "performance_optimizations_used": 0,
            "cache_hits": 0
        }
        
        self.logger.info(f"Initialized ValidationIntegrationManager with {self.config.integration_level.value} level")
    
    def register_validation_hook(self, hook_type: str, hook_function: Callable):
        """
        Register validation hook for custom integration.
        
        Args:
            hook_type: Type of hook ('pre_validation', 'post_validation', etc.)
            hook_function: Function to call at hook point
        """
        if hook_type not in self.validation_hooks:
            self.validation_hooks[hook_type] = []
        
        self.validation_hooks[hook_type].append(hook_function)
        self.logger.debug(f"Registered {hook_type} hook: {hook_function.__name__}")
    
    def add_cross_validation_rule(self, 
                                 datasets: List[str],
                                 rule_function: Callable,
                                 description: str = ""):
        """
        Add cross-validation rule between datasets.
        
        Args:
            datasets: List of dataset names involved in cross-validation
            rule_function: Function to perform cross-validation
            description: Description of the rule
        """
        rule = {
            "datasets": datasets,
            "function": rule_function,
            "description": description,
            "added_at": time.time()
        }
        
        self.cross_validation_rules.append(rule)
        self.logger.info(f"Added cross-validation rule for datasets: {datasets}")
    
    @with_error_handling(category=ErrorCategory.VALIDATION, severity=ErrorSeverity.WARNING)
    def validate_dataframe(self, 
                          df: pd.DataFrame,
                          schema_name: str,
                          dataset_name: Optional[str] = None,
                          mode: ValidationMode = ValidationMode.STANDARD,
                          context: Optional[Dict[str, Any]] = None) -> ValidationResult:
        """
        Integrated validation of DataFrame with full feature set.
        
        Args:
            df: DataFrame to validate
            schema_name: Name of validation schema
            dataset_name: Optional dataset identifier for cross-validation
            mode: Validation mode
            context: Additional context for validation
            
        Returns:
            ValidationResult with comprehensive validation details
        """
        operation_id = f"validate_{schema_name}_{int(time.time())}"
        context = context or {}
        
        # Start monitoring if enabled
        monitor_ctx = None
        if self.monitor:
            monitor_ctx = monitor_validation_operation(
                f"integrated_validation_{schema_name}",
                len(df),
                self.monitor
            )
            monitor_ctx.__enter__()
        
        try:
            # Execute pre-validation hooks
            self._execute_hooks('pre_validation', df, schema_name, context)
            
            # Get optimized schema
            schema = self.schema_registry.get_schema(schema_name, compiled=True)
            if not schema:
                result = ValidationResult()
                result.add_issue(
                    f"Schema '{schema_name}' not found",
                    ValidationIssueLevel.ERROR
                )
                return result
            
            # Choose validation approach based on configuration and data size
            if (self.config.integration_level in [ValidationIntegrationLevel.ENHANCED, ValidationIntegrationLevel.COMPREHENSIVE] 
                and len(df) > 10000):
                # Use performance-optimized validation
                result = self.performance_validator.validate_large_dataset(df, schema, mode)
                self.integration_stats["performance_optimizations_used"] += 1
            else:
                # Use standard validation
                result = validate_schema(df, schema, mode)
            
            # Update monitoring
            if monitor_ctx:
                monitor_ctx.update(
                    rows_processed=len(df),
                    errors_found=result.error_count()
                )
            
            # Error recovery if enabled and needed
            if (self.error_recovery and 
                not result.is_valid and 
                self.config.integration_level != ValidationIntegrationLevel.BASIC):
                
                recovery_results, modified_df = recover_validation_errors(
                    result.issues, df, schema, self.error_recovery
                )
                
                # Update result with recovery information
                successful_recoveries = sum(1 for r in recovery_results if r.success)
                result.details['error_recovery'] = {
                    'attempted': len(recovery_results),
                    'successful': successful_recoveries,
                    'data_modified': any(r.data_modified for r in recovery_results)
                }
                
                self.integration_stats["errors_recovered"] += successful_recoveries
                
                # Re-validate if data was modified and recoveries were successful
                if successful_recoveries > 0 and any(r.data_modified for r in recovery_results):
                    result = validate_schema(modified_df, schema, mode)
                    result.details['post_recovery_validation'] = True
            
            # Cross-validation if enabled and dataset name provided
            if (self.config.cross_validation_enabled and 
                dataset_name and 
                self.config.integration_level == ValidationIntegrationLevel.COMPREHENSIVE):
                
                cross_validation_results = self._perform_cross_validation(df, dataset_name, context)
                if cross_validation_results:
                    result.details['cross_validation'] = cross_validation_results
                    self.integration_stats["cross_validations_performed"] += 1
            
            # Execute post-validation hooks
            self._execute_hooks('post_validation', df, schema_name, context, result)
            
            # Add integration metadata
            result.details['integration_level'] = self.config.integration_level.value
            result.details['validation_manager'] = 'integrated'
            result.details['dataset_name'] = dataset_name
            
            # Execute completion hooks
            self._execute_hooks('validation_complete', df, schema_name, context, result)
            
            self.integration_stats["validations_performed"] += 1
            
            return result
            
        except Exception as e:
            # Execute error hooks
            self._execute_hooks('error_encountered', df, schema_name, context, e)
            raise
            
        finally:
            # End monitoring
            if monitor_ctx:
                monitor_ctx.__exit__(None, None, None)
    
    def validate_file(self,
                     file_path: Union[str, Path],
                     schema_name: str,
                     dataset_name: Optional[str] = None,
                     mode: ValidationMode = ValidationMode.STANDARD,
                     chunk_size: Optional[int] = None) -> ValidationResult:
        """
        Validate file with integrated validation system.
        
        Args:
            file_path: Path to file to validate
            schema_name: Name of validation schema  
            dataset_name: Optional dataset identifier
            mode: Validation mode
            chunk_size: Optional chunk size for large files
            
        Returns:
            ValidationResult from file validation
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            result = ValidationResult()
            result.add_issue(
                f"File not found: {file_path}",
                ValidationIssueLevel.ERROR
            )
            return result
        
        # For large files, use streaming validation
        if (chunk_size or 
            (file_path.stat().st_size > 100_000_000 and  # > 100MB
             self.config.integration_level in [ValidationIntegrationLevel.ENHANCED, ValidationIntegrationLevel.COMPREHENSIVE])):
            
            # Project imports
            from yemen_trade_diagnostic.data.validation.performance_validator import (
                StreamingValidator,
            )
            streaming_validator = StreamingValidator(self.config.performance_config)
            
            schema = self.schema_registry.get_schema(schema_name)
            if not schema:
                result = ValidationResult()
                result.add_issue(
                    f"Schema '{schema_name}' not found",
                    ValidationIssueLevel.ERROR
                )
                return result
            
            return streaming_validator.validate_csv_file(file_path, schema.to_dict(), mode)
        
        else:
            # Load file and validate in memory
            try:
                if file_path.suffix.lower() == '.csv':
                    df = pd.read_csv(file_path)
                elif file_path.suffix.lower() == '.parquet':
                    df = pd.read_parquet(file_path)
                else:
                    result = ValidationResult()
                    result.add_issue(
                        f"Unsupported file format: {file_path.suffix}",
                        ValidationIssueLevel.ERROR
                    )
                    return result
                
                return self.validate_dataframe(df, schema_name, dataset_name, mode,
                                             {"file_path": str(file_path)})
            
            except Exception as e:
                result = ValidationResult()
                result.add_issue(
                    f"Error reading file: {str(e)}",
                    ValidationIssueLevel.ERROR
                )
                return result
    
    def _execute_hooks(self, hook_type: str, *args, **kwargs):
        """Execute registered hooks of specified type."""
        if hook_type not in self.validation_hooks:
            return
        
        for hook_function in self.validation_hooks[hook_type]:
            try:
                hook_function(*args, **kwargs)
                self.integration_stats["hook_executions"] += 1
            except Exception as e:
                self.logger.warning(f"Hook execution failed ({hook_type}): {e}")
    
    def _perform_cross_validation(self, 
                                df: pd.DataFrame,
                                dataset_name: str,
                                context: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Perform cross-validation with related datasets."""
        applicable_rules = [
            rule for rule in self.cross_validation_rules
            if dataset_name in rule["datasets"]
        ]
        
        if not applicable_rules:
            return None
        
        cross_validation_results = {
            "rules_applied": len(applicable_rules),
            "violations": [],
            "passed": 0,
            "failed": 0
        }
        
        for rule in applicable_rules:
            try:
                violations = rule["function"](df, dataset_name, context)
                
                if violations:
                    cross_validation_results["violations"].extend(violations)
                    cross_validation_results["failed"] += 1
                else:
                    cross_validation_results["passed"] += 1
                    
            except Exception as e:
                self.logger.warning(f"Cross-validation rule failed: {e}")
                cross_validation_results["failed"] += 1
        
        return cross_validation_results
    
    def get_integration_statistics(self) -> Dict[str, Any]:
        """Get integration system statistics."""
        stats = self.integration_stats.copy()
        
        # Add component statistics
        if self.performance_validator:
            stats["performance_validator"] = self.performance_validator.get_performance_stats()
        
        if self.schema_registry:
            stats["schema_registry"] = self.schema_registry.get_performance_stats()
        
        if self.error_recovery:
            stats["error_recovery"] = self.error_recovery.get_recovery_stats()
        
        if self.monitor:
            stats["monitoring"] = self.monitor.get_performance_analytics(time_window_hours=24)
        
        return stats
    
    def create_integration_report(self, output_file: Optional[Path] = None) -> Dict[str, Any]:
        """Create comprehensive integration system report."""
        # Standard library imports
        import json
        
        report = {
            "timestamp": time.time(),
            "configuration": {
                "integration_level": self.config.integration_level.value,
                "monitoring_enabled": self.config.enable_monitoring,
                "error_recovery_enabled": self.config.enable_error_recovery,
                "caching_enabled": self.config.enable_caching,
                "hardware_acceleration": self.config.enable_hardware_acceleration,
                "cross_validation_enabled": self.config.cross_validation_enabled
            },
            "statistics": self.get_integration_statistics(),
            "registered_hooks": {
                hook_type: len(hooks) 
                for hook_type, hooks in self.validation_hooks.items()
            },
            "cross_validation_rules": len(self.cross_validation_rules),
            "system_health": self._assess_integration_health()
        }
        
        if output_file:
            output_file.parent.mkdir(parents=True, exist_ok=True)
            with open(output_file, 'w') as f:
                json.dump(report, f, indent=2, default=str)
            
            self.logger.info(f"Integration report saved to {output_file}")
        
        return report
    
    def _assess_integration_health(self) -> Dict[str, Any]:
        """Assess overall integration system health."""
        health = {
            "status": "healthy",
            "issues": [],
            "recommendations": []
        }
        
        # Check if components are properly initialized
        if not self.schema_registry:
            health["issues"].append("Schema registry not initialized")
            health["status"] = "degraded"
        
        if self.config.enable_monitoring and not self.monitor:
            health["issues"].append("Monitoring enabled but not initialized")
            health["status"] = "degraded"
        
        if self.config.enable_error_recovery and not self.error_recovery:
            health["issues"].append("Error recovery enabled but not initialized")
            health["status"] = "degraded"
        
        # Check performance
        stats = self.integration_stats
        if stats["validations_performed"] > 0:
            error_recovery_rate = stats["errors_recovered"] / stats["validations_performed"]
            if error_recovery_rate > 0.1:  # > 10% error rate
                health["issues"].append(f"High error recovery rate: {error_recovery_rate:.1%}")
                health["recommendations"].append("Review data quality or validation schemas")
        
        # Check hook usage
        if len(self.config.validation_hooks) > 0 and stats["hook_executions"] == 0:
            health["issues"].append("Validation hooks configured but not executing")
        
        if health["issues"]:
            health["status"] = "degraded" if health["status"] == "healthy" else "unhealthy"
        
        return health


# Global integration manager
_integration_manager = None


def get_validation_integration_manager(config: Optional[ValidationConfiguration] = None) -> ValidationIntegrationManager:
    """Get global validation integration manager."""
    global _integration_manager
    if _integration_manager is None:
        _integration_manager = ValidationIntegrationManager(config)
    return _integration_manager


# Convenience functions for common validation tasks

def validate_with_integration(df: pd.DataFrame,
                            schema_name: str,
                            dataset_name: Optional[str] = None,
                            integration_level: ValidationIntegrationLevel = ValidationIntegrationLevel.STANDARD,
                            mode: ValidationMode = ValidationMode.STANDARD) -> ValidationResult:
    """
    Validate DataFrame using integrated validation system.
    
    Args:
        df: DataFrame to validate
        schema_name: Name of validation schema
        dataset_name: Optional dataset identifier
        integration_level: Level of integration features to use
        mode: Validation mode
        
    Returns:
        ValidationResult from integrated validation
    """
    config = ValidationConfiguration(integration_level=integration_level)
    manager = ValidationIntegrationManager(config)
    return manager.validate_dataframe(df, schema_name, dataset_name, mode)


def setup_data_loader_validation(loader_class: Type,
                                validation_config: Optional[ValidationConfiguration] = None):
    """
    Set up validation integration for data loader class.
    
    Args:
        loader_class: Data loader class to integrate with
        validation_config: Optional validation configuration
    """
    manager = get_validation_integration_manager(validation_config)
    
    # Add validation hook to loader's load method
    original_load = loader_class.load
    
    def integrated_load(self, *args, **kwargs):
        # Get data from original load
        data = original_load(self, *args, **kwargs)
        
        # Validate if schema is available
        if hasattr(self, 'schema_name') and isinstance(data, pd.DataFrame):
            validation_result = manager.validate_dataframe(
                data, 
                self.schema_name,
                getattr(self, 'dataset_name', None)
            )
            
            # Store validation result in loader
            self._last_validation_result = validation_result
            
            # Log validation issues
            if not validation_result.is_valid:
                logger.warning(f"Validation issues in {self.name}: {validation_result.error_count()} errors")
        
        return data
    
    # Replace load method
    loader_class.load = integrated_load
    
    logger.info(f"Integrated validation with {loader_class.__name__}")


# Export public symbols
__all__ = [
    'ValidationIntegrationLevel', 'ValidationConfiguration', 'ValidationIntegrationManager',
    'get_validation_integration_manager', 'validate_with_integration', 'setup_data_loader_validation'
]