"""
Advanced Error Handling and Recovery for Data Validation

Provides intelligent error categorization, recovery strategies, and comprehensive
error reporting for the validation system.
"""

# Standard library imports
import json
import time
import traceback
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional, Set, Tuple, Union

# Third-party imports
import numpy as np
import pandas as pd

# Project imports
from yemen_trade_diagnostic.interfaces.cache_interface import <PERSON>acheLevel, get_cache_manager
from yemen_trade_diagnostic.interfaces.error_interface import ErrorCategory, ErrorSeverity
from yemen_trade_diagnostic.interfaces.logging_interface import get_logger

# V2 interface imports
from yemen_trade_diagnostic.interfaces.validation_interface import (
    ValidationIssue,
    ValidationIssueLevel,
    ValidationMode,
    ValidationResult,
)

logger = get_logger(__name__)


class ErrorRecoveryStrategy(Enum):
    """Recovery strategies for different types of validation errors."""
    IGNORE = "ignore"                    # Skip the error
    FIX_AUTOMATIC = "fix_automatic"      # Attempt automatic fix
    FIX_INTERACTIVE = "fix_interactive"  # Prompt for user input
    REPLACE_DEFAULT = "replace_default"  # Use default value
    REMOVE_RECORD = "remove_record"      # Remove problematic record
    STOP_PROCESSING = "stop_processing"  # Stop validation
    CONTINUE_WITH_WARNING = "continue_with_warning"  # Continue but log warning


class ErrorType(Enum):
    """Categorization of validation errors."""
    MISSING_COLUMN = "missing_column"
    WRONG_TYPE = "wrong_type"
    OUT_OF_RANGE = "out_of_range"
    INVALID_VALUE = "invalid_value"
    DUPLICATE_VALUE = "duplicate_value"
    CONSTRAINT_VIOLATION = "constraint_violation"
    FORMAT_ERROR = "format_error"
    RELATIONSHIP_ERROR = "relationship_error"
    CUSTOM_VALIDATION_ERROR = "custom_validation_error"
    SYSTEM_ERROR = "system_error"


@dataclass
class ErrorPattern:
    """Pattern for matching and handling specific error types."""
    error_type: ErrorType
    pattern: str = ""
    column: str = ""
    severity: ValidationIssueLevel = ValidationIssueLevel.ERROR
    recovery_strategy: ErrorRecoveryStrategy = ErrorRecoveryStrategy.CONTINUE_WITH_WARNING
    auto_fix_function: Optional[Callable] = None
    custom_message: str = ""
    context_requirements: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ErrorRecoveryResult:
    """Result of error recovery operation."""
    success: bool
    strategy_used: ErrorRecoveryStrategy
    original_error: ValidationIssue
    fixed_value: Any = None
    recovery_message: str = ""
    data_modified: bool = False
    additional_context: Dict[str, Any] = field(default_factory=dict)


class ValidationErrorRecovery:
    """
    Advanced error recovery system for data validation.
    
    Features:
    - Intelligent error categorization
    - Multiple recovery strategies
    - Configurable error patterns
    - Automatic fix suggestions
    - Recovery success tracking
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize error recovery system.
        
        Args:
            config: Configuration for error recovery behavior
        """
        self.config = config or {}
        self.logger = logger
        # Initialize cache manager lazily to avoid circular dependencies
        self._cache_manager = None
        
        # Error patterns for different scenarios
        self.error_patterns: Dict[ErrorType, List[ErrorPattern]] = {}
        self._initialize_default_patterns()
        
        # Recovery statistics
        self.recovery_stats = {
            "errors_encountered": 0,
            "successful_recoveries": 0,
            "failed_recoveries": 0,
            "automatic_fixes": 0,
            "manual_interventions": 0,
            "recovery_by_strategy": {strategy.value: 0 for strategy in ErrorRecoveryStrategy},
            "recovery_by_error_type": {error_type.value: 0 for error_type in ErrorType}
        }
        
        # Custom recovery functions
        self.custom_recovery_functions: Dict[str, Callable] = {}
        
        self.logger.info("Initialized ValidationErrorRecovery system")
    
    @property
    def cache_manager(self):
        """Lazy initialization of cache manager to avoid circular dependencies."""
        if self._cache_manager is None:
            try:
                self._cache_manager = get_cache_manager()
            except:
                # If cache manager fails to initialize, create a simple fallback
                self._cache_manager = None
                self.logger.warning("Cache manager initialization failed, disabling caching")
        return self._cache_manager
    
    def _initialize_default_patterns(self):
        """Initialize default error patterns and recovery strategies."""
        
        # Missing column patterns
        self.error_patterns[ErrorType.MISSING_COLUMN] = [
            ErrorPattern(
                error_type=ErrorType.MISSING_COLUMN,
                pattern="Required column .* is missing",
                recovery_strategy=ErrorRecoveryStrategy.STOP_PROCESSING,
                custom_message="Critical column missing - cannot proceed with validation"
            )
        ]
        
        # Wrong type patterns
        self.error_patterns[ErrorType.WRONG_TYPE] = [
            ErrorPattern(
                error_type=ErrorType.WRONG_TYPE,
                pattern="Column .* has wrong type",
                recovery_strategy=ErrorRecoveryStrategy.FIX_AUTOMATIC,
                auto_fix_function=self._fix_type_conversion,
                custom_message="Attempting automatic type conversion"
            )
        ]
        
        # Out of range patterns
        self.error_patterns[ErrorType.OUT_OF_RANGE] = [
            ErrorPattern(
                error_type=ErrorType.OUT_OF_RANGE,
                pattern="values below minimum|values above maximum",
                recovery_strategy=ErrorRecoveryStrategy.FIX_AUTOMATIC,
                auto_fix_function=self._fix_range_clipping,
                custom_message="Clipping values to valid range"
            )
        ]
        
        # Invalid value patterns
        self.error_patterns[ErrorType.INVALID_VALUE] = [
            ErrorPattern(
                error_type=ErrorType.INVALID_VALUE,
                pattern="contains disallowed values",
                recovery_strategy=ErrorRecoveryStrategy.REPLACE_DEFAULT,
                auto_fix_function=self._fix_invalid_values,
                custom_message="Replacing invalid values with defaults"
            )
        ]
        
        # Duplicate value patterns
        self.error_patterns[ErrorType.DUPLICATE_VALUE] = [
            ErrorPattern(
                error_type=ErrorType.DUPLICATE_VALUE,
                pattern="duplicate values|duplicate non-unique",
                recovery_strategy=ErrorRecoveryStrategy.REMOVE_RECORD,
                auto_fix_function=self._fix_duplicates,
                custom_message="Removing duplicate records"
            )
        ]
    
    def add_error_pattern(self, pattern: ErrorPattern):
        """Add custom error pattern."""
        if pattern.error_type not in self.error_patterns:
            self.error_patterns[pattern.error_type] = []
        
        self.error_patterns[pattern.error_type].append(pattern)
        self.logger.debug(f"Added error pattern for {pattern.error_type.value}")
    
    def add_custom_recovery_function(self, name: str, func: Callable):
        """Add custom recovery function."""
        self.custom_recovery_functions[name] = func
        self.logger.debug(f"Added custom recovery function: {name}")
    
    def categorize_error(self, error: ValidationIssue) -> ErrorType:
        """
        Categorize validation error based on message and context.
        
        Args:
            error: Validation issue to categorize
            
        Returns:
            ErrorType category
        """
        message = error.message.lower()
        
        # Check for specific patterns
        if "required column" in message and "missing" in message:
            return ErrorType.MISSING_COLUMN
        elif "wrong type" in message or "type:" in message:
            return ErrorType.WRONG_TYPE
        elif "below minimum" in message or "above maximum" in message:
            return ErrorType.OUT_OF_RANGE
        elif "disallowed values" in message or "invalid" in message:
            return ErrorType.INVALID_VALUE
        elif "duplicate" in message:
            return ErrorType.DUPLICATE_VALUE
        elif "constraint" in message or "violation" in message:
            return ErrorType.CONSTRAINT_VIOLATION
        elif "format" in message or "pattern" in message:
            return ErrorType.FORMAT_ERROR
        elif "relationship" in message or "dependency" in message:
            return ErrorType.RELATIONSHIP_ERROR
        elif "custom" in message or "business rule" in message:
            return ErrorType.CUSTOM_VALIDATION_ERROR
        else:
            return ErrorType.SYSTEM_ERROR
    
    def find_matching_pattern(self, error: ValidationIssue, error_type: ErrorType) -> Optional[ErrorPattern]:
        """
        Find matching error pattern for recovery strategy.
        
        Args:
            error: Validation issue
            error_type: Categorized error type
            
        Returns:
            Matching error pattern or None
        """
        if error_type not in self.error_patterns:
            return None
        
        # Standard library imports
        import re
        
        for pattern in self.error_patterns[error_type]:
            # Check message pattern
            if pattern.pattern and re.search(pattern.pattern, error.message, re.IGNORECASE):
                # Check column if specified
                if pattern.column and error.location != pattern.column:
                    continue
                
                # Check context requirements
                if pattern.context_requirements:
                    if not all(
                        key in error.context and error.context[key] == value
                        for key, value in pattern.context_requirements.items()
                    ):
                        continue
                
                return pattern
        
        # Return default pattern if no specific match
        if self.error_patterns[error_type]:
            return self.error_patterns[error_type][0]
        
        return None
    
    def recover_from_error(self, 
                          error: ValidationIssue, 
                          df: pd.DataFrame,
                          schema: Dict[str, Any],
                          context: Optional[Dict[str, Any]] = None) -> ErrorRecoveryResult:
        """
        Attempt to recover from validation error.
        
        Args:
            error: Validation issue to recover from
            df: DataFrame being validated
            schema: Validation schema
            context: Additional context for recovery
            
        Returns:
            ErrorRecoveryResult with recovery details
        """
        self.recovery_stats["errors_encountered"] += 1
        
        # Categorize error
        error_type = self.categorize_error(error)
        self.recovery_stats["recovery_by_error_type"][error_type.value] += 1
        
        # Find matching pattern
        pattern = self.find_matching_pattern(error, error_type)
        if not pattern:
            self.logger.warning(f"No recovery pattern found for error type: {error_type.value}")
            return ErrorRecoveryResult(
                success=False,
                strategy_used=ErrorRecoveryStrategy.CONTINUE_WITH_WARNING,
                original_error=error,
                recovery_message=f"No recovery strategy available for {error_type.value}"
            )
        
        # Apply recovery strategy
        strategy = pattern.recovery_strategy
        self.recovery_stats["recovery_by_strategy"][strategy.value] += 1
        
        try:
            if strategy == ErrorRecoveryStrategy.IGNORE:
                result = ErrorRecoveryResult(
                    success=True,
                    strategy_used=strategy,
                    original_error=error,
                    recovery_message="Error ignored as per configuration"
                )
            
            elif strategy == ErrorRecoveryStrategy.FIX_AUTOMATIC:
                result = self._apply_automatic_fix(error, df, schema, pattern, context)
            
            elif strategy == ErrorRecoveryStrategy.REPLACE_DEFAULT:
                result = self._apply_default_replacement(error, df, schema, pattern, context)
            
            elif strategy == ErrorRecoveryStrategy.REMOVE_RECORD:
                result = self._apply_record_removal(error, df, schema, pattern, context)
            
            elif strategy == ErrorRecoveryStrategy.CONTINUE_WITH_WARNING:
                result = ErrorRecoveryResult(
                    success=True,
                    strategy_used=strategy,
                    original_error=error,
                    recovery_message="Continuing with warning - error not fixed"
                )
            
            else:
                result = ErrorRecoveryResult(
                    success=False,
                    strategy_used=strategy,
                    original_error=error,
                    recovery_message=f"Recovery strategy {strategy.value} not implemented"
                )
            
            # Update statistics
            if result.success:
                self.recovery_stats["successful_recoveries"] += 1
                if result.strategy_used == ErrorRecoveryStrategy.FIX_AUTOMATIC:
                    self.recovery_stats["automatic_fixes"] += 1
            else:
                self.recovery_stats["failed_recoveries"] += 1
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error during recovery: {e}")
            self.recovery_stats["failed_recoveries"] += 1
            
            return ErrorRecoveryResult(
                success=False,
                strategy_used=strategy,
                original_error=error,
                recovery_message=f"Recovery failed: {str(e)}"
            )
    
    def _apply_automatic_fix(self, 
                           error: ValidationIssue, 
                           df: pd.DataFrame,
                           schema: Dict[str, Any],
                           pattern: ErrorPattern,
                           context: Optional[Dict[str, Any]]) -> ErrorRecoveryResult:
        """Apply automatic fix for error."""
        if pattern.auto_fix_function:
            try:
                fixed_value = pattern.auto_fix_function(error, df, schema, context)
                return ErrorRecoveryResult(
                    success=True,
                    strategy_used=ErrorRecoveryStrategy.FIX_AUTOMATIC,
                    original_error=error,
                    fixed_value=fixed_value,
                    data_modified=True,
                    recovery_message=pattern.custom_message or "Applied automatic fix"
                )
            except Exception as e:
                return ErrorRecoveryResult(
                    success=False,
                    strategy_used=ErrorRecoveryStrategy.FIX_AUTOMATIC,
                    original_error=error,
                    recovery_message=f"Automatic fix failed: {str(e)}"
                )
        else:
            return ErrorRecoveryResult(
                success=False,
                strategy_used=ErrorRecoveryStrategy.FIX_AUTOMATIC,
                original_error=error,
                recovery_message="No automatic fix function provided"
            )
    
    def _apply_default_replacement(self, 
                                 error: ValidationIssue, 
                                 df: pd.DataFrame,
                                 schema: Dict[str, Any],
                                 pattern: ErrorPattern,
                                 context: Optional[Dict[str, Any]]) -> ErrorRecoveryResult:
        """Apply default value replacement."""
        if not error.location:
            return ErrorRecoveryResult(
                success=False,
                strategy_used=ErrorRecoveryStrategy.REPLACE_DEFAULT,
                original_error=error,
                recovery_message="No column location specified for default replacement"
            )
        
        column = error.location
        
        # Determine default value based on column type
        if column in schema.get('column_types', {}):
            column_type = schema['column_types'][column]
            
            if column_type == 'numeric':
                default_value = 0
            elif column_type == 'string':
                default_value = 'UNKNOWN'
            elif column_type == 'integer':
                default_value = 0
            elif column_type == 'boolean':
                default_value = False
            else:
                default_value = None
        else:
            default_value = None
        
        # Apply default value (this would modify the DataFrame)
        # Note: In practice, this would need to be coordinated with the calling code
        
        return ErrorRecoveryResult(
            success=True,
            strategy_used=ErrorRecoveryStrategy.REPLACE_DEFAULT,
            original_error=error,
            fixed_value=default_value,
            data_modified=True,
            recovery_message=f"Replaced invalid values in column '{column}' with default: {default_value}"
        )
    
    def _apply_record_removal(self, 
                            error: ValidationIssue, 
                            df: pd.DataFrame,
                            schema: Dict[str, Any],
                            pattern: ErrorPattern,
                            context: Optional[Dict[str, Any]]) -> ErrorRecoveryResult:
        """Apply record removal for error."""
        # This would mark records for removal
        # In practice, this would need to be coordinated with the calling code
        
        return ErrorRecoveryResult(
            success=True,
            strategy_used=ErrorRecoveryStrategy.REMOVE_RECORD,
            original_error=error,
            data_modified=True,
            recovery_message="Marked problematic records for removal"
        )
    
    # Automatic fix functions
    
    def _fix_type_conversion(self, 
                           error: ValidationIssue, 
                           df: pd.DataFrame,
                           schema: Dict[str, Any],
                           context: Optional[Dict[str, Any]]) -> Any:
        """Attempt automatic type conversion."""
        if not error.location:
            raise ValueError("No column location for type conversion")
        
        column = error.location
        expected_type = schema.get('column_types', {}).get(column)
        
        if expected_type == 'numeric':
            # Convert to numeric, coercing errors to NaN
            return pd.to_numeric(df[column], errors='coerce')
        elif expected_type == 'integer':
            # Convert to integer
            return pd.to_numeric(df[column], errors='coerce').astype('Int64')
        elif expected_type == 'string':
            # Convert to string
            return df[column].astype(str)
        else:
            raise ValueError(f"Cannot convert to type: {expected_type}")
    
    def _fix_range_clipping(self, 
                          error: ValidationIssue, 
                          df: pd.DataFrame,
                          schema: Dict[str, Any],
                          context: Optional[Dict[str, Any]]) -> Any:
        """Clip values to valid range."""
        if not error.location:
            raise ValueError("No column location for range clipping")
        
        column = error.location
        ranges = schema.get('value_ranges', {}).get(column, {})
        
        if not ranges:
            raise ValueError(f"No ranges defined for column: {column}")
        
        series = df[column].copy()
        
        if 'min' in ranges:
            series = series.clip(lower=ranges['min'])
        
        if 'max' in ranges:
            series = series.clip(upper=ranges['max'])
        
        return series
    
    def _fix_invalid_values(self, 
                          error: ValidationIssue, 
                          df: pd.DataFrame,
                          schema: Dict[str, Any],
                          context: Optional[Dict[str, Any]]) -> Any:
        """Replace invalid values with defaults."""
        if not error.location:
            raise ValueError("No column location for value replacement")
        
        column = error.location
        allowed_values = schema.get('allowed_values', {}).get(column, [])
        
        if not allowed_values:
            raise ValueError(f"No allowed values defined for column: {column}")
        
        series = df[column].copy()
        
        # Replace invalid values with the first allowed value (or most common)
        default_value = allowed_values[0] if allowed_values else 'UNKNOWN'
        mask = ~series.isin(allowed_values)
        series.loc[mask] = default_value
        
        return series
    
    def _fix_duplicates(self, 
                       error: ValidationIssue, 
                       df: pd.DataFrame,
                       schema: Dict[str, Any],
                       context: Optional[Dict[str, Any]]) -> Any:
        """Remove duplicate records."""
        if not error.location:
            # Handle unique combinations
            unique_combinations = schema.get('unique_combinations', [])
            if unique_combinations:
                # Return indices of rows to keep (drop duplicates)
                return df.drop_duplicates(subset=unique_combinations[0]).index
        else:
            # Handle single column duplicates
            column = error.location
            return df.drop_duplicates(subset=[column]).index
        
        raise ValueError("Cannot determine duplicate removal strategy")
    
    def create_error_report(self, 
                          errors: List[ValidationIssue],
                          recovery_results: List[ErrorRecoveryResult],
                          output_file: Optional[Path] = None) -> Dict[str, Any]:
        """
        Create comprehensive error and recovery report.
        
        Args:
            errors: List of validation errors
            recovery_results: List of recovery results
            output_file: Optional file to save report
            
        Returns:
            Error report dictionary
        """
        # Categorize errors
        error_categories = {}
        for error in errors:
            error_type = self.categorize_error(error)
            if error_type not in error_categories:
                error_categories[error_type] = []
            error_categories[error_type].append(error)
        
        # Create report
        report = {
            "timestamp": time.time(),
            "summary": {
                "total_errors": len(errors),
                "total_recoveries_attempted": len(recovery_results),
                "successful_recoveries": sum(1 for r in recovery_results if r.success),
                "failed_recoveries": sum(1 for r in recovery_results if not r.success),
                "data_modifications": sum(1 for r in recovery_results if r.data_modified)
            },
            "error_breakdown": {
                error_type.value: len(error_list) 
                for error_type, error_list in error_categories.items()
            },
            "recovery_strategies": {
                strategy.value: sum(1 for r in recovery_results if r.strategy_used == strategy)
                for strategy in ErrorRecoveryStrategy
            },
            "detailed_errors": [
                {
                    "error": error.to_dict(),
                    "category": self.categorize_error(error).value,
                    "recovery_attempted": i < len(recovery_results),
                    "recovery_result": recovery_results[i].success if i < len(recovery_results) else None
                }
                for i, error in enumerate(errors)
            ],
            "recovery_details": [
                {
                    "strategy": result.strategy_used.value,
                    "success": result.success,
                    "message": result.recovery_message,
                    "data_modified": result.data_modified
                }
                for result in recovery_results
            ],
            "performance_stats": self.get_recovery_stats()
        }
        
        # Save to file if requested
        if output_file:
            output_file.parent.mkdir(parents=True, exist_ok=True)
            with open(output_file, 'w') as f:
                json.dump(report, f, indent=2, default=str)
            self.logger.info(f"Error report saved to {output_file}")
        
        return report
    
    def get_recovery_stats(self) -> Dict[str, Any]:
        """Get recovery statistics."""
        stats = self.recovery_stats.copy()
        
        if stats["errors_encountered"] > 0:
            stats["recovery_success_rate"] = (
                stats["successful_recoveries"] / stats["errors_encountered"]
            )
        else:
            stats["recovery_success_rate"] = 0
        
        return stats
    
    def reset_recovery_stats(self):
        """Reset recovery statistics."""
        self.recovery_stats = {
            "errors_encountered": 0,
            "successful_recoveries": 0,
            "failed_recoveries": 0,
            "automatic_fixes": 0,
            "manual_interventions": 0,
            "recovery_by_strategy": {strategy.value: 0 for strategy in ErrorRecoveryStrategy},
            "recovery_by_error_type": {error_type.value: 0 for error_type in ErrorType}
        }
        self.logger.info("Recovery statistics reset")


# Convenience functions

def create_error_recovery_system(config: Optional[Dict[str, Any]] = None) -> ValidationErrorRecovery:
    """Create error recovery system with configuration."""
    return ValidationErrorRecovery(config)


def recover_validation_errors(errors: List[ValidationIssue],
                            df: pd.DataFrame,
                            schema: Dict[str, Any],
                            recovery_system: Optional[ValidationErrorRecovery] = None) -> Tuple[List[ErrorRecoveryResult], pd.DataFrame]:
    """
    Recover from validation errors.
    
    Args:
        errors: List of validation errors
        df: DataFrame being validated
        schema: Validation schema
        recovery_system: Optional recovery system (creates default if None)
        
    Returns:
        Tuple of (recovery_results, potentially_modified_dataframe)
    """
    if recovery_system is None:
        recovery_system = ValidationErrorRecovery()
    
    recovery_results = []
    modified_df = df.copy()
    
    for error in errors:
        result = recovery_system.recover_from_error(error, modified_df, schema)
        recovery_results.append(result)
        
        # Apply data modifications if successful
        if result.success and result.data_modified and result.fixed_value is not None:
            if error.location and error.location in modified_df.columns:
                modified_df[error.location] = result.fixed_value
    
    return recovery_results, modified_df


# Export public symbols
__all__ = [
    'ErrorRecoveryStrategy', 'ErrorType', 'ErrorPattern', 'ErrorRecoveryResult',
    'ValidationErrorRecovery', 'create_error_recovery_system', 'recover_validation_errors'
]