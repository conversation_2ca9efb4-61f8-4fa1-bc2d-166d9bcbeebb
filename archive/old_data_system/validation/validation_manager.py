"""
Data Validation Manager for Yemen Trade Diagnostic V2

Central manager for data validation operations, coordinating
validation rules, helpers, and V2 interfaces.
"""

# Standard library imports
import json
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

# Third-party imports
import pandas as pd

# Project imports
# Internal validation imports
from yemen_trade_diagnostic.data.validation.validation_helper import DataValidationHelper
from yemen_trade_diagnostic.data.validation.validation_rules import (
    DataValidationRule,
    RuleType,
    ValidationRuleSet,
)
from yemen_trade_diagnostic.interfaces.cache_interface import CacheLevel, cache_get, cache_set
from yemen_trade_diagnostic.interfaces.error_interface import (
    ErrorCategory,
    ErrorSeverity,
    with_error_handling,
)
from yemen_trade_diagnostic.interfaces.hardware_interface import get_hardware_manager
from yemen_trade_diagnostic.interfaces.logging_interface import get_logger

# V2 interface imports
from yemen_trade_diagnostic.interfaces.validation_interface import ValidationIssueLevel
from yemen_trade_diagnostic.interfaces.validation_interface import (
    ValidationManager as BaseValidationManager,
)
from yemen_trade_diagnostic.interfaces.validation_interface import (
    ValidationMode,
    ValidationResult,
    create_validation_report,
    validate_schema,
)

logger = get_logger(__name__)


class DataValidationManager:
    """
    Central manager for data validation operations.
    
    Coordinates validation rules, helpers, and integrates with V2 interfaces
    for comprehensive data quality assurance.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.base_manager = BaseValidationManager()
        self.helper = DataValidationHelper()
        self.hw_manager = get_hardware_manager()
        self.logger = logger
        
        # Rule sets cache
        self.rule_sets: Dict[str, ValidationRuleSet] = {}
        self._initialize_default_rulesets()
        
        # Validation cache
        self.cache_enabled = self.config.get("cache_validations", True)
        self.cache_ttl = self.config.get("cache_ttl", 3600)  # 1 hour
    
    def _initialize_default_rulesets(self):
        """Initialize default validation rule sets."""
        # BACI trade data rules
        self.rule_sets["baci"] = ValidationRuleSet.create_baci_ruleset()
        
        # World Bank data rules
        wb_ruleset = ValidationRuleSet("World Bank Data", ValidationMode.STANDARD)
        wb_ruleset.add_rule(DataValidationRule(
            name="required_columns",
            rule_type=RuleType.REQUIRED_COLUMN,
            column="indicator_code"
        ))
        self.rule_sets["worldbank"] = wb_ruleset
        
        # Yemen-specific rules
        yemen_ruleset = ValidationRuleSet("Yemen Trade Data", ValidationMode.STANDARD)
        yemen_ruleset.add_rule(DataValidationRule(
            name="yemen_country_code",
            rule_type=RuleType.CUSTOM,
            custom_validator=self._validate_yemen_code,
            error_message="Yemen country code (YEM) not found in data"
        ))
        self.rule_sets["yemen"] = yemen_ruleset
    
    def _validate_yemen_code(self, df: pd.DataFrame, params: Dict[str, Any]) -> bool:
        """Custom validator for Yemen country code."""
        exporter_col = params.get("exporter_col", "i")
        importer_col = params.get("importer_col", "j")
        
        has_yemen_export = False
        has_yemen_import = False
        
        if exporter_col in df.columns:
            has_yemen_export = "YEM" in df[exporter_col].values
        
        if importer_col in df.columns:
            has_yemen_import = "YEM" in df[importer_col].values
        
        return has_yemen_export or has_yemen_import
    
    @with_error_handling(
        category=ErrorCategory.VALIDATION,
        severity=ErrorSeverity.ERROR,
        fallback_value=ValidationResult()
    )
    def validate_dataset(self, df: pd.DataFrame, dataset_type: str,
                        mode: Optional[ValidationMode] = None,
                        use_cache: bool = True) -> ValidationResult:
        """
        Validate a dataset using appropriate rules.
        
        Args:
            df: DataFrame to validate
            dataset_type: Type of dataset (baci, worldbank, yemen)
            mode: Validation mode to use
            use_cache: Whether to use cached results
            
        Returns:
            ValidationResult with all issues found
        """
        # Check cache first
        if use_cache and self.cache_enabled:
            cache_key = f"validation_{dataset_type}_{df.shape}_{df.columns.tolist()}"
            cached_result = cache_get(cache_key, level=CacheLevel.MEMORY)
            if cached_result is not None:
                self.logger.info(f"Using cached validation result for {dataset_type}")
                return cached_result
        
        self.logger.info(f"Validating {dataset_type} dataset with {len(df)} rows")
        
        # Get appropriate ruleset
        if dataset_type not in self.rule_sets:
            self.logger.warning(f"No ruleset found for dataset type '{dataset_type}', using basic validation")
            return self.helper.validate_full_dataset(df, dataset_type)
        
        ruleset = self.rule_sets[dataset_type]
        
        # Override mode if specified
        if mode is not None:
            original_mode = ruleset.mode
            ruleset.mode = mode
        
        try:
            # Run validation
            result = ruleset.validate(df)
            
            # Add dataset info
            result.context["dataset_type"] = dataset_type
            result.context["row_count"] = len(df)
            result.context["column_count"] = len(df.columns)
            
            # Additional checks from helper
            if dataset_type == "baci":
                helper_result = self.helper.validate_full_dataset(df, "baci")
                result.merge(helper_result)
            
            # Cache result
            if use_cache and self.cache_enabled:
                cache_set(cache_key, result, level=CacheLevel.MEMORY, ttl=self.cache_ttl)
            
            return result
            
        finally:
            # Restore original mode
            if mode is not None:
                ruleset.mode = original_mode
    
    def add_ruleset(self, name: str, ruleset: ValidationRuleSet):
        """Add a custom validation ruleset."""
        self.rule_sets[name] = ruleset
        self.logger.info(f"Added ruleset '{name}' with {len(ruleset.rules)} rules")
    
    def get_ruleset(self, name: str) -> Optional[ValidationRuleSet]:
        """Get a validation ruleset by name."""
        return self.rule_sets.get(name)
    
    def validate_file(self, file_path: Union[str, Path], dataset_type: str,
                     mode: Optional[ValidationMode] = None) -> ValidationResult:
        """
        Validate a data file.
        
        Args:
            file_path: Path to file to validate
            dataset_type: Type of dataset
            mode: Validation mode to use
            
        Returns:
            ValidationResult with issues found
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            result = ValidationResult()
            result.add_issue(
                ValidationIssueLevel.ERROR,
                f"File not found: {file_path}",
                {"file_path": str(file_path)}
            )
            return result
        
        try:
            # Read file based on extension
            if file_path.suffix == ".csv":
                df = pd.read_csv(file_path)
            elif file_path.suffix == ".parquet":
                df = pd.read_parquet(file_path)
            else:
                result = ValidationResult()
                result.add_issue(
                    ValidationIssueLevel.ERROR,
                    f"Unsupported file type: {file_path.suffix}",
                    {"file_path": str(file_path)}
                )
                return result
            
            # Validate data
            result = self.validate_dataset(df, dataset_type, mode)
            result.context["file_path"] = str(file_path)
            
            return result
            
        except Exception as e:
            result = ValidationResult()
            result.add_issue(
                ValidationIssueLevel.ERROR,
                f"Error reading file: {str(e)}",
                {"file_path": str(file_path), "error": str(e)}
            )
            return result
    
    def create_validation_report(self, results: Union[ValidationResult, List[ValidationResult]],
                               output_path: Optional[Union[str, Path]] = None) -> Dict[str, Any]:
        """
        Create a comprehensive validation report.
        
        Args:
            results: Validation results to report on
            output_path: Optional path to save report
            
        Returns:
            Dict containing report data
        """
        if isinstance(results, ValidationResult):
            results = [results]
        
        report = {
            "summary": {
                "total_validations": len(results),
                "passed": sum(1 for r in results if r.is_valid()),
                "failed": sum(1 for r in results if not r.is_valid()),
                "total_issues": sum(len(r.issues) for r in results),
                "issue_breakdown": {
                    "info": 0,
                    "warning": 0,
                    "error": 0,
                    "critical": 0
                }
            },
            "details": []
        }
        
        # Count issues by level
        for result in results:
            for issue in result.issues:
                level = issue["level"].value
                report["summary"]["issue_breakdown"][level] += 1
            
            # Add detailed result
            report["details"].append({
                "valid": result.is_valid(),
                "issue_count": len(result.issues),
                "context": result.context,
                "issues": result.issues
            })
        
        # Save report if path provided
        if output_path:
            output_path = Path(output_path)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            if output_path.suffix == ".json":
                with open(output_path, 'w') as f:
                    json.dump(report, f, indent=2, default=str)
            else:
                # Create text report
                with open(output_path, 'w') as f:
                    f.write(self._format_text_report(report))
            
            self.logger.info(f"Validation report saved to {output_path}")
        
        return report
    
    def _format_text_report(self, report: Dict[str, Any]) -> str:
        """Format report as human-readable text."""
        lines = [
            "Yemen Trade Diagnostic - Data Validation Report",
            "=" * 45,
            "",
            "SUMMARY",
            f"Total Validations: {report['summary']['total_validations']}",
            f"Passed: {report['summary']['passed']}",
            f"Failed: {report['summary']['failed']}",
            f"Total Issues: {report['summary']['total_issues']}",
            "",
            "Issue Breakdown:",
        ]
        
        for level, count in report["summary"]["issue_breakdown"].items():
            lines.append(f"  {level.upper()}: {count}")
        
        lines.extend(["", "DETAILED RESULTS", "-" * 45])
        
        for i, detail in enumerate(report["details"]):
            lines.extend([
                f"\nValidation {i+1}:",
                f"  Valid: {detail['valid']}",
                f"  Issues: {detail['issue_count']}",
                f"  Context: {detail['context']}",
            ])
            
            if detail["issues"]:
                lines.append("  Issues:")
                for issue in detail["issues"]:
                    lines.append(f"    [{issue['level']}] {issue['message']}")
        
        return "\n".join(lines)