"""
Data Validation Rules for Yemen Trade Diagnostic V2

Provides a rule-based validation system for trade data with 
hardware acceleration support.
"""

# Standard library imports
from dataclasses import dataclass, field
from enum import Enum
from typing import Any, Callable, Dict, List, Optional, Union

# Third-party imports
import numpy as np
import pandas as pd

# Project imports
from yemen_trade_diagnostic.interfaces.error_interface import (
    Error<PERSON>ategory,
    ErrorSeverity,
    with_error_handling,
)
from yemen_trade_diagnostic.interfaces.hardware_interface import get_hardware_manager
from yemen_trade_diagnostic.interfaces.logging_interface import get_logger

# V2 interface imports
from yemen_trade_diagnostic.interfaces.validation_interface import (
    ValidationIssueLevel,
    ValidationMode,
    ValidationResult,
)

logger = get_logger(__name__)


class RuleType(Enum):
    """Types of validation rules."""
    REQUIRED_COLUMN = "required_column"
    DATA_TYPE = "data_type"
    VALUE_RANGE = "value_range"
    REGEX_PATTERN = "regex_pattern"
    UNIQUE_VALUES = "unique_values"
    FOREIGN_KEY = "foreign_key"
    CONSISTENCY = "consistency"
    CUSTOM = "custom"


@dataclass
class DataValidationRule:
    """Individual validation rule definition."""
    name: str
    rule_type: RuleType
    column: Optional[str] = None
    parameters: Dict[str, Any] = field(default_factory=dict)
    error_message: Optional[str] = None
    severity: ValidationIssueLevel = ValidationIssueLevel.ERROR
    enabled: bool = True
    custom_validator: Optional[Callable] = None
    
    def validate(self, df: pd.DataFrame) -> ValidationResult:
        """Execute this rule on a dataframe."""
        result = ValidationResult()
        
        if not self.enabled:
            return result
        
        try:
            if self.rule_type == RuleType.REQUIRED_COLUMN:
                self._validate_required_column(df, result)
            elif self.rule_type == RuleType.DATA_TYPE:
                self._validate_data_type(df, result)
            elif self.rule_type == RuleType.VALUE_RANGE:
                self._validate_value_range(df, result)
            elif self.rule_type == RuleType.REGEX_PATTERN:
                self._validate_regex_pattern(df, result)
            elif self.rule_type == RuleType.UNIQUE_VALUES:
                self._validate_unique_values(df, result)
            elif self.rule_type == RuleType.FOREIGN_KEY:
                self._validate_foreign_key(df, result)
            elif self.rule_type == RuleType.CONSISTENCY:
                self._validate_consistency(df, result)
            elif self.rule_type == RuleType.CUSTOM:
                self._validate_custom(df, result)
        except Exception as e:
            result.add_issue(
                ValidationIssueLevel.ERROR,
                f"Error executing rule '{self.name}': {str(e)}",
                {"rule": self.name, "error": str(e)}
            )
        
        return result
    
    def _validate_required_column(self, df: pd.DataFrame, result: ValidationResult):
        """Validate required column exists."""
        if self.column not in df.columns:
            result.add_issue(
                self.severity,
                self.error_message or f"Required column '{self.column}' is missing",
                {"column": self.column, "available_columns": list(df.columns)}
            )
    
    def _validate_data_type(self, df: pd.DataFrame, result: ValidationResult):
        """Validate column data type."""
        if self.column not in df.columns:
            return
        
        expected_type = self.parameters.get("expected_type")
        actual_type = str(df[self.column].dtype)
        
        type_matches = False
        if expected_type == "numeric":
            type_matches = pd.api.types.is_numeric_dtype(df[self.column])
        elif expected_type == "string":
            type_matches = pd.api.types.is_string_dtype(df[self.column])
        elif expected_type == "datetime":
            type_matches = pd.api.types.is_datetime64_dtype(df[self.column])
        else:
            type_matches = actual_type == expected_type
        
        if not type_matches:
            result.add_issue(
                self.severity,
                self.error_message or f"Column '{self.column}' has incorrect data type",
                {
                    "column": self.column,
                    "expected_type": expected_type,
                    "actual_type": actual_type
                }
            )
    
    def _validate_value_range(self, df: pd.DataFrame, result: ValidationResult):
        """Validate values are within specified range."""
        if self.column not in df.columns:
            return
        
        min_value = self.parameters.get("min")
        max_value = self.parameters.get("max")
        
        if min_value is not None:
            invalid_count = (df[self.column] < min_value).sum()
            if invalid_count > 0:
                result.add_issue(
                    self.severity,
                    self.error_message or f"Column '{self.column}' has {invalid_count} values below minimum",
                    {
                        "column": self.column,
                        "min_value": min_value,
                        "invalid_count": int(invalid_count)
                    }
                )
        
        if max_value is not None:
            invalid_count = (df[self.column] > max_value).sum()
            if invalid_count > 0:
                result.add_issue(
                    self.severity,
                    self.error_message or f"Column '{self.column}' has {invalid_count} values above maximum",
                    {
                        "column": self.column,
                        "max_value": max_value,
                        "invalid_count": int(invalid_count)
                    }
                )
    
    def _validate_regex_pattern(self, df: pd.DataFrame, result: ValidationResult):
        """Validate values match regex pattern."""
        if self.column not in df.columns:
            return
        
        pattern = self.parameters.get("pattern")
        if not pattern:
            return
        
        # Convert to string and check pattern
        str_series = df[self.column].astype(str)
        invalid_mask = ~str_series.str.match(pattern, na=False)
        invalid_count = invalid_mask.sum()
        
        if invalid_count > 0:
            sample_invalid = df.loc[invalid_mask, self.column].head(5).tolist()
            result.add_issue(
                self.severity,
                self.error_message or f"Column '{self.column}' has {invalid_count} values not matching pattern",
                {
                    "column": self.column,
                    "pattern": pattern,
                    "invalid_count": int(invalid_count),
                    "sample": sample_invalid
                }
            )
    
    def _validate_unique_values(self, df: pd.DataFrame, result: ValidationResult):
        """Validate unique value constraints."""
        if self.column not in df.columns:
            return
        
        if self.parameters.get("must_be_unique", False):
            duplicates = df[self.column].duplicated().sum()
            if duplicates > 0:
                result.add_issue(
                    self.severity,
                    self.error_message or f"Column '{self.column}' has {duplicates} duplicate values",
                    {
                        "column": self.column,
                        "duplicate_count": int(duplicates)
                    }
                )
    
    def _validate_foreign_key(self, df: pd.DataFrame, result: ValidationResult):
        """Validate foreign key relationships."""
        if self.column not in df.columns:
            return
        
        reference_values = self.parameters.get("reference_values", set())
        invalid_values = set(df[self.column].unique()) - set(reference_values)
        
        if invalid_values:
            result.add_issue(
                self.severity,
                self.error_message or f"Column '{self.column}' has invalid foreign key values",
                {
                    "column": self.column,
                    "invalid_count": len(invalid_values),
                    "sample": list(invalid_values)[:10]
                }
            )
    
    def _validate_consistency(self, df: pd.DataFrame, result: ValidationResult):
        """Validate consistency between columns."""
        columns = self.parameters.get("columns", [])
        if len(columns) < 2:
            return
        
        # Example: Check if value > 0 when quantity > 0
        if "value_quantity_consistency" in self.parameters:
            value_col = self.parameters["value_column"]
            quantity_col = self.parameters["quantity_column"]
            
            if value_col in df.columns and quantity_col in df.columns:
                inconsistent = df[(df[quantity_col] > 0) & (df[value_col] <= 0)]
                if len(inconsistent) > 0:
                    result.add_issue(
                        self.severity,
                        self.error_message or "Inconsistent value/quantity relationship",
                        {
                            "inconsistent_count": len(inconsistent),
                            "sample": inconsistent.head(3).to_dict('records')
                        }
                    )
    
    def _validate_custom(self, df: pd.DataFrame, result: ValidationResult):
        """Run custom validation function."""
        if self.custom_validator:
            custom_result = self.custom_validator(df, self.parameters)
            if isinstance(custom_result, ValidationResult):
                result.merge(custom_result)
            elif isinstance(custom_result, bool) and not custom_result:
                result.add_issue(
                    self.severity,
                    self.error_message or "Custom validation failed",
                    {"rule": self.name}
                )


class ValidationRuleSet:
    """Collection of validation rules for a dataset."""
    
    def __init__(self, name: str, mode: ValidationMode = ValidationMode.STANDARD):
        self.name = name
        self.mode = mode
        self.rules: List[DataValidationRule] = []
        self.hw_manager = get_hardware_manager()
        self.logger = logger
    
    def add_rule(self, rule: DataValidationRule):
        """Add a validation rule to the set."""
        self.rules.append(rule)
        self.logger.debug(f"Added rule '{rule.name}' to ruleset '{self.name}'")
    
    def remove_rule(self, rule_name: str):
        """Remove a rule by name."""
        self.rules = [r for r in self.rules if r.name != rule_name]
    
    def get_rule(self, rule_name: str) -> Optional[DataValidationRule]:
        """Get a rule by name."""
        for rule in self.rules:
            if rule.name == rule_name:
                return rule
        return None
    
    @with_error_handling(
        category=ErrorCategory.VALIDATION,
        severity=ErrorSeverity.ERROR,
        fallback_value=ValidationResult()
    )
    def validate(self, df: pd.DataFrame) -> ValidationResult:
        """Execute all rules in the set."""
        result = ValidationResult()
        
        self.logger.info(f"Executing validation ruleset '{self.name}' with {len(self.rules)} rules")
        
        # Execute rules based on mode
        for rule in self.rules:
            try:
                rule_result = rule.validate(df)
                result.merge(rule_result)
                
                # In strict mode, stop on first error
                if self.mode == ValidationMode.STRICT and not rule_result.is_valid():
                    self.logger.warning(f"Stopping validation in strict mode due to failed rule '{rule.name}'")
                    break
                    
            except Exception as e:
                self.logger.error(f"Error executing rule '{rule.name}': {str(e)}")
                if self.mode == ValidationMode.STRICT:
                    raise
        
        # Add summary
        result.context["ruleset"] = self.name
        result.context["rules_executed"] = len(self.rules)
        result.context["mode"] = self.mode.value
        
        return result
    
    @classmethod
    def create_baci_ruleset(cls) -> 'ValidationRuleSet':
        """Create a standard BACI validation ruleset."""
        ruleset = cls("BACI Trade Data", ValidationMode.STANDARD)
        
        # Required columns
        for col in ['t', 'i', 'j', 'k', 'v', 'q']:
            ruleset.add_rule(DataValidationRule(
                name=f"required_{col}",
                rule_type=RuleType.REQUIRED_COLUMN,
                column=col,
                severity=ValidationIssueLevel.ERROR
            ))
        
        # Data types
        ruleset.add_rule(DataValidationRule(
            name="year_numeric",
            rule_type=RuleType.DATA_TYPE,
            column='t',
            parameters={"expected_type": "numeric"}
        ))
        
        # Value ranges
        ruleset.add_rule(DataValidationRule(
            name="year_range",
            rule_type=RuleType.VALUE_RANGE,
            column='t',
            parameters={"min": 2000, "max": 2025},
            severity=ValidationIssueLevel.WARNING
        ))
        
        ruleset.add_rule(DataValidationRule(
            name="positive_values",
            rule_type=RuleType.VALUE_RANGE,
            column='v',
            parameters={"min": 0}
        ))
        
        # Pattern matching
        ruleset.add_rule(DataValidationRule(
            name="product_code_format",
            rule_type=RuleType.REGEX_PATTERN,
            column='k',
            parameters={"pattern": r'^\d{6}$'},
            severity=ValidationIssueLevel.WARNING
        ))
        
        # Consistency
        ruleset.add_rule(DataValidationRule(
            name="value_quantity_consistency",
            rule_type=RuleType.CONSISTENCY,
            parameters={
                "value_quantity_consistency": True,
                "value_column": "v",
                "quantity_column": "q"
            },
            severity=ValidationIssueLevel.WARNING
        ))
        
        return ruleset