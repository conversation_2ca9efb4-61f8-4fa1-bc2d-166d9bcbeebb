"""
Validation Performance Monitoring and Analytics

Provides real-time monitoring, performance analytics, and alerting
for the validation system.
"""

# Standard library imports
import json
import statistics
import threading
import time
from collections import defaultdict, deque
from dataclasses import asdict, dataclass
from datetime import datetime, timedelta
from enum import Enum
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional, Union

# Project imports
from yemen_trade_diagnostic.interfaces.cache_interface import CacheLevel, get_cache_manager

# V2 interface imports
from yemen_trade_diagnostic.interfaces.logging_interface import get_logger

logger = get_logger(__name__)


class AlertLevel(Enum):
    """Alert severity levels."""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


@dataclass
class ValidationMetric:
    """Single validation performance metric."""
    timestamp: float
    operation: str
    duration: float
    rows_processed: int
    throughput: float
    errors_found: int
    memory_usage_mb: float
    cpu_percent: float
    success: bool
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


@dataclass
class ValidationAlert:
    """Validation system alert."""
    timestamp: float
    level: AlertLevel
    message: str
    metric_name: str
    current_value: float
    threshold_value: float
    context: Dict[str, Any] = None
    resolved: bool = False
    resolved_at: Optional[float] = None
    
    def __post_init__(self):
        if self.context is None:
            self.context = {}


class ValidationMonitor:
    """
    Real-time monitoring and analytics for validation performance.
    
    Features:
    - Performance metrics collection
    - Real-time throughput monitoring
    - Memory and CPU usage tracking
    - Threshold-based alerting
    - Historical performance analysis
    - Bottleneck detection
    """
    
    def __init__(self, 
                 config: Optional[Dict[str, Any]] = None,
                 alert_callback: Optional[Callable[[ValidationAlert], None]] = None):
        """
        Initialize validation monitor.
        
        Args:
            config: Monitoring configuration
            alert_callback: Optional callback for alerts
        """
        self.config = config or {}
        self.logger = logger
        # Initialize cache manager lazily to avoid circular dependencies
        self._cache_manager = None
        self.alert_callback = alert_callback
        
        # Metrics storage (in-memory circular buffers)
        self.max_metrics = self.config.get('max_metrics_stored', 10000)
        self.metrics = deque(maxlen=self.max_metrics)
        self.alerts = deque(maxlen=1000)
        
        # Real-time monitoring
        self.current_operations: Dict[str, ValidationMetric] = {}
        self.operation_lock = threading.Lock()
        
        # Performance thresholds
        self.thresholds = {
            'min_throughput': self.config.get('min_throughput', 1000),  # rows/sec
            'max_validation_time': self.config.get('max_validation_time', 300),  # seconds
            'max_memory_usage': self.config.get('max_memory_usage', 2048),  # MB
            'max_cpu_percent': self.config.get('max_cpu_percent', 80),  # percent
            'max_error_rate': self.config.get('max_error_rate', 0.05),  # 5%
            'max_consecutive_failures': self.config.get('max_consecutive_failures', 3)
        }
        
        # Analytics
        self.analytics_enabled = self.config.get('analytics_enabled', True)
        self.analytics_cache_ttl = self.config.get('analytics_cache_ttl', 300)  # 5 minutes
        
        # State tracking
        self.consecutive_failures = 0
        self.last_success_time = time.time()
        self.monitoring_start_time = time.time()
        
        self.logger.info("Initialized ValidationMonitor with real-time analytics")
    
    @property
    def cache_manager(self):
        """Lazy initialization of cache manager to avoid circular dependencies."""
        if self._cache_manager is None:
            try:
                self._cache_manager = get_cache_manager()
            except:
                # If cache manager fails to initialize, create a simple fallback
                self._cache_manager = None
                self.logger.warning("Cache manager initialization failed, disabling caching")
        return self._cache_manager
    
    def start_operation(self, operation_id: str, operation_type: str, rows_to_process: int = 0) -> str:
        """
        Start monitoring a validation operation.
        
        Args:
            operation_id: Unique identifier for operation
            operation_type: Type of validation operation
            rows_to_process: Number of rows to be processed
            
        Returns:
            Operation tracking ID
        """
        with self.operation_lock:
            metric = ValidationMetric(
                timestamp=time.time(),
                operation=operation_type,
                duration=0,
                rows_processed=rows_to_process,
                throughput=0,
                errors_found=0,
                memory_usage_mb=0,
                cpu_percent=0,
                success=False,
                metadata={'operation_id': operation_id, 'status': 'started'}
            )
            
            self.current_operations[operation_id] = metric
            self.logger.debug(f"Started monitoring operation: {operation_id} ({operation_type})")
            
            return operation_id
    
    def update_operation(self, 
                        operation_id: str, 
                        rows_processed: Optional[int] = None,
                        errors_found: Optional[int] = None,
                        memory_usage_mb: Optional[float] = None,
                        cpu_percent: Optional[float] = None,
                        metadata: Optional[Dict[str, Any]] = None):
        """
        Update metrics for ongoing operation.
        
        Args:
            operation_id: Operation identifier
            rows_processed: Number of rows processed so far
            errors_found: Number of errors found
            memory_usage_mb: Current memory usage
            cpu_percent: Current CPU usage
            metadata: Additional metadata to update
        """
        with self.operation_lock:
            if operation_id not in self.current_operations:
                self.logger.warning(f"Operation {operation_id} not found for update")
                return
            
            metric = self.current_operations[operation_id]
            current_time = time.time()
            
            # Update values if provided
            if rows_processed is not None:
                metric.rows_processed = rows_processed
            
            if errors_found is not None:
                metric.errors_found = errors_found
            
            if memory_usage_mb is not None:
                metric.memory_usage_mb = memory_usage_mb
            
            if cpu_percent is not None:
                metric.cpu_percent = cpu_percent
            
            # Calculate current duration and throughput
            metric.duration = current_time - metric.timestamp
            if metric.duration > 0 and metric.rows_processed > 0:
                metric.throughput = metric.rows_processed / metric.duration
            
            # Update metadata
            if metadata:
                metric.metadata.update(metadata)
            
            metric.metadata['status'] = 'running'
            metric.metadata['last_update'] = current_time
    
    def finish_operation(self, 
                        operation_id: str, 
                        success: bool = True,
                        final_rows_processed: Optional[int] = None,
                        final_errors_found: Optional[int] = None,
                        metadata: Optional[Dict[str, Any]] = None):
        """
        Finish monitoring an operation and store final metrics.
        
        Args:
            operation_id: Operation identifier
            success: Whether operation succeeded
            final_rows_processed: Final number of rows processed
            final_errors_found: Final number of errors found
            metadata: Final metadata
        """
        with self.operation_lock:
            if operation_id not in self.current_operations:
                self.logger.warning(f"Operation {operation_id} not found for completion")
                return
            
            metric = self.current_operations[operation_id]
            current_time = time.time()
            
            # Update final values
            metric.success = success
            metric.duration = current_time - metric.timestamp
            
            if final_rows_processed is not None:
                metric.rows_processed = final_rows_processed
            
            if final_errors_found is not None:
                metric.errors_found = final_errors_found
            
            # Calculate final throughput
            if metric.duration > 0 and metric.rows_processed > 0:
                metric.throughput = metric.rows_processed / metric.duration
            
            # Update metadata
            metric.metadata['status'] = 'completed' if success else 'failed'
            metric.metadata['completed_at'] = current_time
            
            if metadata:
                metric.metadata.update(metadata)
            
            # Store completed metric
            self.metrics.append(metric)
            
            # Remove from current operations
            del self.current_operations[operation_id]
            
            # Update failure tracking
            if success:
                self.consecutive_failures = 0
                self.last_success_time = current_time
            else:
                self.consecutive_failures += 1
            
            # Check thresholds and generate alerts
            self._check_thresholds(metric)
            
            self.logger.info(f"Completed operation {operation_id}: "
                           f"{metric.rows_processed} rows in {metric.duration:.3f}s "
                           f"({metric.throughput:.0f} rows/sec) - {'SUCCESS' if success else 'FAILED'}")
    
    def _check_thresholds(self, metric: ValidationMetric):
        """Check performance thresholds and generate alerts."""
        current_time = time.time()
        
        # Check throughput threshold
        if (metric.throughput > 0 and 
            metric.throughput < self.thresholds['min_throughput']):
            self._create_alert(
                AlertLevel.WARNING,
                f"Low validation throughput: {metric.throughput:.0f} rows/sec",
                "throughput",
                metric.throughput,
                self.thresholds['min_throughput'],
                {"operation": metric.operation, "duration": metric.duration}
            )
        
        # Check validation time threshold
        if metric.duration > self.thresholds['max_validation_time']:
            self._create_alert(
                AlertLevel.WARNING,
                f"Validation took too long: {metric.duration:.1f}s",
                "validation_time",
                metric.duration,
                self.thresholds['max_validation_time'],
                {"operation": metric.operation, "rows": metric.rows_processed}
            )
        
        # Check memory usage threshold
        if metric.memory_usage_mb > self.thresholds['max_memory_usage']:
            self._create_alert(
                AlertLevel.WARNING,
                f"High memory usage: {metric.memory_usage_mb:.1f}MB",
                "memory_usage",
                metric.memory_usage_mb,
                self.thresholds['max_memory_usage'],
                {"operation": metric.operation}
            )
        
        # Check CPU usage threshold
        if metric.cpu_percent > self.thresholds['max_cpu_percent']:
            self._create_alert(
                AlertLevel.WARNING,
                f"High CPU usage: {metric.cpu_percent:.1f}%",
                "cpu_usage",
                metric.cpu_percent,
                self.thresholds['max_cpu_percent'],
                {"operation": metric.operation}
            )
        
        # Check error rate threshold
        if metric.rows_processed > 0:
            error_rate = metric.errors_found / metric.rows_processed
            if error_rate > self.thresholds['max_error_rate']:
                self._create_alert(
                    AlertLevel.ERROR,
                    f"High error rate: {error_rate:.2%}",
                    "error_rate",
                    error_rate,
                    self.thresholds['max_error_rate'],
                    {"operation": metric.operation, "errors": metric.errors_found}
                )
        
        # Check consecutive failures
        if self.consecutive_failures >= self.thresholds['max_consecutive_failures']:
            self._create_alert(
                AlertLevel.CRITICAL,
                f"Multiple consecutive failures: {self.consecutive_failures}",
                "consecutive_failures",
                self.consecutive_failures,
                self.thresholds['max_consecutive_failures'],
                {"time_since_last_success": current_time - self.last_success_time}
            )
    
    def _create_alert(self, 
                     level: AlertLevel, 
                     message: str, 
                     metric_name: str,
                     current_value: float, 
                     threshold_value: float,
                     context: Optional[Dict[str, Any]] = None):
        """Create and store alert."""
        alert = ValidationAlert(
            timestamp=time.time(),
            level=level,
            message=message,
            metric_name=metric_name,
            current_value=current_value,
            threshold_value=threshold_value,
            context=context or {}
        )
        
        self.alerts.append(alert)
        
        # Log alert
        log_level = {
            AlertLevel.INFO: logger.info,
            AlertLevel.WARNING: logger.warning,
            AlertLevel.ERROR: logger.error,
            AlertLevel.CRITICAL: logger.critical
        }[level]
        
        log_level(f"VALIDATION ALERT [{level.value.upper()}]: {message}")
        
        # Call alert callback if provided
        if self.alert_callback:
            try:
                self.alert_callback(alert)
            except Exception as e:
                logger.error(f"Error in alert callback: {e}")
    
    def get_current_operations(self) -> Dict[str, Dict[str, Any]]:
        """Get currently running operations."""
        with self.operation_lock:
            return {
                op_id: {
                    'operation': metric.operation,
                    'duration': time.time() - metric.timestamp,
                    'rows_processed': metric.rows_processed,
                    'throughput': metric.throughput,
                    'errors_found': metric.errors_found,
                    'metadata': metric.metadata
                }
                for op_id, metric in self.current_operations.items()
            }
    
    def get_recent_metrics(self, 
                          limit: int = 100, 
                          operation_type: Optional[str] = None,
                          time_window_hours: Optional[float] = None) -> List[Dict[str, Any]]:
        """
        Get recent validation metrics.
        
        Args:
            limit: Maximum number of metrics to return
            operation_type: Filter by operation type
            time_window_hours: Only return metrics from last N hours
            
        Returns:
            List of metric dictionaries
        """
        current_time = time.time()
        cutoff_time = current_time - (time_window_hours * 3600) if time_window_hours else 0
        
        filtered_metrics = []
        for metric in reversed(self.metrics):
            if metric.timestamp < cutoff_time:
                break
            
            if operation_type and metric.operation != operation_type:
                continue
            
            filtered_metrics.append(asdict(metric))
            
            if len(filtered_metrics) >= limit:
                break
        
        return filtered_metrics
    
    def get_alerts(self, 
                   limit: int = 50,
                   level: Optional[AlertLevel] = None,
                   resolved: Optional[bool] = None) -> List[Dict[str, Any]]:
        """
        Get validation alerts.
        
        Args:
            limit: Maximum number of alerts to return
            level: Filter by alert level
            resolved: Filter by resolution status
            
        Returns:
            List of alert dictionaries
        """
        filtered_alerts = []
        
        for alert in reversed(self.alerts):
            if level and alert.level != level:
                continue
            
            if resolved is not None and alert.resolved != resolved:
                continue
            
            filtered_alerts.append(asdict(alert))
            
            if len(filtered_alerts) >= limit:
                break
        
        return filtered_alerts
    
    def get_performance_analytics(self, time_window_hours: float = 24) -> Dict[str, Any]:
        """
        Get comprehensive performance analytics.
        
        Args:
            time_window_hours: Time window for analysis
            
        Returns:
            Analytics dictionary
        """
        if not self.analytics_enabled:
            return {"analytics_disabled": True}
        
        # Check cache first
        cache_key = f"validation_analytics_{time_window_hours}"
        cached_analytics = None
        if self.cache_manager:
            try:
                cached_analytics = self.cache_manager.get(cache_key, level=CacheLevel.MEMORY)
                if cached_analytics:
                    return cached_analytics
            except:
                self.logger.debug("Analytics cache lookup failed")
        
        current_time = time.time()
        cutoff_time = current_time - (time_window_hours * 3600)
        
        # Filter metrics by time window
        recent_metrics = [
            metric for metric in self.metrics 
            if metric.timestamp >= cutoff_time
        ]
        
        if not recent_metrics:
            return {"message": "No metrics available for analysis"}
        
        # Calculate analytics
        analytics = self._calculate_analytics(recent_metrics, time_window_hours)
        
        # Cache analytics
        if self.cache_manager:
            try:
                self.cache_manager.set(
                    cache_key, analytics, 
                    level=CacheLevel.MEMORY, 
                    ttl=self.analytics_cache_ttl
                )
            except:
                self.logger.debug("Analytics caching failed")
        
        return analytics
    
    def _calculate_analytics(self, metrics: List[ValidationMetric], time_window_hours: float) -> Dict[str, Any]:
        """Calculate detailed performance analytics."""
        if not metrics:
            return {}
        
        # Basic statistics
        throughputs = [m.throughput for m in metrics if m.throughput > 0]
        durations = [m.duration for m in metrics]
        memory_usages = [m.memory_usage_mb for m in metrics if m.memory_usage_mb > 0]
        cpu_usages = [m.cpu_percent for m in metrics if m.cpu_percent > 0]
        error_counts = [m.errors_found for m in metrics]
        
        # Success rate
        successful_operations = sum(1 for m in metrics if m.success)
        success_rate = successful_operations / len(metrics) if metrics else 0
        
        # Operation type breakdown
        operation_stats = defaultdict(lambda: {"count": 0, "avg_throughput": 0, "avg_duration": 0})
        for metric in metrics:
            op_type = metric.operation
            operation_stats[op_type]["count"] += 1
            operation_stats[op_type]["total_throughput"] = operation_stats[op_type].get("total_throughput", 0) + metric.throughput
            operation_stats[op_type]["total_duration"] = operation_stats[op_type].get("total_duration", 0) + metric.duration
        
        # Calculate averages
        for op_type, stats in operation_stats.items():
            if stats["count"] > 0:
                stats["avg_throughput"] = stats["total_throughput"] / stats["count"]
                stats["avg_duration"] = stats["total_duration"] / stats["count"]
        
        analytics = {
            "time_window_hours": time_window_hours,
            "total_operations": len(metrics),
            "successful_operations": successful_operations,
            "failed_operations": len(metrics) - successful_operations,
            "success_rate": success_rate,
            "performance_summary": {
                "throughput": {
                    "min": min(throughputs) if throughputs else 0,
                    "max": max(throughputs) if throughputs else 0,
                    "mean": statistics.mean(throughputs) if throughputs else 0,
                    "median": statistics.median(throughputs) if throughputs else 0,
                    "std_dev": statistics.stdev(throughputs) if len(throughputs) > 1 else 0
                },
                "duration": {
                    "min": min(durations) if durations else 0,
                    "max": max(durations) if durations else 0,
                    "mean": statistics.mean(durations) if durations else 0,
                    "median": statistics.median(durations) if durations else 0
                },
                "memory_usage": {
                    "min": min(memory_usages) if memory_usages else 0,
                    "max": max(memory_usages) if memory_usages else 0,
                    "mean": statistics.mean(memory_usages) if memory_usages else 0
                },
                "cpu_usage": {
                    "min": min(cpu_usages) if cpu_usages else 0,
                    "max": max(cpu_usages) if cpu_usages else 0,
                    "mean": statistics.mean(cpu_usages) if cpu_usages else 0
                },
                "errors": {
                    "total": sum(error_counts),
                    "mean_per_operation": statistics.mean(error_counts) if error_counts else 0,
                    "max_in_operation": max(error_counts) if error_counts else 0
                }
            },
            "operation_breakdown": dict(operation_stats),
            "trends": self._calculate_trends(metrics),
            "threshold_violations": self._count_threshold_violations(),
            "system_health": self._assess_system_health(metrics)
        }
        
        return analytics
    
    def _calculate_trends(self, metrics: List[ValidationMetric]) -> Dict[str, Any]:
        """Calculate performance trends."""
        if len(metrics) < 2:
            return {"insufficient_data": True}
        
        # Sort by timestamp
        sorted_metrics = sorted(metrics, key=lambda m: m.timestamp)
        
        # Split into first and second half
        mid_point = len(sorted_metrics) // 2
        first_half = sorted_metrics[:mid_point]
        second_half = sorted_metrics[mid_point:]
        
        def avg_throughput(metric_list):
            throughputs = [m.throughput for m in metric_list if m.throughput > 0]
            return statistics.mean(throughputs) if throughputs else 0
        
        def avg_duration(metric_list):
            durations = [m.duration for m in metric_list]
            return statistics.mean(durations) if durations else 0
        
        first_half_throughput = avg_throughput(first_half)
        second_half_throughput = avg_throughput(second_half)
        
        first_half_duration = avg_duration(first_half)
        second_half_duration = avg_duration(second_half)
        
        return {
            "throughput_trend": {
                "direction": "improving" if second_half_throughput > first_half_throughput else "declining",
                "change_percent": ((second_half_throughput - first_half_throughput) / first_half_throughput * 100) 
                                 if first_half_throughput > 0 else 0,
                "first_half_avg": first_half_throughput,
                "second_half_avg": second_half_throughput
            },
            "duration_trend": {
                "direction": "improving" if second_half_duration < first_half_duration else "declining",
                "change_percent": ((first_half_duration - second_half_duration) / first_half_duration * 100) 
                                 if first_half_duration > 0 else 0,
                "first_half_avg": first_half_duration,
                "second_half_avg": second_half_duration
            }
        }
    
    def _count_threshold_violations(self) -> Dict[str, int]:
        """Count threshold violations in recent alerts."""
        violation_counts = defaultdict(int)
        
        # Count violations in last hour
        current_time = time.time()
        cutoff_time = current_time - 3600  # 1 hour
        
        for alert in self.alerts:
            if alert.timestamp >= cutoff_time:
                violation_counts[alert.metric_name] += 1
        
        return dict(violation_counts)
    
    def _assess_system_health(self, metrics: List[ValidationMetric]) -> Dict[str, Any]:
        """Assess overall system health."""
        if not metrics:
            return {"status": "unknown", "reason": "no_metrics"}
        
        recent_metrics = metrics[-10:]  # Last 10 operations
        success_rate = sum(1 for m in recent_metrics if m.success) / len(recent_metrics)
        
        # Health scoring
        health_score = 100
        
        # Deduct for failures
        health_score -= (1 - success_rate) * 50
        
        # Deduct for slow performance
        recent_throughputs = [m.throughput for m in recent_metrics if m.throughput > 0]
        if recent_throughputs:
            avg_throughput = statistics.mean(recent_throughputs)
            if avg_throughput < self.thresholds['min_throughput']:
                health_score -= 20
        
        # Deduct for recent alerts
        recent_alerts = [a for a in self.alerts if a.timestamp > time.time() - 3600]
        health_score -= len(recent_alerts) * 5
        
        # Determine status
        if health_score >= 90:
            status = "excellent"
        elif health_score >= 75:
            status = "good"
        elif health_score >= 50:
            status = "fair"
        elif health_score >= 25:
            status = "poor"
        else:
            status = "critical"
        
        return {
            "status": status,
            "score": max(0, health_score),
            "success_rate": success_rate,
            "consecutive_failures": self.consecutive_failures,
            "time_since_last_success": time.time() - self.last_success_time,
            "recent_alerts": len(recent_alerts),
            "uptime_hours": (time.time() - self.monitoring_start_time) / 3600
        }
    
    def export_metrics(self, 
                      output_file: Path, 
                      format: str = "json",
                      time_window_hours: Optional[float] = None) -> bool:
        """
        Export metrics to file.
        
        Args:
            output_file: Output file path
            format: Export format ("json" or "csv")
            time_window_hours: Time window for export
            
        Returns:
            Success status
        """
        try:
            if time_window_hours:
                cutoff_time = time.time() - (time_window_hours * 3600)
                export_metrics = [m for m in self.metrics if m.timestamp >= cutoff_time]
            else:
                export_metrics = list(self.metrics)
            
            output_file.parent.mkdir(parents=True, exist_ok=True)
            
            if format.lower() == "json":
                with open(output_file, 'w') as f:
                    json.dump([asdict(m) for m in export_metrics], f, indent=2, default=str)
            
            elif format.lower() == "csv":
                # Third-party imports
                import pandas as pd
                df = pd.DataFrame([asdict(m) for m in export_metrics])
                df.to_csv(output_file, index=False)
            
            else:
                raise ValueError(f"Unsupported format: {format}")
            
            self.logger.info(f"Exported {len(export_metrics)} metrics to {output_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to export metrics: {e}")
            return False


# Global monitor instance
_validation_monitor = None


def get_validation_monitor(config: Optional[Dict[str, Any]] = None) -> ValidationMonitor:
    """Get global validation monitor instance."""
    global _validation_monitor
    if _validation_monitor is None:
        _validation_monitor = ValidationMonitor(config)
    return _validation_monitor


# Context manager for operation monitoring
class monitor_validation_operation:
    """Context manager for monitoring validation operations."""
    
    def __init__(self, 
                 operation_type: str,
                 rows_to_process: int = 0,
                 monitor: Optional[ValidationMonitor] = None):
        """
        Initialize operation monitor.
        
        Args:
            operation_type: Type of validation operation
            rows_to_process: Number of rows to process
            monitor: Optional monitor instance
        """
        self.operation_type = operation_type
        self.rows_to_process = rows_to_process
        self.monitor = monitor or get_validation_monitor()
        self.operation_id = None
        self.start_time = None
    
    def __enter__(self):
        """Start monitoring operation."""
        self.start_time = time.time()
        self.operation_id = f"{self.operation_type}_{int(self.start_time)}"
        self.monitor.start_operation(self.operation_id, self.operation_type, self.rows_to_process)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Finish monitoring operation."""
        success = exc_type is None
        self.monitor.finish_operation(
            self.operation_id, 
            success=success,
            metadata={'exception': str(exc_val) if exc_val else None}
        )
    
    def update(self, **kwargs):
        """Update operation metrics."""
        self.monitor.update_operation(self.operation_id, **kwargs)


# Export public symbols
__all__ = [
    'AlertLevel', 'ValidationMetric', 'ValidationAlert', 'ValidationMonitor',
    'get_validation_monitor', 'monitor_validation_operation'
]