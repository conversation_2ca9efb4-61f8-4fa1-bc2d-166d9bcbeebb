"""
Advanced Data Validation System for Yemen Trade Diagnostic

This module provides comprehensive, high-performance data validation capabilities
optimized for large datasets (11+ million rows) with hardware acceleration,
intelligent error recovery, and comprehensive monitoring.

Key Features:
- Performance-optimized validation for large datasets
- Hardware acceleration support (Neural Engine, GPU)
- Intelligent error recovery and handling
- Real-time performance monitoring and analytics
- Schema optimization and caching
- Comprehensive integration with data loading workflows
- Cross-validation between related datasets
- Streaming validation for very large files

Components:
- PerformanceValidator: High-performance validation with hardware acceleration
- SchemaOptimizer: Optimized schema management with caching and versioning
- ErrorRecovery: Intelligent error categorization and recovery strategies
- ValidationMonitoring: Real-time performance monitoring and alerting
- IntegrationManager: Consistent validation across all data workflows
"""

# Standard library imports
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

# Third-party imports
import pandas as pd

# Project imports
# Import error recovery components
from yemen_trade_diagnostic.data.validation.error_recovery import (
    ErrorPattern,
    ErrorRecoveryResult,
    ErrorRecoveryStrategy,
    ErrorType,
    ValidationErrorRecovery,
    create_error_recovery_system,
    recover_validation_errors,
)

# Import integration components  
from yemen_trade_diagnostic.data.validation.integration_manager import (
    ValidationConfiguration,
    ValidationIntegrationLevel,
    ValidationIntegrationManager,
    get_validation_integration_manager,
    setup_data_loader_validation,
    validate_with_integration,
)

# Import performance validation components
from yemen_trade_diagnostic.data.validation.performance_validator import (
    PerformanceValidator,
    StreamingValidator,
    ValidationPerformanceConfig,
    ValidationPerformanceLevel,
    create_performance_validator,
    create_streaming_validator,
    validate_large_csv_file,
    validate_large_dataframe,
)

# Import schema optimization components
from yemen_trade_diagnostic.data.validation.schema_optimizer import (
    OptimizedSchemaRegistry,
    SchemaMetadata,
    SchemaVersion,
    get_optimized_schema_registry,
    get_schema_optimized,
    preload_common_schemas,
    register_schema_optimized,
)
from yemen_trade_diagnostic.data.validation.validation_helper import DataValidationHelper

# Import existing validation components for compatibility
from yemen_trade_diagnostic.data.validation.validation_manager import DataValidationManager

# Import monitoring components
from yemen_trade_diagnostic.data.validation.validation_monitoring import (
    AlertLevel,
    ValidationAlert,
    ValidationMetric,
    ValidationMonitor,
    get_validation_monitor,
    monitor_validation_operation,
)
from yemen_trade_diagnostic.data.validation.validation_rules import (
    DataValidationRule,
    RuleType,
    ValidationRuleSet,
)
from yemen_trade_diagnostic.interfaces.logging_interface import get_logger

# Import core validation interfaces
from yemen_trade_diagnostic.interfaces.validation_interface import (
    ValidationIssueLevel,
    ValidationMode,
    ValidationResult,
)

logger = get_logger(__name__)


# High-level convenience functions

def validate_dataframe_optimized(df: pd.DataFrame,
                               schema_name: str,
                               optimization_level: ValidationPerformanceLevel = ValidationPerformanceLevel.BALANCED,
                               enable_error_recovery: bool = True,
                               enable_monitoring: bool = True,
                               mode: ValidationMode = ValidationMode.STANDARD) -> ValidationResult:
    """
    High-level function to validate DataFrame with all optimizations.
    
    This is the recommended function for most validation use cases as it provides
    the best balance of performance, error handling, and monitoring.
    
    Args:
        df: DataFrame to validate
        schema_name: Name of validation schema
        optimization_level: Performance optimization level
        enable_error_recovery: Enable automatic error recovery
        enable_monitoring: Enable performance monitoring
        mode: Validation mode
        
    Returns:
        ValidationResult with comprehensive validation details
    """
    logger.info(f"Starting optimized validation of {len(df)} rows with schema '{schema_name}'")
    
    # Configure integration
    config = ValidationConfiguration(
        integration_level=ValidationIntegrationLevel.ENHANCED,
        enable_monitoring=enable_monitoring,
        enable_error_recovery=enable_error_recovery,
        enable_hardware_acceleration=True,
        performance_config=ValidationPerformanceConfig(
            level=optimization_level,
            use_hardware_acceleration=True,
            use_vectorized_operations=True,
            use_parallel_processing=len(df) > 100_000,
            cache_validation_results=True
        )
    )
    
    # Get integration manager
    manager = ValidationIntegrationManager(config)
    
    # Perform validation
    result = manager.validate_dataframe(df, schema_name, mode=mode)
    
    logger.info(f"Validation completed: {'PASSED' if result.is_valid else 'FAILED'} "
               f"({result.error_count()} errors, {result.warning_count()} warnings)")
    
    return result


def validate_file_optimized(file_path: Union[str, Path],
                          schema_name: str,
                          optimization_level: ValidationPerformanceLevel = ValidationPerformanceLevel.BALANCED,
                          enable_streaming: bool = True,
                          chunk_size: int = 100_000,
                          mode: ValidationMode = ValidationMode.STANDARD) -> ValidationResult:
    """
    High-level function to validate large files with streaming support.
    
    Args:
        file_path: Path to file to validate
        schema_name: Name of validation schema
        optimization_level: Performance optimization level
        enable_streaming: Use streaming validation for large files
        chunk_size: Chunk size for streaming validation
        mode: Validation mode
        
    Returns:
        ValidationResult from file validation
    """
    file_path = Path(file_path)
    logger.info(f"Starting optimized file validation: {file_path.name}")
    
    # Check file size for streaming decision
    file_size_mb = file_path.stat().st_size / (1024 * 1024)
    use_streaming = enable_streaming and file_size_mb > 100  # > 100MB
    
    if use_streaming:
        logger.info(f"Using streaming validation for large file ({file_size_mb:.1f}MB)")
        
        # Get optimized schema
        schema = get_schema_optimized(schema_name)
        if not schema:
            result = ValidationResult()
            result.add_issue(
                f"Schema '{schema_name}' not found",
                ValidationIssueLevel.ERROR
            )
            return result
        
        # Use streaming validator
        config = ValidationPerformanceConfig(
            level=optimization_level,
            chunk_size=chunk_size,
            use_hardware_acceleration=True
        )
        
        streaming_validator = StreamingValidator(config)
        result = streaming_validator.validate_csv_file(file_path, schema.to_dict(), mode)
    
    else:
        logger.info(f"Using in-memory validation for file ({file_size_mb:.1f}MB)")
        
        # Load file and validate in memory
        try:
            if file_path.suffix.lower() == '.csv':
                df = pd.read_csv(file_path)
            elif file_path.suffix.lower() == '.parquet':
                df = pd.read_parquet(file_path)
            else:
                result = ValidationResult()
                result.add_issue(
                    f"Unsupported file format: {file_path.suffix}",
                    ValidationIssueLevel.ERROR
                )
                return result
            
            result = validate_dataframe_optimized(
                df, schema_name, optimization_level, mode=mode
            )
            
        except Exception as e:
            result = ValidationResult()
            result.add_issue(
                f"Error reading file: {str(e)}",
                ValidationIssueLevel.ERROR
            )
            return result
    
    logger.info(f"File validation completed: {'PASSED' if result.is_valid else 'FAILED'}")
    return result


def setup_validation_system(config: Optional[Dict[str, Any]] = None) -> ValidationIntegrationManager:
    """
    Set up comprehensive validation system with all components.
    
    This function initializes and configures all validation components
    for optimal performance and integration.
    
    Args:
        config: Optional configuration dictionary
        
    Returns:
        Configured ValidationIntegrationManager
    """
    logger.info("Setting up comprehensive validation system")
    
    # Create configuration
    validation_config = ValidationConfiguration(
        integration_level=ValidationIntegrationLevel.COMPREHENSIVE,
        enable_monitoring=config.get('enable_monitoring', True) if config else True,
        enable_error_recovery=config.get('enable_error_recovery', True) if config else True,
        enable_hardware_acceleration=config.get('enable_hardware_acceleration', True) if config else True,
        cross_validation_enabled=config.get('cross_validation_enabled', True) if config else True,
        performance_config=ValidationPerformanceConfig(
            level=ValidationPerformanceLevel.BALANCED,
            use_hardware_acceleration=True,
            use_vectorized_operations=True,
            use_parallel_processing=True,
            cache_validation_results=True
        )
    )
    
    # Initialize integration manager
    manager = ValidationIntegrationManager(validation_config)
    
    # Preload common schemas for better performance
    preload_common_schemas()
    
    # Set up standard cross-validation rules
    _setup_standard_cross_validation_rules(manager)
    
    logger.info("Validation system setup complete")
    return manager


def _setup_standard_cross_validation_rules(manager: ValidationIntegrationManager):
    """Set up standard cross-validation rules for trade data."""
    
    def validate_country_consistency(df: pd.DataFrame, dataset_name: str, context: Dict[str, Any]) -> List[str]:
        """Validate country code consistency across datasets."""
        violations = []
        
        # Check if country codes are consistent with reference data
        if 'country_code' in df.columns:
            # This would check against a reference country codes dataset
            # Implementation would depend on available reference data
            pass
        
        return violations
    
    def validate_product_consistency(df: pd.DataFrame, dataset_name: str, context: Dict[str, Any]) -> List[str]:
        """Validate product code consistency across datasets."""
        violations = []
        
        # Check if product codes are consistent with reference data
        if 'product_code' in df.columns:
            # This would check against a reference product codes dataset
            # Implementation would depend on available reference data
            pass
        
        return violations
    
    # Register cross-validation rules
    manager.add_cross_validation_rule(
        ['baci', 'yemen_exports', 'yemen_imports'],
        validate_country_consistency,
        "Validate country code consistency across trade datasets"
    )
    
    manager.add_cross_validation_rule(
        ['baci', 'yemen_exports', 'yemen_imports'],
        validate_product_consistency,
        "Validate product code consistency across trade datasets"
    )


def get_validation_performance_report(time_window_hours: float = 24) -> Dict[str, Any]:
    """
    Get comprehensive validation performance report.
    
    Args:
        time_window_hours: Time window for performance analysis
        
    Returns:
        Performance report dictionary
    """
    manager = get_validation_integration_manager()
    monitor = get_validation_monitor()
    schema_registry = get_optimized_schema_registry()
    
    report = {
        "timestamp": pd.Timestamp.now().isoformat(),
        "time_window_hours": time_window_hours,
        "integration_statistics": manager.get_integration_statistics(),
        "monitoring_analytics": monitor.get_performance_analytics(time_window_hours),
        "schema_performance": schema_registry.get_performance_stats(),
        "system_health": manager._assess_integration_health()
    }
    
    return report


def export_validation_metrics(output_dir: Union[str, Path],
                            time_window_hours: float = 24,
                            include_alerts: bool = True) -> bool:
    """
    Export validation metrics and reports to files.
    
    Args:
        output_dir: Directory to save export files
        time_window_hours: Time window for metrics
        include_alerts: Include alerts in export
        
    Returns:
        Success status
    """
    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    try:
        # Export performance report
        performance_report = get_validation_performance_report(time_window_hours)
        with open(output_dir / 'validation_performance_report.json', 'w') as f:
            # Standard library imports
            import json
            json.dump(performance_report, f, indent=2, default=str)
        
        # Export metrics
        monitor = get_validation_monitor()
        monitor.export_metrics(
            output_dir / 'validation_metrics.json',
            format='json',
            time_window_hours=time_window_hours
        )
        
        # Export alerts if requested
        if include_alerts:
            alerts = monitor.get_alerts(limit=1000)
            with open(output_dir / 'validation_alerts.json', 'w') as f:
                # Standard library imports
                import json
                json.dump(alerts, f, indent=2, default=str)
        
        # Export integration report
        manager = get_validation_integration_manager()
        manager.create_integration_report(output_dir / 'validation_integration_report.json')
        
        logger.info(f"Validation metrics exported to {output_dir}")
        return True
        
    except Exception as e:
        logger.error(f"Failed to export validation metrics: {e}")
        return False


# Initialize validation system on import
logger.info("Initializing advanced validation system")

# Preload common schemas for better performance
try:
    preload_common_schemas()
    logger.debug("Common schemas preloaded successfully")
except Exception as e:
    logger.warning(f"Failed to preload common schemas: {e}")

# Export all public symbols
__all__ = [
    # Core validation results
    'ValidationResult', 'ValidationMode', 'ValidationIssueLevel',
    
    # Performance validation
    'PerformanceValidator', 'StreamingValidator', 'ValidationPerformanceLevel',
    'ValidationPerformanceConfig', 'validate_large_dataframe', 'validate_large_csv_file',
    
    # Schema optimization
    'OptimizedSchemaRegistry', 'SchemaVersion', 'get_schema_optimized',
    'register_schema_optimized', 'preload_common_schemas',
    
    # Error recovery
    'ValidationErrorRecovery', 'ErrorRecoveryStrategy', 'ErrorType',
    'recover_validation_errors',
    
    # Monitoring
    'ValidationMonitor', 'get_validation_monitor', 'monitor_validation_operation',
    
    # Integration
    'ValidationIntegrationManager', 'ValidationIntegrationLevel',
    'get_validation_integration_manager', 'validate_with_integration',
    
    # High-level convenience functions
    'validate_dataframe_optimized', 'validate_file_optimized', 'setup_validation_system',
    'get_validation_performance_report', 'export_validation_metrics',
    
    # Legacy compatibility
    'DataValidationManager', 'DataValidationHelper', 'ValidationRuleSet'
]