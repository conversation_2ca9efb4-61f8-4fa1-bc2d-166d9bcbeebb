"""
Schema validation utilities for the Yemen Trade Diagnostic system.

This module provides tools for validating DataFrame structures and content
against predefined schemas.
"""

# Standard library imports
import json
import os
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Tuple, Union

# Third-party imports
import numpy as np
import pandas as pd

# Project imports
from yemen_trade_diagnostic.interfaces.cache_interface import (
    CacheLevel,
    CachePriority,
    DataLifetime,
    memoize,
)
from yemen_trade_diagnostic.interfaces.error_interface import (
    ErrorCategory,
    ErrorSeverity,
    report_error,
    with_error_handling,
)
from yemen_trade_diagnostic.interfaces.logging_interface import get_logger
from yemen_trade_diagnostic.interfaces.validation_interface import (
    ValidationIssueLevel,
    ValidationMode,
    ValidationResult,
    ValidationStrategy,
)

# Global logger for this module
logger = get_logger(__name__)


class SchemaValidationError(Exception):
    """Exception raised when schema validation fails."""
    pass


class SchemaDefinition:
    """
    Data schema definition.
    
    This class represents a schema for a DataFrame, including column definitions,
    data types, constraints, and relationships.
    """
    
    def __init__(self, schema_dict: Dict[str, Any]):
        """
        Initialize a schema definition.
        
        Args:
            schema_dict: Dictionary containing schema definition
        """
        self.name = schema_dict.get('name', 'unknown')
        self.description = schema_dict.get('description', '')
        self.required_columns = schema_dict.get('required_columns', [])
        self.column_types = schema_dict.get('column_types', {})
        self.value_ranges = schema_dict.get('value_ranges', {})
        self.unique_combinations = schema_dict.get('unique_combinations', [])
        self.relationships = schema_dict.get('relationships', [])
        self.no_missing = schema_dict.get('no_missing', [])
        self.custom_validations = schema_dict.get('custom_validations', {})
        self.row_filters = schema_dict.get('row_filters', [])
        self.statistics = schema_dict.get('statistics', {})
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the schema to a dictionary.
        
        Returns:
            Dictionary containing the schema definition
        """
        return {
            'name': self.name,
            'description': self.description,
            'required_columns': self.required_columns,
            'column_types': self.column_types,
            'value_ranges': self.value_ranges,
            'unique_combinations': self.unique_combinations,
            'relationships': self.relationships,
            'no_missing': self.no_missing,
            'custom_validations': self.custom_validations,
            'row_filters': self.row_filters,
            'statistics': self.statistics
        }


@with_error_handling(category=ErrorCategory.VALIDATION, severity=ErrorSeverity.ERROR)
@memoize(ttl=3600, level=CacheLevel.MEMORY, lifetime=DataLifetime.TEMPORARY)
def load_schema(schema_path: Union[str, Path]) -> SchemaDefinition:
    """
    Load a schema from a JSON file.
    
    Args:
        schema_path: Path to the schema file
        
    Returns:
        SchemaDefinition object
        
    Raises:
        SchemaValidationError: If the schema file is invalid or not found
    """
    try:
        schema_path = Path(schema_path)
        
        if not schema_path.exists():
            raise SchemaValidationError(f"Schema file not found: {schema_path}")
        
        with open(schema_path, 'r') as f:
            schema_dict = json.load(f)
        
        # Perform basic validation on the schema itself
        if not isinstance(schema_dict, dict):
            raise SchemaValidationError(f"Schema file does not contain a valid JSON object: {schema_path}")
        
        # Check for required fields
        required_fields = ['name', 'required_columns']
        missing_fields = [field for field in required_fields if field not in schema_dict]
        if missing_fields:
            raise SchemaValidationError(f"Schema file missing required fields: {', '.join(missing_fields)}")
        
        return SchemaDefinition(schema_dict)
        
    except json.JSONDecodeError as e:
        raise SchemaValidationError(f"Failed to parse schema file {schema_path}: {str(e)}") from e
    except Exception as e:
        raise SchemaValidationError(f"Error loading schema file {schema_path}: {str(e)}") from e


def load_schema_from_dict(schema_dict: Dict[str, Any]) -> SchemaDefinition:
    """
    Load a schema from a dictionary.
    
    Args:
        schema_dict: Dictionary containing schema definition
        
    Returns:
        SchemaDefinition object
        
    Raises:
        SchemaValidationError: If the schema dictionary is invalid
    """
    try:
        # Perform basic validation on the schema
        if not isinstance(schema_dict, dict):
            raise SchemaValidationError("Schema is not a valid dictionary")
        
        # Check for required fields
        required_fields = ['name', 'required_columns']
        missing_fields = [field for field in required_fields if field not in schema_dict]
        if missing_fields:
            raise SchemaValidationError(f"Schema missing required fields: {', '.join(missing_fields)}")
        
        return SchemaDefinition(schema_dict)
        
    except Exception as e:
        raise SchemaValidationError(f"Error loading schema from dictionary: {str(e)}") from e


@with_error_handling(category=ErrorCategory.VALIDATION, severity=ErrorSeverity.ERROR)
def validate_schema(data: pd.DataFrame, schema: Union[Dict[str, Any], SchemaDefinition, Path, str], 
                  mode: ValidationMode = ValidationMode.STANDARD) -> ValidationResult:
    """
    Validate a DataFrame against a schema.
    
    Args:
        data: DataFrame to validate
        schema: Schema definition, path to schema file, or schema dictionary
        mode: Validation mode
        
    Returns:
        ValidationResult object
        
    Raises:
        SchemaValidationError: If validation fails in critical ways
    """
    # Create validation result
    result = ValidationResult()
    
    # Load the schema if needed
    if isinstance(schema, dict):
        schema_def = load_schema_from_dict(schema)
    elif isinstance(schema, SchemaDefinition):
        schema_def = schema
    elif isinstance(schema, (str, Path)):
        schema_def = load_schema(schema)
    else:
        error_msg = f"Invalid schema type: {type(schema)}"
        result.add_issue(error_msg, ValidationIssueLevel.ERROR)
        return result
    
    try:
        # Check if data is None or empty
        if data is None:
            result.add_issue("Data is None", ValidationIssueLevel.ERROR)
            return result
        
        if not isinstance(data, pd.DataFrame):
            result.add_issue(f"Data is not a pandas DataFrame (got {type(data)})", ValidationIssueLevel.ERROR)
            return result
        
        if data.empty:
            issue_level = ValidationIssueLevel.WARNING
            if mode == ValidationMode.STRICT:
                issue_level = ValidationIssueLevel.ERROR
            
            result.add_issue("DataFrame is empty", issue_level)
            if mode == ValidationMode.STRICT:
                return result
        
        # Check required columns
        missing_columns = [col for col in schema_def.required_columns if col not in data.columns]
        if missing_columns:
            result.add_issue(
                f"Missing required columns: {', '.join(missing_columns)}",
                ValidationIssueLevel.ERROR
            )
            return result
        
        # Check column types
        for col, expected_type in schema_def.column_types.items():
            if col not in data.columns:
                continue
            
            # Determine validation level based on column importance
            validation_level = ValidationIssueLevel.ERROR if col in schema_def.required_columns else ValidationIssueLevel.WARNING
            
            valid = False
            actual_type = data[col].dtype
            
            # Check numeric type
            if expected_type == 'numeric':
                valid = pd.api.types.is_numeric_dtype(data[col])
            
            # Check string type
            elif expected_type == 'string':
                valid = (pd.api.types.is_string_dtype(data[col]) or 
                        pd.api.types.is_object_dtype(data[col]))
            
            # Check datetime type
            elif expected_type == 'datetime':
                valid = pd.api.types.is_datetime64_dtype(data[col])
            
            # Check boolean type
            elif expected_type == 'boolean':
                valid = pd.api.types.is_bool_dtype(data[col])
            
            # Check integer type (includes Int64, etc.)
            elif expected_type == 'integer':
                valid = pd.api.types.is_integer_dtype(data[col])
            
            # If validation failed, add issue
            if not valid:
                result.add_issue(
                    f"Column '{col}' has wrong type: expected {expected_type}, got {actual_type}",
                    validation_level,
                    location=col
                )
        
        # Check value ranges
        for col, range_def in schema_def.value_ranges.items():
            if col not in data.columns:
                continue
            
            if not pd.api.types.is_numeric_dtype(data[col]):
                # Add a warning for non-numeric columns with range constraints
                result.add_issue(
                    f"Cannot check value range for non-numeric column '{col}'",
                    ValidationIssueLevel.WARNING,
                    location=col
                )
                continue
            
            if 'min' in range_def:
                min_val = range_def['min']
                below_min = (data[col] < min_val)
                
                # Handle nullable types
                if pd.api.types.is_nullable_dtype(data[col]):
                    below_min = below_min.fillna(False)
                
                below_min_count = below_min.sum()
                
                if below_min_count > 0:
                    result.add_issue(
                        f"Column '{col}' has {below_min_count} values below minimum {min_val}",
                        ValidationIssueLevel.WARNING,
                        location=col
                    )
            
            if 'max' in range_def:
                max_val = range_def['max']
                above_max = (data[col] > max_val)
                
                # Handle nullable types
                if pd.api.types.is_nullable_dtype(data[col]):
                    above_max = above_max.fillna(False)
                
                above_max_count = above_max.sum()
                
                if above_max_count > 0:
                    result.add_issue(
                        f"Column '{col}' has {above_max_count} values above maximum {max_val}",
                        ValidationIssueLevel.WARNING,
                        location=col
                    )
        
        # Check for missing values
        for col in schema_def.no_missing:
            if col not in data.columns:
                continue
            
            missing_count = data[col].isna().sum()
            if missing_count > 0:
                result.add_issue(
                    f"Column '{col}' has {missing_count} missing values",
                    ValidationIssueLevel.ERROR if mode == ValidationMode.STRICT else ValidationIssueLevel.WARNING,
                    location=col
                )
        
        # Check unique combinations
        for combo in schema_def.unique_combinations:
            if not all(col in data.columns for col in combo):
                continue
            
            duplicate_count = len(data[data.duplicated(subset=combo, keep=False)])
            if duplicate_count > 0:
                result.add_issue(
                    f"Found {duplicate_count} duplicates for combination: {', '.join(combo)}",
                    ValidationIssueLevel.ERROR if mode == ValidationMode.STRICT else ValidationIssueLevel.WARNING
                )
        
        # Check relationships (functional dependencies)
        for rel in schema_def.relationships:
            rel_type = rel.get('type')
            rel_columns = rel.get('columns', [])
            
            if rel_type == 'functional_dependency' and len(rel_columns) >= 2:
                if not all(col in data.columns for col in rel_columns):
                    continue
                
                # Get key columns (all but the last one)
                key_cols = rel_columns[:-1]
                dependent_col = rel_columns[-1]
                
                # Group by key columns and check for multiple values in dependent column
                grouped = data.groupby(key_cols)[dependent_col].nunique()
                violations = grouped[grouped > 1]
                
                if len(violations) > 0:
                    result.add_issue(
                        f"Functional dependency violation: {', '.join(key_cols)} -> {dependent_col} "
                        f"has {len(violations)} violations",
                        ValidationIssueLevel.WARNING
                    )
        
        # Run custom validations
        for col, validations in schema_def.custom_validations.items():
            if col not in data.columns:
                continue
            
            for validation in validations:
                if validation == 'positive':
                    if not pd.api.types.is_numeric_dtype(data[col]):
                        continue
                    
                    negative_values = data[data[col] < 0][col]
                    if len(negative_values) > 0:
                        result.add_issue(
                            f"Column '{col}' has {len(negative_values)} negative values",
                            ValidationIssueLevel.WARNING,
                            location=col
                        )
                
                elif validation == 'unique':
                    duplicate_count = len(data) - data[col].nunique()
                    if duplicate_count > 0:
                        result.add_issue(
                            f"Column '{col}' has {duplicate_count} duplicate values",
                            ValidationIssueLevel.WARNING,
                            location=col
                        )
        
        # Return validation result
        return result
        
    except Exception as e:
        error_msg = f"Error validating data: {str(e)}"
        logger.error(error_msg, exc_info=True)
        result.add_issue(error_msg, ValidationIssueLevel.ERROR)
        
        # Report error
        report_error(
            e,
            component="schema_validator",
            operation="validate_schema",
            schema_name=schema_def.name,
            data_shape=data.shape if isinstance(data, pd.DataFrame) else None
        )
        
        return result


def validate_against_multiple_schemas(data: pd.DataFrame, 
                                     schemas: List[Union[Dict[str, Any], SchemaDefinition, Path, str]],
                                     mode: ValidationMode = ValidationMode.STANDARD) -> ValidationResult:
    """
    Validate a DataFrame against multiple schemas.
    
    Args:
        data: DataFrame to validate
        schemas: List of schema definitions, paths to schema files, or schema dictionaries
        mode: Validation mode
        
    Returns:
        ValidationResult object
        
    Raises:
        SchemaValidationError: If validation fails in critical ways
    """
    # Create validation result
    result = ValidationResult()
    
    # Validate against each schema
    for i, schema in enumerate(schemas):
        schema_result = validate_schema(data, schema, mode)
        
        # Add schema index to issues
        for issue in schema_result.issues:
            # Add schema index to issue message
            issue.message = f"Schema {i}: {issue.message}"
            result.add_issue(
                issue.message,
                issue.level,
                context=issue.context,
                location=issue.location
            )
    
    return result


def get_schema_compatibility(schema1: Union[Dict[str, Any], SchemaDefinition], 
                           schema2: Union[Dict[str, Any], SchemaDefinition]) -> Tuple[bool, List[str]]:
    """
    Check compatibility between two schemas.
    
    Args:
        schema1: First schema
        schema2: Second schema
        
    Returns:
        Tuple of (is_compatible, incompatibilities)
    """
    # Load schemas if needed
    if isinstance(schema1, dict):
        schema1 = load_schema_from_dict(schema1)
    if isinstance(schema2, dict):
        schema2 = load_schema_from_dict(schema2)
    
    incompatibilities = []
    
    # Check required columns
    schema1_required = set(schema1.required_columns)
    schema2_required = set(schema2.required_columns)
    
    # Check for columns that are required in one schema but not in the other
    required_diff = schema1_required.symmetric_difference(schema2_required)
    if required_diff:
        incompatibilities.append(f"Different required columns: {', '.join(required_diff)}")
    
    # Check column types for common columns
    common_columns = set(schema1.column_types.keys()) & set(schema2.column_types.keys())
    for col in common_columns:
        type1 = schema1.column_types[col]
        type2 = schema2.column_types[col]
        
        if type1 != type2:
            incompatibilities.append(f"Column '{col}' has different types: {type1} vs {type2}")
    
    # Check value ranges for common columns
    common_range_columns = set(schema1.value_ranges.keys()) & set(schema2.value_ranges.keys())
    for col in common_range_columns:
        range1 = schema1.value_ranges[col]
        range2 = schema2.value_ranges[col]
        
        # Check minimum values
        if 'min' in range1 and 'min' in range2 and range1['min'] != range2['min']:
            incompatibilities.append(f"Column '{col}' has different minimum values: {range1['min']} vs {range2['min']}")
        
        # Check maximum values
        if 'max' in range1 and 'max' in range2 and range1['max'] != range2['max']:
            incompatibilities.append(f"Column '{col}' has different maximum values: {range1['max']} vs {range2['max']}")
    
    is_compatible = len(incompatibilities) == 0
    
    return is_compatible, incompatibilities