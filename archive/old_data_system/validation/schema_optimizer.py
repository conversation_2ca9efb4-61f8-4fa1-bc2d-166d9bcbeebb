"""
Schema Management and Optimization Module

Provides optimized schema loading, caching, versioning, and management
for high-performance validation operations.
"""

# Standard library imports
import hashlib
import json
import pickle
import threading
import time
from concurrent.futures import Thread<PERSON>oolExecutor
from dataclasses import asdict, dataclass
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Tuple, Union

# Project imports
from yemen_trade_diagnostic.data.schema_management import Schema, get_schema_registry
from yemen_trade_diagnostic.interfaces.cache_interface import CacheLevel, get_cache_manager
from yemen_trade_diagnostic.interfaces.error_interface import (
    ErrorCategory,
    ErrorSeverity,
    with_error_handling,
)

# V2 interface imports
from yemen_trade_diagnostic.interfaces.logging_interface import get_logger

logger = get_logger(__name__)


class SchemaVersion(Enum):
    """Schema version types for evolution handling."""
    MAJOR = "major"      # Breaking changes
    MINOR = "minor"      # New features, backward compatible
    PATCH = "patch"      # Bug fixes, backward compatible


@dataclass
class SchemaMetadata:
    """Metadata for schema versioning and caching."""
    name: str
    version: str = "1.0.0"
    created_at: float = None
    updated_at: float = None
    hash: str = ""
    size_bytes: int = 0
    complexity_score: int = 0
    usage_count: int = 0
    last_accessed: float = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = time.time()
        if self.updated_at is None:
            self.updated_at = self.created_at
        if self.last_accessed is None:
            self.last_accessed = self.created_at


class OptimizedSchemaRegistry:
    """
    Optimized schema registry with caching, versioning, and performance features.
    
    Features:
    - Multi-level caching (memory, disk)
    - Schema versioning and migration
    - Lazy loading of schemas
    - Performance monitoring
    - Concurrent access optimization
    """
    
    def __init__(self, cache_dir: Optional[Path] = None):
        """
        Initialize optimized schema registry.
        
        Args:
            cache_dir: Directory for schema caching
        """
        self.logger = logger
        # Initialize cache manager lazily to avoid circular dependencies
        self._cache_manager = None
        
        # Schema storage
        self.schemas: Dict[str, Schema] = {}
        self.schema_metadata: Dict[str, SchemaMetadata] = {}
        self.compiled_schemas: Dict[str, Any] = {}  # Pre-compiled for performance
        
        # Versioning
        self.schema_versions: Dict[str, List[str]] = {}  # name -> versions
        self.version_mapping: Dict[str, Dict[str, str]] = {}  # name -> version -> actual_name
        
        # Caching
        self.cache_dir = cache_dir or Path.home() / '.yemen_trade_diagnostic' / 'schema_cache'
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # Performance tracking
        self.performance_stats = {
            "schema_loads": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "total_load_time": 0.0,
            "compilation_time": 0.0
        }
        
        # Thread safety
        self._lock = threading.RLock()
        
        # Load existing schemas
        self._load_cached_schemas()
        
        self.logger.info(f"Initialized OptimizedSchemaRegistry with cache at {self.cache_dir}")
    
    @property 
    def cache_manager(self):
        """Lazy initialization of cache manager to avoid circular dependencies."""
        if self._cache_manager is None:
            try:
                self._cache_manager = get_cache_manager()
            except:
                # If cache manager fails to initialize, create a simple fallback
                self._cache_manager = None
                self.logger.warning("Cache manager initialization failed, disabling caching")
        return self._cache_manager
    
    def _load_cached_schemas(self):
        """Load schemas from cache directory."""
        try:
            metadata_file = self.cache_dir / 'schema_metadata.json'
            if metadata_file.exists():
                with open(metadata_file, 'r') as f:
                    metadata_dict = json.load(f)
                
                for name, meta_data in metadata_dict.items():
                    self.schema_metadata[name] = SchemaMetadata(**meta_data)
                
                self.logger.info(f"Loaded metadata for {len(self.schema_metadata)} schemas from cache")
        
        except Exception as e:
            self.logger.warning(f"Failed to load cached schema metadata: {e}")
    
    def _save_schema_metadata(self):
        """Save schema metadata to cache."""
        try:
            metadata_file = self.cache_dir / 'schema_metadata.json'
            metadata_dict = {
                name: asdict(metadata) 
                for name, metadata in self.schema_metadata.items()
            }
            
            with open(metadata_file, 'w') as f:
                json.dump(metadata_dict, f, indent=2)
                
        except Exception as e:
            self.logger.warning(f"Failed to save schema metadata: {e}")
    
    def _calculate_schema_hash(self, schema: Union[Schema, Dict[str, Any]]) -> str:
        """Calculate hash for schema for caching and versioning."""
        if isinstance(schema, Schema):
            schema_dict = schema.to_dict()
        else:
            schema_dict = schema
        
        schema_str = json.dumps(schema_dict, sort_keys=True)
        return hashlib.sha256(schema_str.encode()).hexdigest()[:16]
    
    def _calculate_complexity_score(self, schema: Union[Schema, Dict[str, Any]]) -> int:
        """Calculate complexity score for schema optimization."""
        if isinstance(schema, Schema):
            schema_dict = schema.to_dict()
        else:
            schema_dict = schema
        
        score = 0
        score += len(schema_dict.get('required_columns', [])) * 1
        score += len(schema_dict.get('column_types', {})) * 2
        score += len(schema_dict.get('value_ranges', {})) * 3
        score += len(schema_dict.get('allowed_values', {})) * 2
        score += len(schema_dict.get('unique_columns', [])) * 4
        score += len(schema_dict.get('unique_combinations', [])) * 5
        score += len(schema_dict.get('relationships', [])) * 6
        score += len(schema_dict.get('custom_validators', [])) * 8
        
        return score
    
    def _compile_schema(self, schema: Schema) -> Dict[str, Any]:
        """Pre-compile schema for faster validation operations."""
        start_time = time.time()
        
        compiled = {
            'name': schema.name,
            'description': schema.description,
            'required_columns_set': set(schema.required_columns),
            'column_types_dict': dict(schema.column_types),
            'value_ranges_dict': dict(schema.value_ranges),
            'allowed_values_dict': dict(schema.allowed_values),
            'unique_columns_set': set(schema.unique_columns),
            'unique_combinations_list': list(schema.unique_combinations),
            'relationships_list': list(schema.relationships)
        }
        
        # Pre-compile regex patterns for string validations
        compiled['regex_patterns'] = {}
        for column, column_type in schema.column_types.items():
            if isinstance(column_type, dict) and 'pattern' in column_type:
                # Standard library imports
                import re
                compiled['regex_patterns'][column] = re.compile(column_type['pattern'])
        
        # Pre-compile allowed values sets for faster lookups
        compiled['allowed_values_sets'] = {
            column: set(values) 
            for column, values in schema.allowed_values.items()
        }
        
        compile_time = time.time() - start_time
        self.performance_stats["compilation_time"] += compile_time
        
        self.logger.debug(f"Compiled schema '{schema.name}' in {compile_time:.4f}s")
        return compiled
    
    @with_error_handling(category=ErrorCategory.VALIDATION, severity=ErrorSeverity.WARNING)
    def register_schema(self, 
                       schema: Union[Schema, Dict[str, Any]], 
                       name: Optional[str] = None,
                       version: str = "1.0.0",
                       force_update: bool = False) -> str:
        """
        Register schema with optimization and versioning.
        
        Args:
            schema: Schema to register
            name: Schema name (auto-detected if not provided)
            version: Schema version
            force_update: Force update if schema exists
            
        Returns:
            Schema identifier (name:version)
        """
        with self._lock:
            # Convert dict to Schema if needed
            if isinstance(schema, dict):
                schema_name = name or schema.get('name', 'unknown_schema')
                schema_obj = Schema.from_dict({**schema, 'name': schema_name})
            else:
                schema_obj = schema
                schema_name = name or schema_obj.name
            
            # Calculate metadata
            schema_hash = self._calculate_schema_hash(schema_obj)
            complexity_score = self._calculate_complexity_score(schema_obj)
            schema_id = f"{schema_name}:{version}"
            
            # Check if schema already exists
            if schema_id in self.schemas and not force_update:
                existing_hash = self.schema_metadata[schema_id].hash
                if existing_hash == schema_hash:
                    self.logger.debug(f"Schema '{schema_id}' already registered with same hash")
                    return schema_id
            
            # Register schema
            self.schemas[schema_id] = schema_obj
            self.compiled_schemas[schema_id] = self._compile_schema(schema_obj)
            
            # Update metadata
            current_time = time.time()
            if schema_id in self.schema_metadata:
                metadata = self.schema_metadata[schema_id]
                metadata.updated_at = current_time
                metadata.hash = schema_hash
                metadata.complexity_score = complexity_score
            else:
                metadata = SchemaMetadata(
                    name=schema_name,
                    version=version,
                    created_at=current_time,
                    hash=schema_hash,
                    complexity_score=complexity_score
                )
                self.schema_metadata[schema_id] = metadata
            
            # Update versioning info
            if schema_name not in self.schema_versions:
                self.schema_versions[schema_name] = []
                self.version_mapping[schema_name] = {}
            
            if version not in self.schema_versions[schema_name]:
                self.schema_versions[schema_name].append(version)
                self.schema_versions[schema_name].sort(key=lambda v: [int(x) for x in v.split('.')])
            
            self.version_mapping[schema_name][version] = schema_id
            
            # Cache schema to disk
            self._cache_schema_to_disk(schema_id, schema_obj)
            self._save_schema_metadata()
            
            # Cache in memory
            if self.cache_manager:
                try:
                    cache_key = f"schema_{schema_id}"
                    self.cache_manager.set(
                        cache_key, schema_obj, 
                        level=CacheLevel.MEMORY, 
                        ttl=3600
                    )
                except:
                    self.logger.debug("Schema caching failed, continuing without cache")
            
            self.logger.info(f"Registered schema '{schema_id}' (complexity: {complexity_score})")
            return schema_id
    
    def _cache_schema_to_disk(self, schema_id: str, schema: Schema):
        """Cache schema to disk for persistence."""
        try:
            schema_file = self.cache_dir / f"{schema_id.replace(':', '_')}.pkl"
            with open(schema_file, 'wb') as f:
                pickle.dump(schema, f)
        except Exception as e:
            self.logger.warning(f"Failed to cache schema to disk: {e}")
    
    def _load_schema_from_disk(self, schema_id: str) -> Optional[Schema]:
        """Load schema from disk cache."""
        try:
            schema_file = self.cache_dir / f"{schema_id.replace(':', '_')}.pkl"
            if schema_file.exists():
                with open(schema_file, 'rb') as f:
                    return pickle.load(f)
        except Exception as e:
            self.logger.warning(f"Failed to load schema from disk: {e}")
        return None
    
    @with_error_handling(category=ErrorCategory.VALIDATION, severity=ErrorSeverity.WARNING)
    def get_schema(self, 
                   name: str, 
                   version: Optional[str] = None,
                   compiled: bool = False) -> Optional[Union[Schema, Dict[str, Any]]]:
        """
        Get schema with performance optimization.
        
        Args:
            name: Schema name
            version: Schema version (latest if not specified)
            compiled: Return compiled schema for faster validation
            
        Returns:
            Schema or compiled schema dict
        """
        start_time = time.time()
        
        with self._lock:
            # Determine schema ID
            if version is None:
                # Get latest version
                if name in self.schema_versions and self.schema_versions[name]:
                    version = self.schema_versions[name][-1]  # Latest version
                else:
                    # Try direct name lookup
                    if name in self.schemas:
                        schema_id = name
                    else:
                        self.logger.warning(f"Schema '{name}' not found")
                        return None
            
            if 'schema_id' not in locals():
                schema_id = f"{name}:{version}"
            
            # Check memory cache first
            if schema_id in self.schemas:
                schema = self.schemas[schema_id]
                self.performance_stats["cache_hits"] += 1
            else:
                # Try memory cache via cache manager
                cache_key = f"schema_{schema_id}"
                schema = None
                if self.cache_manager:
                    try:
                        schema = self.cache_manager.get(cache_key, level=CacheLevel.MEMORY)
                    except:
                        self.logger.debug("Cache lookup failed")
                
                if schema is None:
                    # Try disk cache
                    schema = self._load_schema_from_disk(schema_id)
                    
                    if schema is None:
                        self.logger.warning(f"Schema '{schema_id}' not found in any cache")
                        self.performance_stats["cache_misses"] += 1
                        return None
                    
                    # Cache in memory for future use
                    self.schemas[schema_id] = schema
                    if self.cache_manager:
                        try:
                            self.cache_manager.set(cache_key, schema, level=CacheLevel.MEMORY, ttl=3600)
                        except:
                            self.logger.debug("Schema caching failed")
                
                self.performance_stats["cache_hits"] += 1
            
            # Update access statistics
            if schema_id in self.schema_metadata:
                metadata = self.schema_metadata[schema_id]
                metadata.usage_count += 1
                metadata.last_accessed = time.time()
            
            # Return compiled version if requested
            if compiled:
                if schema_id not in self.compiled_schemas:
                    self.compiled_schemas[schema_id] = self._compile_schema(schema)
                result = self.compiled_schemas[schema_id]
            else:
                result = schema
            
            # Update performance stats
            load_time = time.time() - start_time
            self.performance_stats["schema_loads"] += 1
            self.performance_stats["total_load_time"] += load_time
            
            self.logger.debug(f"Retrieved schema '{schema_id}' in {load_time:.4f}s")
            return result
    
    def get_schema_versions(self, name: str) -> List[str]:
        """Get all versions of a schema."""
        return self.schema_versions.get(name, [])
    
    def migrate_schema(self, 
                      from_version: str, 
                      to_version: str, 
                      migration_func: callable) -> bool:
        """
        Migrate schema from one version to another.
        
        Args:
            from_version: Source version
            to_version: Target version  
            migration_func: Function to perform migration
            
        Returns:
            Success status
        """
        # Implementation for schema migration
        # This would be used for handling schema evolution
        try:
            # Get source schema
            source_schema = self.get_schema(from_version)
            if source_schema is None:
                return False
            
            # Apply migration
            migrated_schema = migration_func(source_schema)
            
            # Register migrated schema
            self.register_schema(migrated_schema, version=to_version)
            
            self.logger.info(f"Successfully migrated schema from {from_version} to {to_version}")
            return True
            
        except Exception as e:
            self.logger.error(f"Schema migration failed: {e}")
            return False
    
    def cleanup_cache(self, max_age_hours: int = 24, max_unused_hours: int = 6):
        """Clean up old and unused schemas from cache."""
        current_time = time.time()
        max_age_seconds = max_age_hours * 3600
        max_unused_seconds = max_unused_hours * 3600
        
        schemas_to_remove = []
        
        with self._lock:
            for schema_id, metadata in self.schema_metadata.items():
                # Remove if too old
                if current_time - metadata.created_at > max_age_seconds:
                    schemas_to_remove.append(schema_id)
                    continue
                
                # Remove if unused for too long
                if current_time - metadata.last_accessed > max_unused_seconds:
                    schemas_to_remove.append(schema_id)
                    continue
            
            # Remove from caches
            for schema_id in schemas_to_remove:
                if schema_id in self.schemas:
                    del self.schemas[schema_id]
                if schema_id in self.compiled_schemas:
                    del self.compiled_schemas[schema_id]
                if schema_id in self.schema_metadata:
                    del self.schema_metadata[schema_id]
                
                # Remove from disk cache
                try:
                    schema_file = self.cache_dir / f"{schema_id.replace(':', '_')}.pkl"
                    if schema_file.exists():
                        schema_file.unlink()
                except Exception:
                    pass
        
        if schemas_to_remove:
            self.logger.info(f"Cleaned up {len(schemas_to_remove)} cached schemas")
            self._save_schema_metadata()
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics."""
        stats = self.performance_stats.copy()
        
        if stats["schema_loads"] > 0:
            stats["average_load_time"] = stats["total_load_time"] / stats["schema_loads"]
            stats["cache_hit_rate"] = stats["cache_hits"] / (stats["cache_hits"] + stats["cache_misses"])
        else:
            stats["average_load_time"] = 0
            stats["cache_hit_rate"] = 0
        
        stats["schemas_in_memory"] = len(self.schemas)
        stats["compiled_schemas"] = len(self.compiled_schemas)
        
        return stats
    
    def preload_schemas(self, schema_names: List[str]):
        """Preload schemas for better performance."""
        def load_schema(name):
            self.get_schema(name, compiled=True)
        
        with ThreadPoolExecutor(max_workers=4) as executor:
            executor.map(load_schema, schema_names)
        
        self.logger.info(f"Preloaded {len(schema_names)} schemas")


# Global optimized registry instance
_optimized_registry = None


def get_optimized_schema_registry() -> OptimizedSchemaRegistry:
    """Get global optimized schema registry instance."""
    global _optimized_registry
    if _optimized_registry is None:
        _optimized_registry = OptimizedSchemaRegistry()
    return _optimized_registry


# Convenience functions

def register_schema_optimized(schema: Union[Schema, Dict[str, Any]], 
                            name: Optional[str] = None,
                            version: str = "1.0.0") -> str:
    """Register schema with optimization."""
    registry = get_optimized_schema_registry()
    return registry.register_schema(schema, name, version)


def get_schema_optimized(name: str, 
                        version: Optional[str] = None,
                        compiled: bool = True) -> Optional[Union[Schema, Dict[str, Any]]]:
    """Get optimized schema."""
    registry = get_optimized_schema_registry()
    return registry.get_schema(name, version, compiled)


def preload_common_schemas():
    """Preload commonly used schemas."""
    registry = get_optimized_schema_registry()
    common_schemas = [
        'baci_trade_data_semantic',
        'yemen_exports',
        'yemen_imports', 
        'country_codes_processed',
        'product_codes',
        'worldbank_processed'
    ]
    registry.preload_schemas(common_schemas)


# Export public symbols
__all__ = [
    'SchemaVersion', 'SchemaMetadata', 'OptimizedSchemaRegistry',
    'get_optimized_schema_registry', 'register_schema_optimized',
    'get_schema_optimized', 'preload_common_schemas'
]