"""
Data Validator for Yemen Trade Diagnostic

This module provides comprehensive data validation for trade data at different pipeline stages.
It validates data integrity, checks for common issues, and provides clear error reporting.
"""

# Standard library imports
import json
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union

# Third-party imports
import numpy as np
import pandas as pd

# Project imports
from yemen_trade_diagnostic.interfaces.error_interface import (
    ErrorCategory,
    ErrorSeverity,
    with_error_handling,
)
from yemen_trade_diagnostic.interfaces.logging_interface import get_logger
from yemen_trade_diagnostic.interfaces.validation_interface import (
    ValidationIssueLevel,
    ValidationResult,
)

logger = get_logger(__name__)


class DataValidationResult:
    """Container for validation results with detailed issue tracking."""
    
    def __init__(self):
        self.is_valid = True
        self.checks = {}
        self.issues = []
        self.warnings = []
        self.stats = {}
        self.timestamp = datetime.now()
        
    def add_issue(self, issue: str, level: str = "error"):
        """Add a validation issue."""
        if level == "error":
            self.is_valid = False
            self.issues.append(issue)
        else:
            self.warnings.append(issue)
            
    def add_check(self, check_name: str, passed: bool, details: Optional[str] = None):
        """Add a validation check result."""
        self.checks[check_name] = {
            "passed": passed,
            "details": details
        }
        if not passed:
            self.is_valid = False
            
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "is_valid": self.is_valid,
            "checks": self.checks,
            "issues": self.issues,
            "warnings": self.warnings,
            "stats": self.stats,
            "timestamp": self.timestamp.isoformat()
        }


class DataValidator:
    """
    Comprehensive data validator for trade data.
    
    Validates data at different stages of the pipeline to ensure integrity
    and identify issues early.
    """
    
    def __init__(self):
        self.logger = logger
        self.country_codes = self._load_country_codes()
        self.product_codes = self._load_product_codes()
        
    def _load_country_codes(self) -> Dict[int, str]:
        """Load valid country codes."""
        try:
            path = Path("data/raw/baci/country_codes_V202501.csv")
            if path.exists():
                df = pd.read_csv(path)
                return dict(zip(df['country_code'], df['country_name_abbreviation']))
            else:
                self.logger.warning("Country codes file not found, using default Yemen code")
                return {887: "YEM"}  # At least include Yemen
        except Exception as e:
            self.logger.error(f"Error loading country codes: {e}")
            return {887: "YEM"}
            
    def _load_product_codes(self) -> set:
        """Load valid product codes."""
        try:
            path = Path("data/raw/baci/product_codes_HS02_V202501.csv")
            if path.exists():
                df = pd.read_csv(path)
                return set(df['code'].astype(str).str.zfill(6))
            else:
                self.logger.warning("Product codes file not found")
                return set()
        except Exception as e:
            self.logger.error(f"Error loading product codes: {e}")
            return set()
    
    @with_error_handling(category=ErrorCategory.VALIDATION, severity=ErrorSeverity.WARNING)
    def validate_baci_data(self, data: pd.DataFrame, year: Optional[int] = None) -> DataValidationResult:
        """
        Validate BACI trade data.
        
        Args:
            data: BACI DataFrame to validate
            year: Expected year (optional)
            
        Returns:
            DataValidationResult with detailed findings
        """
        result = DataValidationResult()
        
        # Check if DataFrame is empty
        if data is None or data.empty:
            result.add_issue("DataFrame is empty or None", "error")
            return result
            
        result.stats["total_rows"] = len(data)
        result.stats["memory_mb"] = data.memory_usage(deep=True).sum() / 1024**2
        
        # Check for required columns (both raw and semantic names)
        raw_columns = ['t', 'i', 'j', 'k', 'v', 'q']
        semantic_columns = ['year', 'exporter_code', 'importer_code', 'product_code', 'trade_value_usd', 'quantity']
        
        has_raw = all(col in data.columns for col in raw_columns)
        has_semantic = all(col in data.columns for col in semantic_columns)
        
        if not has_raw and not has_semantic:
            result.add_issue(f"Missing required columns. Found: {list(data.columns)}", "error")
            return result
            
        # Determine which column set we're using
        if has_semantic:
            year_col = 'year'
            exporter_col = 'exporter_code'
            importer_col = 'importer_code'
            product_col = 'product_code'
            value_col = 'trade_value_usd'
            quantity_col = 'quantity'
        else:
            year_col = 't'
            exporter_col = 'i'
            importer_col = 'j'
            product_col = 'k'
            value_col = 'v'
            quantity_col = 'q'
            
        result.add_check("required_columns", True, f"Using {'semantic' if has_semantic else 'raw'} column names")
        
        # Check year values
        if year_col in data.columns:
            unique_years = data[year_col].unique()
            result.stats["unique_years"] = list(unique_years)
            
            if year is not None and year not in unique_years:
                result.add_issue(f"Expected year {year} not found. Found: {unique_years}", "error")
                result.add_check("year_match", False, f"Expected {year}, found {unique_years}")
            else:
                result.add_check("year_match", True)
                
        # Check trade values
        if value_col in data.columns:
            zero_values = (data[value_col] == 0).sum()
            null_values = data[value_col].isna().sum()
            negative_values = (data[value_col] < 0).sum()
            
            result.stats["trade_values"] = {
                "total": len(data),
                "zero": int(zero_values),
                "null": int(null_values),
                "negative": int(negative_values),
                "positive": int(len(data) - zero_values - null_values - negative_values),
                "min": float(data[value_col].min()) if len(data) > 0 else 0,
                "max": float(data[value_col].max()) if len(data) > 0 else 0,
                "mean": float(data[value_col].mean()) if len(data) > 0 else 0
            }
            
            if negative_values > 0:
                result.add_issue(f"Found {negative_values} negative trade values", "error")
                result.add_check("no_negative_values", False, f"{negative_values} negative values")
            else:
                result.add_check("no_negative_values", True)
                
            if zero_values > len(data) * 0.9:
                result.add_issue(f"Over 90% of trade values are zero ({zero_values}/{len(data)})", "warning")
                
        # Check for Yemen data
        if exporter_col in data.columns and importer_col in data.columns:
            yemen_code = 887
            yemen_exports = data[data[exporter_col] == yemen_code]
            yemen_imports = data[data[importer_col] == yemen_code]
            
            result.stats["yemen_data"] = {
                "export_rows": len(yemen_exports),
                "import_rows": len(yemen_imports),
                "total_yemen_rows": len(data[(data[exporter_col] == yemen_code) | (data[importer_col] == yemen_code)])
            }
            
            if len(yemen_exports) == 0 and len(yemen_imports) == 0:
                result.add_issue("No Yemen data found (country code 887)", "warning")
                result.add_check("has_yemen_data", False, "No Yemen trade data")
            else:
                result.add_check("has_yemen_data", True, 
                               f"{result.stats['yemen_data']['total_yemen_rows']} Yemen-related rows")
                
        # Check country codes validity
        if self.country_codes and exporter_col in data.columns:
            invalid_exporters = ~data[exporter_col].isin(self.country_codes.keys())
            invalid_count = invalid_exporters.sum()
            if invalid_count > 0:
                unique_invalid = data[invalid_exporters][exporter_col].unique()[:5]
                result.add_issue(f"Found {invalid_count} rows with invalid exporter codes. Examples: {list(unique_invalid)}", "warning")
                
        # Check product codes validity
        if self.product_codes and product_col in data.columns:
            # Convert to string and pad with zeros
            product_strings = data[product_col].astype(str).str.zfill(6)
            invalid_products = ~product_strings.isin(self.product_codes)
            invalid_count = invalid_products.sum()
            if invalid_count > len(data) * 0.1:  # More than 10% invalid
                unique_invalid = product_strings[invalid_products].unique()[:5]
                result.add_issue(f"Found {invalid_count} rows with invalid product codes. Examples: {list(unique_invalid)}", "warning")
                
        return result
    
    @with_error_handling(category=ErrorCategory.VALIDATION, severity=ErrorSeverity.WARNING)
    def validate_pipeline_data(self, 
                             data: Dict[str, pd.DataFrame], 
                             pipeline_name: str,
                             expected_outputs: Optional[List[str]] = None) -> DataValidationResult:
        """
        Validate pipeline output data.
        
        Args:
            data: Dictionary of DataFrames from pipeline
            pipeline_name: Name of the pipeline
            expected_outputs: Expected output keys
            
        Returns:
            DataValidationResult with detailed findings
        """
        result = DataValidationResult()
        
        # Check if data is empty
        if not data:
            result.add_issue(f"Pipeline {pipeline_name} returned no data", "error")
            return result
            
        result.stats["output_keys"] = list(data.keys())
        
        # Check expected outputs
        if expected_outputs:
            missing = set(expected_outputs) - set(data.keys())
            if missing:
                result.add_issue(f"Missing expected outputs: {missing}", "error")
                result.add_check("expected_outputs", False, f"Missing: {missing}")
            else:
                result.add_check("expected_outputs", True)
                
        # Validate each output
        for key, df in data.items():
            if df is None:
                result.add_issue(f"Output '{key}' is None", "error")
                continue
                
            if isinstance(df, pd.DataFrame):
                if df.empty:
                    result.add_issue(f"Output '{key}' is empty DataFrame", "warning")
                    
                # Check for all-zero numeric columns
                numeric_cols = df.select_dtypes(include=[np.number]).columns
                for col in numeric_cols:
                    if (df[col] == 0).all():
                        result.add_issue(f"Column '{col}' in output '{key}' contains all zeros", "warning")
                        
                result.stats[f"{key}_shape"] = df.shape
                result.stats[f"{key}_memory_mb"] = df.memory_usage(deep=True).sum() / 1024**2
                
        return result
    
    def validate_data_flow(self, 
                          input_data: pd.DataFrame,
                          output_data: pd.DataFrame,
                          operation: str) -> DataValidationResult:
        """
        Validate data transformation between pipeline stages.
        
        Args:
            input_data: Input DataFrame
            output_data: Output DataFrame
            operation: Description of the operation
            
        Returns:
            DataValidationResult with detailed findings
        """
        result = DataValidationResult()
        
        # Check basic properties
        if input_data is not None and not input_data.empty:
            if output_data is None or output_data.empty:
                result.add_issue(f"Operation '{operation}' produced empty output from non-empty input", "error")
                result.add_check("data_preserved", False, "Output is empty")
            else:
                result.add_check("data_preserved", True)
                
                # Check if data was significantly reduced
                input_rows = len(input_data)
                output_rows = len(output_data)
                
                if output_rows < input_rows * 0.1:  # Lost more than 90% of data
                    result.add_issue(
                        f"Operation '{operation}' reduced data significantly: {input_rows} -> {output_rows} rows",
                        "warning"
                    )
                    
                result.stats["input_rows"] = input_rows
                result.stats["output_rows"] = output_rows
                result.stats["retention_rate"] = output_rows / input_rows if input_rows > 0 else 0
                
        return result
        
    def generate_validation_report(self, results: List[DataValidationResult]) -> Dict[str, Any]:
        """Generate a comprehensive validation report."""
        report = {
            "timestamp": datetime.now().isoformat(),
            "total_validations": len(results),
            "passed": sum(1 for r in results if r.is_valid),
            "failed": sum(1 for r in results if not r.is_valid),
            "total_issues": sum(len(r.issues) for r in results),
            "total_warnings": sum(len(r.warnings) for r in results),
            "validations": [r.to_dict() for r in results]
        }
        
        return report