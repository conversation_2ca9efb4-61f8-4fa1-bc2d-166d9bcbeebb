"""
Performance-Optimized Data Validation Module

This module provides high-performance validation implementations optimized for large datasets
(11+ million rows) with hardware acceleration support and intelligent caching.
"""

# Standard library imports
import hashlib
import json
import time
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor
from dataclasses import dataclass
from enum import Enum
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional, Set, Tuple, Union

# Third-party imports
import numpy as np
import pandas as pd

# Project imports
from yemen_trade_diagnostic.interfaces.cache_interface import CacheLevel, get_cache_manager
from yemen_trade_diagnostic.interfaces.error_interface import (
    ErrorCategory,
    ErrorSeverity,
    with_error_handling,
)
from yemen_trade_diagnostic.interfaces.hardware_interface import (
    AccelerationType,
    get_hardware_manager,
)
from yemen_trade_diagnostic.interfaces.logging_interface import get_logger

# V2 interface imports
from yemen_trade_diagnostic.interfaces.validation_interface import (
    ValidationIssue,
    ValidationIssueLevel,
    ValidationMode,
    ValidationResult,
)

logger = get_logger(__name__)


class ValidationPerformanceLevel(Enum):
    """Performance levels for validation optimization."""
    MAXIMUM_SPEED = "maximum_speed"      # Fastest validation, minimal checks
    BALANCED = "balanced"                # Balance between speed and thoroughness
    COMPREHENSIVE = "comprehensive"      # Most thorough validation
    CUSTOM = "custom"                   # Custom performance configuration


@dataclass
class ValidationPerformanceConfig:
    """Configuration for performance-optimized validation."""
    level: ValidationPerformanceLevel = ValidationPerformanceLevel.BALANCED
    use_hardware_acceleration: bool = True
    use_vectorized_operations: bool = True
    use_parallel_processing: bool = True
    max_workers: Optional[int] = None
    chunk_size: int = 100_000
    cache_validation_results: bool = True
    cache_ttl: int = 3600  # 1 hour
    early_termination: bool = True
    batch_size: int = 10_000
    memory_limit_mb: Optional[int] = None
    validate_schema_cache: bool = True


class PerformanceValidator:
    """
    High-performance validator optimized for large datasets.
    
    Features:
    - Hardware acceleration for numerical validations
    - Vectorized operations using NumPy
    - Parallel processing for large datasets
    - Intelligent caching of validation results
    - Memory-efficient chunk processing
    - Early termination for fail-fast scenarios
    """
    
    def __init__(self, config: Optional[ValidationPerformanceConfig] = None):
        """
        Initialize the performance validator.
        
        Args:
            config: Performance configuration settings
        """
        self.config = config or ValidationPerformanceConfig()
        self.logger = logger
        self.hw_manager = get_hardware_manager()
        # Initialize cache manager lazily to avoid circular dependencies
        self._cache_manager = None
        
        # Performance tracking
        self.performance_stats = {
            "validations_run": 0,
            "total_rows_validated": 0,
            "total_validation_time": 0.0,
            "cache_hits": 0,
            "hardware_accelerated_operations": 0,
            "vectorized_operations": 0
        }
        
        # Schema cache for fast access
        self.schema_cache: Dict[str, Any] = {}
        
        # Compiled validation functions cache
        self.compiled_validators: Dict[str, Callable] = {}
        
        self.logger.info(f"Initialized PerformanceValidator with {self.config.level.value} optimization")
    
    @property
    def cache_manager(self):
        """Lazy initialization of cache manager to avoid circular dependencies."""
        if self._cache_manager is None:
            try:
                self._cache_manager = get_cache_manager()
            except:
                # If cache manager fails to initialize, create a simple fallback
                self._cache_manager = None
                self.logger.warning("Cache manager initialization failed, disabling caching")
        return self._cache_manager
    
    def _get_cache_key(self, df: pd.DataFrame, schema_hash: str, mode: ValidationMode) -> str:
        """Generate cache key for validation result."""
        df_hash = hashlib.md5(
            f"{df.shape}_{df.columns.tolist()}_{df.dtypes.to_dict()}".encode()
        ).hexdigest()[:16]
        return f"perf_val_{df_hash}_{schema_hash}_{mode.value}"
    
    def _get_schema_hash(self, schema: Dict[str, Any]) -> str:
        """Generate hash for schema for caching."""
        schema_str = json.dumps(schema, sort_keys=True)
        return hashlib.md5(schema_str.encode()).hexdigest()[:16]
    
    @with_error_handling(category=ErrorCategory.VALIDATION, severity=ErrorSeverity.WARNING)
    def validate_large_dataset(self, 
                             df: pd.DataFrame, 
                             schema: Dict[str, Any],
                             mode: ValidationMode = ValidationMode.STANDARD) -> ValidationResult:
        """
        Validate large dataset with performance optimizations.
        
        Args:
            df: DataFrame to validate
            schema: Validation schema
            mode: Validation mode
            
        Returns:
            ValidationResult with performance metrics
        """
        start_time = time.time()
        schema_hash = self._get_schema_hash(schema)
        
        # Check cache first
        if self.config.cache_validation_results and self.cache_manager:
            cache_key = self._get_cache_key(df, schema_hash, mode)
            try:
                cached_result = self.cache_manager.get(cache_key, level=CacheLevel.MEMORY)
                if cached_result is not None:
                    self.performance_stats["cache_hits"] += 1
                    self.logger.debug(f"Using cached validation result for {len(df)} rows")
                    return cached_result
            except:
                self.logger.debug("Cache lookup failed, proceeding with validation")
        
        # Choose validation strategy based on data size and config
        if len(df) > self.config.chunk_size and self.config.use_parallel_processing:
            result = self._validate_parallel_chunks(df, schema, mode)
        elif len(df) > 50_000 and self.config.use_vectorized_operations:
            result = self._validate_vectorized(df, schema, mode)
        else:
            result = self._validate_standard(df, schema, mode)
        
        # Add performance metadata
        elapsed_time = time.time() - start_time
        result.details.update({
            "validation_time": elapsed_time,
            "rows_validated": len(df),
            "throughput": len(df) / elapsed_time if elapsed_time > 0 else 0,
            "optimization_level": self.config.level.value,
            "hardware_accelerated": self.config.use_hardware_acceleration,
            "vectorized": self.config.use_vectorized_operations,
            "parallel": self.config.use_parallel_processing
        })
        
        # Update performance stats
        self.performance_stats["validations_run"] += 1
        self.performance_stats["total_rows_validated"] += len(df)
        self.performance_stats["total_validation_time"] += elapsed_time
        
        # Cache result
        if self.config.cache_validation_results and self.cache_manager:
            try:
                self.cache_manager.set(
                    cache_key, result, 
                    level=CacheLevel.MEMORY, 
                    ttl=self.config.cache_ttl
                )
            except:
                self.logger.debug("Cache storage failed, continuing without cache")
        
        self.logger.info(f"Validated {len(df)} rows in {elapsed_time:.3f}s "
                        f"({len(df)/elapsed_time:.0f} rows/sec)")
        
        return result
    
    def _validate_parallel_chunks(self, 
                                df: pd.DataFrame, 
                                schema: Dict[str, Any],
                                mode: ValidationMode) -> ValidationResult:
        """Validate using parallel chunk processing."""
        chunk_size = self.config.chunk_size
        num_chunks = (len(df) + chunk_size - 1) // chunk_size
        max_workers = self.config.max_workers or min(4, num_chunks)
        
        self.logger.debug(f"Parallel validation: {num_chunks} chunks, {max_workers} workers")
        
        # Split DataFrame into chunks
        chunks = [df.iloc[i*chunk_size:(i+1)*chunk_size].copy() 
                 for i in range(num_chunks)]
        
        # Validate chunks in parallel
        combined_result = ValidationResult()
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit validation tasks
            futures = [
                executor.submit(self._validate_chunk, chunk, schema, mode, i)
                for i, chunk in enumerate(chunks)
            ]
            
            # Collect results
            for future in futures:
                try:
                    chunk_result = future.result(timeout=300)  # 5 minute timeout
                    combined_result.merge(chunk_result)
                    
                    # Early termination for fail-fast scenarios
                    if (self.config.early_termination and 
                        mode == ValidationMode.STRICT and 
                        not combined_result.is_valid):
                        self.logger.debug("Early termination due to validation failure")
                        break
                        
                except Exception as e:
                    self.logger.error(f"Chunk validation failed: {e}")
                    combined_result.add_issue(
                        f"Parallel validation error: {str(e)}",
                        ValidationIssueLevel.ERROR
                    )
        
        return combined_result
    
    def _validate_chunk(self, 
                       chunk: pd.DataFrame, 
                       schema: Dict[str, Any],
                       mode: ValidationMode,
                       chunk_id: int) -> ValidationResult:
        """Validate a single chunk of data."""
        return self._validate_vectorized(chunk, schema, mode)
    
    def _validate_vectorized(self, 
                           df: pd.DataFrame, 
                           schema: Dict[str, Any],
                           mode: ValidationMode) -> ValidationResult:
        """Validate using vectorized operations."""
        result = ValidationResult()
        
        # Use NumPy for vectorized validations where possible
        if self.config.use_hardware_acceleration and self.hw_manager and self.hw_manager.is_hardware_acceleration_available():
            result = self._validate_hardware_accelerated(df, schema, mode)
        else:
            result = self._validate_numpy_vectorized(df, schema, mode)
        
        self.performance_stats["vectorized_operations"] += 1
        return result
    
    def _validate_hardware_accelerated(self, 
                                     df: pd.DataFrame, 
                                     schema: Dict[str, Any],
                                     mode: ValidationMode) -> ValidationResult:
        """Validate using hardware acceleration (Neural Engine/GPU)."""
        result = ValidationResult()
        
        try:
            # Use hardware-accelerated operations for numerical validations
            for column, constraints in schema.get('column_types', {}).items():
                if column not in df.columns:
                    continue
                
                if constraints == 'numeric' and pd.api.types.is_numeric_dtype(df[column]):
                    # Hardware-accelerated range checks
                    if 'value_ranges' in schema and column in schema['value_ranges']:
                        ranges = schema['value_ranges'][column]
                        
                        # Use Neural Engine for large array operations
                        values_array = df[column].values
                        
                        if 'min' in ranges:
                            min_val = ranges['min']
                            # Vectorized comparison using hardware acceleration
                            invalid_mask = values_array < min_val
                            invalid_count = np.sum(invalid_mask)
                            
                            if invalid_count > 0:
                                result.add_issue(
                                    f"Column '{column}' has {invalid_count} values below minimum {min_val}",
                                    ValidationIssueLevel.ERROR,
                                    location=column
                                )
                        
                        if 'max' in ranges:
                            max_val = ranges['max']
                            invalid_mask = values_array > max_val
                            invalid_count = np.sum(invalid_mask)
                            
                            if invalid_count > 0:
                                result.add_issue(
                                    f"Column '{column}' has {invalid_count} values above maximum {max_val}",
                                    ValidationIssueLevel.ERROR,
                                    location=column
                                )
            
            self.performance_stats["hardware_accelerated_operations"] += 1
            
        except Exception as e:
            self.logger.warning(f"Hardware acceleration failed, falling back: {e}")
            # Fall back to standard validation
            result = self._validate_numpy_vectorized(df, schema, mode)
        
        return result
    
    def _validate_numpy_vectorized(self, 
                                 df: pd.DataFrame, 
                                 schema: Dict[str, Any],
                                 mode: ValidationMode) -> ValidationResult:
        """Validate using NumPy vectorized operations."""
        result = ValidationResult()
        
        # Required columns check (vectorized)
        if 'required_columns' in schema:
            required_cols = set(schema['required_columns'])
            existing_cols = set(df.columns)
            missing_cols = required_cols - existing_cols
            
            for col in missing_cols:
                result.add_issue(
                    f"Required column '{col}' is missing",
                    ValidationIssueLevel.ERROR,
                    location=col
                )
        
        # Column type validation (vectorized)
        if 'column_types' in schema:
            for column, expected_type in schema['column_types'].items():
                if column not in df.columns:
                    continue
                
                if expected_type == 'numeric':
                    if not pd.api.types.is_numeric_dtype(df[column]):
                        result.add_issue(
                            f"Column '{column}' is not numeric",
                            ValidationIssueLevel.ERROR,
                            location=column
                        )
                elif expected_type == 'string':
                    if not (pd.api.types.is_string_dtype(df[column]) or 
                           pd.api.types.is_object_dtype(df[column])):
                        result.add_issue(
                            f"Column '{column}' is not string type",
                            ValidationIssueLevel.ERROR,
                            location=column
                        )
        
        # Value range validation (vectorized)
        if 'value_ranges' in schema:
            for column, ranges in schema['value_ranges'].items():
                if column not in df.columns or not pd.api.types.is_numeric_dtype(df[column]):
                    continue
                
                values = df[column].dropna()
                if len(values) == 0:
                    continue
                
                if 'min' in ranges:
                    min_val = ranges['min']
                    invalid_count = np.sum(values < min_val)
                    if invalid_count > 0:
                        result.add_issue(
                            f"Column '{column}' has {invalid_count} values below minimum {min_val}",
                            ValidationIssueLevel.ERROR,
                            location=column
                        )
                
                if 'max' in ranges:
                    max_val = ranges['max']
                    invalid_count = np.sum(values > max_val)
                    if invalid_count > 0:
                        result.add_issue(
                            f"Column '{column}' has {invalid_count} values above maximum {max_val}",
                            ValidationIssueLevel.ERROR,
                            location=column
                        )
        
        # Uniqueness validation (vectorized)
        if 'unique_columns' in schema:
            for column in schema['unique_columns']:
                if column not in df.columns:
                    continue
                
                duplicate_count = df[column].duplicated().sum()
                if duplicate_count > 0:
                    result.add_issue(
                        f"Column '{column}' has {duplicate_count} duplicate values",
                        ValidationIssueLevel.ERROR,
                        location=column
                    )
        
        return result
    
    def _validate_standard(self, 
                         df: pd.DataFrame, 
                         schema: Dict[str, Any],
                         mode: ValidationMode) -> ValidationResult:
        """Standard validation for smaller datasets."""
        # Use existing validation interface for smaller datasets
        # Project imports
        from yemen_trade_diagnostic.data.schema_management import validate_schema
        return validate_schema(df, schema, mode)
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics."""
        stats = self.performance_stats.copy()
        
        if stats["total_validation_time"] > 0:
            stats["average_throughput"] = (
                stats["total_rows_validated"] / stats["total_validation_time"]
            )
        else:
            stats["average_throughput"] = 0
        
        if stats["validations_run"] > 0:
            stats["cache_hit_rate"] = stats["cache_hits"] / stats["validations_run"]
        else:
            stats["cache_hit_rate"] = 0
        
        return stats
    
    def reset_performance_stats(self):
        """Reset performance statistics."""
        self.performance_stats = {
            "validations_run": 0,
            "total_rows_validated": 0,
            "total_validation_time": 0.0,
            "cache_hits": 0,
            "hardware_accelerated_operations": 0,
            "vectorized_operations": 0
        }
        self.logger.info("Performance statistics reset")


class StreamingValidator:
    """
    Streaming validator for very large datasets that don't fit in memory.
    
    Processes data in chunks from disk without loading entire dataset.
    """
    
    def __init__(self, config: Optional[ValidationPerformanceConfig] = None):
        """Initialize streaming validator."""
        self.config = config or ValidationPerformanceConfig()
        self.logger = logger
        self.perf_validator = PerformanceValidator(config)
    
    @with_error_handling(category=ErrorCategory.VALIDATION, severity=ErrorSeverity.ERROR)
    def validate_csv_file(self, 
                         file_path: Union[str, Path],
                         schema: Dict[str, Any],
                         mode: ValidationMode = ValidationMode.STANDARD,
                         progress_callback: Optional[Callable] = None) -> ValidationResult:
        """
        Validate large CSV file using streaming approach.
        
        Args:
            file_path: Path to CSV file
            schema: Validation schema
            mode: Validation mode
            progress_callback: Optional callback for progress updates
            
        Returns:
            Combined validation result
        """
        file_path = Path(file_path)
        if not file_path.exists():
            result = ValidationResult()
            result.add_issue(
                f"File not found: {file_path}",
                ValidationIssueLevel.ERROR
            )
            return result
        
        self.logger.info(f"Starting streaming validation of {file_path}")
        start_time = time.time()
        
        combined_result = ValidationResult()
        total_rows = 0
        processed_rows = 0
        
        try:
            # Read file in chunks
            chunk_iter = pd.read_csv(
                file_path, 
                chunksize=self.config.chunk_size,
                low_memory=False
            )
            
            for chunk_id, chunk in enumerate(chunk_iter):
                chunk_start = time.time()
                
                # Validate chunk
                chunk_result = self.perf_validator._validate_vectorized(chunk, schema, mode)
                combined_result.merge(chunk_result)
                
                processed_rows += len(chunk)
                total_rows = processed_rows  # Update as we go
                
                # Progress callback
                if progress_callback:
                    elapsed_time = time.time() - start_time
                    progress_callback(processed_rows, elapsed_time, chunk_id)
                
                # Early termination
                if (self.config.early_termination and 
                    mode == ValidationMode.STRICT and 
                    not combined_result.is_valid):
                    self.logger.info(f"Early termination after {processed_rows} rows")
                    break
                
                chunk_time = time.time() - chunk_start
                self.logger.debug(f"Chunk {chunk_id}: {len(chunk)} rows in {chunk_time:.3f}s")
        
        except Exception as e:
            self.logger.error(f"Streaming validation failed: {e}")
            combined_result.add_issue(
                f"Streaming validation error: {str(e)}",
                ValidationIssueLevel.ERROR
            )
        
        # Add performance metadata
        elapsed_time = time.time() - start_time
        combined_result.details.update({
            "file_path": str(file_path),
            "streaming_validation": True,
            "total_rows_processed": processed_rows,
            "validation_time": elapsed_time,
            "throughput": processed_rows / elapsed_time if elapsed_time > 0 else 0,
            "chunk_size": self.config.chunk_size
        })
        
        self.logger.info(f"Streaming validation complete: {processed_rows} rows in {elapsed_time:.3f}s "
                        f"({processed_rows/elapsed_time:.0f} rows/sec)")
        
        return combined_result


# Factory functions for easy instantiation

def create_performance_validator(level: ValidationPerformanceLevel = ValidationPerformanceLevel.BALANCED,
                               **kwargs) -> PerformanceValidator:
    """Create a performance validator with specified optimization level."""
    config = ValidationPerformanceConfig(level=level, **kwargs)
    return PerformanceValidator(config)


def create_streaming_validator(chunk_size: int = 100_000, **kwargs) -> StreamingValidator:
    """Create a streaming validator for very large files."""
    config = ValidationPerformanceConfig(chunk_size=chunk_size, **kwargs)
    return StreamingValidator(config)


# Convenience functions for direct use

def validate_large_dataframe(df: pd.DataFrame, 
                           schema: Dict[str, Any],
                           optimization_level: ValidationPerformanceLevel = ValidationPerformanceLevel.BALANCED,
                           mode: ValidationMode = ValidationMode.STANDARD) -> ValidationResult:
    """
    Validate large DataFrame with performance optimizations.
    
    Args:
        df: DataFrame to validate
        schema: Validation schema
        optimization_level: Performance optimization level
        mode: Validation mode
        
    Returns:
        ValidationResult with performance metrics
    """
    validator = create_performance_validator(optimization_level)
    return validator.validate_large_dataset(df, schema, mode)


def validate_large_csv_file(file_path: Union[str, Path],
                          schema: Dict[str, Any],
                          chunk_size: int = 100_000,
                          mode: ValidationMode = ValidationMode.STANDARD,
                          progress_callback: Optional[Callable] = None) -> ValidationResult:
    """
    Validate large CSV file using streaming approach.
    
    Args:
        file_path: Path to CSV file
        schema: Validation schema
        chunk_size: Size of chunks to process
        mode: Validation mode
        progress_callback: Optional progress callback
        
    Returns:
        ValidationResult from streaming validation
    """
    validator = create_streaming_validator(chunk_size=chunk_size)
    return validator.validate_csv_file(file_path, schema, mode, progress_callback)


# Export all public symbols
__all__ = [
    'ValidationPerformanceLevel', 'ValidationPerformanceConfig',
    'PerformanceValidator', 'StreamingValidator',
    'create_performance_validator', 'create_streaming_validator',
    'validate_large_dataframe', 'validate_large_csv_file'
]