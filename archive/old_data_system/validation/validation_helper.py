"""
Data Validation Helper for Yemen Trade Diagnostic V2

Provides helper functions and utilities for data validation tasks,
integrated with V2 interfaces for error handling and logging.
"""

# Standard library imports
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union

# Third-party imports
import numpy as np
import pandas as pd

# Project imports
from yemen_trade_diagnostic.interfaces.error_interface import (
    ErrorCategory,
    ErrorSeverity,
    with_error_handling,
)
from yemen_trade_diagnostic.interfaces.hardware_interface import get_hardware_manager
from yemen_trade_diagnostic.interfaces.logging_interface import get_logger

# V2 interface imports
from yemen_trade_diagnostic.interfaces.validation_interface import (
    ValidationIssueLevel,
    ValidationManager,
    ValidationMode,
    ValidationResult,
    validate_dataframe,
    with_validation,
)

logger = get_logger(__name__)


class DataValidationHelper:
    """Helper class for data validation tasks in V2 architecture."""
    
    def __init__(self):
        self.validation_manager = ValidationManager()
        self.hw_manager = get_hardware_manager()
        self.logger = logger
        
    @with_error_handling(
        category=ErrorCategory.VALIDATION,
        severity=ErrorSeverity.ERROR,
        fallback_value=False
    )
    def validate_baci_columns(self, df: pd.DataFrame) -> ValidationResult:
        """Validate BACI data columns."""
        required_columns = ['t', 'i', 'j', 'k', 'v', 'q']
        
        result = ValidationResult()
        
        # Check for missing columns
        missing_cols = set(required_columns) - set(df.columns)
        if missing_cols:
            result.add_issue(
                ValidationIssueLevel.ERROR,
                f"Missing required BACI columns: {missing_cols}",
                {"missing_columns": list(missing_cols)}
            )
        
        # Check data types
        if 't' in df.columns and not pd.api.types.is_numeric_dtype(df['t']):
            result.add_issue(
                ValidationIssueLevel.ERROR,
                "Year column 't' must be numeric",
                {"actual_dtype": str(df['t'].dtype)}
            )
            
        # Check for null values
        for col in required_columns:
            if col in df.columns:
                null_count = df[col].isna().sum()
                if null_count > 0:
                    result.add_issue(
                        ValidationIssueLevel.WARNING,
                        f"Column '{col}' contains {null_count} null values",
                        {"column": col, "null_count": int(null_count)}
                    )
        
        return result
    
    @with_error_handling(
        category=ErrorCategory.VALIDATION,
        severity=ErrorSeverity.ERROR,
        fallback_value=False
    )
    def validate_year_range(self, df: pd.DataFrame, column: str = 't',
                          min_year: int = 2000, max_year: int = 2025) -> ValidationResult:
        """Validate year values are within expected range."""
        result = ValidationResult()
        
        if column not in df.columns:
            result.add_issue(
                ValidationIssueLevel.ERROR,
                f"Year column '{column}' not found",
                {"column": column}
            )
            return result
        
        # Use hardware acceleration if available
        if self.hw_manager.is_hardware_available():
            years = self.hw_manager.accelerate_array(df[column].values, operation="unique")
        else:
            years = df[column].unique()
        
        # Check year bounds
        invalid_years = years[(years < min_year) | (years > max_year)]
        if len(invalid_years) > 0:
            result.add_issue(
                ValidationIssueLevel.ERROR,
                f"Invalid years found: {invalid_years}",
                {
                    "invalid_years": invalid_years.tolist(),
                    "expected_range": [min_year, max_year]
                }
            )
        
        return result
    
    @with_error_handling(
        category=ErrorCategory.VALIDATION,
        severity=ErrorSeverity.ERROR,
        fallback_value=False
    )
    def validate_country_codes(self, df: pd.DataFrame, 
                             exporter_col: str = 'i',
                             importer_col: str = 'j') -> ValidationResult:
        """Validate country codes are valid."""
        result = ValidationResult()
        
        # Load country codes reference
        try:
            # Project imports
            from yemen_trade_diagnostic.data.loaders import CountryCodesLoader
            loader = CountryCodesLoader()
            country_codes = loader.load()
            valid_codes = set(country_codes['iso_code'].unique())
        except Exception as e:
            result.add_issue(
                ValidationIssueLevel.ERROR,
                f"Failed to load country codes: {str(e)}",
                {"error": str(e)}
            )
            return result
        
        # Check exporter codes
        if exporter_col in df.columns:
            invalid_exporters = set(df[exporter_col].unique()) - valid_codes
            if invalid_exporters:
                result.add_issue(
                    ValidationIssueLevel.WARNING,
                    f"Invalid exporter codes: {list(invalid_exporters)[:10]}...",
                    {
                        "column": exporter_col,
                        "invalid_count": len(invalid_exporters),
                        "sample": list(invalid_exporters)[:10]
                    }
                )
        
        # Check importer codes
        if importer_col in df.columns:
            invalid_importers = set(df[importer_col].unique()) - valid_codes
            if invalid_importers:
                result.add_issue(
                    ValidationIssueLevel.WARNING,
                    f"Invalid importer codes: {list(invalid_importers)[:10]}...",
                    {
                        "column": importer_col,
                        "invalid_count": len(invalid_importers),
                        "sample": list(invalid_importers)[:10]
                    }
                )
        
        return result
    
    @with_error_handling(
        category=ErrorCategory.VALIDATION,
        severity=ErrorSeverity.ERROR,
        fallback_value=False
    )
    def validate_product_codes(self, df: pd.DataFrame,
                             product_col: str = 'k',
                             code_length: int = 6) -> ValidationResult:
        """Validate product codes match expected format."""
        result = ValidationResult()
        
        if product_col not in df.columns:
            result.add_issue(
                ValidationIssueLevel.ERROR,
                f"Product column '{product_col}' not found",
                {"column": product_col}
            )
            return result
        
        # Check code format
        invalid_codes = df[~df[product_col].astype(str).str.match(r'^\d{' + str(code_length) + r'}$')]
        if len(invalid_codes) > 0:
            sample_invalid = invalid_codes[product_col].head(10).tolist()
            result.add_issue(
                ValidationIssueLevel.WARNING,
                f"Invalid product codes found: {len(invalid_codes)} rows",
                {
                    "invalid_count": len(invalid_codes),
                    "expected_length": code_length,
                    "sample": sample_invalid
                }
            )
        
        return result
    
    @with_error_handling(
        category=ErrorCategory.VALIDATION,
        severity=ErrorSeverity.ERROR,
        fallback_value=False
    )
    def validate_trade_values(self, df: pd.DataFrame,
                            value_col: str = 'v',
                            quantity_col: str = 'q') -> ValidationResult:
        """Validate trade values are reasonable."""
        result = ValidationResult()
        
        # Check for negative values
        if value_col in df.columns:
            negative_values = df[df[value_col] < 0]
            if len(negative_values) > 0:
                result.add_issue(
                    ValidationIssueLevel.ERROR,
                    f"Negative trade values found: {len(negative_values)} rows",
                    {
                        "column": value_col,
                        "negative_count": len(negative_values),
                        "sample_values": negative_values[value_col].head(5).tolist()
                    }
                )
        
        # Check for extremely high values
        if value_col in df.columns:
            threshold = df[value_col].quantile(0.999)
            extreme_values = df[df[value_col] > threshold * 10]
            if len(extreme_values) > 0:
                result.add_issue(
                    ValidationIssueLevel.WARNING,
                    f"Extremely high values found: {len(extreme_values)} rows",
                    {
                        "column": value_col,
                        "extreme_count": len(extreme_values),
                        "threshold": float(threshold * 10),
                        "sample_values": extreme_values[value_col].head(5).tolist()
                    }
                )
        
        # Check quantity consistency
        if quantity_col in df.columns:
            zero_quantity = df[(df[quantity_col] == 0) & (df[value_col] > 0)]
            if len(zero_quantity) > 0:
                result.add_issue(
                    ValidationIssueLevel.WARNING,
                    f"Inconsistent quantity/value: {len(zero_quantity)} rows with zero quantity but positive value",
                    {
                        "inconsistent_count": len(zero_quantity),
                        "sample_rows": zero_quantity.head(3).to_dict('records')
                    }
                )
        
        return result
    
    def validate_full_dataset(self, df: pd.DataFrame, 
                            dataset_type: str = "baci") -> ValidationResult:
        """Run comprehensive validation on a dataset."""
        result = ValidationResult()
        
        self.logger.info(f"Running full validation on {dataset_type} dataset")
        
        # Run individual validations based on dataset type
        if dataset_type == "baci":
            result.merge(self.validate_baci_columns(df))
            result.merge(self.validate_year_range(df))
            result.merge(self.validate_country_codes(df))
            result.merge(self.validate_product_codes(df))
            result.merge(self.validate_trade_values(df))
        
        # Add summary
        result.context["total_rows"] = len(df)
        result.context["total_columns"] = len(df.columns)
        result.context["dataset_type"] = dataset_type
        
        self.logger.info(f"Validation complete: {result.is_valid()} - {len(result.issues)} issues found")
        
        return result