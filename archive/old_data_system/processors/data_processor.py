"""
Implementation of DataProcessor for the Yemen Trade Diagnostic system.

This module provides concrete implementations of the processor base classes
for common data processing tasks in the Yemen Trade Diagnostic system, including
filtering, transformation, enrichment, and aggregation.
"""

# Standard library imports
from enum import Enum
from typing import Any, Callable, Dict, List, Optional, Tuple, Union

# Third-party imports
import numpy as np
import pandas as pd

# Project imports
from yemen_trade_diagnostic.data.processors.base_processor import (
    DataFrameProcessor,
    MultiStageProcessor,
    ProcessingMode,
    ProcessingResult,
)
from yemen_trade_diagnostic.interfaces.error_interface import (
    ErrorCategory,
    ErrorSeverity,
    with_error_handling,
)
from yemen_trade_diagnostic.interfaces.logging_interface import get_logger
from yemen_trade_diagnostic.utils.memory_utils import get_memory_usage, optimize_dataframe

logger = get_logger(__name__)

class FilterType(Enum):
    """Types of filtering operations for DataFrames."""
    EXACT = "exact"
    CONTAINS = "contains"
    REGEX = "regex"
    RANGE = "range"
    GREATER_THAN = "greater_than"
    LESS_THAN = "less_than"
    IS_IN = "is_in"
    IS_NULL = "is_null"
    IS_NOT_NULL = "is_not_null"
    CUSTOM = "custom"

class GroupOperation(Enum):
    """Types of group operations for DataFrames."""
    SUM = "sum"
    MEAN = "mean"
    MEDIAN = "median"
    MIN = "min"
    MAX = "max"
    COUNT = "count"
    STD = "std"
    VAR = "var"
    FIRST = "first"
    LAST = "last"
    CUSTOM = "custom"

class DataFilterProcessor(DataFrameProcessor):
    """
    Processor for filtering DataFrames based on various criteria.
    
    This processor supports a wide range of filtering operations, including
    exact matches, contains, regex, range, comparison operators, and custom filters.
    """
    
    def __init__(self, 
                name: str = "data_filter",
                filter_type: Union[FilterType, str] = FilterType.EXACT,
                **kwargs) -> None:
        """
        Initialize the data filter processor.
        
        Args:
            name: Processor name
            filter_type: Type of filtering to apply
            **kwargs: Additional arguments for DataFrameProcessor
        """
        super().__init__(name, **kwargs)
        
        if isinstance(filter_type, str):
            try:
                self.filter_type = FilterType(filter_type)
            except ValueError:
                logger.warning(f"Invalid filter type: {filter_type}, defaulting to EXACT")
                self.filter_type = FilterType.EXACT
        else:
            self.filter_type = filter_type
    
    def _process_data(self, 
                     data: pd.DataFrame, 
                     params: Optional[Dict[str, Any]] = None) -> pd.DataFrame:
        """
        Filter DataFrame based on specified criteria.
        
        Args:
            data: Input DataFrame
            params: Filtering parameters including:
                   - filters: Dict of {column: value} filters
                   - filter_type: Override default filter type
                   - custom_filter: Custom filter function
                   
        Returns:
            Filtered DataFrame
        """
        if not params or "filters" not in params:
            logger.warning("No filters provided, returning original DataFrame")
            return data
        
        # Get filter parameters
        filters = params.get("filters", {})
        filter_type = params.get("filter_type", self.filter_type)
        
        if isinstance(filter_type, str):
            try:
                filter_type = FilterType(filter_type)
            except ValueError:
                logger.warning(f"Invalid filter type: {filter_type}, using default: {self.filter_type.value}")
                filter_type = self.filter_type
        
        # Create a copy of the input DataFrame
        # Using copy to avoid modifying the original DataFrame
        filtered_df = data.copy()
        
        # Apply filters based on filter type
        if filter_type == FilterType.EXACT:
            for column, value in filters.items():
                if column in filtered_df.columns:
                    if isinstance(value, list):
                        filtered_df = filtered_df[filtered_df[column].isin(value)]
                    else:
                        filtered_df = filtered_df[filtered_df[column] == value]
        
        elif filter_type == FilterType.CONTAINS:
            for column, value in filters.items():
                if column in filtered_df.columns:
                    if isinstance(value, list):
                        mask = filtered_df[column].astype(str).apply(
                            lambda x: any(str(v) in x for v in value)
                        )
                        filtered_df = filtered_df[mask]
                    else:
                        filtered_df = filtered_df[filtered_df[column].astype(str).str.contains(str(value))]
        
        elif filter_type == FilterType.REGEX:
            for column, pattern in filters.items():
                if column in filtered_df.columns:
                    filtered_df = filtered_df[filtered_df[column].astype(str).str.match(pattern)]
        
        elif filter_type == FilterType.RANGE:
            for column, value in filters.items():
                if column in filtered_df.columns and isinstance(value, list) and len(value) == 2:
                    min_val, max_val = value
                    filtered_df = filtered_df[
                        (filtered_df[column] >= min_val) & 
                        (filtered_df[column] <= max_val)
                    ]
        
        elif filter_type == FilterType.GREATER_THAN:
            for column, value in filters.items():
                if column in filtered_df.columns:
                    filtered_df = filtered_df[filtered_df[column] > value]
        
        elif filter_type == FilterType.LESS_THAN:
            for column, value in filters.items():
                if column in filtered_df.columns:
                    filtered_df = filtered_df[filtered_df[column] < value]
        
        elif filter_type == FilterType.IS_IN:
            for column, values in filters.items():
                if column in filtered_df.columns and isinstance(values, list):
                    filtered_df = filtered_df[filtered_df[column].isin(values)]
        
        elif filter_type == FilterType.IS_NULL:
            for column in filters:
                if column in filtered_df.columns:
                    filtered_df = filtered_df[filtered_df[column].isna()]
        
        elif filter_type == FilterType.IS_NOT_NULL:
            for column in filters:
                if column in filtered_df.columns:
                    filtered_df = filtered_df[~filtered_df[column].isna()]
        
        elif filter_type == FilterType.CUSTOM:
            # Custom filter function provided in params
            custom_filter = params.get("custom_filter")
            if callable(custom_filter):
                filtered_df = custom_filter(filtered_df)
            else:
                logger.warning("Custom filter specified but no function provided")
        
        logger.info(f"Filtered DataFrame from {len(data)} to {len(filtered_df)} rows")
        
        # Optimize memory usage of the filtered DataFrame if needed
        if self.mode == ProcessingMode.MEMORY_EFFICIENT or params.get("optimize_memory", False):
            filtered_df = optimize_dataframe(filtered_df)
            
        return filtered_df

class DataGroupProcessor(DataFrameProcessor):
    """
    Processor for grouping and aggregating DataFrames.
    
    This processor supports grouping by one or more columns and applying
    various aggregation operations to other columns.
    """
    
    def __init__(self, 
                name: str = "data_group",
                **kwargs) -> None:
        """Initialize the data group processor."""
        super().__init__(name, **kwargs)
    
    def _process_data(self, 
                     data: pd.DataFrame, 
                     params: Optional[Dict[str, Any]] = None) -> pd.DataFrame:
        """
        Group and aggregate DataFrame.
        
        Args:
            data: Input DataFrame
            params: Grouping parameters including:
                   - group_by: Column(s) to group by
                   - aggregations: Dict of {column: operation} aggregations
                   - custom_aggregation: Custom aggregation function
                   
        Returns:
            Grouped and aggregated DataFrame
        """
        if not params or "group_by" not in params:
            logger.warning("No group_by columns provided, returning original DataFrame")
            return data
        
        # Get grouping parameters
        group_by = params.get("group_by", [])
        if isinstance(group_by, str):
            group_by = [group_by]
        
        # Check if group_by columns exist
        for col in group_by:
            if col not in data.columns:
                logger.warning(f"Group column '{col}' not found in DataFrame, skipping grouping")
                return data
        
        # Group the DataFrame
        grouped = data.groupby(group_by, as_index=False)
        
        # Apply aggregations
        aggregations = params.get("aggregations", {})
        custom_aggregation = params.get("custom_aggregation")
        
        if custom_aggregation and callable(custom_aggregation):
            # Apply custom aggregation function
            result = custom_aggregation(grouped)
        elif aggregations:
            # Apply standard aggregations
            agg_dict = {}
            for column, operation in aggregations.items():
                if column in data.columns:
                    if isinstance(operation, str):
                        try:
                            operation = GroupOperation(operation)
                        except ValueError:
                            logger.warning(f"Invalid operation: {operation}, defaulting to SUM")
                            operation = GroupOperation.SUM
                    
                    if isinstance(operation, GroupOperation):
                        if operation == GroupOperation.SUM:
                            agg_dict[column] = 'sum'
                        elif operation == GroupOperation.MEAN:
                            agg_dict[column] = 'mean'
                        elif operation == GroupOperation.MEDIAN:
                            agg_dict[column] = 'median'
                        elif operation == GroupOperation.MIN:
                            agg_dict[column] = 'min'
                        elif operation == GroupOperation.MAX:
                            agg_dict[column] = 'max'
                        elif operation == GroupOperation.COUNT:
                            agg_dict[column] = 'count'
                        elif operation == GroupOperation.STD:
                            agg_dict[column] = 'std'
                        elif operation == GroupOperation.VAR:
                            agg_dict[column] = 'var'
                        elif operation == GroupOperation.FIRST:
                            agg_dict[column] = 'first'
                        elif operation == GroupOperation.LAST:
                            agg_dict[column] = 'last'
                        elif operation == GroupOperation.CUSTOM:
                            # Skip custom operations here
                            pass
                    elif callable(operation):
                        agg_dict[column] = operation
            
            if agg_dict:
                result = grouped.agg(agg_dict)
            else:
                logger.warning("No valid aggregations provided, returning count per group")
                result = grouped.size().reset_index(name='count')
        else:
            # Default to count if no aggregations specified
            logger.info("No aggregations specified, using count")
            result = grouped.size().reset_index(name='count')
        
        logger.info(f"Grouped DataFrame by {group_by}, resulting in {len(result)} groups")
        
        # Optimize memory usage if needed
        if self.mode == ProcessingMode.MEMORY_EFFICIENT or params.get("optimize_memory", False):
            result = optimize_dataframe(result)
            
        return result

class DataTransformProcessor(DataFrameProcessor):
    """
    Processor for transforming DataFrames with various operations.
    
    This processor supports column transformations, adding new columns,
    renaming columns, and other common DataFrame transformations.
    """
    
    def __init__(self, 
                name: str = "data_transform",
                **kwargs) -> None:
        """Initialize the data transform processor."""
        super().__init__(name, **kwargs)
    
    def _process_data(self, 
                     data: pd.DataFrame, 
                     params: Optional[Dict[str, Any]] = None) -> pd.DataFrame:
        """
        Transform DataFrame with various operations.
        
        Args:
            data: Input DataFrame
            params: Transformation parameters including:
                   - transformations: List of transformation configurations
                   - add_columns: Dict of {column: value/function} to add
                   - drop_columns: List of columns to drop
                   - rename_columns: Dict of {old_name: new_name}
                   - custom_transform: Custom transform function
                   
        Returns:
            Transformed DataFrame
        """
        if not params:
            logger.warning("No transformation parameters provided, returning original DataFrame")
            return data
        
        # Create a copy of the DataFrame to avoid modifying the original
        transformed_df = data.copy()
        
        # Apply custom transform if provided
        custom_transform = params.get("custom_transform")
        if callable(custom_transform):
            transformed_df = custom_transform(transformed_df)
        
        # Apply column transformations
        transformations = params.get("transformations", [])
        for transform in transformations:
            if isinstance(transform, dict):
                column = transform.get("column")
                operation = transform.get("operation")
                args = transform.get("args", {})
                
                if column and operation and column in transformed_df.columns:
                    if operation == "apply" and "function" in args and callable(args["function"]):
                        transformed_df[column] = transformed_df[column].apply(args["function"])
                    elif operation == "map" and "mapping" in args:
                        transformed_df[column] = transformed_df[column].map(args["mapping"])
                    elif operation == "replace" and "to_replace" in args and "value" in args:
                        transformed_df[column] = transformed_df[column].replace(args["to_replace"], args["value"])
                    elif operation == "fillna" and "value" in args:
                        transformed_df[column] = transformed_df[column].fillna(args["value"])
                    elif operation == "astype" and "dtype" in args:
                        try:
                            transformed_df[column] = transformed_df[column].astype(args["dtype"])
                        except (ValueError, TypeError) as e:
                            logger.warning(f"Error converting column {column} to {args['dtype']}: {str(e)}")
                    elif operation == "normalize" and args.get("method") == "min_max":
                        min_val = transformed_df[column].min()
                        max_val = transformed_df[column].max()
                        if max_val > min_val:
                            transformed_df[column] = (transformed_df[column] - min_val) / (max_val - min_val)
                    elif operation == "normalize" and args.get("method") == "z_score":
                        mean = transformed_df[column].mean()
                        std = transformed_df[column].std()
                        if std > 0:
                            transformed_df[column] = (transformed_df[column] - mean) / std
                    elif operation == "bin" and "bins" in args:
                        transformed_df[column] = pd.cut(transformed_df[column], args["bins"], labels=args.get("labels"))
                    elif operation == "log" and transformed_df[column].min() > 0:
                        base = args.get("base", np.e)
                        transformed_df[column] = np.log(transformed_df[column]) / np.log(base)
        
        # Add new columns
        add_columns = params.get("add_columns", {})
        for column, value in add_columns.items():
            if callable(value):
                transformed_df[column] = value(transformed_df)
            else:
                transformed_df[column] = value
        
        # Drop columns
        drop_columns = params.get("drop_columns", [])
        if drop_columns:
            transformed_df = transformed_df.drop(columns=[col for col in drop_columns if col in transformed_df.columns])
        
        # Rename columns
        rename_columns = params.get("rename_columns", {})
        if rename_columns:
            transformed_df = transformed_df.rename(columns=rename_columns)
        
        logger.info(f"Transformed DataFrame: {len(transformed_df)} rows, {len(transformed_df.columns)} columns")
        
        # Optimize memory usage if needed
        if self.mode == ProcessingMode.MEMORY_EFFICIENT or params.get("optimize_memory", False):
            transformed_df = optimize_dataframe(transformed_df)
            
        return transformed_df

class DataEnrichProcessor(DataFrameProcessor):
    """
    Processor for enriching DataFrames with additional data.
    
    This processor supports merging with other DataFrames, adding
    computed columns, and other enrichment operations.
    """
    
    def __init__(self, 
                name: str = "data_enrich",
                **kwargs) -> None:
        """Initialize the data enrich processor."""
        super().__init__(name, **kwargs)
    
    def _process_data(self, 
                     data: pd.DataFrame, 
                     params: Optional[Dict[str, Any]] = None) -> pd.DataFrame:
        """
        Enrich DataFrame with additional data.
        
        Args:
            data: Input DataFrame
            params: Enrichment parameters including:
                   - join_data: DataFrame to join with
                   - join_on: Column(s) to join on
                   - join_how: Join type (left, right, inner, outer)
                   - computed_columns: Dict of {column: function} for computed columns
                   - custom_enrich: Custom enrichment function
                   
        Returns:
            Enriched DataFrame
        """
        if not params:
            logger.warning("No enrichment parameters provided, returning original DataFrame")
            return data
        
        # Create a copy of the DataFrame to avoid modifying the original
        enriched_df = data.copy()
        
        # Apply custom enrichment if provided
        custom_enrich = params.get("custom_enrich")
        if callable(custom_enrich):
            enriched_df = custom_enrich(enriched_df)
        
        # Merge with another DataFrame
        join_data = params.get("join_data")
        join_on = params.get("join_on")
        join_how = params.get("join_how", "left")
        
        if isinstance(join_data, pd.DataFrame) and join_on:
            try:
                enriched_df = pd.merge(
                    enriched_df, 
                    join_data, 
                    on=join_on, 
                    how=join_how,
                    suffixes=params.get("suffixes", ("", "_y"))
                )
                logger.info(f"Merged with join_data on {join_on} using {join_how} join")
            except Exception as e:
                logger.error(f"Error merging DataFrames: {str(e)}")
        
        # Add computed columns
        computed_columns = params.get("computed_columns", {})
        for column, function in computed_columns.items():
            if callable(function):
                try:
                    enriched_df[column] = function(enriched_df)
                    logger.debug(f"Added computed column '{column}'")
                except Exception as e:
                    logger.warning(f"Error computing column '{column}': {str(e)}")
        
        logger.info(f"Enriched DataFrame: {len(enriched_df)} rows, {len(enriched_df.columns)} columns")
        
        # Optimize memory usage if needed
        if self.mode == ProcessingMode.MEMORY_EFFICIENT or params.get("optimize_memory", False):
            enriched_df = optimize_dataframe(enriched_df)
            
        return enriched_df

class DataCleanProcessor(DataFrameProcessor):
    """
    Processor for cleaning and preparing DataFrames.
    
    This processor supports handling missing values, removing duplicates,
    standardizing column names, and other data cleaning operations.
    """
    
    def __init__(self, 
                name: str = "data_clean",
                **kwargs) -> None:
        """Initialize the data clean processor."""
        super().__init__(name, **kwargs)
    
    def _process_data(self, 
                     data: pd.DataFrame, 
                     params: Optional[Dict[str, Any]] = None) -> pd.DataFrame:
        """
        Clean and prepare DataFrame.
        
        Args:
            data: Input DataFrame
            params: Cleaning parameters including:
                   - handle_missing: Dict of {column: {method: value}} for handling missing values
                   - remove_duplicates: Whether to remove duplicate rows
                   - standardize_columns: Whether to standardize column names
                   - drop_na_columns: List of columns to drop NA values for
                   - drop_na_threshold: Threshold for dropping rows with NA values
                   - custom_clean: Custom cleaning function
                   
        Returns:
            Cleaned DataFrame
        """
        if not params:
            logger.warning("No cleaning parameters provided, returning original DataFrame")
            return data
        
        # Create a copy of the DataFrame to avoid modifying the original
        cleaned_df = data.copy()
        
        # Apply custom cleaning if provided
        custom_clean = params.get("custom_clean")
        if callable(custom_clean):
            cleaned_df = custom_clean(cleaned_df)
        
        # Handle missing values
        handle_missing = params.get("handle_missing", {})
        for column, config in handle_missing.items():
            if column in cleaned_df.columns:
                method = config.get("method", "fillna")
                value = config.get("value")
                
                if method == "fillna" and value is not None:
                    cleaned_df[column] = cleaned_df[column].fillna(value)
                elif method == "drop":
                    cleaned_df = cleaned_df.dropna(subset=[column])
                elif method == "interpolate":
                    cleaned_df[column] = cleaned_df[column].interpolate(method=config.get("interpolation", "linear"))
                elif method == "mean":
                    if pd.api.types.is_numeric_dtype(cleaned_df[column]):
                        cleaned_df[column] = cleaned_df[column].fillna(cleaned_df[column].mean())
                elif method == "median":
                    if pd.api.types.is_numeric_dtype(cleaned_df[column]):
                        cleaned_df[column] = cleaned_df[column].fillna(cleaned_df[column].median())
                elif method == "mode":
                    cleaned_df[column] = cleaned_df[column].fillna(cleaned_df[column].mode()[0])
                elif method == "forward":
                    cleaned_df[column] = cleaned_df[column].fillna(method="ffill")
                elif method == "backward":
                    cleaned_df[column] = cleaned_df[column].fillna(method="bfill")
        
        # Remove duplicate rows
        if params.get("remove_duplicates", False):
            subset = params.get("duplicate_subset")
            keep = params.get("duplicate_keep", "first")
            
            before_len = len(cleaned_df)
            cleaned_df = cleaned_df.drop_duplicates(subset=subset, keep=keep)
            after_len = len(cleaned_df)
            
            logger.info(f"Removed {before_len - after_len} duplicate rows")
        
        # Standardize column names
        if params.get("standardize_columns", False):
            clean_columns = {}
            for col in cleaned_df.columns:
                # Convert to lowercase, replace spaces with underscores
                new_col = str(col).lower().replace(" ", "_")
                # Remove non-alphanumeric characters except underscores
                new_col = "".join(c for c in new_col if c.isalnum() or c == "_")
                # Ensure it starts with a letter
                if new_col and not new_col[0].isalpha():
                    new_col = "col_" + new_col
                clean_columns[col] = new_col
            
            cleaned_df = cleaned_df.rename(columns=clean_columns)
            logger.info("Standardized column names")
        
        # Drop rows with NA values in specified columns
        drop_na_columns = params.get("drop_na_columns", [])
        if drop_na_columns:
            before_len = len(cleaned_df)
            cleaned_df = cleaned_df.dropna(subset=[col for col in drop_na_columns if col in cleaned_df.columns])
            after_len = len(cleaned_df)
            
            logger.info(f"Dropped {before_len - after_len} rows with NA values in specified columns")
        
        # Drop rows with many NA values
        drop_na_threshold = params.get("drop_na_threshold")
        if drop_na_threshold is not None:
            before_len = len(cleaned_df)
            cleaned_df = cleaned_df.dropna(thresh=int(drop_na_threshold * len(cleaned_df.columns)))
            after_len = len(cleaned_df)
            
            logger.info(f"Dropped {before_len - after_len} rows with less than {drop_na_threshold} non-NA values")
        
        logger.info(f"Cleaned DataFrame: {len(cleaned_df)} rows, {len(cleaned_df.columns)} columns")
        
        # Optimize memory usage if needed
        if self.mode == ProcessingMode.MEMORY_EFFICIENT or params.get("optimize_memory", False):
            cleaned_df = optimize_dataframe(cleaned_df)
            
        return cleaned_df

class DataPipelineProcessor(MultiStageProcessor):
    """
    Processor for chaining multiple data processing operations.
    
    This processor supports building data processing pipelines with
    multiple stages that can be executed in sequence or in parallel.
    """
    
    def __init__(self, 
                name: str = "data_pipeline",
                **kwargs) -> None:
        """Initialize the data pipeline processor."""
        super().__init__(name, **kwargs)
    
    def add_filter_stage(self, 
                        name: str, 
                        filter_type: Union[FilterType, str] = FilterType.EXACT,
                        dependencies: Optional[List[str]] = None) -> None:
        """
        Add a filtering stage to the pipeline.
        
        Args:
            name: Stage name
            filter_type: Type of filtering to apply
            dependencies: List of stage names this stage depends on
        """
        processor = DataFilterProcessor(
            name=f"{self.name}.{name}",
            filter_type=filter_type,
            cache_enabled=self.cache_enabled,
            validate_inputs=self.validate_inputs,
            validate_outputs=self.validate_outputs,
            hardware_acceleration=self.hardware_acceleration,
            mode=self.mode
        )
        
        self.add_stage(name, processor, dependencies)
    
    def add_group_stage(self, 
                       name: str, 
                       dependencies: Optional[List[str]] = None) -> None:
        """
        Add a grouping stage to the pipeline.
        
        Args:
            name: Stage name
            dependencies: List of stage names this stage depends on
        """
        processor = DataGroupProcessor(
            name=f"{self.name}.{name}",
            cache_enabled=self.cache_enabled,
            validate_inputs=self.validate_inputs,
            validate_outputs=self.validate_outputs,
            hardware_acceleration=self.hardware_acceleration,
            mode=self.mode
        )
        
        self.add_stage(name, processor, dependencies)
    
    def add_transform_stage(self, 
                          name: str, 
                          dependencies: Optional[List[str]] = None) -> None:
        """
        Add a transformation stage to the pipeline.
        
        Args:
            name: Stage name
            dependencies: List of stage names this stage depends on
        """
        processor = DataTransformProcessor(
            name=f"{self.name}.{name}",
            cache_enabled=self.cache_enabled,
            validate_inputs=self.validate_inputs,
            validate_outputs=self.validate_outputs,
            hardware_acceleration=self.hardware_acceleration,
            mode=self.mode
        )
        
        self.add_stage(name, processor, dependencies)
    
    def add_enrich_stage(self, 
                       name: str, 
                       dependencies: Optional[List[str]] = None) -> None:
        """
        Add an enrichment stage to the pipeline.
        
        Args:
            name: Stage name
            dependencies: List of stage names this stage depends on
        """
        processor = DataEnrichProcessor(
            name=f"{self.name}.{name}",
            cache_enabled=self.cache_enabled,
            validate_inputs=self.validate_inputs,
            validate_outputs=self.validate_outputs,
            hardware_acceleration=self.hardware_acceleration,
            mode=self.mode
        )
        
        self.add_stage(name, processor, dependencies)
    
    def add_clean_stage(self, 
                      name: str, 
                      dependencies: Optional[List[str]] = None) -> None:
        """
        Add a cleaning stage to the pipeline.
        
        Args:
            name: Stage name
            dependencies: List of stage names this stage depends on
        """
        processor = DataCleanProcessor(
            name=f"{self.name}.{name}",
            cache_enabled=self.cache_enabled,
            validate_inputs=self.validate_inputs,
            validate_outputs=self.validate_outputs,
            hardware_acceleration=self.hardware_acceleration,
            mode=self.mode
        )
        
        self.add_stage(name, processor, dependencies)
    
    def add_custom_stage(self, 
                       name: str, 
                       processor: DataFrameProcessor,
                       dependencies: Optional[List[str]] = None) -> None:
        """
        Add a custom stage to the pipeline.
        
        Args:
            name: Stage name
            processor: Custom processor for this stage
            dependencies: List of stage names this stage depends on
        """
        # Override processor properties to match pipeline settings
        processor.cache_enabled = self.cache_enabled
        processor.validate_inputs = self.validate_inputs
        processor.validate_outputs = self.validate_outputs
        processor.hardware_acceleration = self.hardware_acceleration
        processor.mode = self.mode
        
        self.add_stage(name, processor, dependencies)
    
    def create_standard_pipeline(self) -> None:
        """
        Create a standard data processing pipeline with common stages.
        
        This creates a pipeline with the following stages:
        1. clean (DataCleanProcessor)
        2. filter (DataFilterProcessor)
        3. transform (DataTransformProcessor)
        4. enrich (DataEnrichProcessor)
        5. group (DataGroupProcessor)
        """
        # Add stages in sequence
        self.add_clean_stage("clean")
        self.add_filter_stage("filter", dependencies=["clean"])
        self.add_transform_stage("transform", dependencies=["filter"])
        self.add_enrich_stage("enrich", dependencies=["transform"])
        self.add_group_stage("group", dependencies=["enrich"])
        
        logger.info(f"Created standard pipeline with 5 stages: clean → filter → transform → enrich → group")

# Factory function for creating processors
def create_processor(processor_type: str, name: str = None, **kwargs) -> DataFrameProcessor:
    """
    Create a data processor of the specified type.
    
    Args:
        processor_type: Type of processor to create
        name: Optional processor name (defaults to processor_type)
        **kwargs: Additional arguments for the processor
        
    Returns:
        Data processor instance
    """
    processor_name = name or processor_type
    
    if processor_type == "filter":
        return DataFilterProcessor(name=processor_name, **kwargs)
    elif processor_type == "group":
        return DataGroupProcessor(name=processor_name, **kwargs)
    elif processor_type == "transform":
        return DataTransformProcessor(name=processor_name, **kwargs)
    elif processor_type == "enrich":
        return DataEnrichProcessor(name=processor_name, **kwargs)
    elif processor_type == "clean":
        return DataCleanProcessor(name=processor_name, **kwargs)
    elif processor_type == "pipeline":
        return DataPipelineProcessor(name=processor_name, **kwargs)
    else:
        logger.warning(f"Unknown processor type: {processor_type}, defaulting to filter")
        return DataFilterProcessor(name=processor_name, **kwargs)

# Helper function for processing data with a chain of processors
@with_error_handling(category=ErrorCategory.CALCULATION, severity=ErrorSeverity.ERROR)
def process_data_with_chain(data: pd.DataFrame, 
                           processors: List[Tuple[DataFrameProcessor, Dict[str, Any]]]) -> pd.DataFrame:
    """
    Process data with a chain of processors.
    
    Args:
        data: Input DataFrame
        processors: List of (processor, params) tuples to apply in sequence
        
    Returns:
        Processed DataFrame
    """
    current_data = data
    
    for processor, params in processors:
        result = processor.process(current_data, params)
        current_data = result.data
    
    return current_data