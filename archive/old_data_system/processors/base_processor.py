"""
Base classes for data processors in the Yemen Trade Diagnostic system.

This module provides abstract base classes and utility functions for data processing,
with built-in support for validation, caching, error handling, hardware acceleration,
and data lineage tracking.
"""

# Standard library imports
import hashlib
import json
import time
from abc import ABC, abstractmethod
from enum import Enum
from typing import Any, Callable, Dict, Generic, List, Optional, Set, Tuple, TypeVar, Union

# Third-party imports
import numpy as np
import pandas as pd

# Project imports
from yemen_trade_diagnostic.interfaces.cache_interface import <PERSON>acheLevel, get_cache_manager
from yemen_trade_diagnostic.interfaces.error_interface import (
    ErrorCategory,
    ErrorSeverity,
    report_error,
    with_error_handling,
)
from yemen_trade_diagnostic.interfaces.hardware_interface import (
    AccelerationType,
    get_hardware_manager,
)
from yemen_trade_diagnostic.interfaces.logging_interface import get_logger
from yemen_trade_diagnostic.interfaces.validation_interface import (
    ValidationResult,
    get_validation_manager,
)

# Type variables for generic processor implementations
I = TypeVar('I')  # Input type
O = TypeVar('O')  # Output type

class ProcessingMode(Enum):
    """Processing modes for different performance/accuracy tradeoffs."""
    FAST = "fast"           # Prioritize speed over accuracy
    BALANCED = "balanced"   # Balance speed and accuracy
    PRECISE = "precise"     # Prioritize accuracy over speed
    MEMORY_EFFICIENT = "memory_efficient"  # Prioritize memory efficiency

class ProcessingStage(Enum):
    """Stages in the data processing pipeline."""
    PREPARATION = "preparation"  # Initial data preparation
    TRANSFORMATION = "transformation"  # Main data transformation
    ENRICHMENT = "enrichment"  # Data enrichment with additional information
    AGGREGATION = "aggregation"  # Data aggregation
    FINALIZATION = "finalization"  # Final formatting and cleanup

class ProcessingResult(Generic[O]):
    """
    Container for processing results with metadata.
    
    Includes the processed data along with metadata about the processing
    operation, time taken, hardware used, etc.
    """
    
    def __init__(self, 
                data: O, 
                metadata: Dict[str, Any],
                validation_result: Optional[ValidationResult] = None) -> None:
        """
        Initialize a processing result.
        
        Args:
            data: The processed data
            metadata: Metadata about the processing operation
            validation_result: Optional validation result for the processed data
        """
        self.data = data
        self.metadata = metadata
        self.validation_result = validation_result or {"valid": True, "errors": [], "warnings": []}
        
    @property
    def is_valid(self) -> bool:
        """
        Check if the processed data is valid.
        
        Returns:
            Whether the data is valid according to validation rules
        """
        return self.validation_result.get("valid", True)
    
    @property
    def validation_errors(self) -> List[Dict[str, Any]]:
        """
        Get validation errors.
        
        Returns:
            List of validation error details
        """
        return self.validation_result.get("errors", [])
    
    @property
    def validation_warnings(self) -> List[Dict[str, Any]]:
        """
        Get validation warnings.
        
        Returns:
            List of validation warning details
        """
        return self.validation_result.get("warnings", [])
    
    def get_source_info(self) -> Dict[str, Any]:
        """
        Get information about the data source.
        
        Returns:
            Source information
        """
        return self.metadata.get("source", {})
    
    def get_processing_info(self) -> Dict[str, Any]:
        """
        Get information about the processing operation.
        
        Returns:
            Processing information
        """
        return self.metadata.get("processing", {})
    
    def get_lineage(self) -> List[Dict[str, Any]]:
        """
        Get data lineage information.
        
        Returns:
            List of lineage entries tracing processing history
        """
        return self.metadata.get("lineage", [])
    
    def add_lineage_entry(self, operation: str, details: Dict[str, Any]) -> None:
        """
        Add an entry to the data lineage.
        
        Args:
            operation: Name of the operation
            details: Details of the operation
        """
        if "lineage" not in self.metadata:
            self.metadata["lineage"] = []
        
        entry = {
            "operation": operation,
            "timestamp": time.time(),
            "details": details
        }
        
        self.metadata["lineage"].append(entry)
    
    def with_data(self, new_data: O) -> 'ProcessingResult[O]':
        """
        Create a new result with updated data but same metadata.
        
        Args:
            new_data: The new processed data
            
        Returns:
            New ProcessingResult instance
        """
        result = ProcessingResult(new_data, self.metadata.copy(), self.validation_result)
        result.add_lineage_entry("data_update", {"description": "Data updated"})
        return result
    
    def with_validation(self, validation_result: ValidationResult) -> 'ProcessingResult[O]':
        """
        Create a new result with updated validation but same data.
        
        Args:
            validation_result: New validation result
            
        Returns:
            New ProcessingResult instance
        """
        result = ProcessingResult(self.data, self.metadata.copy(), validation_result)
        result.add_lineage_entry("validation_update", {
            "description": "Validation updated",
            "valid": validation_result.get("valid", True),
            "error_count": len(validation_result.get("errors", [])),
            "warning_count": len(validation_result.get("warnings", []))
        })
        return result

class ProcessorBase(Generic[I, O], ABC):
    """
    Abstract base class for all data processors.
    
    Provides a common interface and utility methods for data processing
    with support for validation, caching, error handling, hardware acceleration,
    and data lineage tracking.
    """
    
    def __init__(self, 
                 name: str,
                 cache_enabled: bool = True,
                 validate_inputs: bool = True,
                 validate_outputs: bool = True,
                 hardware_acceleration: bool = True,
                 mode: ProcessingMode = ProcessingMode.BALANCED) -> None:
        """
        Initialize the processor.
        
        Args:
            name: Name of the processor
            cache_enabled: Whether caching is enabled
            validate_inputs: Whether to validate inputs
            validate_outputs: Whether to validate outputs
            hardware_acceleration: Whether to use hardware acceleration
            mode: Processing mode for balancing speed/accuracy/memory
        """
        self.name = name
        self.cache_enabled = cache_enabled
        self.validate_inputs = validate_inputs
        self.validate_outputs = validate_outputs
        self.hardware_acceleration = hardware_acceleration
        self.mode = mode
        
        # Get interfaces
        self.logger = get_logger(f"processor.{name}")
        self.hw_manager = get_hardware_manager()
        self.cache_manager = get_cache_manager()
        self.validation_manager = get_validation_manager()
        
        # Initialize metadata
        self._metadata = {
            "name": name,
            "cache_enabled": cache_enabled,
            "validate_inputs": validate_inputs,
            "validate_outputs": validate_outputs,
            "hardware_acceleration": hardware_acceleration,
            "mode": mode.value,
            "statistics": {
                "process_calls": 0,
                "cache_hits": 0,
                "validation_failures": 0,
                "errors": 0,
                "total_processing_time": 0.0,
                "hardware_accelerated_operations": 0
            }
        }
        
        # Check hardware acceleration availability
        self.hardware_available = hardware_acceleration and self.hw_manager.is_hardware_acceleration_available()
        # Use the manager's current acceleration type
        self.acceleration_type = self.hw_manager._acceleration_type if self.hardware_available else None
        
        self._metadata.update({
            "hardware_available": self.hardware_available,
            "acceleration_type": self.acceleration_type.value if self.acceleration_type else "none"
        })
        
        self.logger.info(f"Initialized {name} processor with mode {mode.value}")
        if self.hardware_available:
            self.logger.info(f"Hardware acceleration available using {self.acceleration_type.value}")
    
    @property
    def metadata(self) -> Dict[str, Any]:
        """Get processor metadata."""
        return self._metadata
    
    def _get_cache_key(self, data: I, params: Optional[Dict[str, Any]] = None) -> str:
        """
        Generate a cache key for input data and parameters.
        
        Args:
            data: Input data
            params: Processing parameters
            
        Returns:
            Cache key string
        """
        key_parts = [self.name, self._get_data_signature(data)]
        
        if params:
            # Sort parameters by key to ensure consistent keys
            sorted_params = sorted(params.items())
            params_str = json.dumps(sorted_params)
            key_parts.append(params_str)
        
        key_parts.append(self.mode.value)
        
        # Generate a hash of the combined parts
        joined_parts = "::".join(key_parts)
        return f"{self.name}:{hashlib.md5(joined_parts.encode()).hexdigest()}"
    
    def _get_data_signature(self, data: I) -> str:
        """
        Generate a signature for input data.
        
        Args:
            data: Input data
            
        Returns:
            Data signature string
        """
        # Default implementation for different data types
        if isinstance(data, pd.DataFrame):
            # For DataFrames, use column names, shape, and hash of first/last rows
            columns = list(data.columns)
            shape = data.shape
            
            # Sample data for signature to avoid processing entire large DataFrame
            sample = None
            if len(data) > 0:
                if len(data) <= 10:
                    sample = data
                else:
                    # Take first 5 and last 5 rows for signature
                    sample = pd.concat([data.head(5), data.tail(5)])
            
            sample_hash = "empty" if sample is None or len(sample) == 0 else hashlib.md5(
                pd.util.hash_pandas_object(sample).values.tobytes()
            ).hexdigest()
            
            return f"df:{shape[0]}x{shape[1]}:{','.join(columns)}:{sample_hash}"
            
        elif isinstance(data, np.ndarray):
            # For NumPy arrays, use shape, dtype, and hash of data sample
            shape = data.shape
            dtype = str(data.dtype)
            
            # Sample data for signature
            sample = None
            if data.size > 0:
                if data.size <= 1000:
                    sample = data
                else:
                    # Take a subset for signature
                    indices = np.linspace(0, data.size-1, 1000, dtype=int)
                    sample = data.flat[indices]
            
            sample_hash = "empty" if sample is None or sample.size == 0 else hashlib.md5(
                sample.tobytes()
            ).hexdigest()
            
            return f"array:{shape}:{dtype}:{sample_hash}"
            
        elif isinstance(data, (list, tuple)):
            # For lists/tuples, use length and sample of elements
            length = len(data)
            
            # Sample data for signature
            sample = None
            if length > 0:
                if length <= 100:
                    sample = data
                else:
                    # Take first and last elements
                    sample = list(data[:50]) + list(data[-50:])
            
            sample_str = str(sample) if sample else "empty"
            sample_hash = hashlib.md5(sample_str.encode()).hexdigest()
            
            return f"list:{length}:{sample_hash}"
            
        elif isinstance(data, dict):
            # For dictionaries, use keys and sample of values
            keys = sorted(data.keys())
            
            # Use keys for signature
            keys_str = ",".join(str(k) for k in keys)
            keys_hash = hashlib.md5(keys_str.encode()).hexdigest()
            
            return f"dict:{len(keys)}:{keys_hash}"
            
        else:
            # For other types, convert to string and hash
            try:
                data_str = str(data)
                # Truncate very long strings
                if len(data_str) > 1000:
                    data_str = data_str[:500] + "..." + data_str[-500:]
                return f"other:{type(data).__name__}:{hashlib.md5(data_str.encode()).hexdigest()}"
            except:
                # Fallback for objects that can't be converted to string
                return f"other:{type(data).__name__}:unknown"
    
    def _validate_input(self, data: I) -> ValidationResult:
        """
        Validate input data.
        
        Args:
            data: Input data to validate
            
        Returns:
            Validation result
        """
        if not self.validate_inputs:
            return {"valid": True, "errors": [], "warnings": []}
        
        schema = self.get_input_schema()
        if not schema:
            return {"valid": True, "errors": [], "warnings": [{"message": "No input schema available for validation"}]}
        
        return self.validation_manager.validate_data(data, schema)
    
    def _validate_output(self, data: O) -> ValidationResult:
        """
        Validate output data.
        
        Args:
            data: Output data to validate
            
        Returns:
            Validation result
        """
        if not self.validate_outputs:
            return {"valid": True, "errors": [], "warnings": []}
        
        schema = self.get_output_schema()
        if not schema:
            return {"valid": True, "errors": [], "warnings": [{"message": "No output schema available for validation"}]}
        
        return self.validation_manager.validate_data(data, schema)
    
    def get_input_schema(self) -> Dict[str, Any]:
        """
        Get schema for input validation.
        
        Returns:
            Input schema dictionary or None
        """
        # Default implementation returns None
        # Subclasses should override to provide input schemas
        return {}
    
    def get_output_schema(self) -> Dict[str, Any]:
        """
        Get schema for output validation.
        
        Returns:
            Output schema dictionary or None
        """
        # Default implementation returns None
        # Subclasses should override to provide output schemas
        return {}
    
    @abstractmethod
    def _process_data(self, 
                    data: I, 
                    params: Optional[Dict[str, Any]] = None) -> O:
        """
        Process data without caching, validation, or error handling.
        
        Args:
            data: Input data
            params: Processing parameters
            
        Returns:
            Processed data
        """
        pass
    
    @with_error_handling(category=ErrorCategory.CALCULATION, severity=ErrorSeverity.ERROR)
    def process(self, 
               data: I, 
               params: Optional[Dict[str, Any]] = None,
               bypass_cache: bool = False) -> ProcessingResult[O]:
        """
        Process data with caching, validation, and error handling.
        
        Args:
            data: Input data
            params: Processing parameters
            bypass_cache: Whether to bypass cache and force processing
            
        Returns:
            Processing result with data and metadata
        """
        start_time = time.time()
        cache_key = None
        
        # Create basic source metadata
        source_info = {
            "type": type(data).__name__,
            "signature": self._get_data_signature(data)
        }
        
        # Add data-type specific metadata
        if isinstance(data, pd.DataFrame):
            source_info.update({
                "shape": data.shape,
                "columns": list(data.columns),
                "dtypes": {col: str(dtype) for col, dtype in data.dtypes.items()}
            })
        elif isinstance(data, np.ndarray):
            source_info.update({
                "shape": data.shape,
                "dtype": str(data.dtype)
            })
        elif isinstance(data, (list, tuple)):
            source_info.update({
                "length": len(data)
            })
        
        # Try to load from cache if enabled
        if self.cache_enabled and not bypass_cache:
            cache_key = self._get_cache_key(data, params)
            cached_result = self.cache_manager.get(cache_key, level=CacheLevel.MEMORY)
            
            if cached_result is not None:
                self.logger.debug(f"Cache hit for {self.name} processing")
                self._metadata["statistics"]["process_calls"] += 1
                self._metadata["statistics"]["cache_hits"] += 1
                
                # Add cache hit information to lineage
                if isinstance(cached_result, ProcessingResult):
                    cached_result.add_lineage_entry("cache_retrieval", {
                        "processor": self.name,
                        "cache_key": cache_key
                    })
                    return cached_result
                else:
                    # Handle case where cached item is not a ProcessingResult
                    self.logger.warning(f"Cached item is not a ProcessingResult: {type(cached_result)}")
                    
                    # Create a new ProcessingResult
                    result_metadata = {
                        "source": source_info,
                        "processing": {
                            "processor": self.name,
                            "mode": self.mode.value,
                            "params": params,
                            "cached": True,
                            "processing_time": 0.0,
                            "hardware_accelerated": False
                        },
                        "lineage": [{
                            "operation": "cache_retrieval",
                            "timestamp": time.time(),
                            "details": {
                                "processor": self.name,
                                "cache_key": cache_key,
                                "note": "Cached item was not a ProcessingResult"
                            }
                        }]
                    }
                    
                    result = ProcessingResult(cached_result, result_metadata)
                    
                    if self.validate_outputs:
                        validation_result = self._validate_output(cached_result)
                        result = result.with_validation(validation_result)
                    
                    return result
        
        # Validate input if enabled
        if self.validate_inputs:
            input_validation = self._validate_input(data)
            if not input_validation["valid"]:
                self._metadata["statistics"]["validation_failures"] += 1
                
                # Log validation errors
                for error in input_validation["errors"]:
                    self.logger.warning(f"Input validation error: {error['message']}")
                
                # If critical validation errors, raise exception
                if any(error.get("critical", False) for error in input_validation["errors"]):
                    error_msg = "Critical input validation errors"
                    self.logger.error(error_msg)
                    report_error(
                        ValueError(error_msg),
                        component=self.__class__.__name__,
                        operation="process",
                        extra={"validation_errors": input_validation["errors"]}
                    )
                    raise ValueError(error_msg)
        
        # Process the data
        self.logger.info(f"Processing data with {self.name}")
        
        hardware_used = False
        try:
            # Process data
            processed_data = self._process_data(data, params)
            
            # Check if hardware acceleration was used
            hardware_used = getattr(processed_data, "_hardware_accelerated", False)
            if not hardware_used and hasattr(self, "_used_hardware_acceleration"):
                hardware_used = self._used_hardware_acceleration
            
            # Validate output if enabled
            output_validation = {"valid": True, "errors": [], "warnings": []}
            if self.validate_outputs:
                output_validation = self._validate_output(processed_data)
                if not output_validation["valid"]:
                    self._metadata["statistics"]["validation_failures"] += 1
                    
                    # Log validation errors
                    for error in output_validation["errors"]:
                        self.logger.warning(f"Output validation error: {error['message']}")
                    
                    # If critical validation errors, handle according to policy
                    # (but don't raise, since we already have a result)
                    if any(error.get("critical", False) for error in output_validation["errors"]):
                        error_msg = "Critical output validation errors"
                        self.logger.error(error_msg)
                        report_error(
                            ValueError(error_msg),
                            component=self.__class__.__name__,
                            operation="process",
                            extra={"validation_errors": output_validation["errors"]}
                        )
            
            # Calculate processing time
            processing_time = time.time() - start_time
            
            # Create result metadata
            result_metadata = {
                "source": source_info,
                "processing": {
                    "processor": self.name,
                    "mode": self.mode.value,
                    "params": params,
                    "cached": False,
                    "processing_time": processing_time,
                    "hardware_accelerated": hardware_used,
                    "acceleration_type": self.acceleration_type.value if hardware_used else "none"
                },
                "lineage": [{
                    "operation": "process",
                    "timestamp": time.time(),
                    "details": {
                        "processor": self.name,
                        "mode": self.mode.value,
                        "hardware_accelerated": hardware_used
                    }
                }]
            }
            
            # Create processing result
            result = ProcessingResult(processed_data, result_metadata, output_validation)
            
            # Cache the result if enabled
            if self.cache_enabled and cache_key:
                self.cache_manager.set(cache_key, result, level=CacheLevel.MEMORY)
            
            # Update statistics
            self._metadata["statistics"]["process_calls"] += 1
            self._metadata["statistics"]["total_processing_time"] += processing_time
            if hardware_used:
                self._metadata["statistics"]["hardware_accelerated_operations"] += 1
            
            self.logger.debug(f"Processed data in {processing_time:.2f} seconds")
            
            return result
            
        except Exception as e:
            self._metadata["statistics"]["errors"] += 1
            self.logger.error(f"Error processing data with {self.name}: {str(e)}")
            report_error(
                e,
                component=self.__class__.__name__,
                operation="process",
                extra={"data_signature": self._get_data_signature(data), "params": params}
            )
            raise
    
    def clear_cache(self) -> None:
        """Clear cached processing results for this processor."""
        if self.cache_enabled:
            pattern = f"{self.name}:*"
            count = self.cache_manager.clear(pattern)
            self.logger.info(f"Cleared {count} cached items for {self.name}")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get processor statistics."""
        return self._metadata["statistics"]

class DataFrameProcessor(ProcessorBase[pd.DataFrame, pd.DataFrame]):
    """
    Processor for pandas DataFrame operations.
    
    Provides specialized functionality for processing DataFrames, with
    optimizations and hardware acceleration specific to tabular data.
    """
    
    def __init__(self, 
                 name: str,
                 **kwargs) -> None:
        """Initialize the DataFrame processor."""
        super().__init__(name, **kwargs)
    
    def _optimize_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Optimize DataFrame memory usage.
        
        Args:
            df: DataFrame to optimize
            
        Returns:
            Optimized DataFrame
        """
        # Use hardware acceleration if available
        if self.hardware_acceleration and self.hardware_available:
            try:
                self._used_hardware_acceleration = True
                return self.hw_manager.accelerate_dataframe(df, "optimize_memory")
            except Exception as e:
                self.logger.warning(f"Hardware acceleration failed for DataFrame optimization: {str(e)}")
                self._used_hardware_acceleration = False
        
        # Fallback to standard optimization
        for col in df.columns:
            if pd.api.types.is_numeric_dtype(df[col]):
                c_min = df[col].min()
                c_max = df[col].max()
                
                # Convert to smallest possible integer type
                if pd.api.types.is_integer_dtype(df[col]):
                    if c_min >= 0:
                        if c_max < 2**8:
                            df[col] = df[col].astype(np.uint8)
                        elif c_max < 2**16:
                            df[col] = df[col].astype(np.uint16)
                        elif c_max < 2**32:
                            df[col] = df[col].astype(np.uint32)
                        else:
                            df[col] = df[col].astype(np.uint64)
                    else:
                        if c_min > -2**7 and c_max < 2**7:
                            df[col] = df[col].astype(np.int8)
                        elif c_min > -2**15 and c_max < 2**15:
                            df[col] = df[col].astype(np.int16)
                        elif c_min > -2**31 and c_max < 2**31:
                            df[col] = df[col].astype(np.int32)
                        else:
                            df[col] = df[col].astype(np.int64)
                elif pd.api.types.is_float_dtype(df[col]):
                    df[col] = df[col].astype(np.float32)
                    
        # Optimize string columns using categorical
        for col in df.select_dtypes(include=['object']).columns:
            if df[col].nunique() < len(df[col]) * 0.5:  # If less than 50% unique values
                df[col] = df[col].astype('category')
        
        return df
    
    def filter_dataframe(self, 
                        df: pd.DataFrame, 
                        filters: Dict[str, Any],
                        mode: str = "exact") -> pd.DataFrame:
        """
        Apply filters to a DataFrame.
        
        Args:
            df: DataFrame to filter
            filters: Dictionary of filters to apply
            mode: Filter mode ('exact', 'contains', 'regex', etc.)
            
        Returns:
            Filtered DataFrame
        """
        filtered_df = df.copy()
        
        for column, value in filters.items():
            if column in filtered_df.columns:
                if mode == "exact":
                    # Exact matching
                    if isinstance(value, list):
                        filtered_df = filtered_df[filtered_df[column].isin(value)]
                    else:
                        filtered_df = filtered_df[filtered_df[column] == value]
                        
                elif mode == "contains":
                    # Contains matching
                    if isinstance(value, list):
                        mask = filtered_df[column].apply(
                            lambda x: any(str(v) in str(x) for v in value)
                        )
                        filtered_df = filtered_df[mask]
                    else:
                        filtered_df = filtered_df[filtered_df[column].astype(str).str.contains(str(value))]
                        
                elif mode == "regex":
                    # Regex matching
                    filtered_df = filtered_df[filtered_df[column].astype(str).str.match(value)]
                    
                elif mode == "range":
                    # Range matching
                    if isinstance(value, list) and len(value) == 2:
                        min_val, max_val = value
                        filtered_df = filtered_df[
                            (filtered_df[column] >= min_val) & 
                            (filtered_df[column] <= max_val)
                        ]
        
        return filtered_df
    
    def _process_data(self, 
                    data: pd.DataFrame, 
                    params: Optional[Dict[str, Any]] = None) -> pd.DataFrame:
        """
        Process DataFrame data.
        
        Args:
            data: Input DataFrame
            params: Processing parameters
            
        Returns:
            Processed DataFrame
        """
        self._used_hardware_acceleration = False
        
        # Default implementation just does memory optimization
        # Subclasses should override for specific processing
        return self._optimize_dataframe(data)

class NumericProcessor(ProcessorBase[np.ndarray, np.ndarray]):
    """
    Processor for NumPy array operations.
    
    Provides specialized functionality for processing numeric arrays,
    with hardware acceleration for common matrix and vector operations.
    """
    
    def __init__(self, 
                 name: str,
                 **kwargs) -> None:
        """Initialize the numeric processor."""
        super().__init__(name, **kwargs)
    
    def _process_data(self, 
                    data: np.ndarray, 
                    params: Optional[Dict[str, Any]] = None) -> np.ndarray:
        """
        Process NumPy array data.
        
        Args:
            data: Input array
            params: Processing parameters
            
        Returns:
            Processed array
        """
        # Default implementation returns the input array
        # Subclasses should override for specific processing
        return data

class MultiStageProcessor(ProcessorBase[I, O]):
    """
    Processor with multiple processing stages.
    
    Allows complex processing workflows with multiple stages, dependencies
    between stages, and flexible execution order.
    """
    
    def __init__(self, 
                 name: str,
                 stages: Optional[List[Dict[str, Any]]] = None,
                 **kwargs) -> None:
        """
        Initialize the multi-stage processor.
        
        Args:
            name: Name of the processor
            stages: List of stage configurations
            **kwargs: Additional arguments for ProcessorBase
        """
        super().__init__(name, **kwargs)
        
        self.stages = stages or []
        self.stage_processors = {}
        
        # Initialize stage processors
        for stage in self.stages:
            stage_name = stage.get("name")
            processor_class = stage.get("processor_class")
            processor_kwargs = stage.get("processor_kwargs", {})
            
            if stage_name and processor_class:
                processor = processor_class(
                    name=f"{name}.{stage_name}",
                    **processor_kwargs
                )
                self.stage_processors[stage_name] = processor
    
    def add_stage(self, 
                name: str, 
                processor: ProcessorBase,
                dependencies: Optional[List[str]] = None) -> None:
        """
        Add a processing stage.
        
        Args:
            name: Stage name
            processor: Processor for this stage
            dependencies: List of stage names this stage depends on
        """
        self.stages.append({
            "name": name,
            "processor_class": processor.__class__,
            "processor_kwargs": {
                "name": f"{self.name}.{name}",
                "cache_enabled": processor.cache_enabled,
                "validate_inputs": processor.validate_inputs,
                "validate_outputs": processor.validate_outputs,
                "hardware_acceleration": processor.hardware_acceleration,
                "mode": processor.mode
            },
            "dependencies": dependencies or []
        })
        
        self.stage_processors[name] = processor
    
    def _get_execution_order(self) -> List[str]:
        """
        Get topological ordering of stages respecting dependencies.
        
        Returns:
            List of stage names in execution order
        """
        # Build dependency graph
        graph = {stage["name"]: set(stage.get("dependencies", [])) for stage in self.stages}
        
        # Topological sort
        result = []
        temp_marks = set()
        perm_marks = set()
        
        def visit(node):
            if node in perm_marks:
                return
            if node in temp_marks:
                raise ValueError(f"Circular dependency detected in stages: {node}")
            
            temp_marks.add(node)
            
            for dep in graph.get(node, set()):
                visit(dep)
            
            temp_marks.remove(node)
            perm_marks.add(node)
            result.append(node)
        
        # Visit all nodes
        for node in graph:
            if node not in perm_marks:
                visit(node)
        
        return list(reversed(result))
    
    def _process_data(self, 
                    data: I, 
                    params: Optional[Dict[str, Any]] = None) -> O:
        """
        Process data through multiple stages.
        
        Args:
            data: Input data
            params: Processing parameters
            
        Returns:
            Final processed data
        """
        if not self.stages:
            return data  # No stages defined
        
        # Get execution order
        try:
            execution_order = self._get_execution_order()
        except ValueError as e:
            self.logger.error(f"Error determining stage execution order: {str(e)}")
            raise
        
        # Initialize stage inputs/outputs
        stage_outputs = {}
        current_data = data
        
        # Process each stage in order
        for stage_name in execution_order:
            stage_processor = self.stage_processors.get(stage_name)
            if not stage_processor:
                self.logger.warning(f"No processor found for stage {stage_name}, skipping")
                continue
            
            # Extract parameters for this stage
            stage_params = {}
            if params and f"{stage_name}_params" in params:
                stage_params = params[f"{stage_name}_params"]
            
            # Process the data
            self.logger.debug(f"Processing stage {stage_name}")
            result = stage_processor.process(current_data, stage_params)
            
            # Store output for potential use by later stages
            stage_outputs[stage_name] = result.data
            
            # Update current data for the next stage
            current_data = result.data
        
        # Return the output of the last stage
        return current_data