"""
Shared Data Manager

Provides centralized data loading and caching across multiple pipelines
to reduce redundant loads and improve performance. Includes support for
memory-mapped files and optimized binary formats.
"""
# Standard library imports
import logging
import os
import time
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional, Tuple, Union

# Third-party imports
import numpy as np
import pandas as pd

# Project imports
from yemen_trade_diagnostic.data.unified_loader_api import load_data
from yemen_trade_diagnostic.interfaces.hardware_interface import get_hardware_manager
from yemen_trade_diagnostic.interfaces.logging_interface import get_logger


class SharedDataManager:
    """
    Manages shared data loading across pipelines to reduce redundant I/O operations.
    
    This singleton class implements a basic cache for commonly accessed datasets,
    keeping them in memory to be shared across different pipelines, improving
    overall performance.
    """
    _instance = None

    @classmethod
    def get_instance(cls) -> 'SharedDataManager':
        """Get the singleton instance of the SharedDataManager."""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance

    def __init__(self):
        """Initialize the shared data manager."""
        if SharedDataManager._instance is not None:
            raise RuntimeError("Use SharedDataManager.get_instance() instead")

        self._cache: Dict[str, Tuple[Any, float]] = {}       # key -> (data, timestamp)
        self._mmap_cache: Dict[str, Any] = {}               # memory-mapped files cache
        self._ttl = 3600                                    # default TTL = 1 hour
        self._logger = get_logger(__name__)
        self._hw_manager = get_hardware_manager()

        # Directory for optimized formats
        self._cache_dir = Path("data/cache")
        self._cache_dir.mkdir(parents=True, exist_ok=True)

        # Flag: use memory mapping by default
        self._use_memory_mapping = True

        self._logger.info("SharedDataManager initialized with memory-mapping support")

    def get_data(
        self,
        data_source: str,
        year: Optional[int] = None,
        columns: Optional[List[str]] = None,
        use_memory_mapping: Optional[bool] = None,
        convert_to_optimized: bool = True
    ) -> Any:
        """
        Get data from cache or load it if missing/expired.

        Args:
            data_source: Identifier, e.g. 'baci', 'yemen_exports'
            year: Optional year filter
            columns: Optional subset of columns
            use_memory_mapping: Override default memory-mapping flag
            convert_to_optimized: Save to optimized format after loading

        Returns:
            Loaded data (DataFrame, array, etc.)
        """
        cache_key = self._get_cache_key(data_source, year, columns)
        mmap_flag = use_memory_mapping if use_memory_mapping is not None else self._use_memory_mapping

        # 1) Return from mmap cache
        if mmap_flag and cache_key in self._mmap_cache:
            self._logger.debug(f"Memory-mapped cache hit for {cache_key}")
            return self._mmap_cache[cache_key]

        # 2) Return from in-memory cache if not expired
        if cache_key in self._cache:
            data, ts = self._cache[cache_key]
            if time.time() - ts < self._ttl:
                self._logger.debug(f"Cache hit for {cache_key}")
                return data
            self._logger.debug(f"Cache expired for {cache_key}")

        # 3) Load from optimized file on disk
        optimized_path = self._get_optimized_path(data_source, year)
        if optimized_path.exists():
            self._logger.info(f"Loading optimized format: {optimized_path}")
            start = time.time()
            if mmap_flag:
                data = self._load_memory_mapped(optimized_path)
                self._mmap_cache[cache_key] = data
            else:
                data = self._load_from_optimized(optimized_path)
                self._cache[cache_key] = (data, time.time())
            self._logger.info(f"Loaded in {time.time() - start:.2f}s")
            return data

        # 4) Auto-convert matching CSV if present
        data_dir = Path("data/")
        if data_dir.exists():
            patterns = []
            patterns.extend(data_dir.glob(f"{data_source}/*.csv"))
            if year is not None:
                patterns.extend(data_dir.glob(f"{data_source}/{year}/*.csv"))
                patterns.extend(data_dir.glob(f"{data_source}/*_{year}.csv"))

            if patterns:
                csv_file = patterns[0]
                self._logger.info(f"Converting CSV to optimized: {csv_file}")
                try:
                    df = pd.read_csv(csv_file)
                    self._convert_to_optimized_format(df, data_source, year)
                    if optimized_path.exists():
                        return self.get_data(data_source, year, columns, use_memory_mapping, convert_to_optimized)
                except Exception as e:
                    self._logger.error(f"CSV conversion error ({csv_file}): {e}")

        # 5) Load from original source
        self._logger.info(f"Loading {cache_key} from source")
        start = time.time()
        data = load_data(data_source, year=year, columns=columns)
        load_time = time.time() - start

        # 5a) Handle None
        if data is None:
            self._logger.warning(
                f"Data source {data_source} year {year} returned None — creating empty DataFrame"
            )
            if data_source == "baci":
                data = pd.DataFrame(columns=['t','i','j','k','v','q'])
            elif data_source == "yemen_exports":
                data = pd.DataFrame(columns=[
                    'year','exporter_iso','product_code','trade_value_usd',
                    'quantity','destination'
                ])
            elif data_source == "gdp_per_capita":
                data = pd.DataFrame(columns=['country_code','year','gdp_per_capita'])
            else:
                data = pd.DataFrame()

        # 5b) Cache & optional convert
        if data is not None:
            if convert_to_optimized:
                self._convert_to_optimized_format(data, data_source, year)

            if mmap_flag:
                self._mmap_cache[cache_key] = data
            else:
                self._cache[cache_key] = (data, time.time())

            self._logger.info(f"Cached {cache_key} (loaded in {load_time:.2f}s)")
        else:
            self._logger.warning(f"Failed to load data for {cache_key}")

        return data

    def _load_memory_mapped(self, file_path: Path) -> Any:
        """
        Load data with memory-mapping to reduce RAM usage.
        Optimized for M3 Pro with 36GB RAM.
        """
        ext = file_path.suffix.lower()
        try:
            if ext == '.npy':
                return np.load(file_path, mmap_mode='r')
            elif ext == '.parquet':
                # Third-party imports
                import pyarrow as pa
                import pyarrow.parquet as pq

                # For large BACI files, use more efficient approach
                if "baci" in file_path.name.lower() and file_path.stat().st_size > 500_000_000:  # >500MB
                    self._logger.info(f"Using optimized loading for large BACI file: {file_path}")
                    # Read metadata only first to get schema
                    pf = pq.ParquetFile(file_path)
                    # Read in chunks to avoid excessive memory usage
                    chunks = []
                    for batch in pf.iter_batches(batch_size=250_000, columns=None):
                        chunks.append(batch.to_pandas())
                        # Force garbage collection to free memory
                        batch = None
                        # Standard library imports
                        import gc
                        gc.collect()
                    return pd.concat(chunks, ignore_index=True)
                else:
                    # Standard approach for smaller files
                    tbl = pq.read_table(file_path, memory_map=True)
                    return tbl.to_pandas()
            elif ext == '.feather':
                # Third-party imports
                import pyarrow.feather as feather
                return feather.read_feather(file_path, memory_map=True)
            elif ext == '.csv':
                # Larger chunk size for M3 Pro with 36GB RAM
                return pd.read_csv(file_path, chunksize=250_000)
            else:
                self._logger.warning(f"Unsupported format for mmap: {ext}")
        except Exception as e:
            self._logger.error(f"Error mmap-loading {file_path}: {e}")
            # Standard library imports
            import traceback
            self._logger.error(traceback.format_exc())
        return None

    def _load_from_optimized(self, file_path: Path) -> Optional[pd.DataFrame]:
        """
        Load data from an optimized format file.
        """
        ext = file_path.suffix.lower()
        try:
            if ext == '.parquet':
                return pd.read_parquet(file_path)
            elif ext == '.feather':
                return pd.read_feather(file_path)
            self._logger.warning(f"Unsupported optimized format: {ext}")
        except Exception as e:
            self._logger.error(f"Error loading optimized file {file_path}: {e}")
        return None

    def _convert_to_optimized_format(
        self,
        data: pd.DataFrame,
        data_source: str,
        year: Optional[int] = None
    ) -> None:
        """
        Convert a DataFrame to Parquet and save in the cache directory.
        """
        if not isinstance(data, pd.DataFrame):
            return

        out_path = self._get_optimized_path(data_source, year)
        out_path.parent.mkdir(parents=True, exist_ok=True)
        try:
            start = time.time()
            data.to_parquet(out_path, index=False)
            self._logger.info(
                f"Converted {data_source} year {year} → {out_path} in {time.time() - start:.2f}s"
            )
        except Exception as e:
            self._logger.error(f"Error converting to optimized: {e}")

    def _get_optimized_path(
        self,
        data_source: str,
        year: Optional[int] = None
    ) -> Path:
        """
        Build the Path for the optimized Parquet file.
        """
        suffix = f"_{year}" if year is not None else ""
        return self._cache_dir / f"{data_source}{suffix}.parquet"

    def clear_cache(self, data_source: Optional[str] = None) -> None:
        """
        Clear in-memory caches. If data_source is None, clears all.
        """
        if data_source:
            prefix = f"{data_source}:"
            for d in [self._cache, self._mmap_cache]:
                keys = [k for k in d if k.startswith(prefix)]
                for k in keys:
                    del d[k]
            self._logger.info(f"Cleared in-memory cache for {data_source}")
        else:
            self._cache.clear()
            self._mmap_cache.clear()
            self._logger.info("Cleared all in-memory caches")

    def clear_optimized_files(self, data_source: Optional[str] = None) -> None:
        """
        Remove optimized files from disk. If data_source is None, removes all.
        """
        pattern = f"{data_source}*.parquet" if data_source else "*.parquet"
        for f in self._cache_dir.glob(pattern):
            try:
                f.unlink()
            except Exception as e:
                self._logger.error(f"Error deleting {f}: {e}")
        msg = f"Removed optimized files for {data_source}" if data_source else "Removed all optimized files"
        self._logger.info(msg)

    def _get_cache_key(
        self,
        data_source: str,
        year: Optional[int],
        columns: Optional[List[str]]
    ) -> str:
        """Generate a consistent cache key."""
        cols = ",".join(sorted(columns)) if columns else "all"
        return f"{data_source}:{year or 'all'}:{cols}"

    def precompute_optimized_formats(self, data_sources: List[str], years: List[int]) -> None:
        """
        Bulk-precompute and save optimized Parquet for multiple sources/years.
        """
        for src in data_sources:
            for yr in years:
                try:
                    self._logger.info(f"Precomputing {src} year {yr}")
                    df = load_data(src, year=yr)
                    if isinstance(df, pd.DataFrame):
                        self._convert_to_optimized_format(df, src, yr)
                except Exception as e:
                    self._logger.error(f"Error precomputing {src} {yr}: {e}")

    def convert_all_csv_to_optimized(
        self,
        data_dir: Optional[Path] = None,
        verbose: bool = True
    ) -> Dict[str, List[Path]]:
        """
        Walk data_dir, convert every CSV to Parquet, and return a map of conversions.
        """
        data_dir = data_dir or Path("data/")
        if not data_dir.exists():
            self._logger.warning(f"Data directory {data_dir} not found")
            return {}

        csvs = list(data_dir.glob("**/*.csv"))
        if verbose:
            self._logger.info(f"Found {len(csvs)} CSV files in {data_dir}")

        converted: Dict[str, List[Path]] = {}
        for csv in csvs:
            try:
                rel = csv.relative_to(data_dir)
                src_name = rel.parts[0].lower()
                yr = None
                if len(rel.parts) > 1 and rel.parts[1].isdigit():
                    yr = int(rel.parts[1])
                else:
                    # Standard library imports
                    import re
                    m = re.search(r'_(\d{4})\.csv$', csv.name)
                    if m:
                        yr = int(m.group(1))

                out = self._get_optimized_path(src_name, yr)
                if not out.exists():
                    if verbose:
                        self._logger.info(f"Converting {csv} → {out}")
                    df = pd.read_csv(csv)
                    out.parent.mkdir(parents=True, exist_ok=True)
                    df.to_parquet(out, index=False)
                    converted.setdefault(src_name, []).append(out)
                    if verbose:
                        ratio = csv.stat().st_size / out.stat().st_size
                        self._logger.info(f"Size reduction {ratio:.1f}×")
            except Exception as e:
                self._logger.error(f"Error converting {csv}: {e}")

        self._logger.info(f"Converted {sum(len(v) for v in converted.values())} CSVs")
        return converted

    def set_ttl(self, seconds: int) -> None:
        """Adjust cache time-to-live (seconds)."""
        self._ttl = seconds
        self._logger.info(f"TTL set to {seconds} seconds")

    def set_memory_mapping(self, enabled: bool) -> None:
        """Enable/disable memory mapping globally."""
        self._use_memory_mapping = enabled
        state = "enabled" if enabled else "disabled"
        self._logger.info(f"Memory mapping {state}")

    def get_cache_stats(self) -> Dict[str, Any]:
        """
        Return stats on in-memory caches and optimized files.
        """
        optimized_files = list(self._cache_dir.glob("*.parquet"))
        total_size = sum(f.stat().st_size for f in optimized_files)
        return {
            "regular_cache_items": len(self._cache),
            "mmap_cache_items": len(self._mmap_cache),
            "optimized_files_count": len(optimized_files),
            "optimized_files_size_mb": total_size / (1024 * 1024),
            "ttl_seconds": self._ttl,
            "memory_mapping": self._use_memory_mapping
        }


def get_shared_data_manager() -> SharedDataManager:
    """Convenience accessor for the singleton."""
    return SharedDataManager.get_instance()