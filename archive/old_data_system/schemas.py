"""
Data schemas module for Yemen Trade Diagnostic.

This module provides schema definitions and validation utilities for various
data types used in the application.
"""

# Standard library imports
from enum import Enum
from typing import Any, Dict, List, Optional, Union

# Third-party imports
import pandas as pd


class DataType(Enum):
    """Enumeration of supported data types."""
    STRING = "string"
    INTEGER = "integer"
    FLOAT = "float"
    BOOLEAN = "boolean"
    DATETIME = "datetime"
    CATEGORICAL = "categorical"


class FieldSchema:
    """Schema definition for a single data field."""
    
    def __init__(self, 
                 name: str,
                 data_type: DataType,
                 required: bool = True,
                 nullable: bool = False,
                 description: str = "",
                 constraints: Optional[Dict[str, Any]] = None):
        self.name = name
        self.data_type = data_type
        self.required = required
        self.nullable = nullable
        self.description = description
        self.constraints = constraints or {}
    
    def validate(self, value: Any) -> bool:
        """Validate a value against this field schema."""
        if value is None:
            return self.nullable
        
        # Type checking based on data_type
        if self.data_type == DataType.STRING:
            return isinstance(value, str)
        elif self.data_type == DataType.INTEGER:
            return isinstance(value, int)
        elif self.data_type == DataType.FLOAT:
            return isinstance(value, (int, float))
        elif self.data_type == DataType.BOOLEAN:
            return isinstance(value, bool)
        elif self.data_type == DataType.DATETIME:
            return pd.api.types.is_datetime64_any_dtype(pd.Series([value]))
        elif self.data_type == DataType.CATEGORICAL:
            allowed_values = self.constraints.get('allowed_values', [])
            return value in allowed_values
        
        return True


class DataSchema:
    """Schema definition for a complete dataset."""
    
    def __init__(self, 
                 name: str,
                 fields: List[FieldSchema],
                 description: str = "",
                 version: str = "1.0"):
        self.name = name
        self.fields = fields
        self.description = description
        self.version = version
        self._field_map = {field.name: field for field in fields}
    
    def validate_dataframe(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Validate a DataFrame against this schema."""
        results = {
            'valid': True,
            'errors': [],
            'warnings': []
        }
        
        # Check required fields
        for field in self.fields:
            if field.required and field.name not in df.columns:
                results['errors'].append(f"Required field '{field.name}' is missing")
                results['valid'] = False
        
        # Check field types and constraints
        for column in df.columns:
            if column in self._field_map:
                field = self._field_map[column]
                # Sample validation on first few rows
                sample_values = df[column].head(100).dropna()
                invalid_values = []
                
                for value in sample_values:
                    if not field.validate(value):
                        invalid_values.append(value)
                
                if invalid_values:
                    results['warnings'].append(f"Field '{column}' has invalid values: {invalid_values[:5]}")
        
        return results
    
    def get_field(self, name: str) -> Optional[FieldSchema]:
        """Get field schema by name."""
        return self._field_map.get(name)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert schema to dictionary representation."""
        return {
            'name': self.name,
            'description': self.description,
            'version': self.version,
            'fields': [
                {
                    'name': field.name,
                    'data_type': field.data_type.value,
                    'required': field.required,
                    'nullable': field.nullable,
                    'description': field.description,
                    'constraints': field.constraints
                }
                for field in self.fields
            ]
        }


# Predefined schemas for common data types
BACI_SCHEMA = DataSchema(
    name="baci_trade_data",
    description="BACI international trade data schema",
    fields=[
        FieldSchema("year", DataType.INTEGER, description="Trade year"),
        FieldSchema("exporter", DataType.STRING, description="Exporter country code"),
        FieldSchema("importer", DataType.STRING, description="Importer country code"),
        FieldSchema("product", DataType.STRING, description="Product code (HS classification)"),
        FieldSchema("value", DataType.FLOAT, description="Trade value in thousands USD"),
        FieldSchema("quantity", DataType.FLOAT, nullable=True, description="Trade quantity")
    ]
)

COUNTRY_CODES_SCHEMA = DataSchema(
    name="country_codes",
    description="Country codes and names mapping",
    fields=[
        FieldSchema("country_code", DataType.STRING, description="ISO country code"),
        FieldSchema("country_name", DataType.STRING, description="Country name"),
        FieldSchema("iso2", DataType.STRING, nullable=True, description="ISO 2-letter code"),
        FieldSchema("iso3", DataType.STRING, nullable=True, description="ISO 3-letter code")
    ]
)

PRODUCT_CODES_SCHEMA = DataSchema(
    name="product_codes",
    description="Product codes and descriptions",
    fields=[
        FieldSchema("product_code", DataType.STRING, description="HS product code"),
        FieldSchema("product_name", DataType.STRING, description="Product description"),
        FieldSchema("hs_level", DataType.INTEGER, description="HS classification level")
    ]
)

WORLDBANK_SCHEMA = DataSchema(
    name="worldbank_indicators",
    description="World Bank economic indicators",
    fields=[
        FieldSchema("country_code", DataType.STRING, description="Country code"),
        FieldSchema("year", DataType.INTEGER, description="Year"),
        FieldSchema("indicator_code", DataType.STRING, description="Indicator code"),
        FieldSchema("indicator_name", DataType.STRING, description="Indicator name"),
        FieldSchema("value", DataType.FLOAT, nullable=True, description="Indicator value")
    ]
)

# Schema registry
SCHEMA_REGISTRY = {
    'baci': BACI_SCHEMA,
    'country_codes': COUNTRY_CODES_SCHEMA,
    'product_codes': PRODUCT_CODES_SCHEMA,
    'worldbank': WORLDBANK_SCHEMA
}


def get_schema(name: str) -> Optional[DataSchema]:
    """Get a schema by name from the registry."""
    return SCHEMA_REGISTRY.get(name)


def register_schema(schema: DataSchema) -> None:
    """Register a new schema in the registry."""
    SCHEMA_REGISTRY[schema.name] = schema


def validate_data(data: pd.DataFrame, schema_name: str) -> Dict[str, Any]:
    """Validate data against a named schema."""
    schema = get_schema(schema_name)
    if schema is None:
        return {
            'valid': False,
            'errors': [f"Schema '{schema_name}' not found"],
            'warnings': []
        }
    
    return schema.validate_dataframe(data)


# Alias for backward compatibility
Schema = DataSchema


def validate_schema(data: pd.DataFrame, schema: DataSchema) -> Dict[str, Any]:
    """Validate data against a schema instance."""
    return schema.validate_dataframe(data)


class SchemaInference:
    """Infer schema from data."""
    
    @staticmethod
    def infer_from_dataframe(df: pd.DataFrame, name: str = "inferred_schema") -> DataSchema:
        """Infer a schema from a DataFrame."""
        fields = []
        for column in df.columns:
            dtype = df[column].dtype
            nullable = df[column].isnull().any()
            
            # Map pandas dtype to DataType
            if pd.api.types.is_string_dtype(dtype):
                data_type = DataType.STRING
            elif pd.api.types.is_integer_dtype(dtype):
                data_type = DataType.INTEGER
            elif pd.api.types.is_float_dtype(dtype):
                data_type = DataType.FLOAT
            elif pd.api.types.is_bool_dtype(dtype):
                data_type = DataType.BOOLEAN
            elif pd.api.types.is_datetime64_any_dtype(dtype):
                data_type = DataType.DATETIME
            else:
                data_type = DataType.STRING  # Default
            
            fields.append(FieldSchema(
                name=column,
                data_type=data_type,
                nullable=nullable,
                description=f"Inferred from column {column}"
            ))
        
        return DataSchema(name=name, fields=fields, description="Auto-inferred schema")