"""
Base class for DataLoaders that require advanced memory optimization techniques,
including optimized I/O and chunked processing.

IMPORTANT: This module provides backward compatibility with the previous memory
optimization implementation. New code should use the MemoryOptimizedFileLoader
from the loader_base module instead.
"""
# Standard library imports
import logging
import sys
import traceback
import warnings
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional, Type, TypeVar, Union

# Third-party imports
import pandas as pd

# Project imports
# Import from loader_base first to avoid circular imports
from yemen_trade_diagnostic.data.loader_base import DataLoaderBase, DataSource, FileDataLoader
from yemen_trade_diagnostic.data.loader_base import (
    MemoryOptimizedFileLoader as NewMemoryOptimizedFileLoader,
)
from yemen_trade_diagnostic.interfaces.error_interface import (
    <PERSON><PERSON>r<PERSON>ate<PERSON>y,
    ErrorManager,
    ErrorSeverity,
    report_error,
    with_error_handling,
)
from yemen_trade_diagnostic.interfaces.hardware_interface import accelerate_dataframe
from yemen_trade_diagnostic.interfaces.logging_interface import get_logger
from yemen_trade_diagnostic.utils.memory_utils import (
    optimize_dataframe,
    read_csv_optimized_v2,
    read_json_optimized_v2,
    read_parquet_optimized_v2,
)

# Get logger for this module
logger = get_logger(__name__)

# Type variables for generic functions
T = TypeVar('T')

# Display deprecation warning
warnings.warn(
    "The MemoryOptimizedDataLoader class is deprecated and will be removed in a future version. "
    "Please use MemoryOptimizedFileLoader from loader_base module instead.",
    DeprecationWarning,
    stacklevel=2
)

class MemoryOptimizedDataLoader(DataLoaderBase):
    """
    DEPRECATED: Extends DataLoader to provide standardized memory-optimized loading.
    
    This class is kept for backward compatibility and will be removed in a future version.
    New code should use MemoryOptimizedFileLoader from loader_base instead.
    """
    def __init__(self, source_name: str, config: Dict[str, Any] = None):
        warnings.warn(
            "MemoryOptimizedDataLoader is deprecated and will be removed in a future version. "
            "Please use MemoryOptimizedFileLoader from loader_base module instead.",
            DeprecationWarning,
            stacklevel=2
        )
        super().__init__(source_name, config)
        # Logger specific to this subclass, can also use self.base_logger from parent
        self.mem_opt_logger = get_logger(f"{self.__class__.__module__}.{self.__class__.__name__}")
        self.memory_options = self.config.get('memory_options', {})

    def _get_file_path(self, **kwargs) -> Path:
        """
        Abstract-like method: Concrete subclasses should implement this to return 
        the full Path to the data file based on kwargs (e.g., year, filename parts).
        Example: For BaciLoader, kwargs might include 'year'.
        """
        raise NotImplementedError("_get_file_path must be implemented by MemoryOptimizedDataLoader subclasses.")

    @with_error_handling(category=ErrorCategory.DATA_ACCESS, severity=ErrorSeverity.ERROR)
    def load(self, apply_acceleration: bool = True, **kwargs) -> pd.DataFrame:
        """
        Memory-optimized load method.
        Detects file type and uses the appropriate optimized reader.
        Handles chunking based on config for supported types (CSV, JSONL).
        
        Args:
            apply_acceleration: Whether to apply hardware acceleration to the loaded data
            **kwargs: Additional arguments to pass to the reader functions
            
        Returns:
            Loaded DataFrame, optimized for memory usage
            
        Raises:
            FileNotFoundError: If the file does not exist
            ValueError: If the file type is unsupported
            Various pandas exceptions: If there are issues reading the file
        """
        try:
            file_path = self._get_file_path(**kwargs)
            
            chunksize = self.memory_options.get('chunksize')
            dtype_spec = self.memory_options.get('dtype_spec') # Primarily for CSV
            usecols = self.memory_options.get('usecols')
            apply_opt_df_in_reader = self.memory_options.get('apply_optimize_df_in_reader', True)
            cat_thresh = self.memory_options.get('category_optimization_threshold', 0.5)

            # Kwargs for the reader functions, excluding those managed by MemoryOptimizedDataLoader directly
            reader_kwargs = {k:v for k,v in kwargs.items() if k not in ['apply_acceleration']}
            # Add options from self.memory_options that are direct passthroughs if not handled above
            # e.g., for pd.read_json specific args like 'orient', 'lines' if they are in memory_options
            json_orient = self.memory_options.get('json_orient', 'records')
            json_lines = self.memory_options.get('json_lines', False)

            self.mem_opt_logger.info(f"Optimized load for {self.source_name} from {file_path} (chunksize={chunksize})")

            if not file_path.exists():
                error_msg = f"Required data file not found: {file_path} for source {self.source_name}"
                self.mem_opt_logger.error(error_msg)
                report_error(
                    FileNotFoundError(error_msg),
                    category=ErrorCategory.DATA_ACCESS,
                    severity=ErrorSeverity.ERROR,
                    component="data",
                    operation="load",
                    extra={"file_path": str(file_path), "source_name": self.source_name}
                )
                raise FileNotFoundError(error_msg)

            # Determine file type and call appropriate reader
            file_suffix = file_path.suffix.lower()
            df_or_iterator: Union[pd.DataFrame, pd.io.parsers.readers.TextFileReader, None] = None

            try:
                if file_suffix == '.csv':
                    df_or_iterator = read_csv_optimized_v2(
                        file_path, dtype_spec=dtype_spec, usecols=usecols, chunksize=chunksize,
                        apply_optimize_df=apply_opt_df_in_reader if chunksize is None else False, # only apply to full df
                        category_threshold_for_optimize=cat_thresh, **reader_kwargs
                    )
                elif file_suffix == '.parquet':
                    if chunksize:
                        self.mem_opt_logger.warning(f"Chunking not directly supported by pd.read_parquet. Loading full file for {file_path}.")
                        # Fallback to loading full file, then user can chunk process if needed after load.
                        # Or implement chunked parquet reading if a library supports it and it's required.
                    df_or_iterator = read_parquet_optimized_v2(
                        file_path, usecols=usecols, 
                        apply_optimize_df=apply_opt_df_in_reader, 
                        category_threshold_for_optimize=cat_thresh, **reader_kwargs
                    )
                elif file_suffix == '.json' or file_suffix == '.jsonl':
                    # read_json_optimized_v2 handles lines=True for .jsonl implicitly if configured
                    actual_json_lines = json_lines or (file_suffix == '.jsonl')
                    df_or_iterator = read_json_optimized_v2(
                        file_path, orient=json_orient, lines=actual_json_lines, 
                        chunksize=chunksize if actual_json_lines else None, # Chunksize mainly for JSONL
                        apply_optimize_df=apply_opt_df_in_reader if (chunksize is None or not actual_json_lines) else False,
                        category_threshold_for_optimize=cat_thresh, **reader_kwargs
                    )
                else:
                    error_msg = f"Unsupported file type: {file_suffix} for file {file_path}"
                    self.mem_opt_logger.error(error_msg)
                    report_error(
                        ValueError(error_msg),
                        category=ErrorCategory.DATA_ACCESS,
                        severity=ErrorSeverity.ERROR,
                        component="data",
                        operation="load",
                        extra={"file_path": str(file_path), "file_suffix": file_suffix}
                    )
                    raise ValueError(error_msg)
            except Exception as e:
                error_msg = f"Failed to load data from {file_path}: {str(e)}"
                self.mem_opt_logger.error(error_msg)
                # Extract the original exception for better error reporting
                report_error(
                    e,
                    category=ErrorCategory.DATA_ACCESS,
                    severity=ErrorSeverity.ERROR,
                    component="data",
                    operation="load",
                    extra={"file_path": str(file_path), "source_name": self.source_name}
                )
                raise

            # Handle chunked reading: concatenate chunks
            try:
                if chunksize and isinstance(df_or_iterator, pd.io.parsers.readers.TextFileReader): # For CSV and JSONL iterators
                    self.mem_opt_logger.info(f"Processing in chunks of size {chunksize} for {file_path.name}.")
                    chunks = []
                    for i, chunk_df in enumerate(df_or_iterator):
                        self.mem_opt_logger.debug(f"Processing chunk {i+1} for {file_path.name}. Shape: {chunk_df.shape}")
                        # Apply optimize_dataframe to each chunk if apply_opt_df_in_reader was True
                        # but reader returned iterator (so it didn't apply it itself)
                        if apply_opt_df_in_reader:
                            chunk_df = optimize_dataframe(chunk_df, category_threshold=cat_thresh, inplace=False)
                        chunks.append(chunk_df)
                    
                    if not chunks:
                        self.mem_opt_logger.warning(f"No data loaded in chunks from {file_path}")
                        df = pd.DataFrame()
                    else:
                        df = pd.concat(chunks, ignore_index=True)
                        self.mem_opt_logger.info(f"Concatenated {len(chunks)} chunks. Final shape: {df.shape}")
                elif isinstance(df_or_iterator, pd.DataFrame):
                    df = df_or_iterator # Already a DataFrame
                else: # Should not happen if logic is correct
                    self.mem_opt_logger.error("Optimized reader returned an unexpected type.")
                    df = pd.DataFrame()
            except Exception as e:
                error_msg = f"Failed to process chunks from {file_path}: {str(e)}"
                self.mem_opt_logger.error(error_msg)
                report_error(
                    e,
                    category=ErrorCategory.DATA_ACCESS,
                    severity=ErrorSeverity.ERROR,
                    component="data",
                    operation="process_chunks",
                    extra={"file_path": str(file_path), "source_name": self.source_name}
                )
                raise

            if df.empty and file_suffix != '.csv': # CSV empty check is within read_csv_optimized_v2 for non-chunked
                self.mem_opt_logger.warning(f"No data loaded from {file_path} (or all chunks were empty).")
            
            # Apply hardware acceleration if requested
            try:
                if apply_acceleration and not df.empty:
                    df = accelerate_dataframe(df)
                    self.mem_opt_logger.info(f"Hardware acceleration applied. Final shape for load: {df.shape}")
                elif not df.empty:
                    self.mem_opt_logger.info("Hardware acceleration skipped.")
                else:
                    self.mem_opt_logger.info("Skipping hardware acceleration for empty DataFrame.")
            except Exception as e:
                error_msg = f"Failed to apply hardware acceleration: {str(e)}"
                self.mem_opt_logger.warning(error_msg)
                report_error(
                    e,
                    category=ErrorCategory.HARDWARE,
                    severity=ErrorSeverity.WARNING,
                    component="data",
                    operation="accelerate_dataframe",
                    extra={"file_path": str(file_path), "source_name": self.source_name}
                )
                # Continue with unaccelerated dataframe instead of failing
                self.mem_opt_logger.info("Continuing with unaccelerated DataFrame.")
            
            return df
            
        except Exception as e:
            # Catch-all for any exceptions not handled above
            error_msg = f"Unhandled exception in memory-optimized load: {str(e)}"
            self.mem_opt_logger.error(error_msg)
            self.mem_opt_logger.error(traceback.format_exc())
            report_error(
                e,
                category=ErrorCategory.DATA_ACCESS,
                severity=ErrorSeverity.ERROR,
                component="data",
                operation="load",
                extra={"source_name": self.source_name}
            )
            raise


# Legacy adapter class that combines DataLoaderBase with the legacy 
# MemoryOptimizedDataLoader interface but forwards to the new implementation
class MemoryOptimizedAdapter:
    """
    Adapter class that provides compatibility layer between old and new memory optimization interfaces.
    This class should be used to adapt existing code that uses MemoryOptimizedDataLoader to the
    new MemoryOptimizedFileLoader class.
    """
    
    @staticmethod
    def adapt_loader(loader_class: Type[DataLoaderBase]) -> Type[DataLoaderBase]:
        """
        Create an adapter class that provides the legacy memory optimization interface
        but uses the new MemoryOptimizedFileLoader implementation.
        
        Args:
            loader_class: The loader class to adapt
            
        Returns:
            Adapted loader class
        """
        # Check if the loader already inherits from FileDataLoader
        is_file_loader = issubclass(loader_class, FileDataLoader)
        
        if is_file_loader:
            # If it's already a FileDataLoader, create a class that inherits from MemoryOptimizedFileLoader
            class AdaptedLoader(NewMemoryOptimizedFileLoader, loader_class):
                def __init__(self, source_name: str, config: Dict[str, Any] = None, **kwargs):
                    # Convert source_name and config to appropriate parameters for MemoryOptimizedFileLoader
                    name = source_name
                    source_type = DataSource.from_string(source_name)
                    memory_options = config.get('memory_options', {}) if config else {}
                    
                    # Initialize MemoryOptimizedFileLoader
                    super().__init__(
                        name=name,
                        source_type=source_type,
                        memory_options=memory_options,
                        **kwargs
                    )
                    
                    # Store original parameters for compatibility
                    self.source_name = source_name
                    self.config = config or {}
                
                # Compatibility method for legacy code
                def _get_file_path(self, **kwargs) -> Path:
                    """Legacy method for getting file path."""
                    # Try to use the loader's implementation if available
                    if hasattr(loader_class, '_get_file_path'):
                        get_file_path_method = getattr(loader_class, '_get_file_path')
                        # Check if it's a bound method (instance method) or unbound method (class method)
                        if hasattr(get_file_path_method, '__self__') and get_file_path_method.__self__ is loader_class:
                            # Class method
                            return get_file_path_method(**kwargs)
                        else:
                            # Instance method, call using self
                            return get_file_path_method(self, **kwargs)
                    
                    # Default implementation attempts to resolve path from kwargs
                    if 'file_path' in kwargs:
                        return self._resolve_path(kwargs['file_path'])
                    elif 'path' in kwargs:
                        return self._resolve_path(kwargs['path'])
                    else:
                        raise NotImplementedError("_get_file_path must be implemented or path must be provided in kwargs")
        else:
            # For non-FileDataLoader classes, create a wrapper that adapts to MemoryOptimizedFileLoader
            class AdaptedLoader(NewMemoryOptimizedFileLoader):
                def __init__(self, source_name: str, config: Dict[str, Any] = None, **kwargs):
                    # Convert source_name and config to appropriate parameters for MemoryOptimizedFileLoader
                    name = source_name
                    source_type = DataSource.from_string(source_name)
                    memory_options = config.get('memory_options', {}) if config else {}
                    
                    # Initialize MemoryOptimizedFileLoader
                    super().__init__(
                        name=name,
                        source_type=source_type,
                        memory_options=memory_options,
                        **kwargs
                    )
                    
                    # Store original parameters for compatibility
                    self.source_name = source_name
                    self.config = config or {}
                    
                    # Create instance of original loader for delegation
                    self.original_loader = loader_class(source_name, config)
                
                def _get_file_path(self, **kwargs) -> Path:
                    """Delegate to original loader's _get_file_path method."""
                    if hasattr(self.original_loader, '_get_file_path'):
                        return self.original_loader._get_file_path(**kwargs)
                    
                    # Default implementation attempts to resolve path from kwargs
                    if 'file_path' in kwargs:
                        return self._resolve_path(kwargs['file_path'])
                    elif 'path' in kwargs:
                        return self._resolve_path(kwargs['path'])
                    else:
                        raise NotImplementedError("_get_file_path must be implemented or path must be provided in kwargs")
                
                def __getattr__(self, name):
                    """Delegate unknown attributes to original loader."""
                    if hasattr(self.original_loader, name):
                        return getattr(self.original_loader, name)
                    raise AttributeError(f"{self.__class__.__name__} has no attribute '{name}'")
        
        # Update class name and docstring
        AdaptedLoader.__name__ = f"MemoryOptimized{loader_class.__name__}"
        AdaptedLoader.__qualname__ = f"MemoryOptimized{loader_class.__name__}"
        AdaptedLoader.__doc__ = f"""
        Memory-optimized version of {loader_class.__name__}.
        
        This class adapts {loader_class.__name__} to use the new MemoryOptimizedFileLoader
        implementation while maintaining compatibility with the old interface.
        
        Original class docstring:
        {loader_class.__doc__}
        """
        
        return AdaptedLoader


# Alias for backward compatibility
MemoryOptimizedFileLoader = NewMemoryOptimizedFileLoader