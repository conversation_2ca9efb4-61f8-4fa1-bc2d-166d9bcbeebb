"""
Data Validation Interface

This module provides data-specific validation interfaces that alias to the main
validation interface for backward compatibility.
"""

# Project imports
# Import everything from the main validation interface
from yemen_trade_diagnostic.interfaces.validation_interface import *


# Additional data-specific validation classes
class ValidationReport:
    """A validation report that contains results of data validation."""
    
    def __init__(self, validation_result=None, errors=None, warnings=None, summary=None):
        self.validation_result = validation_result or {}
        self.errors = errors or []
        self.warnings = warnings or []
        self.summary = summary or {}
        self.passed = len(self.errors) == 0
    
    def add_error(self, error_message):
        """Add an error to the report."""
        self.errors.append(error_message)
        self.passed = False
    
    def add_warning(self, warning_message):
        """Add a warning to the report."""
        self.warnings.append(warning_message)
    
    def get_summary(self):
        """Get a summary of the validation report."""
        return {
            'passed': self.passed,
            'error_count': len(self.errors),
            'warning_count': len(self.warnings),
            'errors': self.errors,
            'warnings': self.warnings
        }


class WorkloadClassifier:
    """Classifier for different types of data processing workloads."""
    
    def __init__(self):
        self.workload_types = ['cpu_intensive', 'memory_intensive', 'io_intensive', 'balanced']
    
    def classify_workload(self, data_size, operations, **kwargs):
        """Classify the workload type based on data and operations."""
        if data_size > 1000000:  # Large dataset
            return 'memory_intensive'
        elif 'computation' in operations:
            return 'cpu_intensive'
        elif 'file_io' in operations:
            return 'io_intensive'
        else:
            return 'balanced'
    
    def get_optimal_settings(self, workload_type):
        """Get optimal settings for a workload type."""
        settings = {
            'cpu_intensive': {'threads': 8, 'memory_limit': '4GB'},
            'memory_intensive': {'threads': 4, 'memory_limit': '16GB'},
            'io_intensive': {'threads': 2, 'memory_limit': '2GB'},
            'balanced': {'threads': 4, 'memory_limit': '8GB'}
        }
        return settings.get(workload_type, settings['balanced'])


# Additional validation functions for backward compatibility
def validate_export_data(df, **kwargs):
    """Validate export data format and content."""
    errors = []
    warnings = []
    
    # Basic validation
    if df.empty:
        errors.append("Export data is empty")
    
    required_columns = ['country', 'product', 'year', 'value']
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        errors.append(f"Missing required columns: {missing_columns}")
    
    # Create validation report
    report = ValidationReport(errors=errors, warnings=warnings)
    return report


def create_validation_context(**kwargs):
    """Create a validation context for data processing."""
    return {
        'timestamp': pd.Timestamp.now(),
        'context_id': f"validation_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}",
        'parameters': kwargs
    }


# Alias for backward compatibility
ValidationContext = dict