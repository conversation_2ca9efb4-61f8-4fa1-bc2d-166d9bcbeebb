"""
Data Transformation Framework for Yemen Trade Diagnostic V2

This module provides a framework for transforming data according to chart requirements,
with full integration of V2 interfaces for hardware acceleration, error handling,
validation, and logging.
"""

# Standard library imports
from abc import ABC, abstractmethod
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional, Union

# Third-party imports
import numpy as np
import pandas as pd

# Project imports
from yemen_trade_diagnostic.data.unified_loader_api import load_data

# V2 data module imports
from yemen_trade_diagnostic.data.validation import DataValidationManager
from yemen_trade_diagnostic.interfaces.cache_interface import (
    CacheLevel,
    cache_get_or_compute,
    memoize,
)
from yemen_trade_diagnostic.interfaces.error_interface import (
    ErrorCategory,
    ErrorSeverity,
    report_error,
    with_error_handling,
)

# V2 interface imports
from yemen_trade_diagnostic.interfaces.hardware_interface import (
    AccelerationType,
    get_hardware_manager,
)
from yemen_trade_diagnostic.interfaces.logging_interface import get_logger, log_execution_time
from yemen_trade_diagnostic.interfaces.validation_interface import (
    ValidationMode,
    ValidationResult,
    validate_dataframe,
)
from yemen_trade_diagnostic.utils.column_mapper import ensure_columns_exist
from yemen_trade_diagnostic.utils.config import BACI_TO_SEMANTIC_COLUMN_MAPPING, get_chart_config
from yemen_trade_diagnostic.utils.memory_optimizer import optimize_dataframe_dtypes

logger = get_logger(__name__)


class DataTransformer(ABC):
    """
    Base class for data transformers in V2 architecture.
    
    A data transformer is responsible for transforming raw data into the format
    required by specific charts, with full hardware acceleration support.
    """
    
    def __init__(self, chart_id: str, config: Optional[Dict[str, Any]] = None):
        """Initialize the data transformer."""
        self.chart_id = chart_id
        self.config = config or {}
        self.chart_config = get_chart_config(chart_id)
        
        # Extract chart metadata
        self.chart_type = self.chart_config.get("chart_type", "")
        self.chart_title = self.chart_config.get("title", "")
        self.pipeline = self.chart_config.get("analysis_type", "")
        
        # Initialize managers
        self.hw_manager = get_hardware_manager()
        self.validation_manager = DataValidationManager()
        self.logger = logger
        
        # Check hardware availability
        self.hardware_available = self.hw_manager.is_hardware_available()
        
        self.logger.info(
            f"Initialized data transformer for '{chart_id}'",
            extra={
                "chart_type": self.chart_type,
                "hardware_available": self.hardware_available,
                "pipeline": self.pipeline
            }
        )
    
    @abstractmethod
    @with_error_handling(
        category=ErrorCategory.DATA_ACCESS,
        severity=ErrorSeverity.ERROR
    )
    def transform(self, **kwargs) -> Dict[str, Any]:
        """
        Transform data according to chart requirements.
        
        Args:
            **kwargs: Additional arguments for the transformation
            
        Returns:
            Dictionary containing the transformed data
        """
        pass
    
    @log_execution_time
    def validate_transformed_data(self, data: Dict[str, Any]) -> ValidationResult:
        """Validate the transformed data against chart requirements."""
        try:
            # Get validation schema for chart type
            schema = self._get_validation_schema()
            
            # Validate using V2 validation manager
            if isinstance(data, pd.DataFrame):
                result = self.validation_manager.validate_dataset(
                    data, 
                    dataset_type=self.chart_type,
                    mode=ValidationMode.STANDARD
                )
            else:
                # For dictionary data, create basic validation
                result = ValidationResult()
                if not data:
                    result.add_issue(
                        level="error",
                        message="Transformed data is empty",
                        context={"chart_id": self.chart_id}
                    )
            
            return result
            
        except Exception as e:
            self.logger.error(f"Validation error: {str(e)}")
            result = ValidationResult()
            result.add_issue(
                level="error",
                message=f"Validation failed: {str(e)}",
                context={"chart_id": self.chart_id, "error": str(e)}
            )
            return result
    
    def _get_validation_schema(self) -> Dict[str, Any]:
        """Get validation schema for the chart type."""
        # Basic schema - can be extended by subclasses
        return {
            "required_fields": ["data", "metadata"],
            "data_types": {
                "data": [pd.DataFrame, dict, list],
                "metadata": dict
            }
        }
    
    @memoize(level=CacheLevel.MEMORY, ttl=3600)
    def _accelerate_computation(self, data: pd.DataFrame, 
                              operation: str, **kwargs) -> Any:
        """
        Use hardware acceleration for computations when available.
        
        Args:
            data: Input data
            operation: Operation to perform
            **kwargs: Additional parameters
            
        Returns:
            Computed result
        """
        if self.hardware_available:
            try:
                if operation == "groupby":
                    return self.hw_manager.accelerate_dataframe(
                        data, 
                        operation="groupby",
                        groupby_columns=kwargs.get("columns", []),
                        agg_functions=kwargs.get("agg_functions", {})
                    )
                elif operation == "merge":
                    return self.hw_manager.accelerate_dataframe(
                        data,
                        operation="merge",
                        other=kwargs.get("other"),
                        on=kwargs.get("on"),
                        how=kwargs.get("how", "inner")
                    )
                elif operation == "filter":
                    return self.hw_manager.accelerate_dataframe(
                        data,
                        operation="filter",
                        condition=kwargs.get("condition")
                    )
                else:
                    return self._fallback_computation(data, operation, **kwargs)
                    
            except Exception as e:
                self.logger.warning(
                    f"Hardware acceleration failed, using fallback: {str(e)}"
                )
                return self._fallback_computation(data, operation, **kwargs)
        else:
            return self._fallback_computation(data, operation, **kwargs)
    
    def _fallback_computation(self, data: pd.DataFrame, 
                            operation: str, **kwargs) -> Any:
        """Fallback computation without hardware acceleration."""
        if operation == "groupby":
            columns = kwargs.get("columns", [])
            agg_functions = kwargs.get("agg_functions", {})
            return data.groupby(columns).agg(agg_functions)
        elif operation == "merge":
            other = kwargs.get("other")
            on = kwargs.get("on")
            how = kwargs.get("how", "inner")
            return pd.merge(data, other, on=on, how=how)
        elif operation == "filter":
            condition = kwargs.get("condition")
            return data[condition]
        else:
            raise ValueError(f"Unknown operation: {operation}")
    
    def _standardize_columns(self, df: pd.DataFrame, 
                           mapping: Dict[str, str]) -> pd.DataFrame:
        """Standardize column names using provided mapping."""
        return df.rename(columns=mapping)
    
    def _optimize_memory(self, df: pd.DataFrame) -> pd.DataFrame:
        """Optimize DataFrame memory usage."""
        return optimize_dataframe_dtypes(df)
    
    def _prepare_metadata(self) -> Dict[str, Any]:
        """Prepare standard metadata for transformed data."""
        return {
            "chart_id": self.chart_id,
            "chart_type": self.chart_type,
            "chart_title": self.chart_title,
            "pipeline": self.pipeline,
            "hardware_accelerated": self.hardware_available,
            "transformer_version": "2.0"
        }


class CompositionTransformer(DataTransformer):
    """
    Data transformer for composition charts.
    
    Composition charts show how a whole is divided into parts, such as export/import
    composition by product category, trade composition by sector, or composition
    changes over time.
    """
    
    @with_error_handling(
        category=ErrorCategory.DATA_ACCESS,
        severity=ErrorSeverity.ERROR,
        fallback_value={"data": pd.DataFrame(), "metadata": {}}
    )
    @log_execution_time
    def transform(self, year: int = 2023, data_type: str = "exports") -> Dict[str, Any]:
        """
        Transform data for composition charts.
        
        Args:
            year: The year to analyze
            data_type: Type of data ('exports', 'imports')
            
        Returns:
            Dictionary containing the transformed data
        """
        self.logger.info(
            f"Transforming data for composition chart",
            extra={"year": year, "data_type": data_type}
        )
        
        try:
            # Load data based on type
            if data_type == "exports":
                data = self._load_export_data(year)
            elif data_type == "imports":
                data = self._load_import_data(year)
            else:
                data = self._load_trade_data(year)
            
            # Validate raw data
            validation_result = self.validation_manager.validate_dataset(
                data, dataset_type="baci"
            )
            if not validation_result.is_valid():
                self.logger.warning("Raw data validation failed")
            
            # Transform data
            transformed = self._transform_composition_data(data, year, data_type)
            
            # Prepare result
            result = {
                "data": transformed,
                "metadata": self._prepare_metadata()
            }
            
            # Validate transformed data
            final_validation = self.validate_transformed_data(result)
            if not final_validation.is_valid():
                self.logger.warning("Transformed data validation failed")
            
            return result
            
        except Exception as e:
            self.logger.error(f"Transformation failed: {str(e)}")
            report_error(e, "CompositionTransformer", "transform")
            raise
    
    def _load_export_data(self, year: int) -> pd.DataFrame:
        """Load export data for specified year."""
        return load_data("baci", year=year, filters={"exporter": "YEM"})
    
    def _load_import_data(self, year: int) -> pd.DataFrame:
        """Load import data for specified year."""
        return load_data("baci", year=year, filters={"importer": "YEM"})
    
    def _load_trade_data(self, year: int) -> pd.DataFrame:
        """Load all trade data for specified year."""
        return load_data("baci", year=year)
    
    def _transform_composition_data(self, data: pd.DataFrame, 
                                  year: int, data_type: str) -> pd.DataFrame:
        """Transform raw data into composition format."""
        # Standardize columns to semantic names
        data = self._standardize_columns(data, BACI_TO_SEMANTIC_COLUMN_MAPPING)
        
        # Extract product categories (e.g., HS2 codes)
        data['hs2_code'] = data['product_code'].astype(str).str[:2]
        
        # Use hardware acceleration for groupby if available
        grouped = self._accelerate_computation(
            data,
            operation="groupby",
            columns=['hs2_code'],
            agg_functions={'trade_value_usd': 'sum'}
        )
        
        # Calculate percentages
        total_value = grouped['trade_value_usd'].sum()
        grouped['percentage'] = (grouped['trade_value_usd'] / total_value) * 100
        
        # Sort by value
        grouped = grouped.sort_values('trade_value_usd', ascending=False)
        
        # Add metadata
        grouped['year'] = year
        grouped['data_type'] = data_type
        
        # Optimize memory
        grouped = self._optimize_memory(grouped)
        
        return grouped.reset_index()


class GrowthTransformer(DataTransformer):
    """Transformer for growth-related charts."""
    
    @with_error_handling(
        category=ErrorCategory.DATA_ACCESS,
        severity=ErrorSeverity.ERROR,
        fallback_value={"data": pd.DataFrame(), "metadata": {}}
    )
    @log_execution_time
    def transform(self, start_year: int = 2020, end_year: int = 2023) -> Dict[str, Any]:
        """Transform data for growth analysis."""
        self.logger.info(
            f"Transforming data for growth chart",
            extra={"start_year": start_year, "end_year": end_year}
        )
        
        try:
            # Load multi-year data
            data_frames = []
            for year in range(start_year, end_year + 1):
                yearly_data = load_data("baci", year=year)
                yearly_data['year'] = year
                data_frames.append(yearly_data)
            
            # Combine all years
            all_data = pd.concat(data_frames, ignore_index=True)
            
            # Transform to growth format
            growth_data = self._calculate_growth_rates(all_data)
            
            return {
                "data": growth_data,
                "metadata": self._prepare_metadata()
            }
            
        except Exception as e:
            self.logger.error(f"Growth transformation failed: {str(e)}")
            report_error(e, "GrowthTransformer", "transform")
            raise
    
    def _calculate_growth_rates(self, data: pd.DataFrame) -> pd.DataFrame:
        """Calculate year-over-year growth rates."""
        # Standardize columns
        data = self._standardize_columns(data, BACI_TO_SEMANTIC_COLUMN_MAPPING)
        
        # Group by year
        yearly_totals = self._accelerate_computation(
            data,
            operation="groupby",
            columns=['year'],
            agg_functions={'trade_value_usd': 'sum'}
        )
        
        # Calculate growth rates
        yearly_totals['growth_rate'] = yearly_totals['trade_value_usd'].pct_change() * 100
        
        # Add metadata
        yearly_totals['metric'] = 'total_trade_value'
        
        return yearly_totals.reset_index()


class ConcentrationTransformer(DataTransformer):
    """Transformer for concentration analysis charts."""
    
    @with_error_handling(
        category=ErrorCategory.DATA_ACCESS,
        severity=ErrorSeverity.ERROR,
        fallback_value={"data": pd.DataFrame(), "metadata": {}}
    )
    @log_execution_time
    def transform(self, year: int = 2023) -> Dict[str, Any]:
        """Transform data for concentration analysis."""
        self.logger.info(
            f"Transforming data for concentration chart",
            extra={"year": year}
        )
        
        try:
            # Load data
            data = load_data("baci", year=year, filters={"exporter": "YEM"})
            
            # Calculate concentration metrics
            concentration_data = self._calculate_concentration(data)
            
            return {
                "data": concentration_data,
                "metadata": self._prepare_metadata()
            }
            
        except Exception as e:
            self.logger.error(f"Concentration transformation failed: {str(e)}")
            report_error(e, "ConcentrationTransformer", "transform")
            raise
    
    def _calculate_concentration(self, data: pd.DataFrame) -> pd.DataFrame:
        """Calculate concentration metrics (e.g., HHI)."""
        # Standardize columns
        data = self._standardize_columns(data, BACI_TO_SEMANTIC_COLUMN_MAPPING)
        
        # Group by destination
        market_shares = self._accelerate_computation(
            data,
            operation="groupby",
            columns=['importer_iso'],
            agg_functions={'trade_value_usd': 'sum'}
        )
        
        # Calculate market shares
        total_value = market_shares['trade_value_usd'].sum()
        market_shares['market_share'] = (market_shares['trade_value_usd'] / total_value) * 100
        
        # Calculate HHI
        market_shares['hhi_contribution'] = market_shares['market_share'] ** 2
        hhi = market_shares['hhi_contribution'].sum()
        
        # Add HHI to results
        market_shares['overall_hhi'] = hhi
        
        return market_shares.reset_index()


# Factory function to get appropriate transformer
def get_transformer(chart_id: str, config: Optional[Dict[str, Any]] = None) -> DataTransformer:
    """
    Factory function to get the appropriate transformer for a chart.
    
    Args:
        chart_id: The ID of the chart
        config: Optional configuration
        
    Returns:
        Appropriate DataTransformer instance
    """
    chart_config = get_chart_config(chart_id)
    chart_type = chart_config.get("analysis_type", "").lower()
    
    transformers = {
        "composition": CompositionTransformer,
        "growth": GrowthTransformer,
        "concentration": ConcentrationTransformer,
    }
    
    TransformerClass = transformers.get(chart_type, DataTransformer)
    return TransformerClass(chart_id, config)


# Create specific transformers for common use cases
def create_export_composition_transformer(year: int = 2023) -> Dict[str, Any]:
    """Create transformer for export composition analysis."""
    transformer = CompositionTransformer("export_composition")
    return transformer.transform(year=year, data_type="exports")


def create_import_composition_transformer(year: int = 2023) -> Dict[str, Any]:
    """Create transformer for import composition analysis."""
    transformer = CompositionTransformer("import_composition")
    return transformer.transform(year=year, data_type="imports")


def create_trade_growth_transformer(start_year: int = 2020, 
                                  end_year: int = 2023) -> Dict[str, Any]:
    """Create transformer for trade growth analysis."""
    transformer = GrowthTransformer("trade_growth")
    return transformer.transform(start_year=start_year, end_year=end_year)


def create_market_concentration_transformer(year: int = 2023) -> Dict[str, Any]:
    """Create transformer for market concentration analysis."""
    transformer = ConcentrationTransformer("market_concentration")
    return transformer.transform(year=year)