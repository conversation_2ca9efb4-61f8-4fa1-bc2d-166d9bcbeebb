#!/usr/bin/env python3
"""
Test script to verify pipeline updates to use new data loader.
"""

import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from yemen_trade_diagnostic.interfaces.logging_interface import configure_logging, get_logger, LogLevel
from yemen_trade_diagnostic.pipelines.composition import CompositionPipeline
from yemen_trade_diagnostic.pipelines.concentration import ConcentrationPipeline
from yemen_trade_diagnostic.pipelines.growth import GrowthPipeline
from yemen_trade_diagnostic.pipelines.macroeconomic import MacroeconomicPipeline
from yemen_trade_diagnostic.pipelines.market import MarketPipeline
from yemen_trade_diagnostic.pipelines.rca import RcaPipeline
from yemen_trade_diagnostic.pipelines.sophistication import SophisticationPipeline

# Configure logging
configure_logging(log_level=LogLevel.INFO, log_to_console=True)
logger = get_logger(__name__)

def test_pipeline_initialization():
    """Test that all pipelines can be initialized without errors."""
    logger.info("Testing pipeline initialization...")
    
    pipelines = {
        'composition': CompositionPipeline,
        'concentration': ConcentrationPipeline,
        'growth': GrowthPipeline,
        'macroeconomic': MacroeconomicPipeline,
        'market': MarketPipeline,
        'rca': RcaPipeline,
        'sophistication': SophisticationPipeline
    }
    
    results = {}
    for name, pipeline_class in pipelines.items():
        try:
            pipeline = pipeline_class()
            results[name] = "✓ Success"
            logger.info(f"{name}: Initialized successfully")
        except Exception as e:
            results[name] = f"✗ Failed: {str(e)}"
            logger.error(f"{name}: Failed to initialize - {e}")
    
    return results

def test_pipeline_data_loading():
    """Test that pipelines can load data using the new loader."""
    logger.info("\nTesting data loading in pipelines...")
    
    # Test with a simple pipeline that loads minimal data
    try:
        logger.info("Testing composition pipeline data loading...")
        pipeline = CompositionPipeline()
        
        # The run method will attempt to load data
        # We'll use save=False to avoid file I/O
        results = pipeline.run(year=2020, save=False)
        
        logger.info("Composition pipeline executed successfully")
        return True
    except Exception as e:
        logger.error(f"Failed to test data loading: {e}")
        return False

def main():
    """Run all tests."""
    logger.info("=" * 60)
    logger.info("Testing Pipeline Updates to New Data Loader")
    logger.info("=" * 60)
    
    # Test 1: Pipeline initialization
    logger.info("\nTest 1: Pipeline Initialization")
    logger.info("-" * 30)
    init_results = test_pipeline_initialization()
    
    # Print summary
    logger.info("\nInitialization Summary:")
    for pipeline, status in init_results.items():
        logger.info(f"  {pipeline:20} {status}")
    
    # Test 2: Data loading (optional, requires data files)
    logger.info("\nTest 2: Data Loading (optional)")
    logger.info("-" * 30)
    try:
        data_test_passed = test_pipeline_data_loading()
        if data_test_passed:
            logger.info("  Data loading test: ✓ Passed")
        else:
            logger.info("  Data loading test: ✗ Failed (this may be expected if data files are not available)")
    except Exception as e:
        logger.info(f"  Data loading test: ✗ Skipped ({e})")
    
    # Overall summary
    all_passed = all("Success" in status for status in init_results.values())
    
    logger.info("\n" + "=" * 60)
    if all_passed:
        logger.info("✓ All pipeline initialization tests passed!")
        logger.info("The pipelines have been successfully updated to use the new data loader.")
    else:
        logger.info("✗ Some tests failed. Check the logs above for details.")
    logger.info("=" * 60)

if __name__ == "__main__":
    main()