# AI Agent Prompt: Yemen Trade Diagnostic Non-Model/Pipeline Module Review

## Context
You are tasked with reviewing the Yemen Trade Diagnostic project's supporting modules (excluding models and pipelines). This is a production-ready trade analysis system using BACI data and World Bank indicators, built with modern Python patterns and hardware acceleration support.

## Project Architecture Overview
- **Unified Error Handling System v2.0**: Uses @protect decorators and error_context patterns
- **Hardware Acceleration**: Optimized for Apple Silicon (M3 Pro) with Metal and Neural Engine support
- **Cache System**: Multi-level caching with hardware acceleration
- **Monitoring System**: Comprehensive performance and error tracking
- **Data System**: Unified data loader replacing 37 legacy loaders

## Modules to Review

### 1. **Interfaces** (`src/yemen_trade_diagnostic/interfaces/`)
- `cache_interface.py` - Unified cache interface with multi-level support
- `data_interface.py` - Data loading and transformation interfaces
- `error_interface.py` - Error handling contracts
- `hardware_interface.py` - Hardware acceleration interfaces
- `logging_interface.py` - Logging system interfaces
- `validation_interface.py` - Data validation interfaces
- `visualization_interface.py` - Chart generation interfaces
- `type_definitions.py` - Shared type definitions

### 2. **Hardware System** (`src/yemen_trade_diagnostic/hardware/`)
- **Core** (`core/`): Detector, accelerator, monitor, validation
- **Acceleration** (`acceleration/`): Metal optimization, workload analysis
- **Algorithms** (`algorithms/`): Vectorized operations, parallel processing
- **Cache** (`cache/`): Hardware-aware caching strategies
- **Memory** (`memory/`): Compression, memory pooling
- **Parallel** (`parallel/`): Multi-core processing, tiered execution
- **Validation** (`validation/`): Hardware-accelerated validation

### 3. **Error Handling** (`src/yemen_trade_diagnostic/errors/`)
- `core.py` - Unified error manager with @protect decorator
- `enhanced_circuit_breaker.py` - Adaptive circuit breakers
- `graceful_degradation.py` - Fallback strategies
- `recovery_manager.py` - Error recovery orchestration
- `resource_manager.py` - Resource monitoring and allocation
- `error_analytics.py` - Error pattern analysis
- `health_checks.py` - Component health monitoring

### 4. **Monitoring** (`src/yemen_trade_diagnostic/monitoring/`)
- `decorators.py` - @monitor_performance and other decorators
- `metrics_collector.py` - Metrics aggregation
- `performance_monitor.py` - Real-time performance tracking
- `reporters.py` - Multi-format reporting (HTML, PDF, Excel)
- `visualization.py` - Monitoring dashboards

### 5. **Data System** (`src/yemen_trade_diagnostic/data/`)
- `loader.py` - Unified data loader (load_data function)
- `validation.py` - Data validation rules
- `hardware/io.py` - Hardware-accelerated I/O operations

### 6. **Utilities** (`src/yemen_trade_diagnostic/utils/`)
- `column_mapper.py` & `column_mapping.py` - Column standardization
- `config.py` - Configuration management
- `feature_flags.py` - Feature toggle system
- `file_utils.py` & `output_utils.py` - File operations
- `memory_optimizer.py` & `memory_pool.py` - Memory management
- `pipeline_dependency_manager.py` - Pipeline orchestration
- `standardization.py` - Data standardization utilities

### 7. **Visualization** (`src/yemen_trade_diagnostic/visualization/`)
- **Core** (`core/`): Manager, base classes, configuration
- **Charts** (`charts/`): Chart implementations
- **Exporters** (`exporters/`): Export to various formats
- **Styles** (`styles/`): World Bank styling, themes
- **Utils** (`utils/`): Chart utilities, data preparation

### 8. **CLI** (`src/yemen_trade_diagnostic/cli/`)
- `main.py` - Main CLI entry point
- `enhanced_cli.py` - Advanced CLI features
- `monitor.py` - CLI monitoring commands

## Review Objectives

### 1. **Code Quality Assessment**
- **Design Patterns**: Are SOLID principles followed? Is the code DRY?
- **Type Safety**: Are type hints comprehensive and accurate?
- **Error Handling**: Does every module use the unified error handling system?
- **Documentation**: Are docstrings complete with Args, Returns, Raises?
- **Code Style**: Does it follow PEP 8 and project conventions?

### 2. **Architecture Compliance**
- **Unified Systems**: Are all modules using the unified error handling (@protect) and monitoring (@monitor_performance)?
- **Hardware Awareness**: Do performance-critical modules leverage hardware acceleration?
- **Cache Integration**: Are expensive operations properly cached?
- **Circular Dependencies**: Are there any circular imports?

### 3. **Performance Analysis**
- **Bottlenecks**: Identify potential performance issues
- **Memory Usage**: Check for memory leaks or inefficient data structures
- **Parallelization**: Are CPU-bound operations parallelized?
- **I/O Optimization**: Are file operations batched/streamed appropriately?

### 4. **Integration Points**
- **Interface Consistency**: Do interfaces follow consistent patterns?
- **Module Coupling**: Is coupling loose and cohesion high?
- **Dependency Management**: Are dependencies properly injected?
- **Configuration**: Is configuration centralized and environment-aware?

### 5. **Testing & Monitoring**
- **Test Coverage**: Are critical paths covered by tests?
- **Monitoring Integration**: Do modules emit proper metrics?
- **Health Checks**: Are health check endpoints available?
- **Logging**: Is logging consistent and informative?

### 6. **Security & Reliability**
- **Input Validation**: Are all inputs validated?
- **Resource Limits**: Are there safeguards against resource exhaustion?
- **Graceful Degradation**: Do modules handle failures gracefully?
- **Data Privacy**: Is sensitive data properly handled?

## Specific Areas of Concern

1. **Duplicate Utilities**: 
   - `column_mapper.py` vs `column_mapping.py`
   - `file_utils.py` vs `output_utils.py`
   - Multiple memory-related utilities

2. **Hardware Integration**: 
   - Is hardware acceleration properly optional?
   - Are fallbacks implemented for non-Apple Silicon?

3. **Cache Coherency**:
   - How is cache invalidation handled?
   - Are there race conditions in multi-threaded scenarios?

4. **Error Recovery**:
   - Are circuit breakers configured appropriately?
   - Is the recovery strategy effective?

## Expected Deliverables

1. **Module Assessment Report** for each module:
   ```
   Module: [module_name]
   Status: [Excellent/Good/Needs Improvement/Critical]
   Issues Found: [list]
   Recommendations: [list]
   Code Quality Score: [0-10]
   ```

2. **Integration Issues Report**:
   - Circular dependencies
   - Interface inconsistencies
   - Missing integration points

3. **Performance Optimization Opportunities**:
   - Modules that could benefit from hardware acceleration
   - Caching opportunities
   - Parallelization candidates

4. **Refactoring Recommendations**:
   - Duplicate code to consolidate
   - Patterns to standardize
   - Technical debt to address

5. **Priority Action Items**:
   - Critical fixes (blocking issues)
   - High-priority improvements
   - Nice-to-have enhancements

## Review Guidelines

1. **Start with interfaces** - They define contracts for the entire system
2. **Check error handling** - Every module should use @protect decorator where appropriate
3. **Verify hardware integration** - Performance-critical code should use hardware acceleration
4. **Assess monitoring** - Key operations should have @monitor_performance
5. **Look for patterns** - Identify repeated code that could be abstracted
6. **Consider maintainability** - Code should be easy to understand and modify

## Success Criteria

The review is successful if:
1. All critical issues are identified
2. Integration points are validated
3. Performance bottlenecks are found
4. A clear improvement roadmap is provided
5. The system's production readiness is assessed

## Additional Context

- The project recently completed Phase 2.2+ (Unified Error Handling)
- Phase 2.6 (Data System Streamline) is 80% complete
- The system runs on Apple M3 Pro with 36GB unified memory
- Production deployment is imminent
- Code quality and reliability are paramount

Please provide a comprehensive review following this structure, with actionable recommendations for each issue found.