#!/usr/bin/env python3
"""
Execute Cache System Rollout

This script automates the rollout of the new unified cache system.
It performs validation, testing, and monitoring to ensure a smooth transition.
"""

import os
import sys
import subprocess
import time
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Tuple, Optional

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


class CacheRolloutExecutor:
    """Manages the cache system rollout process."""
    
    def __init__(self):
        self.project_root = project_root
        self.results = {
            "start_time": datetime.now().isoformat(),
            "validation": {},
            "integration_tests": {},
            "performance": {},
            "issues": []
        }
    
    def run_command(self, cmd: List[str], description: str) -> Tuple[bool, str]:
        """Run a command and return success status and output."""
        print(f"\n{'='*60}")
        print(f"Running: {description}")
        print(f"Command: {' '.join(cmd)}")
        print(f"{'='*60}")
        
        try:
            result = subprocess.run(
                cmd,
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=300  # 5 minute timeout
            )
            
            success = result.returncode == 0
            output = result.stdout + result.stderr
            
            if success:
                print(f"✅ Success: {description}")
            else:
                print(f"❌ Failed: {description}")
                print(f"Error: {result.stderr}")
                self.results["issues"].append({
                    "step": description,
                    "error": result.stderr,
                    "time": datetime.now().isoformat()
                })
            
            return success, output
        except subprocess.TimeoutExpired:
            print(f"⏱️ Timeout: {description}")
            self.results["issues"].append({
                "step": description,
                "error": "Command timed out",
                "time": datetime.now().isoformat()
            })
            return False, "Timeout"
        except Exception as e:
            print(f"💥 Exception: {description} - {e}")
            self.results["issues"].append({
                "step": description,
                "error": str(e),
                "time": datetime.now().isoformat()
            })
            return False, str(e)
    
    def phase1_validation(self) -> bool:
        """Phase 1: Run validation script."""
        print("\n" + "="*80)
        print("PHASE 1: VALIDATION")
        print("="*80)
        
        success, output = self.run_command(
            [sys.executable, "scripts/validate_cache_rollout.py"],
            "Cache system validation"
        )
        
        self.results["validation"] = {
            "success": success,
            "output": output,
            "time": datetime.now().isoformat()
        }
        
        return success
    
    def phase2_integration_tests(self) -> bool:
        """Phase 2: Run integration tests with real pipelines."""
        print("\n" + "="*80)
        print("PHASE 2: INTEGRATION TESTING")
        print("="*80)
        
        test_pipelines = [
            ("composition", 2019),
            ("concentration", 2019),
            ("growth", 2019),
            ("market", 2019),
            ("rca", 2019)
        ]
        
        all_success = True
        
        for pipeline, year in test_pipelines:
            success, output = self.run_command(
                [sys.executable, "-m", "yemen_trade_diagnostic.cli", 
                 "pipeline", pipeline, "--year", str(year)],
                f"Pipeline test: {pipeline} ({year})"
            )
            
            self.results["integration_tests"][pipeline] = {
                "success": success,
                "year": year,
                "time": datetime.now().isoformat()
            }
            
            if not success:
                all_success = False
        
        return all_success
    
    def phase3_performance_check(self) -> bool:
        """Phase 3: Check cache performance metrics."""
        print("\n" + "="*80)
        print("PHASE 3: PERFORMANCE MONITORING")
        print("="*80)
        
        # Create a simple performance check script
        perf_script = """
import sys
sys.path.insert(0, '.')

from yemen_trade_diagnostic.hardware.cache import get_cache
import pandas as pd
import numpy as np
import time

# Test cache operations
cache = get_cache()

# Test 1: Basic operations
print("Testing basic cache operations...")
start = time.time()
for i in range(100):
    cache.set(f"test_key_{i}", f"test_value_{i}")
    value = cache.get(f"test_key_{i}")
    assert value == f"test_value_{i}"
basic_time = time.time() - start
print(f"Basic operations: {basic_time:.3f}s")

# Test 2: DataFrame caching
print("\\nTesting DataFrame caching...")
df = pd.DataFrame(np.random.rand(1000, 10))
start = time.time()
cache.set("test_df", df)
cached_df = cache.get("test_df")
assert cached_df is not None
df_time = time.time() - start
print(f"DataFrame caching: {df_time:.3f}s")

# Test 3: Cache statistics
stats = cache.get_statistics()
print(f"\\nCache Statistics:")
print(f"- Hit rate: {stats.get('hit_rate', 0):.2%}")
print(f"- Memory usage: {stats.get('memory_usage_mb', 0):.1f} MB")
print(f"- Total entries: {stats.get('total_entries', 0)}")

# Performance summary
print(f"\\nPerformance Summary:")
print(f"- Basic ops throughput: {100/basic_time:.0f} ops/sec")
print(f"- DataFrame cache time: {df_time*1000:.1f} ms")
print(f"- Overall status: {'PASS' if basic_time < 1.0 and df_time < 0.1 else 'NEEDS OPTIMIZATION'}")
"""
        
        # Write and run performance script
        perf_script_path = self.project_root / "scripts" / "_temp_perf_check.py"
        perf_script_path.write_text(perf_script)
        
        try:
            success, output = self.run_command(
                [sys.executable, str(perf_script_path)],
                "Performance benchmarking"
            )
            
            self.results["performance"] = {
                "success": success,
                "output": output,
                "time": datetime.now().isoformat()
            }
            
            return success
        finally:
            # Clean up temp script
            if perf_script_path.exists():
                perf_script_path.unlink()
    
    def generate_report(self) -> None:
        """Generate a rollout report."""
        self.results["end_time"] = datetime.now().isoformat()
        
        report_path = self.project_root / "output" / f"cache_rollout_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        report_path.parent.mkdir(exist_ok=True)
        
        with open(report_path, 'w') as f:
            json.dump(self.results, f, indent=2)
        
        print(f"\n📊 Report saved to: {report_path}")
        
        # Print summary
        print("\n" + "="*80)
        print("ROLLOUT SUMMARY")
        print("="*80)
        
        print(f"✅ Validation: {'PASSED' if self.results['validation'].get('success') else 'FAILED'}")
        
        if self.results['integration_tests']:
            passed = sum(1 for r in self.results['integration_tests'].values() if r['success'])
            total = len(self.results['integration_tests'])
            print(f"✅ Integration Tests: {passed}/{total} passed")
        
        print(f"✅ Performance: {'PASSED' if self.results['performance'].get('success') else 'FAILED'}")
        
        if self.results['issues']:
            print(f"\n⚠️  Issues encountered: {len(self.results['issues'])}")
            for issue in self.results['issues'][:5]:  # Show first 5 issues
                print(f"   - {issue['step']}: {issue['error'][:100]}...")
    
    def execute_rollout(self) -> bool:
        """Execute the full rollout process."""
        print("🚀 Starting Cache System Rollout")
        print(f"📅 Time: {datetime.now()}")
        print(f"📁 Project: {self.project_root}")
        
        # Phase 1: Validation
        if not self.phase1_validation():
            print("\n❌ Validation failed! Stopping rollout.")
            print("Please fix validation issues before proceeding.")
            self.generate_report()
            return False
        
        # Phase 2: Integration Tests
        if not self.phase2_integration_tests():
            print("\n⚠️  Some integration tests failed!")
            print("Review the failures before proceeding to production.")
        
        # Phase 3: Performance Check
        if not self.phase3_performance_check():
            print("\n⚠️  Performance checks failed!")
            print("Review performance metrics before proceeding.")
        
        # Generate final report
        self.generate_report()
        
        # Overall success
        overall_success = (
            self.results['validation'].get('success', False) and
            all(r['success'] for r in self.results['integration_tests'].values()) and
            self.results['performance'].get('success', False)
        )
        
        if overall_success:
            print("\n✅ ROLLOUT SUCCESSFUL!")
            print("The new cache system is ready for production use.")
        else:
            print("\n⚠️  ROLLOUT COMPLETED WITH ISSUES")
            print("Please review the report and fix any issues before full deployment.")
        
        return overall_success


def main():
    """Main entry point."""
    executor = CacheRolloutExecutor()
    
    try:
        success = executor.execute_rollout()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n🛑 Rollout interrupted by user")
        executor.generate_report()
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        executor.generate_report()
        sys.exit(1)


if __name__ == "__main__":
    main()