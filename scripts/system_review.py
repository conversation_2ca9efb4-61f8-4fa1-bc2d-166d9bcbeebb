#!/usr/bin/env python3
"""
Comprehensive System Review for Yemen Trade Diagnostic Project
This script reviews all major systems and reports their status.
"""

import os
import sys
import time
import json
import traceback
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Disable resource monitoring to avoid circular dependencies
os.environ['DISABLE_RESOURCE_MONITORING'] = 'true'
os.environ['DISABLE_CACHE_HEALTH_CHECKS'] = 'true'


class SystemReview:
    """Comprehensive system review for the Yemen Trade Diagnostic project."""
    
    def __init__(self):
        self.results = {
            'timestamp': datetime.now().isoformat(),
            'systems': {},
            'overall_health': 'Unknown',
            'issues': [],
            'recommendations': []
        }
        self.passed_checks = 0
        self.total_checks = 0
    
    def check_system(self, name: str, check_func):
        """Run a system check and record results."""
        self.total_checks += 1
        print(f"\n{'='*60}")
        print(f"Checking {name}...")
        print('='*60)
        
        try:
            result = check_func()
            self.results['systems'][name] = result
            if result.get('status') == 'OK':
                self.passed_checks += 1
                print(f"✅ {name}: OK")
            else:
                print(f"⚠️  {name}: {result.get('status', 'Unknown')}")
                self.results['issues'].extend(result.get('issues', []))
        except Exception as e:
            self.results['systems'][name] = {
                'status': 'ERROR',
                'error': str(e),
                'traceback': traceback.format_exc()
            }
            print(f"❌ {name}: ERROR - {str(e)}")
            self.results['issues'].append(f"{name}: {str(e)}")
    
    def review_data_system(self) -> Dict[str, Any]:
        """Review the unified data loading system."""
        result = {'status': 'Unknown', 'details': {}, 'issues': []}
        
        try:
            # Test unified data loader
            from yemen_trade_diagnostic.data.loader import load_data, DataSource
            
            # Check if data loader can be imported
            result['details']['import'] = 'OK'
            
            # Check available data sources
            available_sources = [source.value for source in DataSource]
            result['details']['sources'] = available_sources
            
            # Test basic loading (without actual data)
            try:
                # This should fail gracefully if no data available
                test_data = load_data(
                    source=DataSource.COUNTRY_CODES,
                    cache_enabled=False
                )
                result['details']['test_load'] = 'OK' if test_data is not None else 'No data'
            except FileNotFoundError:
                result['details']['test_load'] = 'No data files'
            except Exception as e:
                result['details']['test_load'] = f'Error: {str(e)}'
            
            # Check pipeline migrations (Phase 2.6)
            pipelines_to_check = [
                'pipelines/sophistication_rca_loader.py',
                'pipelines/batch_processing.py'
            ]
            
            migration_status = []
            for pipeline in pipelines_to_check:
                pipeline_path = project_root / 'src' / 'yemen_trade_diagnostic' / pipeline
                if pipeline_path.exists():
                    with open(pipeline_path, 'r') as f:
                        content = f.read()
                        uses_new_loader = 'from yemen_trade_diagnostic.data.loader import load_data' in content
                        migration_status.append({
                            'pipeline': pipeline,
                            'migrated': uses_new_loader
                        })
            
            result['details']['pipeline_migrations'] = migration_status
            all_migrated = all(p['migrated'] for p in migration_status)
            
            if all_migrated:
                result['status'] = 'OK'
            else:
                result['status'] = 'Partial'
                result['issues'].append('Some pipelines not migrated to new data loader')
            
        except ImportError as e:
            result['status'] = 'ERROR'
            result['issues'].append(f'Cannot import data loader: {str(e)}')
        
        return result
    
    def review_error_handling_system(self) -> Dict[str, Any]:
        """Review the unified error handling system v2.0."""
        result = {'status': 'Unknown', 'details': {}, 'issues': []}
        
        try:
            # Test core error handling imports
            from yemen_trade_diagnostic.errors import (
                protect, error_context, OperationType,
                get_health_status, get_performance_metrics
            )
            result['details']['imports'] = 'OK'
            
            # Test decorator functionality
            @protect("test_function", OperationType.COMPUTATION)
            def test_func():
                return "test_result"
            
            test_result = test_func()
            result['details']['decorator_test'] = 'OK' if test_result == "test_result" else 'Failed'
            
            # Check health status
            health = get_health_status()
            result['details']['health_status'] = health.overall_status
            result['details']['circuit_breakers'] = health.circuit_breaker_status
            
            # Check performance metrics
            metrics = get_performance_metrics()
            result['details']['metrics'] = {
                'total_operations': metrics.total_operations,
                'success_rate': f"{metrics.success_rate:.2%}" if metrics.total_operations > 0 else "N/A"
            }
            
            # Check migration status (all 200+ files should use new system)
            old_patterns = [
                'circuit_breaker.py',
                'error_interface.py'
            ]
            
            archive_dir = project_root / 'archive'
            archived_count = 0
            for pattern in old_patterns:
                if (archive_dir / pattern).exists() or any(archive_dir.rglob(f'*{pattern}')):
                    archived_count += 1
            
            result['details']['legacy_files_archived'] = f"{archived_count}/{len(old_patterns)}"
            
            result['status'] = 'OK'
            
        except Exception as e:
            result['status'] = 'ERROR'
            result['issues'].append(f'Error handling system error: {str(e)}')
        
        return result
    
    def review_cache_system(self) -> Dict[str, Any]:
        """Review the cache system."""
        result = {'status': 'Unknown', 'details': {}, 'issues': []}
        
        try:
            from yemen_trade_diagnostic.interfaces.cache_interface import get_cache, CacheLevel
            
            # Get cache instance
            cache = get_cache()
            result['details']['instance_creation'] = 'OK'
            
            # Test basic operations
            test_key = "_system_review_test"
            test_value = {"test": "data", "timestamp": time.time()}
            
            # Set
            cache.set(test_key, test_value, ttl=60)
            
            # Get
            retrieved = cache.get(test_key)
            if retrieved and retrieved.get('test') == 'data':
                result['details']['basic_operations'] = 'OK'
            else:
                result['details']['basic_operations'] = 'Failed'
                result['issues'].append('Cache get/set failed')
            
            # Delete
            cache.delete(test_key)
            
            # Check stats
            stats = cache.get_stats()
            result['details']['stats'] = {
                'get_operations': stats.get('get_operations', 0),
                'set_operations': stats.get('set_operations', 0)
            }
            
            # Check cache levels
            cache.set("mem_test", "value", level=CacheLevel.MEMORY)
            mem_value = cache.get("mem_test", level=CacheLevel.MEMORY)
            cache.delete("mem_test")
            
            result['details']['memory_cache'] = 'OK' if mem_value == "value" else 'Failed'
            
            result['status'] = 'OK'
            
        except Exception as e:
            result['status'] = 'ERROR'
            result['issues'].append(f'Cache system error: {str(e)}')
        
        return result
    
    def review_hardware_system(self) -> Dict[str, Any]:
        """Review the hardware acceleration system."""
        result = {'status': 'Unknown', 'details': {}, 'issues': []}
        
        try:
            from yemen_trade_diagnostic.hardware.core.detector import get_hardware_detector
            
            detector = get_hardware_detector()
            result['details']['detector'] = 'OK'
            
            # Get hardware info
            result['details']['system'] = {
                'is_apple_silicon': detector.is_apple_silicon,
                'is_m3_pro': detector.is_m3_pro,
                'total_cores': detector.total_cores,
                'total_memory_gb': round(detector.total_memory / (1024**3), 2) if detector.total_memory else 0,
                'has_metal': detector.has_metal,
                'should_use_acceleration': detector.should_use_hardware_acceleration()
            }
            
            # Test hardware manager
            try:
                from yemen_trade_diagnostic.hardware import get_hardware_manager
                hw_manager = get_hardware_manager()
                result['details']['hardware_manager'] = 'OK'
            except Exception as e:
                result['details']['hardware_manager'] = f'Error: {str(e)}'
            
            result['status'] = 'OK'
            
        except Exception as e:
            result['status'] = 'ERROR'
            result['issues'].append(f'Hardware system error: {str(e)}')
        
        return result
    
    def review_pipeline_system(self) -> Dict[str, Any]:
        """Review the pipeline system."""
        result = {'status': 'Unknown', 'details': {}, 'issues': []}
        
        try:
            # Check pipeline imports
            pipelines = [
                'composition', 'concentration', 'growth', 'macroeconomic',
                'market', 'rca', 'sophistication'
            ]
            
            import_status = {}
            for pipeline in pipelines:
                try:
                    module = __import__(
                        f'yemen_trade_diagnostic.pipelines.{pipeline}',
                        fromlist=['run_pipeline']
                    )
                    import_status[pipeline] = 'OK'
                except ImportError as e:
                    import_status[pipeline] = f'Import error: {str(e)}'
            
            result['details']['pipeline_imports'] = import_status
            
            # Check orchestrator
            try:
                from yemen_trade_diagnostic.pipelines.orchestrator import PipelineOrchestrator
                result['details']['orchestrator'] = 'OK'
            except ImportError:
                result['details']['orchestrator'] = 'Import error'
                result['issues'].append('Cannot import PipelineOrchestrator')
            
            # All pipelines should be importable
            failed_imports = [p for p, status in import_status.items() if status != 'OK']
            if failed_imports:
                result['status'] = 'Partial'
                result['issues'].append(f'Failed to import pipelines: {", ".join(failed_imports)}')
            else:
                result['status'] = 'OK'
            
        except Exception as e:
            result['status'] = 'ERROR'
            result['issues'].append(f'Pipeline system error: {str(e)}')
        
        return result
    
    def review_monitoring_system(self) -> Dict[str, Any]:
        """Review the monitoring system."""
        result = {'status': 'Unknown', 'details': {}, 'issues': []}
        
        try:
            from yemen_trade_diagnostic.monitoring.decorators import monitor_performance
            from yemen_trade_diagnostic.monitoring.metrics_collector import MetricsCollector
            
            result['details']['imports'] = 'OK'
            
            # Test decorator
            @monitor_performance(track_args=True)
            def test_monitored_func(x):
                return x * 2
            
            test_result = test_monitored_func(5)
            result['details']['decorator_test'] = 'OK' if test_result == 10 else 'Failed'
            
            # Check metrics collector
            collector = MetricsCollector()
            result['details']['metrics_collector'] = 'OK'
            
            result['status'] = 'OK'
            
        except Exception as e:
            result['status'] = 'ERROR'
            result['issues'].append(f'Monitoring system error: {str(e)}')
        
        return result
    
    def review_visualization_system(self) -> Dict[str, Any]:
        """Review the visualization system v2."""
        result = {'status': 'Unknown', 'details': {}, 'issues': []}
        
        try:
            # Check if old visualization is archived
            old_viz_archived = (project_root / 'archive' / 'replaced_modules' / 'visualization_v1').exists()
            result['details']['v1_archived'] = old_viz_archived
            
            # Test v2 imports
            try:
                from yemen_trade_diagnostic.visualization.core.manager import VisualizationManager
                result['details']['v2_manager'] = 'OK'
                
                # Check if manager can be instantiated
                manager = VisualizationManager()
                result['details']['manager_instance'] = 'OK'
            except ImportError as e:
                result['details']['v2_manager'] = f'Import error: {str(e)}'
                result['issues'].append('Cannot import VisualizationManager')
            
            result['status'] = 'OK' if result['details'].get('v2_manager') == 'OK' else 'Partial'
            
        except Exception as e:
            result['status'] = 'ERROR'
            result['issues'].append(f'Visualization system error: {str(e)}')
        
        return result
    
    def review_test_system(self) -> Dict[str, Any]:
        """Review the test system."""
        result = {'status': 'Unknown', 'details': {}, 'issues': []}
        
        try:
            # Check test structure
            test_dirs = ['unit', 'integration', 'performance', 'e2e']
            test_structure = {}
            
            tests_root = project_root / 'tests'
            for test_dir in test_dirs:
                test_path = tests_root / test_dir
                if test_path.exists():
                    # Count test files
                    test_files = list(test_path.rglob('test_*.py'))
                    test_structure[test_dir] = len(test_files)
                else:
                    test_structure[test_dir] = 0
            
            result['details']['test_structure'] = test_structure
            result['details']['total_test_files'] = sum(test_structure.values())
            
            # Check test runner
            test_runner_path = project_root / 'scripts' / 'testing' / 'run_all_tests.py'
            result['details']['test_runner'] = 'OK' if test_runner_path.exists() else 'Missing'
            
            if result['details']['total_test_files'] > 100:
                result['status'] = 'OK'
            else:
                result['status'] = 'Partial'
                result['issues'].append('Low test coverage (less than 100 test files)')
            
        except Exception as e:
            result['status'] = 'ERROR'
            result['issues'].append(f'Test system error: {str(e)}')
        
        return result
    
    def generate_recommendations(self):
        """Generate recommendations based on the review."""
        # Check overall health
        health_percentage = (self.passed_checks / self.total_checks * 100) if self.total_checks > 0 else 0
        
        if health_percentage >= 90:
            self.results['overall_health'] = 'Excellent'
        elif health_percentage >= 70:
            self.results['overall_health'] = 'Good'
        elif health_percentage >= 50:
            self.results['overall_health'] = 'Fair'
        else:
            self.results['overall_health'] = 'Needs Attention'
        
        # Generate recommendations
        if self.results['issues']:
            self.results['recommendations'].append(
                f"Address {len(self.results['issues'])} identified issues"
            )
        
        # System-specific recommendations
        for system, details in self.results['systems'].items():
            if details.get('status') == 'Partial':
                self.results['recommendations'].append(
                    f"Complete implementation of {system}"
                )
            elif details.get('status') == 'ERROR':
                self.results['recommendations'].append(
                    f"Fix critical errors in {system}"
                )
        
        # Phase-specific recommendations
        if self.results['systems'].get('Data System', {}).get('status') != 'OK':
            self.results['recommendations'].append(
                "Complete Phase 2.6 - Data System Streamline (currently at 80%)"
            )
        
        self.results['recommendations'].append(
            "Begin Phase 2.5 - Documentation Enhancement"
        )
    
    def run_review(self):
        """Run the complete system review."""
        print("="*80)
        print("YEMEN TRADE DIAGNOSTIC - COMPREHENSIVE SYSTEM REVIEW")
        print("="*80)
        print(f"Started at: {self.results['timestamp']}")
        
        # Review each system
        self.check_system("Data System", self.review_data_system)
        self.check_system("Error Handling System", self.review_error_handling_system)
        self.check_system("Cache System", self.review_cache_system)
        self.check_system("Hardware System", self.review_hardware_system)
        self.check_system("Pipeline System", self.review_pipeline_system)
        self.check_system("Monitoring System", self.review_monitoring_system)
        self.check_system("Visualization System", self.review_visualization_system)
        self.check_system("Test System", self.review_test_system)
        
        # Generate recommendations
        self.generate_recommendations()
        
        # Print summary
        print("\n" + "="*80)
        print("REVIEW SUMMARY")
        print("="*80)
        print(f"Total Systems Checked: {self.total_checks}")
        print(f"Passed: {self.passed_checks}")
        print(f"Failed/Partial: {self.total_checks - self.passed_checks}")
        print(f"Overall Health: {self.results['overall_health']}")
        print(f"Health Score: {self.passed_checks / self.total_checks * 100:.1f}%")
        
        if self.results['issues']:
            print(f"\nIssues Found ({len(self.results['issues'])}):")
            for i, issue in enumerate(self.results['issues'], 1):
                print(f"  {i}. {issue}")
        
        if self.results['recommendations']:
            print(f"\nRecommendations ({len(self.results['recommendations'])}):")
            for i, rec in enumerate(self.results['recommendations'], 1):
                print(f"  {i}. {rec}")
        
        # Save results
        report_path = project_root / 'SYSTEM_REVIEW_REPORT.json'
        with open(report_path, 'w') as f:
            json.dump(self.results, f, indent=2)
        print(f"\nFull report saved to: {report_path}")
        
        # Also create a markdown report
        self.create_markdown_report()
    
    def create_markdown_report(self):
        """Create a markdown version of the report."""
        report_path = project_root / 'SYSTEM_REVIEW_REPORT.md'
        
        with open(report_path, 'w') as f:
            f.write("# Yemen Trade Diagnostic - System Review Report\n\n")
            f.write(f"**Generated:** {self.results['timestamp']}\n\n")
            f.write(f"**Overall Health:** {self.results['overall_health']} ")
            f.write(f"({self.passed_checks}/{self.total_checks} systems OK)\n\n")
            
            f.write("## System Status\n\n")
            f.write("| System | Status | Details |\n")
            f.write("|--------|--------|--------|\n")
            
            for system, details in self.results['systems'].items():
                status = details.get('status', 'Unknown')
                status_icon = "✅" if status == 'OK' else "⚠️" if status == 'Partial' else "❌"
                
                # Get key details
                key_details = []
                if 'details' in details:
                    for k, v in details['details'].items():
                        if isinstance(v, dict):
                            continue
                        key_details.append(f"{k}: {v}")
                
                details_str = ", ".join(key_details[:3])  # First 3 details
                if len(key_details) > 3:
                    details_str += "..."
                
                f.write(f"| {system} | {status_icon} {status} | {details_str} |\n")
            
            if self.results['issues']:
                f.write(f"\n## Issues ({len(self.results['issues'])})\n\n")
                for issue in self.results['issues']:
                    f.write(f"- {issue}\n")
            
            if self.results['recommendations']:
                f.write(f"\n## Recommendations\n\n")
                for rec in self.results['recommendations']:
                    f.write(f"1. {rec}\n")
            
            f.write("\n## Next Steps\n\n")
            f.write("Based on this review, the priority actions are:\n\n")
            f.write("1. **Complete Phase 2.6** - Finish the remaining 20% of Data System Streamline\n")
            f.write("2. **Begin Phase 2.5** - Start Documentation Enhancement\n")
            f.write("3. **Address any critical issues** identified in this report\n")
            f.write("4. **Run comprehensive tests** to ensure system stability\n")
        
        print(f"Markdown report saved to: {report_path}")


if __name__ == "__main__":
    reviewer = SystemReview()
    reviewer.run_review()