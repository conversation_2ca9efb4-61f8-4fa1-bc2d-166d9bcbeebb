#!/usr/bin/env python3
"""
Migrate cache imports from old utils modules to new hardware.cache module.

This script updates all imports in the codebase to use the new unified cache system.
"""

import ast
import os
import re
from pathlib import Path
from typing import Dict, List, Set, Tuple

# Import mappings from old to new
IMPORT_MAPPINGS = {
    # Direct utils imports
    "yemen_trade_diagnostic.utils.cache_interface": "yemen_trade_diagnostic.hardware.cache",
    "yemen_trade_diagnostic.utils.cache_compression": "yemen_trade_diagnostic.hardware.cache.strategies.compression",
    "yemen_trade_diagnostic.utils.cache_key_optimizer": "yemen_trade_diagnostic.hardware.cache.keys",
    "yemen_trade_diagnostic.utils.cache_prewarm": "yemen_trade_diagnostic.hardware.cache",
    "yemen_trade_diagnostic.utils.intelligent_cache_warming": "yemen_trade_diagnostic.hardware.cache.strategies.warming",
    "yemen_trade_diagnostic.utils.memory_efficient_cache": "yemen_trade_diagnostic.hardware.cache",
    "yemen_trade_diagnostic.utils.progressive_cache": "yemen_trade_diagnostic.hardware.cache.storage.progressive",
    "yemen_trade_diagnostic.utils.smart_cache_invalidation": "yemen_trade_diagnostic.hardware.cache.strategies.invalidation",
    
    # Interface imports
    "yemen_trade_diagnostic.interfaces.cache_interface": "yemen_trade_diagnostic.hardware.cache",
}

# Symbol mappings
SYMBOL_MAPPINGS = {
    # From utils modules
    "get_cache": "get_cache",
    "cache": "Cache",
    "CacheKeyOptimizer": "CacheKeyOptimizer",
    "get_cache_key_optimizer": "get_cache_key_optimizer",
    "optimize_cache_key": "optimize_cache_key",
    "CacheKeyType": "CacheKeyType",
    "SmartCacheInvalidator": "SmartInvalidationStrategy",
    "get_smart_cache_invalidator": "SmartInvalidationStrategy",
    "IntelligentCacheWarmer": "IntelligentWarmingStrategy",
    "ProgressiveCache": "ProgressiveStorage",
    "MemoryEfficientCache": "Cache",
    "CacheCompressor": "AdaptiveCompressionStrategy",
    
    # From interfaces.cache_interface
    "get_cache_manager": "get_cache",
    "CacheManager": "Cache",
    "CacheInterface": "Cache",
    "CacheLevel": "StorageTier",
}

# Patterns for regex-based replacements
REGEX_PATTERNS = [
    # Update instantiations
    (r'get_cache_manager\(\)', 'get_cache()'),
    (r'CacheManager\(\)', 'Cache()'),
    (r'MemoryEfficientCache\((.*?)\)', r'Cache(\1)'),
    (r'ProgressiveCache\((.*?)\)', r'Cache(storage_tiers=["progressive"])'),
    (r'SmartCacheInvalidator\(\)', 'SmartInvalidationStrategy()'),
    (r'IntelligentCacheWarmer\(\)', 'IntelligentWarmingStrategy()'),
    
    # Update method calls that might have changed
    (r'\.invalidate_by_pattern\(', '.invalidate_pattern('),
    (r'\.warm_cache_for_pattern\(', '.warm_cache('),
]


def update_imports_in_file(file_path: Path) -> bool:
    """Update imports in a single file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Update import statements
        for old_module, new_module in IMPORT_MAPPINGS.items():
            # Handle "from X import Y" style
            pattern = rf'from {re.escape(old_module)} import ([^\n]+)'
            matches = re.findall(pattern, content)
            
            for match in matches:
                # Parse imported symbols
                symbols = [s.strip() for s in match.split(',')]
                new_symbols = []
                
                for symbol in symbols:
                    # Handle "as" aliases
                    if ' as ' in symbol:
                        orig_symbol, alias = symbol.split(' as ', 1)
                        orig_symbol = orig_symbol.strip()
                        alias = alias.strip()
                        new_symbol = SYMBOL_MAPPINGS.get(orig_symbol, orig_symbol)
                        new_symbols.append(f"{new_symbol} as {alias}")
                    else:
                        new_symbol = SYMBOL_MAPPINGS.get(symbol, symbol)
                        new_symbols.append(new_symbol)
                
                # Replace the import
                old_import = f"from {old_module} import {match}"
                new_import = f"from {new_module} import {', '.join(new_symbols)}"
                content = content.replace(old_import, new_import)
            
            # Handle "import X" style
            pattern = rf'import {re.escape(old_module)}'
            if pattern in content:
                content = content.replace(
                    f"import {old_module}",
                    f"import {new_module}"
                )
        
        # Apply regex patterns for code updates
        for pattern, replacement in REGEX_PATTERNS:
            content = re.sub(pattern, replacement, content)
        
        # Only write if content changed
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✓ Updated: {file_path}")
            return True
        
        return False
        
    except Exception as e:
        print(f"✗ Error updating {file_path}: {e}")
        return False


def find_python_files(directory: Path, exclude_dirs: Set[str] = None) -> List[Path]:
    """Find all Python files in directory."""
    if exclude_dirs is None:
        exclude_dirs = {'.git', '__pycache__', 'venv', 'env', '.tox', 'archive'}
    
    python_files = []
    
    for root, dirs, files in os.walk(directory):
        # Remove excluded directories from dirs to prevent walking into them
        dirs[:] = [d for d in dirs if d not in exclude_dirs]
        
        for file in files:
            if file.endswith('.py'):
                python_files.append(Path(root) / file)
    
    return python_files


def create_compatibility_wrappers():
    """Create compatibility wrappers for backward compatibility."""
    
    # Create utils/cache_interface.py wrapper
    wrapper_content = '''"""
Backward compatibility wrapper for cache_interface.

This module provides compatibility imports for code that hasn't been migrated yet.
DEPRECATED: Use yemen_trade_diagnostic.hardware.cache instead.
"""

import warnings

warnings.warn(
    "yemen_trade_diagnostic.utils.cache_interface is deprecated. "
    "Use yemen_trade_diagnostic.hardware.cache instead.",
    DeprecationWarning,
    stacklevel=2
)

from yemen_trade_diagnostic.hardware.cache import (
    Cache as CacheInterface,
    Cache as CacheManager,
    get_cache as get_cache_manager,
    StorageTier as CacheLevel,
)

# Provide old function names for compatibility
cache_interface = CacheInterface
cache_manager = CacheManager

__all__ = [
    'CacheInterface',
    'CacheManager', 
    'get_cache_manager',
    'CacheLevel',
    'cache_interface',
    'cache_manager',
]
'''
    
    wrapper_path = Path("src/yemen_trade_diagnostic/utils/cache_interface.py")
    wrapper_path.parent.mkdir(parents=True, exist_ok=True)
    
    with open(wrapper_path, 'w') as f:
        f.write(wrapper_content)
    
    print(f"✓ Created compatibility wrapper: {wrapper_path}")


def main():
    """Main migration function."""
    print("Starting cache import migration...")
    
    # Find project root
    project_root = Path(__file__).parent.parent
    
    # Directories to update
    directories_to_update = [
        project_root / "src",
        project_root / "tests",
        project_root / "scripts",
        project_root / "examples",
    ]
    
    total_updated = 0
    
    for directory in directories_to_update:
        if not directory.exists():
            continue
            
        print(f"\nScanning {directory}...")
        python_files = find_python_files(directory)
        
        for py_file in python_files:
            if update_imports_in_file(py_file):
                total_updated += 1
    
    # Create compatibility wrappers
    print("\nCreating compatibility wrappers...")
    create_compatibility_wrappers()
    
    print(f"\n✅ Migration complete! Updated {total_updated} files.")
    print("\nNext steps:")
    print("1. Run tests to ensure everything works correctly")
    print("2. Review the changes with git diff")
    print("3. Remove compatibility wrappers after full migration")


if __name__ == "__main__":
    main()