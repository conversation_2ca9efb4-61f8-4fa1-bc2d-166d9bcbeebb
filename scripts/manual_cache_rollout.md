# Manual Cache System Rollout - Simplified Approach

## Current Issue
There are circular imports between:
- `hardware.cache` → `errors` (for @protect decorator)
- `errors` → `hardware.cache` (for get_cache function)

## Solution Options

### Option 1: Remove @protect from Cache Modules (Quickest)
1. Remove `@protect` decorators from cache-related files
2. This breaks the circular dependency immediately
3. Cache will work but without error protection

### Option 2: Use Lazy Imports Everywhere (Current Approach)
1. Already fixed in:
   - `recovery_manager.py` ✅
   - `resource_manager.py` ✅
2. Still need to fix:
   - `error_analytics.py`
   - `fallback_strategies.py`

### Option 3: Create Cache Interface Module (Best Long-term)
1. Create `interfaces/cache_interface.py` that doesn't import errors
2. Have both `errors` and `hardware.cache` use this interface
3. Most architecturally sound but requires more refactoring

## Recommended Manual Rollout Steps

### Step 1: Fix Remaining Circular Imports
```bash
# Fix error_analytics.py
# Fix fallback_strategies.py
```

### Step 2: Verify Cache System Works
```python
# Test script
from yemen_trade_diagnostic.hardware.cache import get_cache
cache = get_cache()
print("Cache initialized:", cache is not None)

# Test basic operations
cache.set("test_key", "test_value")
value = cache.get("test_key")
print("Cache working:", value == "test_value")
```

### Step 3: Test with One Pipeline
```bash
# Test with composition pipeline first
python -m yemen_trade_diagnostic.cli pipeline composition --year 2019
```

### Step 4: Monitor for Issues
- Check memory usage
- Monitor for errors
- Verify cache hit rates

### Step 5: Gradual Rollout
1. Test with one pipeline
2. If successful, test with all pipelines
3. Monitor for 24 hours
4. Document any issues

## Rollback Plan
If issues occur:
1. Restore old cache files from archive
2. Revert import changes
3. Use git to restore previous state

## Current Status
- ✅ Cache files consolidated
- ✅ Migration scripts created
- ✅ Compatibility wrappers in place
- ⚠️ Circular import issues need resolution
- ⏳ Testing pending

## Next Immediate Actions
1. Fix circular imports in error_analytics.py and fallback_strategies.py
2. Run basic cache test
3. Test with one pipeline
4. Proceed with full rollout if successful