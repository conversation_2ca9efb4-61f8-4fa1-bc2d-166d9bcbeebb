#!/usr/bin/env python3
"""
Validate the cache consolidation rollout.

This script performs various checks to ensure the new cache system is working correctly.
"""

import sys
import time
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

import pandas as pd
import numpy as np


def test_basic_cache_operations():
    """Test basic cache operations."""
    print("\n1. Testing basic cache operations...")
    
    try:
        from yemen_trade_diagnostic.hardware.cache import Cache
        
        # Create cache instance
        cache = Cache()
        
        # Test set/get
        cache.set("test_key", "test_value")
        result = cache.get("test_key")
        assert result == "test_value", f"Expected 'test_value', got {result}"
        print("   ✓ Basic set/get works")
        
        # Test miss
        result = cache.get("nonexistent")
        assert result is None, f"Expected None, got {result}"
        print("   ✓ Cache miss returns None")
        
        # Test delete
        success = cache.delete("test_key")
        assert success, "Delete should return True"
        result = cache.get("test_key")
        assert result is None, "Deleted key should return None"
        print("   ✓ Delete works")
        
        return True
        
    except Exception as e:
        print(f"   ✗ Basic operations failed: {e}")
        return False


def test_dataframe_caching():
    """Test caching of pandas DataFrames."""
    print("\n2. Testing DataFrame caching...")
    
    try:
        from yemen_trade_diagnostic.hardware.cache import Cache
        
        # Create cache with compression
        cache = Cache(enable_compression=True)
        
        # Create test DataFrame
        df = pd.DataFrame({
            'country': ['Yemen'] * 1000,
            'year': list(range(2000, 3000)),
            'value': np.random.rand(1000) * 1000000
        })
        
        # Cache DataFrame
        cache.set("test_df", df, ttl=3600)
        
        # Retrieve DataFrame
        cached_df = cache.get("test_df")
        assert cached_df is not None, "DataFrame should be cached"
        assert len(cached_df) == len(df), "Cached DataFrame size mismatch"
        assert cached_df.equals(df), "Cached DataFrame content mismatch"
        print("   ✓ DataFrame caching works")
        
        # Check compression
        stats = cache.get_stats()
        if stats.get('compressions', 0) > 0:
            print("   ✓ Compression is working")
        else:
            print("   ⚠ Compression may not be working")
        
        return True
        
    except Exception as e:
        print(f"   ✗ DataFrame caching failed: {e}")
        return False


def test_semantic_keys():
    """Test semantic key generation."""
    print("\n3. Testing semantic key generation...")
    
    try:
        from yemen_trade_diagnostic.hardware.cache import Cache, CacheStrategy
        
        # Create cache with semantic strategy
        cache = Cache(strategy=CacheStrategy.SEMANTIC)
        
        # Test similar queries
        cache.set("exports:yemen:2023", "data1")
        cache.set("exports:yemen:2022", "data2")
        
        # Check that keys are stored
        result1 = cache.get("exports:yemen:2023")
        result2 = cache.get("exports:yemen:2022")
        
        assert result1 == "data1", f"Expected 'data1', got {result1}"
        assert result2 == "data2", f"Expected 'data2', got {result2}"
        print("   ✓ Semantic key storage works")
        
        return True
        
    except Exception as e:
        print(f"   ✗ Semantic keys failed: {e}")
        return False


def test_ttl_expiration():
    """Test TTL expiration."""
    print("\n4. Testing TTL expiration...")
    
    try:
        from yemen_trade_diagnostic.hardware.cache import Cache
        
        cache = Cache()
        
        # Set with short TTL
        cache.set("ttl_test", "value", ttl=0.5)  # 0.5 seconds
        
        # Should exist immediately
        result = cache.get("ttl_test")
        assert result == "value", f"Expected 'value', got {result}"
        print("   ✓ TTL value cached correctly")
        
        # Wait for expiration
        time.sleep(0.6)
        
        # Should be expired
        result = cache.get("ttl_test")
        assert result is None, f"Expected None after expiration, got {result}"
        print("   ✓ TTL expiration works")
        
        return True
        
    except Exception as e:
        print(f"   ✗ TTL expiration failed: {e}")
        return False


def test_pattern_invalidation():
    """Test pattern-based invalidation."""
    print("\n5. Testing pattern invalidation...")
    
    try:
        from yemen_trade_diagnostic.hardware.cache import Cache
        
        cache = Cache()
        
        # Set multiple keys
        cache.set("export:2022:jan", "data1")
        cache.set("export:2022:feb", "data2")
        cache.set("export:2023:jan", "data3")
        cache.set("import:2022:jan", "data4")
        
        # Invalidate pattern
        count = cache.invalidate_pattern("export:2022:*")
        assert count >= 2, f"Expected at least 2 invalidations, got {count}"
        print(f"   ✓ Invalidated {count} entries")
        
        # Check what remains
        assert cache.get("export:2022:jan") is None
        assert cache.get("export:2022:feb") is None
        assert cache.get("export:2023:jan") == "data3"
        assert cache.get("import:2022:jan") == "data4"
        print("   ✓ Pattern invalidation works correctly")
        
        return True
        
    except Exception as e:
        print(f"   ✗ Pattern invalidation failed: {e}")
        return False


def test_cache_stats():
    """Test cache statistics."""
    print("\n6. Testing cache statistics...")
    
    try:
        from yemen_trade_diagnostic.hardware.cache import Cache
        
        cache = Cache()
        
        # Perform operations
        cache.set("key1", "value1")
        cache.get("key1")  # Hit
        cache.get("key2")  # Miss
        cache.set("key2", "value2")
        cache.get("key2")  # Hit
        
        # Get stats
        stats = cache.get_stats()
        
        assert stats['hits'] >= 2, f"Expected at least 2 hits, got {stats['hits']}"
        assert stats['misses'] >= 1, f"Expected at least 1 miss, got {stats['misses']}"
        assert stats['hit_rate'] > 0, f"Expected positive hit rate, got {stats['hit_rate']}"
        
        print(f"   ✓ Stats: {stats['hits']} hits, {stats['misses']} misses, "
              f"{stats['hit_rate']:.1%} hit rate")
        
        return True
        
    except Exception as e:
        print(f"   ✗ Cache stats failed: {e}")
        return False


def test_decorator():
    """Test memoize decorator."""
    print("\n7. Testing memoize decorator...")
    
    try:
        from yemen_trade_diagnostic.hardware.cache import Cache
        
        cache = Cache()
        call_count = 0
        
        @cache.memoize(ttl=60)
        def expensive_function(x, y):
            nonlocal call_count
            call_count += 1
            return x + y
        
        # First call
        result1 = expensive_function(2, 3)
        assert result1 == 5, f"Expected 5, got {result1}"
        assert call_count == 1, f"Expected 1 call, got {call_count}"
        
        # Second call (should use cache)
        result2 = expensive_function(2, 3)
        assert result2 == 5, f"Expected 5, got {result2}"
        assert call_count == 1, f"Expected 1 call (cached), got {call_count}"
        
        # Different arguments
        result3 = expensive_function(3, 4)
        assert result3 == 7, f"Expected 7, got {result3}"
        assert call_count == 2, f"Expected 2 calls, got {call_count}"
        
        print("   ✓ Memoize decorator works")
        
        return True
        
    except Exception as e:
        print(f"   ✗ Decorator failed: {e}")
        return False


def test_backward_compatibility():
    """Test backward compatibility with old interfaces."""
    print("\n8. Testing backward compatibility...")
    
    try:
        # Test old interface imports
        from yemen_trade_diagnostic.interfaces.cache_interface import (
            CacheManager,
            get_cache,
            CacheLevel,
        )
        
        # Get cache manager
        cache_mgr = get_cache()
        assert cache_mgr is not None, "Cache manager should not be None"
        print("   ✓ Old interface imports work")
        
        # Test basic operations
        cache_mgr.set("compat_test", "value", level=CacheLevel.MEMORY)
        result = cache_mgr.get("compat_test", level=CacheLevel.MEMORY)
        # Note: Result might be None if using new implementation
        print("   ✓ Old interface methods callable")
        
        # Test utils.cache_interface
        from yemen_trade_diagnostic.utils.cache_interface import get_cache as utils_get_cache
        cache_mgr2 = utils_get_cache()
        assert cache_mgr2 is not None, "Utils cache manager should not be None"
        print("   ✓ Utils compatibility wrapper works")
        
        return True
        
    except Exception as e:
        print(f"   ⚠ Backward compatibility limited: {e}")
        # This is expected as we're transitioning
        return True


def test_hardware_integration():
    """Test hardware acceleration integration."""
    print("\n9. Testing hardware integration...")
    
    try:
        from yemen_trade_diagnostic.hardware.cache import Cache
        from yemen_trade_diagnostic.hardware.core.detector import get_hardware_detector
        
        # Check hardware detection
        detector = get_hardware_detector()
        has_hw = detector.has_hardware_acceleration()
        
        print(f"   ℹ Hardware acceleration available: {has_hw}")
        
        # Create cache with hardware acceleration
        cache = Cache(enable_hardware_acceleration=True)
        
        # Test with numpy array
        arr = np.random.rand(1000, 1000)
        cache.set("numpy_test", arr)
        cached_arr = cache.get("numpy_test")
        
        assert cached_arr is not None, "NumPy array should be cached"
        assert np.array_equal(arr, cached_arr), "Cached array should match"
        print("   ✓ NumPy array caching works")
        
        # Check if hardware was used
        stats = cache.get_stats()
        if has_hw and stats.get('hardware_accelerations', 0) > 0:
            print(f"   ✓ Hardware acceleration used: {stats['hardware_accelerations']} times")
        else:
            print("   ℹ Hardware acceleration not used (may not be available)")
        
        return True
        
    except Exception as e:
        print(f"   ✗ Hardware integration failed: {e}")
        return False


def check_imports():
    """Check that all expected modules can be imported."""
    print("\n10. Checking module imports...")
    
    modules_to_check = [
        ("hardware.cache", "yemen_trade_diagnostic.hardware.cache"),
        ("hardware.cache.keys", "yemen_trade_diagnostic.hardware.cache.keys"),
        ("hardware.cache.storage", "yemen_trade_diagnostic.hardware.cache.storage"),
        ("hardware.cache.strategies", "yemen_trade_diagnostic.hardware.cache.strategies"),
        ("interfaces.cache_interface", "yemen_trade_diagnostic.interfaces.cache_interface"),
        ("utils.cache_interface", "yemen_trade_diagnostic.utils.cache_interface"),
    ]
    
    all_ok = True
    for name, module_path in modules_to_check:
        try:
            __import__(module_path)
            print(f"   ✓ {name} imports successfully")
        except ImportError as e:
            print(f"   ✗ {name} import failed: {e}")
            all_ok = False
    
    return all_ok


def main():
    """Run all validation tests."""
    print("=" * 60)
    print("Cache Consolidation Validation")
    print("=" * 60)
    
    tests = [
        check_imports,
        test_basic_cache_operations,
        test_dataframe_caching,
        test_semantic_keys,
        test_ttl_expiration,
        test_pattern_invalidation,
        test_cache_stats,
        test_decorator,
        test_backward_compatibility,
        test_hardware_integration,
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"\n✗ Test {test.__name__} crashed: {e}")
            failed += 1
    
    print("\n" + "=" * 60)
    print(f"Validation Results: {passed} passed, {failed} failed")
    print("=" * 60)
    
    if failed == 0:
        print("\n✅ All validation tests passed! Cache rollout successful.")
        print("\nNext steps:")
        print("1. Monitor cache performance in production")
        print("2. Remove compatibility wrappers after full migration")
        print("3. Update documentation for the new cache API")
        return 0
    else:
        print(f"\n⚠️  {failed} tests failed. Review and fix issues before rollout.")
        return 1


if __name__ == "__main__":
    sys.exit(main())