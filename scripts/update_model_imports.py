#!/usr/bin/env python3
"""
Script to update all model imports from unified_loader_api to new data loader.
"""

import os
import re
from pathlib import Path

def update_imports_in_file(filepath):
    """Update imports in a single file."""
    with open(filepath, 'r') as f:
        content = f.read()
    
    original_content = content
    
    # Replace unified_loader_api imports
    content = re.sub(
        r'from yemen_trade_diagnostic\.data\.unified_loader_api import load_data(?:, load_data_range)?',
        'from yemen_trade_diagnostic.data import DataSource, load_data',
        content
    )
    
    # Replace load_data calls with proper DataSource enum
    # This is more complex as we need to identify the source parameter
    
    # Common patterns to replace
    replacements = [
        (r'load_data\("yemen_exports"', 'load_data(DataSource.YEMEN_EXPORTS'),
        (r'load_data\("yemen_imports"', 'load_data(DataSource.YEMEN_IMPORTS'),
        (r'load_data\("baci"', 'load_data(DataSource.BACI'),
        (r'load_data\("gdp_per_capita"', 'load_data(DataSource.GDP_PER_CAPITA'),
        (r'load_data\("worldbank"', 'load_data(DataSource.WORLD_BANK'),
        (r'load_data\("price_index"', 'load_data(DataSource.PRICE_INDEX'),
        (r'load_data\("country_codes"', 'load_data(DataSource.COUNTRY_CODES'),
        (r'load_data\("product_codes"', 'load_data(DataSource.PRODUCT_CODES'),
        (r"load_data\('yemen_exports'", "load_data(DataSource.YEMEN_EXPORTS"),
        (r"load_data\('yemen_imports'", "load_data(DataSource.YEMEN_IMPORTS"),
        (r"load_data\('baci'", "load_data(DataSource.BACI"),
        (r"load_data\('gdp_per_capita'", "load_data(DataSource.GDP_PER_CAPITA"),
        (r"load_data\('worldbank'", "load_data(DataSource.WORLD_BANK"),
        (r"load_data\('price_index'", "load_data(DataSource.PRICE_INDEX"),
        (r"load_data\('country_codes'", "load_data(DataSource.COUNTRY_CODES"),
        (r"load_data\('product_codes'", "load_data(DataSource.PRODUCT_CODES"),
    ]
    
    for old, new in replacements:
        content = re.sub(old, new, content)
    
    # Replace year parameter with years
    content = re.sub(r', year=(\w+)', r', years=\1', content)
    
    # Remove columns parameter (new loader doesn't use it)
    content = re.sub(r', columns=\[[^\]]*\]', '', content)
    
    if content != original_content:
        with open(filepath, 'w') as f:
            f.write(content)
        return True
    return False

def main():
    """Update all model files."""
    project_root = Path(__file__).parent.parent
    models_dir = project_root / "src" / "yemen_trade_diagnostic" / "models"
    
    updated_files = []
    
    for root, dirs, files in os.walk(models_dir):
        for file in files:
            if file.endswith('.py'):
                filepath = Path(root) / file
                if update_imports_in_file(filepath):
                    updated_files.append(filepath)
                    print(f"✓ Updated: {filepath.relative_to(project_root)}")
    
    print(f"\nTotal files updated: {len(updated_files)}")

if __name__ == "__main__":
    main()