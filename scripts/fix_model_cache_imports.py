#!/usr/bin/env python3
"""
Script to fix cache imports in models.
"""

import os
import re
from pathlib import Path

def update_imports_in_file(filepath):
    """Update imports in a single file."""
    with open(filepath, 'r') as f:
        content = f.read()
    
    original_content = content
    
    # Replace hardware.cache memoize import with interface import
    content = re.sub(
        r'from yemen_trade_diagnostic\.hardware\.cache import (.*?)memoize',
        'from yemen_trade_diagnostic.interfaces.cache_interface import \\1memoize',
        content
    )
    
    # Also fix StorageTier imports if they're mixed with memoize
    content = re.sub(
        r'from yemen_trade_diagnostic\.hardware\.cache import StorageTier, memoize',
        'from yemen_trade_diagnostic.hardware.cache import StorageTier\nfrom yemen_trade_diagnostic.interfaces.cache_interface import memoize',
        content
    )
    
    # Fix bare memoize imports
    content = re.sub(
        r'from yemen_trade_diagnostic\.hardware\.cache import memoize',
        'from yemen_trade_diagnostic.interfaces.cache_interface import memoize',
        content
    )
    
    if content != original_content:
        with open(filepath, 'w') as f:
            f.write(content)
        return True
    return False

def main():
    """Update all model files."""
    project_root = Path(__file__).parent.parent
    models_dir = project_root / "src" / "yemen_trade_diagnostic" / "models"
    
    updated_files = []
    
    for root, dirs, files in os.walk(models_dir):
        for file in files:
            if file.endswith('.py'):
                filepath = Path(root) / file
                if update_imports_in_file(filepath):
                    updated_files.append(filepath)
                    print(f"✓ Updated: {filepath.relative_to(project_root)}")
    
    print(f"\nTotal files updated: {len(updated_files)}")

if __name__ == "__main__":
    main()