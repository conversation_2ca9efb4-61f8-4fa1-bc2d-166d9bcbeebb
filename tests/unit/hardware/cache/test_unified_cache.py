"""
Tests for the unified hardware cache module.

This module tests the consolidated cache functionality from hardware.cache.
"""

import unittest
from unittest.mock import Mock, patch

from yemen_trade_diagnostic.hardware.cache import (
    Cache,
    CacheStrategy,
    StorageTier,
    get_cache,
)


class TestUnifiedCache(unittest.TestCase):
    """Test the unified cache implementation."""
    
    def setUp(self):
        """Set up test environment."""
        self.cache = Cache(
            strategy=CacheStrategy.SIMPLE,
            enable_compression=False,
            enable_hardware_acceleration=False,
        )
    
    def test_basic_get_set(self):
        """Test basic cache get/set operations."""
        # Set a value
        self.cache.set("test_key", "test_value")
        
        # Get the value
        result = self.cache.get("test_key")
        self.assertEqual(result, "test_value")
    
    def test_cache_miss(self):
        """Test cache miss returns None."""
        result = self.cache.get("nonexistent_key")
        self.assertIsNone(result)
    
    def test_get_or_compute(self):
        """Test get with compute function."""
        compute_called = False
        
        def compute_func():
            nonlocal compute_called
            compute_called = True
            return "computed_value"
        
        # First call should compute
        result = self.cache.get("compute_key", compute_func)
        self.assertEqual(result, "computed_value")
        self.assertTrue(compute_called)
        
        # Second call should use cache
        compute_called = False
        result = self.cache.get("compute_key", compute_func)
        self.assertEqual(result, "computed_value")
        self.assertFalse(compute_called)
    
    def test_ttl_expiration(self):
        """Test TTL expiration."""
        import time
        
        # Set with short TTL
        self.cache.set("ttl_key", "ttl_value", ttl=0.1)
        
        # Should exist immediately
        self.assertEqual(self.cache.get("ttl_key"), "ttl_value")
        
        # Wait for expiration
        time.sleep(0.2)
        
        # Should be expired
        self.assertIsNone(self.cache.get("ttl_key"))
    
    def test_semantic_keys(self):
        """Test semantic key generation."""
        cache = Cache(strategy=CacheStrategy.SEMANTIC)
        
        # These should potentially share cache
        cache.set("exports:yemen:2023", "data1")
        
        # Get stats to verify
        stats = cache.get_stats()
        self.assertGreater(stats["total_entries"], 0)


class TestCacheStrategies(unittest.TestCase):
    """Test cache strategies."""
    
    def test_eviction_strategy(self):
        """Test LRU eviction."""
        # Create small cache
        cache = Cache(max_memory_mb=1)
        
        # Fill cache
        for i in range(100):
            cache.set(f"key_{i}", f"value_{i}" * 1000)
        
        # Check that eviction occurred
        stats = cache.get_stats()
        self.assertGreater(stats["evictions"], 0)
    
    def test_invalidation_pattern(self):
        """Test pattern-based invalidation."""
        cache = Cache()
        
        # Set multiple keys
        cache.set("export:2022:jan", "data1")
        cache.set("export:2022:feb", "data2")
        cache.set("import:2022:jan", "data3")
        
        # Invalidate pattern
        count = cache.invalidate_pattern("export:2022:*")
        self.assertEqual(count, 2)
        
        # Check remaining
        self.assertIsNone(cache.get("export:2022:jan"))
        self.assertIsNone(cache.get("export:2022:feb"))
        self.assertEqual(cache.get("import:2022:jan"), "data3")


if __name__ == '__main__':
    unittest.main()
