{"timestamp": "2025-05-23T21:43:11.084617", "systems": {"Data System": {"status": "Partial", "details": {"import": "OK", "sources": ["baci", "worldbank", "yemen_exports", "yemen_imports", "gdp", "country_codes", "product_codes"], "test_load": "Error: '>=' not supported between instances of 'int' and 'CircuitBreakerConfig'", "pipeline_migrations": [{"pipeline": "pipelines/sophistication_rca_loader.py", "migrated": false}, {"pipeline": "pipelines/batch_processing.py", "migrated": false}]}, "issues": ["Some pipelines not migrated to new data loader"]}, "Error Handling System": {"status": "ERROR", "details": {"imports": "OK", "decorator_test": "OK"}, "issues": ["Error handling system error: __init__() missing 2 required positional arguments: 'name' and 'checks'"]}, "Cache System": {"status": "OK", "details": {"instance_creation": "OK", "basic_operations": "OK", "stats": {"get_operations": 0, "set_operations": 0}, "memory_cache": "OK"}, "issues": []}, "Hardware System": {"status": "OK", "details": {"detector": "OK", "system": {"is_apple_silicon": true, "is_m3_pro": true, "total_cores": 12, "total_memory_gb": 36.0, "has_metal": true, "should_use_acceleration": true}, "hardware_manager": "Error: cannot import name 'get_hardware_manager' from 'yemen_trade_diagnostic.hardware' (/Users/<USER>/PycharmProjects/Yemen_Trade_Diagnostic/src/yemen_trade_diagnostic/hardware/__init__.py)"}, "issues": []}, "Pipeline System": {"status": "ERROR", "details": {}, "issues": ["Pipeline system error: invalid syntax (composition.py, line 142)"]}, "Monitoring System": {"status": "OK", "details": {"imports": "OK", "decorator_test": "OK", "metrics_collector": "OK"}, "issues": []}, "Visualization System": {"status": "OK", "details": {"v1_archived": false, "v2_manager": "OK", "manager_instance": "OK"}, "issues": []}, "Test System": {"status": "OK", "details": {"test_structure": {"unit": 202, "integration": 52, "performance": 11, "e2e": 1}, "total_test_files": 266, "test_runner": "OK"}, "issues": []}}, "overall_health": "Fair", "issues": ["Some pipelines not migrated to new data loader", "Error handling system error: __init__() missing 2 required positional arguments: 'name' and 'checks'", "Pipeline system error: invalid syntax (composition.py, line 142)"], "recommendations": ["Address 3 identified issues", "Complete implementation of Data System", "Fix critical errors in Error Handling System", "Fix critical errors in Pipeline System", "Complete Phase 2.6 - Data System Streamline (currently at 80%)", "Begin Phase 2.5 - Documentation Enhancement"]}