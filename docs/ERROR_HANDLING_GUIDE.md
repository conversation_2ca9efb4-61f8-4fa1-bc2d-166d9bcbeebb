# Error Handling System v2.0 - Complete Guide

## Overview

Yemen Trade Diagnostic features a state-of-the-art error handling system that provides comprehensive protection, monitoring, and recovery capabilities throughout the entire application. The system is designed around modern Python patterns and provides a unified interface for all error handling needs.

## Quick Start

### Basic Usage

```python
from yemen_trade_diagnostic.errors import protect, error_context, OperationType

# Decorator pattern - most common usage
@protect("operation_name", OperationType.COMPUTATION)
def my_function(data):
    # Your code here - automatically protected
    return process_data(data)

# Context manager pattern
with error_context("data_loading", OperationType.DATA_LOADING):
    data = load_large_dataset()
    # Any errors here are automatically handled
```

### Operation Types

The system provides specialized error handling for different operation types:

- `OperationType.DATA_LOADING` - For data loading operations
- `OperationType.COMPUTATION` - For general computations
- `OperationType.VISUALIZATION` - For chart/visualization generation
- `OperationType.HARDWARE_ACCELERATION` - For hardware-accelerated operations
- `OperationType.PIPELINE_EXECUTION` - For pipeline orchestration
- `OperationType.MODEL_CALCULATION` - For model calculations

## Core Features

### 1. Circuit Breakers

Automatic failure detection and circuit breaking to prevent cascading failures:

```python
@protect("external_api_call", OperationType.DATA_LOADING)
def call_external_api():
    # If this fails repeatedly, circuit breaker will open
    # and provide fast-fail behavior
    return api.get_data()
```

**Features:**
- Adaptive thresholds based on error patterns
- State persistence across application restarts
- Automatic recovery testing
- Configurable failure thresholds

### 2. Graceful Degradation

Automatic fallback strategies when operations fail:

```python
@protect("complex_calculation", OperationType.COMPUTATION)
def complex_calculation(data):
    # If this fails, system automatically tries:
    # 1. Simplified calculation
    # 2. Cached result
    # 3. Default value
    return compute_complex_metric(data)
```

### 3. Performance Monitoring

Real-time monitoring and analytics:

```python
from yemen_trade_diagnostic.errors import get_performance_metrics, get_health_status

# Get performance metrics
metrics = get_performance_metrics()
print(f"Error rate: {metrics['error_rate']}")
print(f"Average response time: {metrics['avg_response_time']}")

# Get health status
health = get_health_status()
print(f"Circuit breakers: {health['circuit_breakers']}")
```

### 4. Hardware-Aware Error Handling

The system automatically adjusts error handling strategies based on available hardware:

```python
# Hardware operations get optimized error handling
@protect("gpu_computation", OperationType.HARDWARE_ACCELERATION)
def accelerated_computation(matrix):
    # Automatically uses hardware-optimized timeouts and fallbacks
    return gpu_accelerated_multiply(matrix)
```

## Configuration

### Predefined Configurations

The system provides optimized configurations for common scenarios:

```python
from yemen_trade_diagnostic.errors import (
    DATA_LOADING_CONFIG,
    COMPUTATION_CONFIG,
    VISUALIZATION_CONFIG,
    HARDWARE_CONFIG,
    PIPELINE_CONFIG,
    MODEL_CONFIG
)

# Use predefined configuration
@protect("data_loading", OperationType.DATA_LOADING, config=DATA_LOADING_CONFIG)
def load_data():
    pass
```

### Custom Configuration

Create custom error handling configurations:

```python
from yemen_trade_diagnostic.errors import ErrorConfig, ErrorHandlingMode

custom_config = ErrorConfig(
    mode=ErrorHandlingMode.RESILIENT,
    max_retries=5,
    timeout_seconds=120,
    circuit_breaker_threshold=3,
    graceful_degradation_enabled=True,
    hardware_aware=True
)

@protect("custom_operation", OperationType.COMPUTATION, config=custom_config)
def custom_operation():
    pass
```

## Advanced Usage

### Direct Error Manager Access

For advanced use cases, access the error manager directly:

```python
from yemen_trade_diagnostic.errors import error_manager

# Execute with full control
result = error_manager.execute_with_protection(
    func=my_function,
    operation_name="complex_operation",
    operation_type=OperationType.COMPUTATION,
    config=custom_config,
    arg1="value1",
    arg2="value2"
)

# Get detailed health status
health = error_manager.get_health_status()

# Reset statistics
error_manager.reset_stats("operation_name")
```

### Error Analytics

Access detailed error analytics:

```python
import pandas as pd
from yemen_trade_diagnostic.errors import get_performance_metrics

# Get performance data as DataFrame
df = get_performance_metrics()
print(df[['operation', 'error_rate', 'recovery_rate', 'avg_duration']])

# Analyze error patterns
high_error_ops = df[df['error_rate'] > 0.1]
print(f"Operations with high error rates: {high_error_ops['operation'].tolist()}")
```

## Migration Guide

### From Old Error Interface

**Old Code:**
```python
from yemen_trade_diagnostic.interfaces.error_interface import with_error_handling

@with_error_handling
def my_function():
    pass
```

**New Code:**
```python
from yemen_trade_diagnostic.errors import protect, OperationType

@protect("my_function", OperationType.COMPUTATION)
def my_function():
    pass
```

### From Hardware Circuit Breaker

**Old Code:**
```python
from yemen_trade_diagnostic.hardware.core.circuit_breaker import CircuitBreaker

breaker = CircuitBreaker("operation")
result = breaker.execute(my_function)
```

**New Code:**
```python
from yemen_trade_diagnostic.errors import protect, OperationType

@protect("operation", OperationType.HARDWARE_ACCELERATION)
def my_function():
    pass

result = my_function()
```

## Best Practices

### 1. Choose Appropriate Operation Types

- Use `DATA_LOADING` for I/O operations
- Use `COMPUTATION` for CPU-intensive tasks
- Use `HARDWARE_ACCELERATION` for GPU/specialized hardware
- Use `VISUALIZATION` for chart generation
- Use `PIPELINE_EXECUTION` for orchestration

### 2. Meaningful Operation Names

```python
# Good - descriptive and unique
@protect("load_baci_trade_data", OperationType.DATA_LOADING)
def load_baci_data():
    pass

# Bad - generic and ambiguous
@protect("process", OperationType.COMPUTATION)
def process():
    pass
```

### 3. Monitor Performance

Regularly check performance metrics and health status:

```python
# In your monitoring/health check code
health = get_health_status()
if health['circuit_breakers']:
    for name, status in health['circuit_breakers'].items():
        if status['state'] != 'CLOSED':
            logger.warning(f"Circuit breaker {name} is {status['state']}")
```

### 4. Use Context Managers for Blocks

For operations that span multiple function calls:

```python
with error_context("data_pipeline", OperationType.PIPELINE_EXECUTION):
    data = load_data()
    processed = process_data(data)
    save_data(processed)
```

## Error Handling Modes

### STRICT Mode
```python
config = ErrorConfig(mode=ErrorHandlingMode.STRICT)
# - Fail fast on any error
# - No retries or fallbacks
# - Maximum performance, minimum resilience
```

### RESILIENT Mode (Default)
```python
config = ErrorConfig(mode=ErrorHandlingMode.RESILIENT)
# - Circuit breakers enabled
# - Limited retries
# - Balanced performance and reliability
```

### GRACEFUL Mode
```python
config = ErrorConfig(mode=ErrorHandlingMode.GRACEFUL)
# - Full graceful degradation
# - Multiple fallback strategies
# - Maximum resilience
```

### PERFORMANCE Mode
```python
config = ErrorConfig(mode=ErrorHandlingMode.PERFORMANCE)
# - Optimized for speed
# - Minimal error handling overhead
# - Used for hardware acceleration
```

## Monitoring and Debugging

### Health Checks

```python
from yemen_trade_diagnostic.errors import get_health_status

health = get_health_status()
print(f"System status: {health}")

# Check specific components
if health['circuit_breakers']:
    for name, cb in health['circuit_breakers'].items():
        print(f"{name}: {cb['state']} ({cb['failure_count']} failures)")
```

### Performance Analysis

```python
from yemen_trade_diagnostic.errors import get_performance_metrics

# Get comprehensive performance data
metrics_df = get_performance_metrics()

# Identify problematic operations
slow_ops = metrics_df[metrics_df['avg_duration'] > 10.0]
unreliable_ops = metrics_df[metrics_df['error_rate'] > 0.1]

print("Slow operations:", slow_ops['operation'].tolist())
print("Unreliable operations:", unreliable_ops['operation'].tolist())
```

## Troubleshooting

### Common Issues

1. **High Error Rates**
   ```python
   # Check which operations are failing
   metrics = get_performance_metrics()
   failing_ops = metrics[metrics['error_rate'] > 0.2]
   ```

2. **Circuit Breakers Opening**
   ```python
   # Check circuit breaker status
   health = get_health_status()
   open_breakers = [name for name, cb in health['circuit_breakers'].items() 
                   if cb['state'] == 'OPEN']
   ```

3. **Performance Degradation**
   ```python
   # Monitor response times
   metrics = get_performance_metrics()
   slow_ops = metrics[metrics['avg_duration'] > 5.0]
   ```

### Debug Mode

Enable verbose logging for debugging:

```python
import logging
logging.getLogger('yemen_trade_diagnostic.errors').setLevel(logging.DEBUG)
```

## Integration Examples

### With Pipelines

```python
from yemen_trade_diagnostic.errors import protect, OperationType
from yemen_trade_diagnostic.pipelines.pipeline import Pipeline

class MyPipeline(Pipeline):
    @protect("pipeline_execution", OperationType.PIPELINE_EXECUTION)
    def run(self, year: int):
        with error_context("data_loading", OperationType.DATA_LOADING):
            data = self.load_data(year)
        
        with error_context("processing", OperationType.COMPUTATION):
            result = self.process_data(data)
        
        return result
```

### With Hardware Acceleration

```python
@protect("gpu_matrix_multiply", OperationType.HARDWARE_ACCELERATION)
def accelerated_matrix_multiply(a, b):
    # Automatically handles hardware failures and fallbacks
    return gpu_multiply(a, b)
```

### With Visualization

```python
@protect("chart_generation", OperationType.VISUALIZATION)
def generate_trade_chart(data):
    # Handles rendering failures, memory issues, etc.
    return create_chart(data)
```

## API Reference

### Core Functions

- `protect(name, operation_type, config=None)` - Decorator for function protection
- `error_context(name, operation_type, config=None)` - Context manager for block protection
- `execute_protected(func, name, operation_type, config=None, *args, **kwargs)` - Direct execution
- `get_health_status()` - Get system health status
- `get_performance_metrics()` - Get performance metrics as DataFrame

### Configuration Classes

- `ErrorConfig` - Error handling configuration
- `ErrorHandlingMode` - Error handling modes (STRICT, RESILIENT, GRACEFUL, PERFORMANCE)
- `OperationType` - Operation types for specialized handling

### Predefined Configs

- `DATA_LOADING_CONFIG` - Optimized for data loading
- `COMPUTATION_CONFIG` - General computation settings
- `VISUALIZATION_CONFIG` - Visualization-specific settings
- `HARDWARE_CONFIG` - Performance-optimized for hardware
- `PIPELINE_CONFIG` - Pipeline orchestration settings
- `MODEL_CONFIG` - Model calculation settings

---

## Support

For issues or questions about the error handling system:

1. Check the [troubleshooting section](#troubleshooting) above
2. Review [performance metrics](#performance-analysis) for your operations
3. Open an issue with error logs and performance data
4. Include your error handling configuration in bug reports

**Note:** The error handling system is designed to be self-healing and provide detailed diagnostics. Most issues can be resolved by reviewing the performance metrics and adjusting configurations accordingly.